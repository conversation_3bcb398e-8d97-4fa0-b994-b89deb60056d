/**
 * نظام الإشعارات العصري
 * Modern Notifications System
 */

class ModernNotifications {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        // إنشاء حاوية الإشعارات
        this.createContainer();
        
        // ربط الأحداث
        this.bindEvents();
    }

    createContainer() {
        // إزالة الحاوية القديمة إن وجدت
        const existingContainer = document.getElementById('modern-notifications-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // إنشاء حاوية جديدة
        this.container = document.createElement('div');
        this.container.id = 'modern-notifications-container';
        this.container.className = 'fixed top-5 left-5 z-50 max-w-md space-y-3 pointer-events-none';
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = 5000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);

        // تأثير الظهور
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // إزالة تلقائية
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `modern-notification ${type} pointer-events-auto`;
        
        const config = this.getTypeConfig(type);
        
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="${config.icon}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="modernNotifications.remove(this.closest('.modern-notification'))">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        return notification;
    }

    getTypeConfig(type) {
        const configs = {
            success: {
                icon: 'fas fa-check-circle',
                color: '#10b981'
            },
            error: {
                icon: 'fas fa-exclamation-circle',
                color: '#ef4444'
            },
            warning: {
                icon: 'fas fa-exclamation-triangle',
                color: '#f59e0b'
            },
            info: {
                icon: 'fas fa-info-circle',
                color: '#06b6d4'
            }
        };

        return configs[type] || configs.info;
    }

    remove(notification) {
        if (!notification) return;

        notification.classList.add('removing');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    success(message, duration = 5000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 7000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 6000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 5000) {
        return this.show(message, 'info', duration);
    }

    bindEvents() {
        // ربط رسائل Django
        this.bindDjangoMessages();
    }

    bindDjangoMessages() {
        // البحث عن رسائل Django وتحويلها لإشعارات عصرية
        const djangoMessages = document.querySelectorAll('.messages .alert, .alert');
        
        djangoMessages.forEach(message => {
            const text = message.textContent.trim();
            let type = 'info';

            if (message.classList.contains('alert-success') || message.classList.contains('success')) {
                type = 'success';
            } else if (message.classList.contains('alert-danger') || message.classList.contains('error')) {
                type = 'error';
            } else if (message.classList.contains('alert-warning') || message.classList.contains('warning')) {
                type = 'warning';
            }

            if (text) {
                this.show(text, type);
                message.style.display = 'none';
            }
        });
    }

    // دالة للتأكيد العصري
    confirm(options) {
        return new Promise((resolve) => {
            const modal = this.createConfirmModal(options, resolve);
            document.body.appendChild(modal);
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 100);
        });
    }

    createConfirmModal(options, resolve) {
        const modal = document.createElement('div');
        modal.className = 'modern-confirm-overlay';
        
        const config = this.getTypeConfig(options.type || 'warning');
        
        modal.innerHTML = `
            <div class="modern-confirm-modal ${options.type || 'warning'}">
                <div class="modern-confirm-header">
                    <div class="modern-confirm-icon">
                        <i class="${config.icon}"></i>
                    </div>
                    <h3 class="modern-confirm-title">${options.title || 'تأكيد العملية'}</h3>
                </div>
                <div class="modern-confirm-body">
                    <p class="modern-confirm-message">${options.message || 'هل أنت متأكد من هذا الإجراء؟'}</p>
                </div>
                <div class="modern-confirm-actions">
                    <button class="modern-btn modern-btn-secondary" onclick="modernNotifications.closeConfirm(this, false)">
                        ${options.cancelText || 'إلغاء'}
                    </button>
                    <button class="modern-btn modern-btn-primary" onclick="modernNotifications.closeConfirm(this, true)">
                        ${options.confirmText || 'تأكيد'}
                    </button>
                </div>
            </div>
        `;

        modal._resolve = resolve;
        return modal;
    }

    closeConfirm(button, result) {
        const modal = button.closest('.modern-confirm-overlay');
        modal.classList.add('removing');
        
        setTimeout(() => {
            modal._resolve(result);
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
}

// إنشاء مثيل عام
const modernNotifications = new ModernNotifications();

// دوال مساعدة عامة
function showSuccess(message) {
    modernNotifications.success(message);
}

function showError(message) {
    modernNotifications.error(message);
}

function showWarning(message) {
    modernNotifications.warning(message);
}

function showInfo(message) {
    modernNotifications.info(message);
}

function confirmAction(options) {
    return modernNotifications.confirm(options);
}

// تصدير للاستخدام في وحدات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernNotifications;
}
