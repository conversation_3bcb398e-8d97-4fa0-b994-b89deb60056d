{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة تحكم المسؤول الأعلى | استقدامي{% endblock %}</title>

    <!-- Google Fonts - Tajawal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'admin-primary': '#0F2557',
                        'admin-secondary': '#1E5F74',
                        'admin-accent': '#4B8F8C',
                        'admin-light': '#E5F1F1',
                        'admin-dark': '#0A1A3F',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS for Super Admin -->
    <link rel="stylesheet" href="{% static 'css/super_admin.css' %}">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="min-h-screen flex bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200"
      x-data="{ sidebarOpen: true, darkMode: localStorage.getItem('darkMode') === 'true' }"
      :class="{ 'dark': darkMode }">

    <!-- Sidebar -->
    <aside class="super-admin-sidebar" :class="{'super-admin-sidebar-collapsed': !sidebarOpen}">
        <!-- Sidebar Header -->
        <div class="super-admin-sidebar-header">
            <div class="flex items-center space-x-3 rtl:space-x-reverse" x-show="sidebarOpen">
                <img src="{% static 'images/logo.webp' %}" alt="شعار المنصة"
                     class="w-10 h-10 object-contain hover:animate-pulse transition-all duration-300"
                     onerror="this.src='{% static 'images/logo-placeholder.png' %}'; this.onerror=null;">
                <div class="flex flex-col">
                    <span class="text-lg font-bold text-admin-accent">استقدامي</span>
                    <span class="text-xs text-gray-300">لوحة المسؤول الأعلى</span>
                </div>
            </div>
            <div class="flex justify-center" x-show="!sidebarOpen">
                <img src="{% static 'images/logo.webp' %}" alt="شعار المنصة"
                     class="w-8 h-8 object-contain hover:animate-pulse transition-all duration-300"
                     onerror="this.src='{% static 'images/logo-placeholder.png' %}'; this.onerror=null;">
            </div>
            <button @click="sidebarOpen = !sidebarOpen" class="text-admin-light hover:text-white">
                <i class="fas" :class="sidebarOpen ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
            </button>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-5">
            <ul class="space-y-2 px-3">
                <!-- Dashboard -->
                <li>
                    <a href="{% url 'super_admin:dashboard' %}"
                       class="super-admin-sidebar-item {% if request.path == '/super_admin/' %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span x-show="sidebarOpen">لوحة التحكم</span>
                    </a>
                </li>

                <!-- Companies Management -->
                <li>
                    <a href="{% url 'super_admin:create_company' %}"
                       class="super-admin-sidebar-item {% if '/super_admin/companies/' in request.path %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <span x-show="sidebarOpen">إدارة الشركات</span>
                    </a>
                </li>

                <!-- Database Management -->
                <li>
                    <a href="{% url 'super_admin:database_management' %}"
                       class="super-admin-sidebar-item {% if '/super_admin/database/' in request.path %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <span x-show="sidebarOpen">قواعد البيانات</span>
                    </a>
                </li>

                <!-- System Logs -->
                <li>
                    <a href="{% url 'super_admin:system_logs' %}"
                       class="super-admin-sidebar-item {% if '/super_admin/logs/' in request.path %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <span x-show="sidebarOpen">سجل الأحداث</span>
                    </a>
                </li>

                <!-- Backup & Scheduling -->
                <li>
                    <a href="{% url 'super_admin:system_backup' %}"
                       class="super-admin-sidebar-item {% if '/super_admin/backup/' in request.path %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-save"></i>
                        </div>
                        <span x-show="sidebarOpen">النسخ الاحتياطي</span>
                    </a>
                </li>

                <!-- Settings -->
                <li>
                    <a href="{% url 'super_admin:system_settings_new' %}"
                       class="super-admin-sidebar-item {% if '/super_admin/settings/' in request.path %}active{% endif %}">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span x-show="sidebarOpen">الإعدادات</span>
                    </a>
                </li>

                <!-- Logout -->
                <li class="mt-auto">
                    <a href="{% url 'super_admin:super_admin_logout' %}"
                       class="super-admin-sidebar-item text-red-300 hover:text-red-100 hover:bg-red-600">
                        <div class="super-admin-sidebar-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <span x-show="sidebarOpen">تسجيل الخروج</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Sidebar Footer -->
        <div class="absolute bottom-0 w-full p-3 border-t border-admin-secondary text-xs text-center" x-show="sidebarOpen">
            <p>استقدامي - الإصدار 1.0</p>
            <p class="text-admin-accent">{% now "Y-m-d" %}</p>
        </div>
    </aside>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col" :class="sidebarOpen ? 'mr-64' : 'mr-16'">
        <!-- Top Header -->
        <header class="bg-white dark:bg-gray-800 shadow-md z-10">
            <div class="flex justify-between items-center px-6 py-3">
                <!-- Page Title -->
                <h1 class="text-xl font-bold text-admin-primary dark:text-admin-accent">
                    {% block page_title %}لوحة التحكم{% endblock %}
                </h1>

                <!-- Right - User Info and Settings -->
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)"
                            class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                            title="تبديل الوضع الليلي">
                        <i class="fas" :class="darkMode ? 'fa-sun text-yellow-300' : 'fa-moon text-gray-500'"></i>
                    </button>

                    <!-- User Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="flex items-center space-x-2 rtl:space-x-reverse bg-admin-primary hover:bg-admin-secondary rounded-full px-3 py-1 transition-colors text-white">
                            <div class="w-8 h-8 rounded-full bg-admin-accent flex items-center justify-center">
                                <i class="fas fa-user-shield text-white"></i>
                            </div>
                            <span class="font-medium text-sm">{{ user.username }}</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div x-show="open" @click.away="open = false"
                             class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-xl text-xs z-50 border border-gray-200 dark:border-gray-700 overflow-hidden transform origin-top-right transition-all duration-300">
                            <div class="p-3 border-b border-gray-200 dark:border-gray-700">
                                <div class="font-bold text-gray-900 dark:text-gray-100 text-xs">{{ user.username }}</div>
                                <div class="text-gray-500 dark:text-gray-400 text-xs">المسؤول الأعلى</div>
                            </div>
                            <div class="py-1">
                                <a href="{% url 'super_admin:user_profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                    <i class="fas fa-user-circle ml-2"></i> الملف الشخصي
                                </a>
                                <a href="{% url 'super_admin:change_password' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                    <i class="fas fa-key ml-2"></i> تغيير كلمة المرور
                                </a>
                                <a href="{% url 'super_admin:system_settings_new' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                    <i class="fas fa-cog ml-2"></i> إعدادات النظام
                                </a>
                            </div>
                            <div class="border-t border-gray-200 dark:border-gray-700 py-1">
                                <a href="{% url 'super_admin:super_admin_logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700">
                                    <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="flex-1 p-6 overflow-auto">
            <!-- Messages -->
            {% if messages %}
            <div class="mb-6">
                {% for message in messages %}
                <div class="super-admin-alert {% if message.tags == 'error' %}super-admin-alert-error{% elif message.tags == 'success' %}super-admin-alert-success{% elif message.tags == 'warning' %}super-admin-alert-warning{% else %}super-admin-alert-info{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Page Content -->
            {% if not user.is_authenticated %}
                <div class="flex items-center justify-center min-h-screen">
                    {% block content_unauthenticated %}{% endblock %}
                </div>
            {% else %}
                {% block content %}{% endblock %}
            {% endif %}
        </main>

        <!-- Footer -->
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 text-center text-sm text-gray-600 dark:text-gray-400">
            <div class="container mx-auto">
                <p>جميع الحقوق محفوظة &copy; {% now "Y" %} استقدامي - نظام إدارة العمالة</p>
                <p>الإصدار 1.0 | تاريخ الإصدار: 2023-12-01</p>
            </div>
        </footer>
    </div>

    <!-- Notification System -->
    <div id="toast-container" class="fixed top-4 left-4 z-50 flex flex-col space-y-4"></div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden" x-data="{ show: false }" x-show="show" x-cloak>
        <div class="absolute inset-0 bg-black bg-opacity-50" @click="show = false"></div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <h3 class="text-lg font-bold mb-4 text-gray-900 dark:text-white">تأكيد الحذف</h3>
            <p class="mb-6 text-gray-700 dark:text-gray-300">هل أنت متأكد من رغبتك في حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="flex justify-end space-x-3 rtl:space-x-reverse">
                <button @click="show = false" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">إلغاء</button>
                <button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">حذف</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Toast notification system
        function showToast(message, type = 'success', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `px-4 py-3 rounded-lg shadow-lg flex items-center ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            } text-white`;

            const icon = document.createElement('i');
            icon.className = `fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
            } ml-3`;

            const text = document.createElement('span');
            text.textContent = message;

            toast.appendChild(icon);
            toast.appendChild(text);

            const container = document.getElementById('toast-container');
            container.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, duration);
        }

        // Delete confirmation system
        function confirmDelete(callback) {
            const modal = document.getElementById('delete-modal');
            const confirmBtn = document.getElementById('confirm-delete');

            modal.__x.$data.show = true;

            confirmBtn.onclick = () => {
                callback();
                modal.__x.$data.show = false;
            };
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
