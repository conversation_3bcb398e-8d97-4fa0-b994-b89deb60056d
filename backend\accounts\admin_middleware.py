"""
ميدلوير لتقييد الوصول إلى صفحة المسؤول الافتراضية في Django
"""

from django.shortcuts import redirect, render
from django.urls import reverse
from django.contrib import messages

class AdminAccessMiddleware:
    """
    ميدلوير لتقييد الوصول إلى صفحة المسؤول الافتراضية في Django
    يسمح فقط للمستخدم المحدد (منشئ النظام) بالوصول إلى صفحة المسؤول
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # التحقق من المسار
        if request.path.startswith('/admin/'):
            # التحقق من تسجيل الدخول
            if not request.user.is_authenticated:
                return redirect('login')

            # التحقق من صلاحيات المسؤول الأعلى (منشئ النظام)
            # يمكن تعديل هذا الشرط حسب احتياجاتك لتحديد المستخدم المسموح له بالوصول
            if not (request.user.is_superuser and request.user.username == 'admin' and hasattr(request.user, 'super_admin_profile')):
                messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
                return redirect('login')

        response = self.get_response(request)
        return response
