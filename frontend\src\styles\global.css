/* تنسيقات عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* تنسيقات خاصة بالاتجاه RTL */
body {
  direction: rtl;
  text-align: right;
}

/* تنسيقات الخط */
body {
  font-family: 'Cairo', sans-serif;
}

/* تنسيقات التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* تنسيقات التحديد */
::selection {
  background-color: #174785;
  color: white;
}

/* تنسيقات الانتقالات */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* تنسيقات الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background-color: white !important;
  }
  
  .print-container {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 640px) {
  .hide-on-mobile {
    display: none !important;
  }
}

/* تنسيقات الشاشات المتوسطة */
@media (max-width: 768px) {
  .hide-on-tablet {
    display: none !important;
  }
}

/* تنسيقات الشاشات الكبيرة */
@media (min-width: 1024px) {
  .hide-on-desktop {
    display: none !important;
  }
}
