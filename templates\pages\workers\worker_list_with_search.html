{% extends 'layouts/dashboard.html' %}
{% load static %}

{% block title %}قائمة العمال | منصة استقدامي السحابية{% endblock %}

{# تفعيل البحث في هذه الصفحة #}
{% block extra_css %}
<style>
    .search-highlight {
        background-color: rgba(255, 255, 0, 0.3);
        padding: 2px;
        border-radius: 2px;
    }
</style>
{% endblock %}

{% block content %}
{# تمرير متغير show_search بقيمة True لإظهار البحث في الشريط العلوي #}
{% with show_search=True %}

<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-800">قائمة العمال</h2>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'workers:worker_create' %}"
               class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-300 flex items-center">
                <i class="fas fa-plus ml-2"></i>
                إضافة عامل جديد
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-wrap gap-4">
            <div class="w-full md:w-auto">
                <label for="nationality-filter" class="block text-sm font-medium text-gray-700 mb-1">الجنسية</label>
                <select id="nationality-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">الكل</option>
                    <option value="saudi">سعودي</option>
                    <option value="egyptian">مصري</option>
                    <option value="indian">هندي</option>
                    <option value="filipino">فلبيني</option>
                    <option value="bangladeshi">بنغلاديشي</option>
                </select>
            </div>

            <div class="w-full md:w-auto">
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select id="status-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">الكل</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="vacation">إجازة</option>
                </select>
            </div>

            <div class="w-full md:w-auto">
                <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">نوع العقد</label>
                <select id="type-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">الكل</option>
                    <option value="contract">عقد دائم</option>
                    <option value="monthly">عقد شهري</option>
                    <option value="custom">خدمة مخصصة</option>
                </select>
            </div>

            <div class="w-full md:w-auto flex items-end">
                <button id="apply-filters" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-300">
                    <i class="fas fa-filter ml-2"></i>
                    تطبيق الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- جدول العمال -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-users text-primary ml-2"></i>
                العمال المسجلين
            </h3>
            <div class="text-sm text-gray-600">
                إجمالي العمال: <span class="font-bold">{{ workers|length }}</span>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table id="workers-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded text-primary focus:ring-primary">
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الاسم
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الجنسية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الجواز
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الإقامة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            نوع العقد
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for worker in workers %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="worker-checkbox rounded text-primary focus:ring-primary" value="{{ worker.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 ml-3">
                                    {% if worker.photo %}
                                    <img class="h-10 w-10 rounded-full object-cover" src="{{ worker.photo.url }}" alt="{{ worker.first_name }}">
                                    {% else %}
                                    <div class="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center text-primary">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ worker.first_name }} {{ worker.last_name }}</div>
                                    <div class="text-sm text-gray-500">{{ worker.job_title }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ worker.get_nationality_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ worker.passport_number }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ worker.iqama_number }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                  {% if worker.status == 'active' %}bg-green-100 text-green-800
                                  {% elif worker.status == 'inactive' %}bg-red-100 text-red-800
                                  {% elif worker.status == 'vacation' %}bg-blue-100 text-blue-800
                                  {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ worker.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ worker.get_contract_type_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{% url 'workers:worker_detail' worker.id %}" class="text-primary hover:text-primary-dark" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'workers:worker_update' worker.id %}" class="text-yellow-600 hover:text-yellow-800" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="text-red-600 hover:text-red-800 delete-worker" data-id="{{ worker.id }}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-10 text-center text-gray-500">
                            <i class="fas fa-users text-4xl mb-3 text-gray-300"></i>
                            <p class="text-lg font-medium">لا يوجد عمال مسجلين</p>
                            <a href="{% url 'workers:worker_create' %}" class="mt-4 inline-block bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-300">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة عامل جديد
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    عرض <span class="font-medium">1</span> إلى <span class="font-medium">{{ workers|length }}</span> من أصل <span class="font-medium">{{ workers|length }}</span> عامل
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50" disabled>
                        السابق
                    </button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50" disabled>
                        التالي
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endwith %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all');
        const workerCheckboxes = document.querySelectorAll('.worker-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                workerCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }

        // تحديث حالة "تحديد الكل" عند تغيير الاختيارات الفردية
        workerCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allChecked = Array.from(workerCheckboxes).every(cb => cb.checked);
                const someChecked = Array.from(workerCheckboxes).some(cb => cb.checked);

                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allChecked;
                    selectAllCheckbox.indeterminate = someChecked && !allChecked;
                }
            });
        });

        // تطبيق الفلاتر
        const applyFiltersBtn = document.getElementById('apply-filters');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', function() {
                const nationalityFilter = document.getElementById('nationality-filter').value;
                const statusFilter = document.getElementById('status-filter').value;
                const typeFilter = document.getElementById('type-filter').value;

                // يمكن إضافة منطق الفلترة هنا
                console.log('تطبيق الفلاتر:', { nationalityFilter, statusFilter, typeFilter });

                // مثال على الفلترة البسيطة
                const rows = document.querySelectorAll('#workers-table tbody tr');
                rows.forEach(row => {
                    let show = true;

                    // يمكن تخصيص منطق الفلترة حسب هيكل الجدول

                    row.style.display = show ? '' : 'none';
                });
            });
        }

        // حذف عامل
        const deleteButtons = document.querySelectorAll('.delete-worker');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const workerId = this.getAttribute('data-id');
                // استخدام نظام التأكيد الموحد
                const deleteUrl = `/workers/${workerId}/delete/`;
                window.deleteItem(deleteUrl);
            });
        });
    });
</script>
{% endblock %}
