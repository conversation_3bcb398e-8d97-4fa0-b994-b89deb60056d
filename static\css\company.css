/*
 * منصة استقدامي - ملف CSS مخصص للشركات
 * يحتوي على أنماط خاصة بواجهة الشركات
 * مختلفة تماماً عن أنماط واجهة المسؤول الأعلى
 */

/* متغيرات CSS للألوان */
:root {
    /* ألوان الشعار */
    --primary-blue: #1e3a8a;
    --primary-purple: #7c3aed;
    --primary-cyan: #06b6d4;
    --light-gray: #f9fafb;
    --gradient-start: #6d28d9;
    --gradient-end: #1e3a8a;
    
    /* ألوان إضافية */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* ظلال وحدود */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* انتقالات */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 200ms ease-in-out;
    --transition-slow: 300ms ease-in-out;
}

/* خط Tajawal */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية */
* {
    font-family: 'Tajawal', sans-serif;
    box-sizing: border-box;
}

body {
    background-color: var(--light-gray);
    font-family: 'Tajawal', sans-serif;
    line-height: 1.6;
    color: #374151;
}

/* شريط التنقل */
.navbar-gradient {
    background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
    box-shadow: var(--shadow-lg);
}

.navbar-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* البطاقات */
.card-hover {
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(124, 58, 237, 0.2);
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-blue) 100%);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    background: linear-gradient(135deg, #8b5cf6 0%, #2563eb 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    box-shadow: var(--shadow-md);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    box-shadow: var(--shadow-md);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
    box-shadow: var(--shadow-md);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

/* الجداول */
.table-hover tbody tr {
    transition: all var(--transition-fast);
}

.table-hover tbody tr:hover {
    background-color: rgba(124, 58, 237, 0.05);
    transform: scale(1.01);
    box-shadow: var(--shadow-sm);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

/* شارات الإشعارات */
.notification-badge {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.8; 
        transform: scale(1.05);
    }
}

/* تأثيرات التدرج للنصوص */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-blue), var(--primary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* شريط التقدم */
.progress-bar {
    background: linear-gradient(90deg, var(--primary-purple), var(--primary-cyan));
    border-radius: 9999px;
    transition: width var(--transition-slow);
}

/* تأثيرات الحركة */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes bounceIn {
    0% { 
        opacity: 0; 
        transform: scale(0.3); 
    }
    50% { 
        opacity: 1; 
        transform: scale(1.05); 
    }
    70% { 
        transform: scale(0.9); 
    }
    100% { 
        opacity: 1; 
        transform: scale(1); 
    }
}

/* الوضع الليلي */
.dark body {
    background-color: #111827;
    color: #f9fafb;
}

.dark .card-hover {
    background-color: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);
}

.dark .card-hover:hover {
    border-color: rgba(124, 58, 237, 0.3);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.dark .dropdown-menu {
    background-color: #374151;
    border-color: rgba(255, 255, 255, 0.1);
}

.dark .dropdown-menu::before {
    background-color: #374151;
    border-color: rgba(255, 255, 255, 0.1);
}

.dark .table-hover tbody tr:hover {
    background-color: rgba(124, 58, 237, 0.1);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .card-hover {
        margin-bottom: 1rem;
    }
    
    .navbar-gradient .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .btn-primary,
    .btn-success,
    .btn-danger,
    .btn-warning,
    .btn-info {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar-gradient,
    .dropdown-menu,
    .btn-primary,
    .btn-success,
    .btn-danger,
    .btn-warning,
    .btn-info {
        display: none !important;
    }
    
    .card-hover {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* تحسينات لإمكانية الوصول */
.focus\:ring-purple-500:focus {
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3);
}

.focus\:ring-blue-500:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.focus\:ring-cyan-500:focus {
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.3);
}

/* تحسينات للأداء */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* تخصيصات إضافية */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
