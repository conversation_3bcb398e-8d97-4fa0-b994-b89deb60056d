# Generated by Django 5.2 on 2025-05-13 14:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0010_adminloginattempt'),
    ]

    operations = [
        migrations.AddField(
            model_name='superadminuser',
            name='account_locked',
            field=models.BooleanField(default=False, verbose_name='الحساب مقفل'),
        ),
        migrations.AddField(
            model_name='superadminuser',
            name='account_locked_until',
            field=models.DateTimeField(blank=True, null=True, verbose_name='الحساب مقفل حتى'),
        ),
        migrations.AddField(
            model_name='superadminuser',
            name='allowed_ips',
            field=models.TextField(blank=True, null=True, verbose_name='عناوين IP المسموح بها'),
        ),
        migrations.AddField(
            model_name='superadminuser',
            name='failed_login_attempts',
            field=models.IntegerField(default=0, verbose_name='عدد محاولات تسجيل الدخول الفاشلة'),
        ),
        migrations.AddField(
            model_name='superadminuser',
            name='ip_restriction_enabled',
            field=models.BooleanField(default=False, verbose_name='تفعيل تقييد عناوين IP'),
        ),
        migrations.AddField(
            model_name='superadminuser',
            name='last_failed_login',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر محاولة تسجيل دخول فاشلة'),
        ),
    ]
