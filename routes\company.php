<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\WorkerController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\UserController;

/*
|--------------------------------------------------------------------------
| Company Routes
|--------------------------------------------------------------------------
|
| Here is where you can register company routes for your application.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "company" middleware group.
|
*/

// Authentication Routes
Route::get('/login', [CompanyController::class, 'login'])->name('login');
Route::post('/login', [CompanyController::class, 'authenticate'])->name('authenticate');
Route::get('/verify-database', [CompanyController::class, 'verifyDatabase'])->name('verify_database');
Route::post('/logout', [CompanyController::class, 'logout'])->name('logout');

// Database Error Routes
Route::get('/database-error', [CompanyController::class, 'databaseError'])->name('database_error');
Route::get('/not-found', [CompanyController::class, 'notFound'])->name('not_found');

// Protected Routes (require Company authentication)
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/', [CompanyController::class, 'dashboard'])->name('dashboard');
    
    // Worker Management
    Route::prefix('workers')->name('workers.')->group(function () {
        Route::get('/', [WorkerController::class, 'index'])->name('index');
        Route::get('/create', [WorkerController::class, 'create'])->name('create');
        Route::post('/', [WorkerController::class, 'store'])->name('store');
        Route::get('/{worker}', [WorkerController::class, 'show'])->name('show');
        Route::get('/{worker}/edit', [WorkerController::class, 'edit'])->name('edit');
        Route::put('/{worker}', [WorkerController::class, 'update'])->name('update');
        Route::delete('/{worker}', [WorkerController::class, 'destroy'])->name('destroy');
        Route::get('/type/{type}', [WorkerController::class, 'byType'])->name('by_type');
        Route::get('/status/{status}', [WorkerController::class, 'byStatus'])->name('by_status');
        Route::post('/{worker}/upload-document', [WorkerController::class, 'uploadDocument'])->name('upload_document');
        Route::delete('/documents/{document}', [WorkerController::class, 'deleteDocument'])->name('delete_document');
    });
    
    // Client Management
    Route::prefix('clients')->name('clients.')->group(function () {
        Route::get('/', [ClientController::class, 'index'])->name('index');
        Route::get('/create', [ClientController::class, 'create'])->name('create');
        Route::post('/', [ClientController::class, 'store'])->name('store');
        Route::get('/{client}', [ClientController::class, 'show'])->name('show');
        Route::get('/{client}/edit', [ClientController::class, 'edit'])->name('edit');
        Route::put('/{client}', [ClientController::class, 'update'])->name('update');
        Route::delete('/{client}', [ClientController::class, 'destroy'])->name('destroy');
        Route::get('/type/{type}', [ClientController::class, 'byType'])->name('by_type');
        Route::post('/{client}/upload-document', [ClientController::class, 'uploadDocument'])->name('upload_document');
        Route::delete('/documents/{document}', [ClientController::class, 'deleteDocument'])->name('delete_document');
    });
    
    // Contract Management
    Route::prefix('contracts')->name('contracts.')->group(function () {
        Route::get('/', [ContractController::class, 'index'])->name('index');
        Route::get('/create', [ContractController::class, 'create'])->name('create');
        Route::post('/', [ContractController::class, 'store'])->name('store');
        Route::get('/{contract}', [ContractController::class, 'show'])->name('show');
        Route::get('/{contract}/edit', [ContractController::class, 'edit'])->name('edit');
        Route::put('/{contract}', [ContractController::class, 'update'])->name('update');
        Route::delete('/{contract}', [ContractController::class, 'destroy'])->name('destroy');
        Route::get('/type/{type}', [ContractController::class, 'byType'])->name('by_type');
        Route::get('/status/{status}', [ContractController::class, 'byStatus'])->name('by_status');
        Route::post('/{contract}/upload-document', [ContractController::class, 'uploadDocument'])->name('upload_document');
        Route::delete('/documents/{document}', [ContractController::class, 'deleteDocument'])->name('delete_document');
    });
    
    // Service Management
    Route::prefix('services')->name('services.')->group(function () {
        Route::get('/', [ServiceController::class, 'index'])->name('index');
        Route::get('/create', [ServiceController::class, 'create'])->name('create');
        Route::post('/', [ServiceController::class, 'store'])->name('store');
        Route::get('/{service}', [ServiceController::class, 'show'])->name('show');
        Route::get('/{service}/edit', [ServiceController::class, 'edit'])->name('edit');
        Route::put('/{service}', [ServiceController::class, 'update'])->name('update');
        Route::delete('/{service}', [ServiceController::class, 'destroy'])->name('destroy');
        Route::get('/type/{type}', [ServiceController::class, 'byType'])->name('by_type');
    });
    
    // Booking Management
    Route::prefix('bookings')->name('bookings.')->group(function () {
        Route::get('/', [BookingController::class, 'index'])->name('index');
        Route::get('/calendar', [BookingController::class, 'calendar'])->name('calendar');
        Route::get('/create', [BookingController::class, 'create'])->name('create');
        Route::post('/', [BookingController::class, 'store'])->name('store');
        Route::get('/{booking}', [BookingController::class, 'show'])->name('show');
        Route::get('/{booking}/edit', [BookingController::class, 'edit'])->name('edit');
        Route::put('/{booking}', [BookingController::class, 'update'])->name('update');
        Route::delete('/{booking}', [BookingController::class, 'destroy'])->name('destroy');
        Route::get('/status/{status}', [BookingController::class, 'byStatus'])->name('by_status');
        Route::post('/{booking}/change-status', [BookingController::class, 'changeStatus'])->name('change_status');
    });
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/workers', [ReportController::class, 'workers'])->name('workers');
        Route::get('/clients', [ReportController::class, 'clients'])->name('clients');
        Route::get('/contracts', [ReportController::class, 'contracts'])->name('contracts');
        Route::get('/services', [ReportController::class, 'services'])->name('services');
        Route::get('/bookings', [ReportController::class, 'bookings'])->name('bookings');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::post('/generate', [ReportController::class, 'generate'])->name('generate');
        Route::get('/export/{type}', [ReportController::class, 'export'])->name('export');
    });
    
    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/create', [UserController::class, 'create'])->name('create');
        Route::post('/', [UserController::class, 'store'])->name('store');
        Route::get('/{user}', [UserController::class, 'show'])->name('show');
        Route::get('/{user}/edit', [UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [UserController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('toggle_status');
    });
    
    // Profile
    Route::get('/profile', [UserController::class, 'profile'])->name('profile');
    Route::put('/profile', [UserController::class, 'updateProfile'])->name('update_profile');
    Route::put('/change-password', [UserController::class, 'changePassword'])->name('change_password');
    
    // Settings
    Route::get('/settings', [CompanyController::class, 'settings'])->name('settings');
    Route::put('/settings', [CompanyController::class, 'updateSettings'])->name('update_settings');
});

// API Routes (for AJAX requests)
Route::prefix('api')->name('api.')->middleware(['auth'])->group(function () {
    Route::get('/workers', [WorkerController::class, 'apiGetWorkers'])->name('get_workers');
    Route::get('/clients', [ClientController::class, 'apiGetClients'])->name('get_clients');
    Route::get('/services', [ServiceController::class, 'apiGetServices'])->name('get_services');
    Route::get('/bookings', [BookingController::class, 'apiGetBookings'])->name('get_bookings');
    Route::post('/bookings/create', [BookingController::class, 'apiCreateBooking'])->name('create_booking');
    Route::put('/bookings/{booking}', [BookingController::class, 'apiUpdateBooking'])->name('update_booking');
    Route::delete('/bookings/{booking}', [BookingController::class, 'apiDeleteBooking'])->name('delete_booking');
});
