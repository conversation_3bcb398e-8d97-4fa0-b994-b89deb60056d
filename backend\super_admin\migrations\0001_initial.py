# Generated by Django 5.2 on 2025-04-29 17:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('serial_number', models.CharField(max_length=16, unique=True, verbose_name='الرقم التسلسلي')),
                ('database_name', models.CharField(max_length=100, unique=True, verbose_name='اسم قاعدة البيانات')),
                ('database_user', models.Char<PERSON><PERSON>(max_length=100, verbose_name='مستخدم قاعدة البيانات')),
                ('database_password', models.CharField(max_length=100, verbose_name='كلمة مرور قاعدة البيانات')),
                ('database_host', models.CharField(default='localhost', max_length=100, verbose_name='مضيف قاعدة البيانات')),
                ('status', models.CharField(choices=[('active', 'نشطة'), ('inactive', 'غير نشطة'), ('suspended', 'معلقة'), ('trial', 'تجريبية')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الاشتراك')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SuperAdminUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('two_factor_enabled', models.BooleanField(default=False, verbose_name='تفعيل التحقق الثنائي')),
                ('two_factor_secret', models.CharField(blank=True, max_length=100, null=True, verbose_name='سر التحقق الثنائي')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP آخر تسجيل دخول')),
                ('access_token', models.CharField(blank=True, max_length=100, null=True, verbose_name='رمز الوصول')),
                ('token_expiry', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الرمز')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='super_admin_profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'مستخدم المسؤول الأعلى',
                'verbose_name_plural': 'مستخدمي المسؤول الأعلى',
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('activate', 'تفعيل'), ('deactivate', 'تعطيل')], max_length=20, verbose_name='الإجراء')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='logs', to='super_admin.company', verbose_name='الشركة')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل النظام',
                'verbose_name_plural': 'سجلات النظام',
                'ordering': ['-created_at'],
            },
        ),
    ]
