/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./templates/**/*.html",
    "./static/**/*.js",
  ],
  theme: {
    extend: {
      colors: {
        // الألوان الأساسية
        primary: {
          DEFAULT: '#174785', // أزرق غامق - اللون الرئيسي للنظام
          dark: '#0a384e',    // أزرق داكن - للعناصر الداكنة والتأثيرات
          medium: '#5a96d4',  // أزرق متوسط - للأزرار
          light: '#e6f0ff',   // أزرق فاتح - للخلفيات الخفيفة
          lighter: '#f0efef', // أبيض مائل للرمادي - للخلفيات الفاتحة جداً
        },
        // الألوان الثانوية
        secondary: {
          DEFAULT: '#e6f0ff', // أزرق فاتح - للخلفيات
          dark: '#c05b2a',    // برتقالي داكن - للتأثيرات
          light: '#cfae86',   // بيج فاتح - للخلفيات الخفيفة
          lightest: '#d1ceba', // بيج فاتح جداً - للخلفيات الفاتحة جداً
        },
        // ألوان محايدة
        neutral: {
          DEFAULT: '#bcb1b2', // رمادي متوسط - للنصوص الثانوية
          dark: '#544025',    // بني داكن - للنصوص الداكنة
          light: '#f0efef',   // أبيض مائل للرمادي - للخلفيات
        },
        // ألوان الحالة
        error: '#e74c3c',
        success: '#2ecc71',
        warning: '#f59e0b',
        info: '#3b82f6',
        accent: '#5a96d4',    // أزرق متوسط للأزرار
      },
      fontFamily: {
        cairo: ['Cairo', 'sans-serif'],
        tajawal: ['Tajawal', 'sans-serif'],
      },
      zIndex: {
        '31': '31',
        '32': '32',
      },
      animation: {
        'slide-in': 'slide-in 0.3s ease-out',
        'fadeIn': 'fadeIn 0.5s ease-in-out',
        'slideInRight': 'slideInRight 0.5s ease-in-out',
        'slideInLeft': 'slideInLeft 0.5s ease-in-out',
        'slideInUp': 'slideInUp 0.5s ease-in-out',
        'slideInDown': 'slideInDown 0.5s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideInDown: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('tailwindcss-rtl'),
  ],
}
