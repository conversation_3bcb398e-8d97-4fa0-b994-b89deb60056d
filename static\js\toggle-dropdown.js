/**
 * وظيفة فتح وإغلاق قائمة المستخدم المنسدلة
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Toggle dropdown script loaded');

    // تعريف وظيفة فتح وإغلاق القائمة المنسدلة
    window.toggleDropdown = function() {
        console.log('Toggle dropdown function called');
        
        const dropdown = document.getElementById('userDropdown');
        if (!dropdown) {
            console.error('User dropdown element not found!');
            return;
        }
        
        const button = document.querySelector('button[onclick="toggleDropdown()"]');
        
        if (dropdown.classList.contains('hidden')) {
            // فتح القائمة
            dropdown.classList.remove('hidden');
            
            // تحديث موضع القائمة المنسدلة بناءً على موضع الزر
            if (button) {
                const buttonRect = button.getBoundingClientRect();
                dropdown.style.top = (buttonRect.bottom + 7) + 'px';
                dropdown.style.right = (window.innerWidth - buttonRect.right) + 'px';
            } else {
                // موضع افتراضي إذا لم يتم العثور على الزر
                dropdown.style.top = '60px';
                dropdown.style.right = '20px';
            }
            
            // تأثير حركي للقائمة - تظهر من الأسفل إلى الأعلى
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'translateY(-5px)';
            
            setTimeout(() => {
                dropdown.style.opacity = '1';
                dropdown.style.transform = 'translateY(0)';
            }, 10);
            
            // تغيير لون الزر
            if (button) {
                button.classList.add('bg-indigo-800');
                button.classList.remove('bg-indigo-600');
                
                // تأثير ظل أكبر
                button.classList.add('shadow-md');
                button.classList.remove('shadow-sm');
            }
        } else {
            // إغلاق القائمة
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'translateY(-10px)';
            
            setTimeout(() => {
                dropdown.classList.add('hidden');
            }, 200);
            
            // إعادة لون الزر
            if (button) {
                button.classList.remove('bg-indigo-800');
                button.classList.add('bg-indigo-600');
                
                // إعادة الظل الأصلي
                button.classList.remove('shadow-md');
                button.classList.add('shadow-sm');
            }
        }
    };
    
    // إغلاق القائمة عند الضغط خارجها
    document.addEventListener('click', function(e) {
        const dropdown = document.getElementById('userDropdown');
        const button = document.querySelector('button[onclick="toggleDropdown()"]');
        
        if (dropdown && !dropdown.classList.contains('hidden') && button) {
            if (!dropdown.contains(e.target) && !button.contains(e.target)) {
                // إغلاق القائمة
                dropdown.style.opacity = '0';
                dropdown.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    dropdown.classList.add('hidden');
                }, 200);
                
                // إعادة لون الزر
                button.classList.remove('bg-indigo-800');
                button.classList.add('bg-indigo-600');
                
                // إعادة الظل الأصلي
                button.classList.remove('shadow-md');
                button.classList.add('shadow-sm');
            }
        }
    });
});
