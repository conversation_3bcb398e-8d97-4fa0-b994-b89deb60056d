<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الشركات - منصة استقدامي</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>

<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md" x-data="{ 
        step: 1, 
        serialNumber: '', 
        username: '', 
        password: '',
        showPassword: false,
        loading: false,
        errors: {}
    }">
        
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <div class="floating-animation">
                <div class="w-20 h-20 mx-auto mb-4 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <i class="fas fa-building text-3xl text-indigo-600"></i>
                </div>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">منصة استقدامي</h1>
            <p class="text-indigo-100">تسجيل دخول الشركات</p>
        </div>

        <!-- Login Form -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Step 1: Serial Number -->
                <div x-show="step === 1" x-transition>
                    <div class="text-center mb-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-2">الرقم التسلسلي للشركة</h2>
                        <p class="text-gray-600 text-sm">أدخل الرقم التسلسلي المكون من 16 رقم</p>
                    </div>
                    
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-hashtag text-gray-400"></i>
                        </div>
                        <input 
                            type="text" 
                            name="serial_number"
                            x-model="serialNumber"
                            placeholder="XXXX-XXXX-XXXX-XXXX"
                            class="input-focus w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
                            maxlength="19"
                            pattern="[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}"
                            style="direction: ltr; text-align: center; font-family: monospace; letter-spacing: 2px;"
                            required
                        >
                    </div>
                    
                    <button 
                        type="button"
                        @click="step = 2"
                        :disabled="!serialNumber || serialNumber.length < 19"
                        class="btn-hover w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i class="fas fa-arrow-left ml-2"></i>
                        التالي
                    </button>
                </div>

                <!-- Step 2: Username & Password -->
                <div x-show="step === 2" x-transition>
                    <div class="text-center mb-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-2">بيانات تسجيل الدخول</h2>
                        <p class="text-gray-600 text-sm">أدخل اسم المستخدم وكلمة المرور</p>
                    </div>
                    
                    <!-- Username -->
                    <div class="relative mb-4">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input 
                            type="text" 
                            name="username"
                            x-model="username"
                            placeholder="اسم المستخدم"
                            class="input-focus w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
                            required
                        >
                    </div>
                    
                    <!-- Password -->
                    <div class="relative mb-6">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input 
                            :type="showPassword ? 'text' : 'password'"
                            name="password"
                            x-model="password"
                            placeholder="كلمة المرور"
                            class="input-focus w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
                            required
                        >
                        <button 
                            type="button"
                            @click="showPassword = !showPassword"
                            class="absolute inset-y-0 left-0 pl-3 flex items-center"
                        >
                            <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-gray-400 hover:text-gray-600"></i>
                        </button>
                    </div>
                    
                    <div class="flex space-x-3 space-x-reverse">
                        <button 
                            type="button"
                            @click="step = 1"
                            class="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 hover:bg-gray-600"
                        >
                            <i class="fas fa-arrow-right ml-2"></i>
                            السابق
                        </button>
                        
                        <button 
                            type="submit"
                            :disabled="!username || !password || loading"
                            class="btn-hover flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                            @click="loading = true"
                        >
                            <span x-show="!loading">
                                <i class="fas fa-sign-in-alt ml-2"></i>
                                دخول
                            </span>
                            <span x-show="loading">
                                <i class="fas fa-spinner fa-spin ml-2"></i>
                                جاري التحقق...
                            </span>
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- Error Messages -->
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-2">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <!-- Footer Links -->
            <div class="mt-6 text-center">
                <a href="{% url 'super_admin:login' %}" class="text-indigo-600 hover:text-indigo-800 text-sm transition-colors duration-300">
                    <i class="fas fa-user-shield ml-1"></i>
                    تسجيل دخول المسؤول الأعلى
                </a>
            </div>
        </div>
        
        <!-- Copyright -->
        <div class="text-center mt-6">
            <p class="text-indigo-100 text-sm">
                © {{ now.year }} منصة استقدامي - جميع الحقوق محفوظة
            </p>
        </div>
    </div>
</body>
</html>
