/**
 * ملف تثبيت Dynamic Web TWAIN
 * 
 * هذا الملف يساعد في تثبيت مكتبة Dynamic Web TWAIN على جهاز المستخدم
 * عندما يكون ذلك ضرورياً للوصول إلى الماسح الضوئي.
 */

// تكوين التثبيت
var Dynamsoft = Dynamsoft || {};
Dynamsoft.WebTwainEnv = Dynamsoft.WebTwainEnv || {};

// إعدادات التثبيت
Dynamsoft.WebTwainEnv.AutoLoad = false;
Dynamsoft.WebTwainEnv.Containers = [{ ContainerId: 'dwtcontrolContainer', Width: '100%', Height: '400px' }];
Dynamsoft.WebTwainEnv.ProductKey = 't0068MQAAAGNcO61He7rFtA/4bA5WRwfN0ONXUn8BYdGsOOm8Stkg39Q3GrOJGTgQxPXlR2yYBDXrjV1+Jrmm4VPdGQ==';
Dynamsoft.WebTwainEnv.Trial = true;

// مسارات الموارد
Dynamsoft.WebTwainEnv.ResourcesPath = '/static/dwt-resources/';

// إعدادات التنزيل والتثبيت
Dynamsoft.WebTwainEnv.IfAddMD5InUploadHeader = true;
Dynamsoft.WebTwainEnv.IfCheckDWTVersion = true;

// رسائل التثبيت
Dynamsoft.WebTwainEnv.CustomizableDisplayInfo = {
    errorMessages: {
        // رسائل الخطأ
        InitializeFailed: 'فشل في تهيئة Dynamic Web TWAIN. يرجى تحديث الصفحة وإعادة المحاولة.',
        InstallFailed: 'فشل في تثبيت Dynamic Web TWAIN. يرجى تنزيل المثبت وتشغيله يدوياً.',
        LoadModuleFailed: 'فشل في تحميل وحدة Dynamic Web TWAIN. يرجى تحديث الصفحة وإعادة المحاولة.',
        DownloadFailed: 'فشل في تنزيل Dynamic Web TWAIN. يرجى التحقق من اتصالك بالإنترنت وإعادة المحاولة.'
    },
    
    // رسائل التثبيت
    install: {
        title: 'تثبيت Dynamic Web TWAIN',
        description: 'مطلوب تثبيت Dynamic Web TWAIN للوصول إلى الماسح الضوئي.',
        downloadNow: 'تنزيل الآن',
        installManually: 'تثبيت يدوي'
    }
};

// دالة التهيئة
function initDWT() {
    if (Dynamsoft && Dynamsoft.WebTwain && Dynamsoft.WebTwain.Load) {
        Dynamsoft.WebTwain.Load();
    } else {
        console.error('لم يتم العثور على Dynamsoft.WebTwain.Load');
    }
}

// تصدير الدوال
window.initDynamicWebTWAIN = initDWT;
