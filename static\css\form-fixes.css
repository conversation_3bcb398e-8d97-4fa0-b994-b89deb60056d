/* إصلاح مشكلة الألوان في حقول الإدخال */

/* حقول الإدخال العامة */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
input[type="month"],
input[type="week"],
textarea,
select {
    color: #1f2937 !important; /* نص رمادي داكن */
    background-color: #ffffff !important; /* خلفية بيضاء */
}

/* النص التوضيحي (placeholder) */
input::placeholder,
textarea::placeholder {
    color: #6b7280 !important; /* رمادي متوسط للنص التوضيحي */
    opacity: 1 !important;
}

/* حقول الإدخال عند التركيز */
input:focus,
textarea:focus,
select:focus {
    color: #1f2937 !important;
    background-color: #ffffff !important;
    outline: none !important;
}

/* حقول الإدخال المعطلة */
input:disabled,
textarea:disabled,
select:disabled {
    color: #9ca3af !important;
    background-color: #f9fafb !important;
    cursor: not-allowed !important;
}

/* خيارات القوائم المنسدلة */
select option {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* حقول الإدخال في الوضع المظلم (إذا كان موجود) */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="tel"],
.dark input[type="password"],
.dark input[type="number"],
.dark input[type="url"],
.dark input[type="search"],
.dark input[type="date"],
.dark input[type="datetime-local"],
.dark input[type="time"],
.dark input[type="month"],
.dark input[type="week"],
.dark textarea,
.dark select {
    color: #f9fafb !important;
    background-color: #374151 !important;
    border-color: #4b5563 !important;
}

.dark input::placeholder,
.dark textarea::placeholder {
    color: #9ca3af !important;
}

/* إصلاح خاص لحقول البحث */
.search-input,
input[name="search"],
input[id*="search"] {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص لحقول التصفية */
.filter-input,
select[name*="filter"],
select[name*="status"] {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص لحقول النماذج */
.form-input,
.form-control {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص لحقول Tailwind CSS */
.text-white {
    color: #ffffff !important;
}

.text-gray-900 {
    color: #1f2937 !important;
}

.text-gray-800 {
    color: #1f2937 !important;
}

.text-gray-700 {
    color: #374151 !important;
}

.text-gray-600 {
    color: #4b5563 !important;
}

.text-gray-500 {
    color: #6b7280 !important;
}

.bg-white {
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في البطاقات */
.card input,
.card textarea,
.card select,
.bg-white input,
.bg-white textarea,
.bg-white select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للجداول */
table input,
table textarea,
table select,
.table input,
.table textarea,
.table select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنماذج المضمنة */
.inline-form input,
.inline-form textarea,
.inline-form select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص لحقول التاريخ والوقت */
input[type="date"],
input[type="datetime-local"],
input[type="time"] {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص لحقول الأرقام */
input[type="number"] {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في المودال */
.modal input,
.modal textarea,
.modal select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في القوائم المنسدلة */
.dropdown input,
.dropdown textarea,
.dropdown select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في الشريط الجانبي */
.sidebar input,
.sidebar textarea,
.sidebar select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في الهيدر */
.header input,
.header textarea,
.header select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في الفوتر */
.footer input,
.footer textarea,
.footer select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح عام لجميع العناصر التفاعلية */
input, textarea, select, button {
    font-family: 'Tajawal', sans-serif !important;
}

/* إصلاح خاص للنصوص المخفية */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* إصلاح خاص للنصوص في الحاويات المرنة */
.flex input,
.flex textarea,
.flex select,
.grid input,
.grid textarea,
.grid select {
    color: #1f2937 !important;
    background-color: #ffffff !important;
}

/* إصلاح خاص للنصوص في الحاويات المتجاوبة */
@media (max-width: 768px) {
    input, textarea, select {
        color: #1f2937 !important;
        background-color: #ffffff !important;
        font-size: 16px !important; /* منع التكبير في iOS */
    }
}

/* إصلاح خاص للنصوص في الطباعة */
@media print {
    input, textarea, select {
        color: #000000 !important;
        background-color: #ffffff !important;
        border: 1px solid #000000 !important;
    }
}
