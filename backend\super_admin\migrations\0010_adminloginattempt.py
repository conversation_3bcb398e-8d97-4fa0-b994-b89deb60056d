# Generated by Django 5.2 on 2025-05-13 14:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0009_blockedserial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminLoginAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=150, verbose_name='اسم المستخدم')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت المحاولة')),
                ('is_successful', models.BooleanField(default=False, verbose_name='نجاح المحاولة')),
            ],
            options={
                'verbose_name': 'محاولة تسجيل دخول المسؤول',
                'verbose_name_plural': 'محاولات تسجيل دخول المسؤول',
                'ordering': ['-created_at'],
            },
        ),
    ]
