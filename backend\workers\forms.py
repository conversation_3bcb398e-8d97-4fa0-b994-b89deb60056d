from django import forms
from .models import Worker, WorkerDocument, RecruitmentCompany
from django.core.validators import FileExtensionValidator

class WorkerForm(forms.ModelForm):
    class Meta:
        model = Worker
        fields = [
            # معلومات شخصية
            'first_name', 'last_name', 'gender', 'nationality', 'date_of_birth', 'phone_number', 'job_title',
            # معلومات جواز السفر
            'passport_number', 'passport_expiry', 'entry_date', 'visa_expiry',
            # معلومات الشركة المستقدمة
            'recruitment_company',
            # معلومات إضافية
            'is_active', 'status', 'salary',
            # الصور والمستندات
            'photo', 'passport_image', 'visa_image'
        ]
        widgets = {
            # معلومات شخصية
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأخير'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'nationality': forms.Select(attrs={'class': 'form-select'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),

            # معلومات جواز السفر
            'passport_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم جواز السفر'}),
            'passport_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'entry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'visa_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),

            # معلومات الشركة المستقدمة
            'recruitment_company': forms.Select(attrs={'class': 'form-select'}),

            # معلومات إضافية
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-select'}),

            # الصور والمستندات
            'photo': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'passport_image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'visa_image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

class WorkerDocumentForm(forms.ModelForm):
    class Meta:
        model = WorkerDocument
        fields = ['document_type', 'title', 'file', 'expiry_date', 'notes']
        widgets = {
            'document_type': forms.Select(attrs={
                'class': 'w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500',
                'placeholder': 'عنوان المستند'
            }),
            'file': forms.FileInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500'
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500',
                'placeholder': 'ملاحظات',
                'rows': 3
            }),
        }


class RecruitmentCompanyForm(forms.ModelForm):
    class Meta:
        model = RecruitmentCompany
        fields = ['name', 'country', 'address', 'phone', 'email', 'contact_person', 'license_number', 'is_active', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الشركة'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الدولة'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'العنوان', 'rows': 3}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'contact_person': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الشخص المسؤول'}),
            'license_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الترخيص'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'ملاحظات', 'rows': 3}),
        }


class WorkerImportForm(forms.Form):
    excel_file = forms.FileField(
        label='ملف الإكسل',
        validators=[FileExtensionValidator(allowed_extensions=['xlsx', 'xls'])],
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.xlsx,.xls'})
    )

    def clean_excel_file(self):
        excel_file = self.cleaned_data.get('excel_file')
        if excel_file:
            if not excel_file.name.endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('يرجى تحميل ملف إكسل صالح (.xlsx أو .xls)')
        return excel_file
