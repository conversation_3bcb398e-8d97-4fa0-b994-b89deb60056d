// تحسين تجربة المستخدم مع جدول سجل النظام
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم مع جدول سجل النظام
    const descriptionCells = document.querySelectorAll('.logs-table td.description');
    
    // إضافة تلميح عند تحريك المؤشر فوق خلايا الوصف
    descriptionCells.forEach(cell => {
        // إضافة أيقونة للإشارة إلى إمكانية عرض المزيد
        const icon = document.createElement('i');
        icon.className = 'fas fa-search-plus text-blue-400 mr-2 opacity-50';
        icon.style.fontSize = '0.75rem';
        cell.prepend(icon);
        
        // إضافة تأثير عند النقر على الخلية
        cell.addEventListener('click', function() {
            // إنشاء نافذة منبثقة لعرض النص الكامل
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.style.direction = 'rtl';
            
            const content = document.createElement('div');
            content.className = 'bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4';
            
            const header = document.createElement('div');
            header.className = 'flex justify-between items-center mb-4 pb-2 border-b border-gray-200';
            
            const title = document.createElement('h3');
            title.className = 'text-lg font-bold text-gray-800';
            title.textContent = 'تفاصيل الإجراء';
            
            const closeBtn = document.createElement('button');
            closeBtn.className = 'text-gray-500 hover:text-gray-700';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => modal.remove());
            
            header.appendChild(title);
            header.appendChild(closeBtn);
            
            const body = document.createElement('div');
            body.className = 'text-gray-700 whitespace-pre-wrap';
            body.textContent = this.textContent.trim();
            
            content.appendChild(header);
            content.appendChild(body);
            modal.appendChild(content);
            
            document.body.appendChild(modal);
            
            // إغلاق النافذة عند النقر خارجها
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        });
    });
});
