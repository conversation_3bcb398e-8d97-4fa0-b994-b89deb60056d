import os
import json
from django.conf import settings

def save_database_config(serial_number, db_name, db_user, db_password, db_host):
    """
    حفظ معلومات قاعدة البيانات في ملف تكوين
    """
    import logging
    logger = logging.getLogger(__name__)

    # التحقق من وجود الرقم التسلسلي
    if not serial_number:
        logger.error("لا يمكن حفظ معلومات قاعدة البيانات: الرقم التسلسلي مطلوب")
        return False

    # التحقق من وجود اسم قاعدة البيانات
    if not db_name:
        logger.error("لا يمكن حفظ معلومات قاعدة البيانات: اسم قاعدة البيانات مطلوب")
        return False

    config_dir = os.path.join(settings.BASE_DIR, 'config')

    # إنشاء مجلد التكوين إذا لم يكن موجودًا
    try:
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            logger.info(f"تم إنشاء مجلد التكوين: {config_dir}")
    except Exception as e:
        logger.error(f"خطأ في إنشاء مجلد التكوين: {e}")
        return False

    config_file = os.path.join(config_dir, 'database_custom.json')

    # قراءة التكوين الحالي إذا كان موجودًا
    config_data = {}
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            logger.info(f"تم قراءة ملف التكوين الحالي: {config_file}")
        except json.JSONDecodeError as e:
            logger.error(f"خطأ في تنسيق ملف التكوين: {e}")
            # إنشاء نسخة احتياطية من الملف القديم
            try:
                import shutil
                backup_file = f"{config_file}.bak"
                shutil.copy2(config_file, backup_file)
                logger.info(f"تم إنشاء نسخة احتياطية من ملف التكوين: {backup_file}")
            except Exception as backup_error:
                logger.error(f"خطأ في إنشاء نسخة احتياطية من ملف التكوين: {backup_error}")
        except Exception as e:
            logger.error(f"خطأ في قراءة ملف التكوين: {e}")

    # إضافة أو تحديث معلومات قاعدة البيانات
    config_data[serial_number] = {
        'db_name': db_name,
        'db_user': db_user,
        'db_password': db_password,
        'db_host': db_host,
        'verified': True
    }

    # حفظ التكوين
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        logger.info(f"تم حفظ معلومات قاعدة البيانات للرقم التسلسلي: {serial_number}")
        return True
    except PermissionError:
        logger.error(f"خطأ في الصلاحيات: لا يمكن الكتابة إلى ملف التكوين {config_file}")
        return False
    except Exception as e:
        logger.error(f"خطأ في حفظ ملف التكوين: {e}")
        return False

def get_database_config(serial_number):
    """
    الحصول على معلومات قاعدة البيانات من ملف التكوين
    """
    import logging
    logger = logging.getLogger(__name__)

    # التحقق من وجود الرقم التسلسلي
    if not serial_number:
        logger.warning("لا يمكن استرجاع معلومات قاعدة البيانات: الرقم التسلسلي مطلوب")
        return None

    config_file = os.path.join(settings.BASE_DIR, 'config', 'database_custom.json')

    if not os.path.exists(config_file):
        logger.warning(f"ملف التكوين غير موجود: {config_file}")
        return None

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        config = config_data.get(serial_number)
        if config:
            logger.info(f"تم استرجاع معلومات قاعدة البيانات للرقم التسلسلي: {serial_number}")
        else:
            logger.warning(f"لم يتم العثور على معلومات قاعدة البيانات للرقم التسلسلي: {serial_number}")

        return config
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تنسيق ملف التكوين: {e}")
        return None
    except PermissionError:
        logger.error(f"خطأ في الصلاحيات: لا يمكن قراءة ملف التكوين {config_file}")
        return None
    except Exception as e:
        logger.error(f"خطأ في قراءة ملف التكوين: {e}")
        return None

def is_database_verified(serial_number):
    """
    التحقق مما إذا كانت قاعدة البيانات قد تم التحقق منها مسبقًا
    """
    import logging
    logger = logging.getLogger(__name__)

    if not serial_number:
        logger.warning("لا يمكن التحقق من قاعدة البيانات: الرقم التسلسلي مطلوب")
        return False

    config = get_database_config(serial_number)
    verified = config is not None and config.get('verified', False)

    if verified:
        logger.info(f"قاعدة البيانات للرقم التسلسلي {serial_number} تم التحقق منها مسبقًا")
    else:
        logger.info(f"قاعدة البيانات للرقم التسلسلي {serial_number} لم يتم التحقق منها مسبقًا")

    return verified

def ensure_database_config_complete(db_config):
    """
    التأكد من اكتمال إعدادات قاعدة البيانات وإضافة الإعدادات المفقودة

    Args:
        db_config: قاموس إعدادات قاعدة البيانات

    Returns:
        dict: قاموس إعدادات قاعدة البيانات المكتمل
    """
    import logging
    logger = logging.getLogger(__name__)

    if not db_config:
        logger.warning("لا يمكن إكمال إعدادات قاعدة البيانات: الإعدادات غير موجودة")
        return db_config

    # قائمة الإعدادات المطلوبة وقيمها الافتراضية
    required_settings = {
        'ATOMIC_REQUESTS': True,
        'AUTOCOMMIT': True,
        'TIME_ZONE': settings.TIME_ZONE,  # استخدام إعداد TIME_ZONE من إعدادات Django
        'USE_TZ': settings.USE_TZ,  # استخدام إعداد USE_TZ من إعدادات Django
        'CONN_MAX_AGE': 0,
        'CONN_HEALTH_CHECKS': False  # إضافة هذا الإعداد لتجنب KeyError
    }

    # إضافة الإعدادات المفقودة
    for key, default_value in required_settings.items():
        if key not in db_config:
            logger.info(f"إضافة الإعداد المفقود {key} إلى إعدادات قاعدة البيانات")
            db_config[key] = default_value

    # التأكد من وجود OPTIONS
    if 'OPTIONS' not in db_config:
        db_config['OPTIONS'] = {
            'timeout': 30,
            'isolation_level': None,
            'check_same_thread': False
        }
    elif not isinstance(db_config['OPTIONS'], dict):
        # إذا كان OPTIONS موجودًا ولكنه ليس قاموسًا، نقوم بتحويله إلى قاموس
        logger.warning(f"تم العثور على OPTIONS ولكنه ليس قاموسًا. نوعه: {type(db_config['OPTIONS'])}")
        db_config['OPTIONS'] = {
            'timeout': 30,
            'isolation_level': None,
            'check_same_thread': False
        }
    else:
        # التأكد من وجود الإعدادات المطلوبة في OPTIONS
        options_defaults = {
            'timeout': 30,
            'isolation_level': None,
            'check_same_thread': False
        }
        for key, value in options_defaults.items():
            if key not in db_config['OPTIONS']:
                logger.info(f"إضافة الإعداد المفقود {key} إلى OPTIONS")
                db_config['OPTIONS'][key] = value

    return db_config

def ensure_all_databases_have_atomic_requests():
    """
    التأكد من أن جميع قواعد البيانات المسجلة تحتوي على إعداد ATOMIC_REQUESTS

    Returns:
        int: عدد قواعد البيانات التي تم تحديثها
    """
    import logging
    from django.conf import settings

    logger = logging.getLogger(__name__)
    updated_count = 0

    for db_name, db_config in settings.DATABASES.items():
        if 'ATOMIC_REQUESTS' not in db_config:
            logger.info(f"إضافة إعداد ATOMIC_REQUESTS إلى قاعدة البيانات {db_name}")
            db_config['ATOMIC_REQUESTS'] = True
            updated_count += 1

    logger.info(f"تم تحديث {updated_count} قاعدة بيانات بإعداد ATOMIC_REQUESTS")
    return updated_count
