{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--secondary-blue); text-decoration: none;">العمال</a></li>
<li style="margin: 0 8px;"> / </li>
<li><span style="color: var(--text-secondary);">استيراد العمال</span></li>
{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 5px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .upload-area:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .upload-area.dragover {
        border-color: #28a745;
        background-color: #f1f8f1;
    }
    #id_excel_file {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- بطاقة استيراد العمال -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-file-import me-2"></i>{{ title }}</h5>
        </div>
        <div class="alert alert-success m-3">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-file-excel fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading">استيراد العمال من ملف إكسل</h5>
                    <p class="mb-0">يمكنك استيراد بيانات العمال من ملف إكسل بسهولة. قم بتحميل ملف إكسل يحتوي على بيانات العمال وانقر على زر "استيراد العمال".</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="mb-4">
                    <div class="upload-area p-4 text-center border rounded mb-3" id="excelDropArea">
                        <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                        <h5>اسحب وأفلت ملف الإكسل هنا أو انقر للتحميل</h5>
                        <p class="text-muted">يمكنك تحميل ملفات إكسل (.xlsx أو .xls) فقط</p>
                        <label for="{{ form.excel_file.id_for_label }}" class="form-label visually-hidden">{{ form.excel_file.label }}</label>
                        {{ form.excel_file }}
                        {% if form.excel_file.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.excel_file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('{{ form.excel_file.id_for_label }}').click();">
                                <i class="fas fa-folder-open me-2"></i> استعراض الملفات
                            </button>
                        </div>
                    </div>
                    <div class="preview-area d-none" id="excelPreviewArea">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <i class="fas fa-file-excel fa-2x text-success me-3"></i>
                            <div class="flex-grow-1">
                                <h6 class="mb-0" id="excelFileName"></h6>
                                <small class="text-muted" id="excelFileSize"></small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="changeExcelBtn">
                                <i class="fas fa-exchange-alt me-1"></i> تغيير الملف
                            </button>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>تنسيق ملف الإكسل</h6>
                    <p>يجب أن يحتوي ملف الإكسل على الأعمدة التالية:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-header bg-primary text-white">الحقول المطلوبة</div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><strong>first_name</strong> - الاسم الأول</li>
                                        <li class="list-group-item"><strong>last_name</strong> - الاسم الأخير</li>
                                        <li class="list-group-item"><strong>passport_number</strong> - رقم جواز السفر</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-header bg-info text-white">معلومات الجواز والتأشيرة</div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><strong>nationality</strong> - الجنسية (رمز الدولة مثل EG, IN, PK)</li>
                                        <li class="list-group-item"><strong>passport_expiry</strong> - تاريخ انتهاء جواز السفر (YYYY-MM-DD)</li>
                                        <li class="list-group-item"><strong>visa_expiry</strong> - تاريخ انتهاء التأشيرة (YYYY-MM-DD)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-header bg-secondary text-white">معلومات شخصية</div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><strong>gender</strong> - الجنس (M للذكر، F للأنثى)</li>
                                        <li class="list-group-item"><strong>date_of_birth</strong> - تاريخ الميلاد (YYYY-MM-DD)</li>
                                        <li class="list-group-item"><strong>date_joined</strong> - تاريخ الانضمام (YYYY-MM-DD)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-header bg-success text-white">معلومات إضافية</div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><strong>is_active</strong> - نشط (True أو False)</li>
                                        <li class="list-group-item"><strong>status</strong> - الحالة (active, inactive, on_leave, terminated)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if template_exists %}
                    <a href="{% static 'templates/worker_import_template.xlsx' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> تحميل قالب الاستيراد
                    </a>
                    {% else %}
                    <button class="btn btn-sm btn-outline-secondary" disabled>
                        <i class="fas fa-download me-1"></i> قالب الاستيراد غير متوفر
                    </button>
                    {% endif %}
                </div>

                <div class="alert alert-warning mt-4">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading">لإنهاء العملية</h6>
                            <p class="mb-0">انقر على زر "استيراد العمال" لبدء عملية الاستيراد أو "إلغاء" للعودة بدون استيراد.</p>
                        </div>
                    </div>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <button type="submit" class="btn btn-lg btn-primary" id="importBtn" disabled>
                        <i class="fas fa-file-import me-1"></i> استيراد العمال
                    </button>
                    <a href="{% url 'workers:worker_list' %}" class="btn btn-lg btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
                <div class="progress mt-3 d-none" id="importProgress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                </div>
                <div class="alert alert-info mt-3 d-none" id="importStatus">
                    <i class="fas fa-spinner fa-spin me-2"></i> جاري معالجة الملف...
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const excelFileInput = document.getElementById('id_excel_file');
        const excelDropArea = document.getElementById('excelDropArea');
        const excelPreviewArea = document.getElementById('excelPreviewArea');
        const excelFileName = document.getElementById('excelFileName');
        const excelFileSize = document.getElementById('excelFileSize');
        const changeExcelBtn = document.getElementById('changeExcelBtn');
        const importBtn = document.getElementById('importBtn');
        const importProgress = document.getElementById('importProgress');
        const importStatus = document.getElementById('importStatus');

        // معالجة اختيار الملف
        excelFileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];

                // التحقق من نوع الملف
                if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                    alert('يرجى اختيار ملف إكسل صالح (.xlsx أو .xls)');
                    this.value = '';
                    return;
                }

                // عرض معلومات الملف
                excelFileName.textContent = file.name;
                excelFileSize.textContent = formatFileSize(file.size);

                // إظهار منطقة المعاينة وإخفاء منطقة السحب والإفلات
                excelDropArea.classList.add('d-none');
                excelPreviewArea.classList.remove('d-none');

                // تفعيل زر الاستيراد
                importBtn.disabled = false;
            }
        });

        // معالجة زر تغيير الملف
        changeExcelBtn.addEventListener('click', function() {
            excelDropArea.classList.remove('d-none');
            excelPreviewArea.classList.add('d-none');
            excelFileInput.value = '';
            importBtn.disabled = true;
        });

        // معالجة السحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            excelDropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            excelDropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            excelDropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            excelDropArea.classList.add('dragover');
        }

        function unhighlight() {
            excelDropArea.classList.remove('dragover');
        }

        excelDropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                excelFileInput.files = files;
                // تشغيل حدث التغيير يدوياً
                excelFileInput.dispatchEvent(new Event('change'));
            }
        }

        // معالجة تقديم النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            if (excelFileInput.files.length === 0) {
                e.preventDefault();
                alert('يرجى اختيار ملف إكسل أولاً');
                return;
            }

            // إظهار شريط التقدم وحالة الاستيراد
            importProgress.classList.remove('d-none');
            importStatus.classList.remove('d-none');
            importBtn.disabled = true;

            // محاكاة التقدم (يمكن استبدالها بتقدم حقيقي باستخدام AJAX)
            simulateProgress();
        });

        function simulateProgress() {
            let progress = 0;
            const progressBar = importProgress.querySelector('.progress-bar');

            const interval = setInterval(function() {
                progress += 5;
                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);

                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }

        // دالة مساعدة لتنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // النقر على منطقة السحب والإفلات لفتح مربع حوار اختيار الملف
        excelDropArea.addEventListener('click', function() {
            excelFileInput.click();
        });
    });
</script>
{% endblock %}