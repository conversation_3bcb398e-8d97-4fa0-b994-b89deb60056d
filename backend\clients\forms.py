from django import forms
from .models import Client, ClientDocument, ClientContact


class ClientForm(forms.ModelForm):
    """نموذج إضافة وتعديل العملاء"""
    
    class Meta:
        model = Client
        fields = [
            'name', 'client_type', 'status', 'phone', 'email', 'address',
            'national_id', 'passport_number', 'commercial_register', 'tax_number',
            'sponsor_name', 'sponsor_phone', 'visa_number', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'أدخل اسم العميل'
            }),
            'client_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': '+966xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': '<EMAIL>'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'rows': 3,
                'placeholder': 'أدخل العنوان الكامل'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم الهوية الوطنية'
            }),
            'passport_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم جواز السفر'
            }),
            'commercial_register': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم السجل التجاري'
            }),
            'tax_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'الرقم الضريبي'
            }),
            'sponsor_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'اسم الكفيل'
            }),
            'sponsor_phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'هاتف الكفيل'
            }),
            'visa_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم التأشيرة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص الحقول حسب نوع العميل
        self.fields['client_type'].choices = Client.CLIENT_TYPE_CHOICES
        self.fields['status'].choices = Client.STATUS_CHOICES
        
        # جعل بعض الحقول مطلوبة حسب نوع العميل
        if self.instance and self.instance.client_type:
            self.customize_fields_by_type(self.instance.client_type)
    
    def customize_fields_by_type(self, client_type):
        """تخصيص الحقول حسب نوع العميل"""
        if client_type == 'individual':
            self.fields['national_id'].required = True
            self.fields['commercial_register'].required = False
            self.fields['tax_number'].required = False
        elif client_type == 'company':
            self.fields['commercial_register'].required = True
            self.fields['tax_number'].required = True
            self.fields['national_id'].required = False
        elif client_type == 'government':
            self.fields['national_id'].required = False
            self.fields['commercial_register'].required = False
            self.fields['tax_number'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        client_type = cleaned_data.get('client_type')
        
        # التحقق من الحقول المطلوبة حسب نوع العميل
        if client_type == 'individual':
            if not cleaned_data.get('national_id') and not cleaned_data.get('passport_number'):
                raise forms.ValidationError('يجب إدخال رقم الهوية الوطنية أو رقم جواز السفر للأفراد')
        
        elif client_type == 'company':
            if not cleaned_data.get('commercial_register'):
                raise forms.ValidationError('رقم السجل التجاري مطلوب للشركات')
        
        return cleaned_data


class ClientDocumentForm(forms.ModelForm):
    """نموذج رفع مستندات العملاء"""
    
    class Meta:
        model = ClientDocument
        fields = ['document_type', 'title', 'file', 'description']
        widgets = {
            'document_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'عنوان المستند'
            }),
            'file': forms.FileInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'rows': 3,
                'placeholder': 'وصف المستند'
            }),
        }


class ClientContactForm(forms.ModelForm):
    """نموذج إضافة جهات اتصال للعملاء"""
    
    class Meta:
        model = ClientContact
        fields = ['name', 'position', 'phone', 'email', 'is_primary', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'اسم جهة الاتصال'
            }),
            'position': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'المنصب'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': '+966xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': '<EMAIL>'
            }),
            'is_primary': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 focus:ring-blue-500'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'rows': 2,
                'placeholder': 'ملاحظات'
            }),
        }


class ClientSearchForm(forms.Form):
    """نموذج البحث في العملاء"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'البحث بالاسم، الهاتف، أو رقم الهوية...'
        })
    )
    
    client_type = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الأنواع')] + Client.CLIENT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الحالات')] + Client.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
        })
    )
