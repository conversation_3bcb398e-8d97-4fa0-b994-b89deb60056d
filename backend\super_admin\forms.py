from django import forms
from django.contrib.auth.forms import AuthenticationForm
from .models import Company, SuperAdminUser, BlockedSerial

class SuperAdminLoginForm(AuthenticationForm):
    """نموذج تسجيل دخول المسؤول الأعلى"""
    username = forms.CharField(
        label='اسم المستخدم',
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'أدخل اسم المستخدم'
        })
    )
    password = forms.CharField(
        label='كلمة المرور',
        widget=forms.PasswordInput(attrs={
            'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'أدخل كلمة المرور'
        })
    )

class CompanyForm(forms.ModelForm):
    """نموذج إنشاء وتعديل الشركات"""
    # حقل إضافي لعرض الرقم التسلسلي (غير مرتبط بالنموذج)
    serial_number_preview = forms.CharField(
        label='الرقم التسلسلي',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-gray-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'readonly': True,
            'id': 'serial_number_preview'
        })
    )

    class Meta:
        model = Company
        fields = ['name', 'database_name', 'database_user', 'database_password', 'database_host', 'status', 'expiry_date', 'admin_username', 'admin_password']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'أدخل اسم الشركة',
                'required': True
            }),
            'database_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'أدخل اسم قاعدة البيانات',
                'required': True
            }),
            'database_user': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'أدخل اسم مستخدم قاعدة البيانات',
                'required': True
            }),
            'database_password': forms.PasswordInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'أدخل كلمة مرور قاعدة البيانات',
                'required': True
            }),
            'database_host': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'أدخل عنوان مضيف قاعدة البيانات',
                'value': 'localhost'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'type': 'date',
                'placeholder': 'yyyy-mm-dd'
            }),
            'admin_username': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'سيتم إنشاؤه تلقائياً',
                'readonly': True
            }),
            'admin_password': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'سيتم إنشاؤها تلقائياً',
                'readonly': True
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة علامة النجمة للحقول المطلوبة
        for field_name in self.fields:
            if self.fields[field_name].required:
                self.fields[field_name].label = f"{self.fields[field_name].label} *"

class TwoFactorForm(forms.Form):
    """نموذج التحقق الثنائي"""
    code = forms.CharField(
        label='رمز التحقق',
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'أدخل رمز التحقق من تطبيق المصادقة',
            'required': True,
            'maxlength': '6',
            'pattern': '[0-9]*',
            'inputmode': 'numeric'
        })
    )

class SystemSettingsForm(forms.Form):
    """نموذج إعدادات النظام العامة"""
    app_name = forms.CharField(
        label='اسم التطبيق',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    default_language = forms.ChoiceField(
        label='اللغة الافتراضية',
        choices=[('ar', 'العربية'), ('en', 'الإنجليزية')],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    enable_registration = forms.BooleanField(
        label='تفعيل التسجيل',
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    maintenance_mode = forms.BooleanField(
        label='وضع الصيانة',
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

class BlockedSerialForm(forms.ModelForm):
    """نموذج إضافة وتعديل الأرقام التسلسلية المحظورة"""
    class Meta:
        model = BlockedSerial
        fields = ['serial_number', 'reason', 'is_permanent', 'blocked_until']
        widgets = {
            'serial_number': forms.TextInput(attrs={
                'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'أدخل الرقم التسلسلي بتنسيق XXXX-XXXX-XXXX-XXXX',
                'pattern': '[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}',
                'title': 'يجب أن يكون الرقم التسلسلي بتنسيق XXXX-XXXX-XXXX-XXXX'
            }),
            'reason': forms.Textarea(attrs={
                'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'أدخل سبب الحظر',
                'rows': 3
            }),
            'is_permanent': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded',
                'id': 'is_permanent_checkbox'
            }),
            'blocked_until': forms.DateInput(attrs={
                'class': 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'type': 'date',
                'id': 'blocked_until_date',
                'placeholder': 'yyyy-mm-dd'
            }),
        }
        labels = {
            'serial_number': 'الرقم التسلسلي',
            'reason': 'سبب الحظر',
            'is_permanent': 'حظر دائم',
            'blocked_until': 'محظور حتى تاريخ'
        }
        help_texts = {
            'serial_number': 'أدخل الرقم التسلسلي بتنسيق XXXX-XXXX-XXXX-XXXX',
            'is_permanent': 'تحديد ما إذا كان الحظر دائمًا أم مؤقتًا',
            'blocked_until': 'تاريخ انتهاء الحظر (فقط للحظر المؤقت)'
        }

    def clean_serial_number(self):
        """التحقق من صحة الرقم التسلسلي"""
        serial_number = self.cleaned_data.get('serial_number')

        # التحقق من تنسيق الرقم التسلسلي
        import re
        if not re.match(r'^[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$', serial_number):
            raise forms.ValidationError('يجب أن يكون الرقم التسلسلي بتنسيق XXXX-XXXX-XXXX-XXXX')

        # التحقق من عدم وجود الرقم التسلسلي مسبقًا (إلا إذا كان هذا تعديلًا)
        if not self.instance.pk and BlockedSerial.objects.filter(serial_number=serial_number).exists():
            raise forms.ValidationError('هذا الرقم التسلسلي محظور بالفعل')

        return serial_number

    def clean(self):
        """التحقق من صحة النموذج بالكامل"""
        cleaned_data = super().clean()
        is_permanent = cleaned_data.get('is_permanent')
        blocked_until = cleaned_data.get('blocked_until')

        # إذا كان الحظر مؤقتًا، يجب تحديد تاريخ انتهاء الحظر
        if not is_permanent and not blocked_until:
            self.add_error('blocked_until', 'يجب تحديد تاريخ انتهاء الحظر للحظر المؤقت')

        return cleaned_data
