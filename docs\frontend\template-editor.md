# وثائق محرر القوالب

## نظرة عامة

محرر القوالب هو أداة متقدمة تتيح للمستخدمين إنشاء وتحرير قوالب المستندات بسهولة. يوفر المحرر:
- واجهة سحب وإفلات سهلة الاستخدام
- معاينة مباشرة للقالب
- دعم للمتغيرات الديناميكية
- تصدير القالب كملف PDF

## هيكل المشروع

```
react-template-editor/
├── public/             # الملفات العامة
│   ├── index.html      # ملف HTML الرئيسي
│   ├── favicon.ico     # أيقونة الموقع
│   └── manifest.json   # ملف التكوين للمتصفح
├── src/                # كود المصدر
│   ├── assets/         # الأصول (الصور، الخطوط، إلخ)
│   ├── components/     # مكونات React القابلة لإعادة الاستخدام
│   │   ├── editor/     # مكونات المحرر
│   │   ├── preview/    # مكونات المعاينة
│   │   └── ui/         # مكونات واجهة المستخدم
│   ├── hooks/          # خطافات React المخصصة
│   ├── store/          # مخازن الحالة (Zustand)
│   ├── utils/          # وظائف مساعدة
│   ├── App.js          # مكون التطبيق الرئيسي
│   └── index.js        # نقطة الدخول
├── package.json        # تبعيات المشروع
└── tailwind.config.js  # تكوين Tailwind CSS
```

## المكونات الرئيسية

### 1. EditorCanvas

المكون الرئيسي للمحرر الذي يتيح للمستخدمين سحب وإفلات العناصر.

```jsx
import EditorCanvas from '../components/editor/EditorCanvas';

<EditorCanvas
  elements={elements}
  onElementAdd={handleElementAdd}
  onElementUpdate={handleElementUpdate}
  onElementDelete={handleElementDelete}
/>
```

### 2. ElementToolbar

شريط أدوات يحتوي على العناصر التي يمكن إضافتها إلى القالب.

```jsx
import ElementToolbar from '../components/editor/ElementToolbar';

<ElementToolbar
  onElementDrag={handleElementDrag}
  elements={availableElements}
/>
```

### 3. PreviewDialog

مكون لمعاينة القالب مع البيانات الفعلية.

```jsx
import PreviewDialog from '../components/editor/PreviewDialog';

<PreviewDialog
  isOpen={isPreviewOpen}
  onClose={closePreview}
  template={currentTemplate}
  data={previewData}
/>
```

## مخازن الحالة

### 1. templateStore

مخزن لإدارة حالة القالب الحالي.

```jsx
import { useTemplateStore } from '../store/templateStore';

function TemplateEditor() {
  const { template, elements, addElement, updateElement, deleteElement } = useTemplateStore();
  
  // استخدام المخزن
}
```

### 2. variablesStore

مخزن لإدارة المتغيرات المتاحة للاستخدام في القوالب.

```jsx
import { useVariablesStore } from '../store/variablesStore';

function VariableSelector() {
  const { variables, categories, loadVariables } = useVariablesStore();
  
  // استخدام المخزن
}
```

## أنواع العناصر

### 1. TextElement

عنصر نص بسيط يمكن تحريره.

```jsx
{
  id: 'text-1',
  type: 'text',
  content: 'نص قابل للتحرير',
  position: { x: 100, y: 100 },
  style: {
    fontSize: '16px',
    fontWeight: 'normal',
    color: '#000000',
    textAlign: 'right'
  }
}
```

### 2. ImageElement

عنصر صورة يمكن تحميلها من الجهاز أو من URL.

```jsx
{
  id: 'image-1',
  type: 'image',
  src: '/path/to/image.jpg',
  position: { x: 100, y: 100 },
  size: { width: 200, height: 150 }
}
```

### 3. TableElement

عنصر جدول يمكن تحرير صفوفه وأعمدته.

```jsx
{
  id: 'table-1',
  type: 'table',
  rows: 3,
  columns: 4,
  data: [
    ['العنوان 1', 'العنوان 2', 'العنوان 3', 'العنوان 4'],
    ['بيانات 1', 'بيانات 2', 'بيانات 3', 'بيانات 4'],
    ['بيانات 5', 'بيانات 6', 'بيانات 7', 'بيانات 8']
  ],
  position: { x: 100, y: 100 },
  style: {
    borderColor: '#cccccc',
    headerBackground: '#f0f0f0'
  }
}
```

### 4. VariableElement

عنصر متغير يتم استبداله بقيمة من البيانات.

```jsx
{
  id: 'variable-1',
  type: 'variable',
  variableId: 'client_name',
  displayName: 'اسم العميل',
  category: 'client',
  position: { x: 100, y: 100 },
  style: {
    fontSize: '16px',
    fontWeight: 'bold',
    color: '#0066cc'
  }
}
```

## استخدام المتغيرات

يمكن استخدام المتغيرات في القوالب لعرض بيانات ديناميكية. المتغيرات متاحة في الفئات التالية:

### 1. بيانات العميل

- `client_name`: اسم العميل
- `client_address`: عنوان العميل
- `client_phone`: هاتف العميل
- `client_email`: بريد العميل
- `client_id`: رقم هوية العميل

### 2. بيانات العقد

- `contract_number`: رقم العقد
- `contract_date`: تاريخ العقد
- `contract_start_date`: تاريخ بدء العقد
- `contract_end_date`: تاريخ انتهاء العقد
- `contract_duration`: مدة العقد

### 3. بيانات الدفع

- `payment_amount`: مبلغ الدفع
- `payment_method`: طريقة الدفع
- `payment_schedule`: جدول الدفع
- `payment_currency`: عملة الدفع
- `payment_tax`: ضريبة الدفع

### 4. بيانات العامل

- `worker_name`: اسم العامل
- `worker_nationality`: جنسية العامل
- `worker_id`: رقم هوية العامل
- `worker_passport`: رقم جواز سفر العامل
- `worker_job`: وظيفة العامل

## تصدير القوالب

يمكن تصدير القوالب بالتنسيقات التالية:

### 1. PDF

تصدير القالب كملف PDF مع البيانات الفعلية.

```jsx
import { exportToPdf } from '../utils/exportUtils';

// تصدير القالب كملف PDF
exportToPdf(template, data, 'اسم_الملف.pdf');
```

### 2. JSON

تصدير القالب كملف JSON لاستيراده لاحقًا.

```jsx
import { exportToJson } from '../utils/exportUtils';

// تصدير القالب كملف JSON
exportToJson(template, 'اسم_الملف.json');
```

## التثبيت والتشغيل

### متطلبات النظام

- Node.js 14+
- npm 6+ أو yarn 1.22+

### تثبيت التبعيات

```bash
# باستخدام npm
npm install

# باستخدام yarn
yarn install
```

### تشغيل خادم التطوير

```bash
# باستخدام npm
npm run dev

# باستخدام yarn
yarn dev
```

### بناء الإصدار النهائي

```bash
# باستخدام npm
npm run build

# باستخدام yarn
yarn build
```
