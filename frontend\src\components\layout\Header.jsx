import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { <PERSON>aBars, FaBell, FaMoon, FaSun, FaSearch } from 'react-icons/fa';

// Hooks
import { useTheme } from '../../hooks/useTheme';

const Header = ({ toggleMobileMenu }) => {
  const { darkMode, toggleDarkMode } = useTheme();
  const location = useLocation();
  const [pageTitle, setPageTitle] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);

  // Update page title based on current route
  useEffect(() => {
    const path = location.pathname;

    if (path.includes('/dashboard')) {
      setPageTitle('لوحة التحكم');
    } else if (path.includes('/workers')) {
      setPageTitle('العمال');
    } else if (path.includes('/contracts')) {
      setPageTitle('العقود');
    } else if (path.includes('/services')) {
      setPageTitle('الخدمات');
    } else if (path.includes('/documents')) {
      setPageTitle('المستندات');
    } else if (path.includes('/reports')) {
      setPageTitle('التقارير');
    } else if (path.includes('/settings')) {
      setPageTitle('الإعدادات');
    } else {
      setPageTitle('نظام إدارة العمالة');
    }
  }, [location]);

  // Mock notifications (in a real app, these would come from an API)
  useEffect(() => {
    setNotifications([
      { id: 1, title: 'انتهاء عقد', message: 'سينتهي عقد العميل أحمد محمد خلال 3 أيام', time: '10:30 ص', read: false },
      { id: 2, title: 'انتهاء جواز سفر', message: 'سينتهي جواز سفر العامل راجيش كومار خلال أسبوع', time: 'أمس', read: false },
      { id: 3, title: 'موعد خدمة', message: 'لديك خدمة تنظيف مجدولة غدًا الساعة 9 صباحًا', time: 'أمس', read: true },
    ]);
  }, []);

  // Toggle notifications dropdown
  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  // Mark notification as read
  const markAsRead = (id) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // Count unread notifications
  const unreadCount = notifications.filter((notification) => !notification.read).length;

  return (
    <header className="bg-white shadow-sm z-10">
      <div className="flex items-center justify-between h-16 px-4 md:px-6">
        {/* Left side - Mobile menu button and page title */}
        <div className="flex items-center">
          <button
            onClick={toggleMobileMenu}
            className="md:hidden p-2 mr-2 rounded-md text-primary hover:text-primary-dark hover:bg-primary-lighter focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <FaBars className="h-6 w-6" />
          </button>

          <h1 className="text-xl font-bold text-neutral-dark">{pageTitle}</h1>
        </div>

        {/* Right side - Search, theme toggle, notifications */}
        <div className="flex items-center space-x-reverse space-x-4">
          {/* Search */}
          <div className="hidden md:block relative">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FaSearch className="h-4 w-4 text-primary" />
            </div>
            <input
              type="text"
              placeholder="بحث..."
              className="search-input"
            />
          </div>

          {/* Theme toggle */}
          <button
            onClick={toggleDarkMode}
            className="p-2 rounded-full text-primary hover:text-primary-dark hover:bg-primary-lighter focus:outline-none focus:ring-2 focus:ring-primary"
          >
            {darkMode ? (
              <FaSun className="h-5 w-5" />
            ) : (
              <FaMoon className="h-5 w-5" />
            )}
          </button>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={toggleNotifications}
              className="p-2 rounded-full text-primary hover:text-primary-dark hover:bg-primary-lighter focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <FaBell className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 block h-5 w-5 rounded-full bg-secondary text-white text-xs flex items-center justify-center">
                  {unreadCount}
                </span>
              )}
            </button>

            {/* Notifications dropdown */}
            {showNotifications && (
              <div className="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 border border-neutral-light">
                <div className="px-4 py-2 border-b border-neutral-light bg-primary-lighter">
                  <h3 className="text-lg font-medium text-neutral-dark">الإشعارات</h3>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`px-4 py-3 hover:bg-primary-lighter cursor-pointer ${
                          !notification.read ? 'bg-secondary-light border-r-4 border-secondary' : ''
                        }`}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex justify-between">
                          <p className="font-medium text-neutral-dark">{notification.title}</p>
                          <p className="text-xs text-neutral">{notification.time}</p>
                        </div>
                        <p className="text-sm text-neutral mt-1">{notification.message}</p>
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-3 text-center text-neutral">
                      لا توجد إشعارات
                    </div>
                  )}
                </div>

                <div className="px-4 py-2 border-t border-neutral-light text-center bg-primary-lighter">
                  <button className="text-primary hover:text-primary-dark text-sm font-medium">
                    عرض جميع الإشعارات
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
