from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone


class Client(models.Model):
    """نموذج العملاء مع جميع الأنواع المطلوبة"""

    CLIENT_TYPE_CHOICES = [
        ('individual', 'فرد'),
        ('company', 'شركة'),
        ('government', 'جهة حكومية'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('pending', 'معلق'),
        ('blocked', 'محظور'),
    ]

    # معلومات أساسية
    name = models.CharField(max_length=255, verbose_name='اسم العميل')
    client_type = models.CharField(
        max_length=20,
        choices=CLIENT_TYPE_CHOICES,
        verbose_name='نوع العميل'
    )
    status = models.Char<PERSON>ield(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name='الحالة'
    )

    # معلومات الاتصال
    phone = models.CharField(
        max_length=20,
        verbose_name='رقم الهاتف',
        validators=[RegexValidator(
            regex=r'^\+?[0-9]{8,15}$',
            message='رقم الهاتف يجب أن يكون بين 8-15 رقم'
        )]
    )
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(verbose_name='العنوان')

    # معلومات الهوية (للأفراد)
    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم الهوية الوطنية'
    )
    passport_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم جواز السفر'
    )

    # معلومات الشركة (للشركات)
    commercial_register = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم السجل التجاري'
    )
    tax_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='الرقم الضريبي'
    )

    # معلومات الكفيل (للأفراد الأجانب)
    sponsor_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='اسم الكفيل'
    )
    sponsor_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='هاتف الكفيل'
    )
    visa_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم التأشيرة'
    )

    # معلومات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عميل'
        verbose_name_plural = 'العملاء'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_client_type_display_ar(self):
        """عرض نوع العميل بالعربية"""
        type_mapping = {
            'individual': 'فرد',
            'company': 'شركة',
            'government': 'جهة حكومية'
        }
        return type_mapping.get(self.client_type, self.client_type)

    def get_status_display_ar(self):
        """عرض حالة العميل بالعربية"""
        status_mapping = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'pending': 'معلق',
            'suspended': 'موقوف'
        }
        return status_mapping.get(self.status, self.status)




class ClientDocument(models.Model):
    """مستندات العملاء"""

    DOCUMENT_TYPE_CHOICES = [
        ('id_copy', 'صورة الهوية'),
        ('passport_copy', 'صورة جواز السفر'),
        ('commercial_register', 'السجل التجاري'),
        ('tax_certificate', 'الشهادة الضريبية'),
        ('authorization_letter', 'خطاب تفويض'),
        ('contract', 'عقد'),
        ('other', 'أخرى'),
    ]

    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name='العميل'
    )
    document_type = models.CharField(
        max_length=30,
        choices=DOCUMENT_TYPE_CHOICES,
        verbose_name='نوع المستند'
    )
    title = models.CharField(max_length=255, verbose_name='عنوان المستند')
    file = models.FileField(
        upload_to='clients/documents/%Y/%m/',
        verbose_name='الملف'
    )
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')

    class Meta:
        verbose_name = 'مستند عميل'
        verbose_name_plural = 'مستندات العملاء'
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.client.name} - {self.title}"


class ClientContact(models.Model):
    """جهات اتصال إضافية للعملاء"""

    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='contacts',
        verbose_name='العميل'
    )
    name = models.CharField(max_length=255, verbose_name='الاسم')
    position = models.CharField(max_length=100, blank=True, null=True, verbose_name='المنصب')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    is_primary = models.BooleanField(default=False, verbose_name='جهة الاتصال الرئيسية')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'جهة اتصال'
        verbose_name_plural = 'جهات الاتصال'
        ordering = ['-is_primary', 'name']

    def __str__(self):
        return f"{self.client.name} - {self.name}"
