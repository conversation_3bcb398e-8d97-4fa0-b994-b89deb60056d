{% extends 'layouts/super_admin.html' %}

{% block title %}إعدادات البريد الإلكتروني{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-envelope ml-2"></i>
                    إعدادات البريد الإلكتروني
                </h1>
                <p class="text-green-100">
                    تكوين إعدادات البريد الإلكتروني للنظام والمصادقة الثنائية
                </p>
            </div>
            <div class="text-center">
                <i class="fas fa-mail-bulk text-4xl text-green-200"></i>
            </div>
        </div>
    </div>

    <!-- Current Configuration -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-info-circle ml-2"></i>
            التكوين الحالي
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Current Email -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-user ml-2"></i>
                    البريد الإلكتروني الحالي
                </h3>
                <p class="text-lg font-mono text-blue-600">{{ current_email|default:'غير محدد' }}</p>
                <p class="text-sm text-gray-500 mt-1">
                    البريد المستخدم لإرسال رموز التحقق
                </p>
            </div>

            <!-- SMTP Status -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-server ml-2"></i>
                    حالة خادم SMTP
                </h3>
                <div class="flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check-circle ml-1"></i>
                        Gmail SMTP
                    </span>
                </div>
                <p class="text-sm text-gray-500 mt-1">
                    smtp.gmail.com:587 (TLS)
                </p>
            </div>
        </div>
    </div>

    <!-- Email Configuration Form -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-cog ml-2"></i>
            تحديث إعدادات البريد الإلكتروني
        </h2>

        <!-- Important Notice -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-yellow-800">ملاحظة مهمة</h3>
                    <div class="text-sm text-yellow-700 mt-1">
                        <p>• يجب استخدام كلمة مرور التطبيق (App Password) وليس كلمة المرور العادية</p>
                        <p>• تأكد من تفعيل المصادقة الثنائية في حساب Gmail أولاً</p>
                        <p>• سيتم إعادة تشغيل الخادم لتطبيق التغييرات</p>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Email Address -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    عنوان البريد الإلكتروني
                    <span class="text-red-500">*</span>
                </label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       value="{{ current_email|default:'' }}"
                       placeholder="<EMAIL>"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-info-circle ml-1"></i>
                    يُفضل استخدام حساب Gmail للحصول على أفضل توافق
                </p>
            </div>

            <!-- App Password -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    كلمة مرور التطبيق (App Password)
                </label>
                <input type="password" 
                       id="password" 
                       name="password" 
                       placeholder="xxxx xxxx xxxx xxxx"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono">
                <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-key ml-1"></i>
                    اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور الحالية
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{% url 'super_admin:user_profile' %}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للملف الشخصي
                </a>
                
                <button type="submit" 
                        onclick="return confirm('هل أنت متأكد من تحديث إعدادات البريد الإلكتروني؟\n\nسيتم إعادة تشغيل الخادم لتطبيق التغييرات.')"
                        class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </form>
    </div>

    <!-- How to Get App Password -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 class="text-lg font-medium text-blue-800 mb-4">
            <i class="fas fa-question-circle ml-2"></i>
            كيفية الحصول على كلمة مرور التطبيق من Gmail
        </h3>
        
        <div class="text-blue-700 space-y-3 text-sm">
            <div class="flex items-start">
                <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full ml-3 mt-0.5">1</span>
                <div>
                    <p class="font-medium">تفعيل المصادقة الثنائية</p>
                    <p>انتقل إلى إعدادات حساب Google وفعّل المصادقة الثنائية</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full ml-3 mt-0.5">2</span>
                <div>
                    <p class="font-medium">الوصول لكلمات مرور التطبيق</p>
                    <p>اذهب إلى: حساب Google → الأمان → كلمات مرور التطبيق</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full ml-3 mt-0.5">3</span>
                <div>
                    <p class="font-medium">إنشاء كلمة مرور جديدة</p>
                    <p>اختر "تطبيق آخر" واكتب "استقدامي" ثم انقر "إنشاء"</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full ml-3 mt-0.5">4</span>
                <div>
                    <p class="font-medium">نسخ كلمة المرور</p>
                    <p>انسخ كلمة المرور المكونة من 16 رقم والصقها في الحقل أعلاه</p>
                </div>
            </div>
        </div>
        
        <div class="mt-4 p-3 bg-blue-100 rounded-lg">
            <p class="text-blue-800 text-sm">
                <i class="fas fa-lightbulb ml-1"></i>
                <strong>نصيحة:</strong> احتفظ بكلمة مرور التطبيق في مكان آمن، لن تتمكن من رؤيتها مرة أخرى
            </p>
        </div>
    </div>

    <!-- Test Email Section -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-paper-plane ml-2"></i>
            اختبار إعدادات البريد الإلكتروني
        </h2>
        
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-medium text-gray-900">إرسال بريد تجريبي</h3>
                    <p class="text-sm text-gray-500">
                        اختبر إعدادات البريد الإلكتروني بإرسال رسالة تجريبية
                    </p>
                </div>
                <button type="button" 
                        onclick="sendTestEmail()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-paper-plane ml-2"></i>
                    إرسال اختبار
                </button>
            </div>
        </div>
    </div>

    <!-- Current Settings Display -->
    <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
        <h3 class="text-lg font-medium text-gray-800 mb-4">
            <i class="fas fa-cogs ml-2"></i>
            الإعدادات التقنية الحالية
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">خادم SMTP:</span>
                <span class="text-gray-600">smtp.gmail.com</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">المنفذ:</span>
                <span class="text-gray-600">587</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">التشفير:</span>
                <span class="text-gray-600">TLS</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">المصادقة:</span>
                <span class="text-gray-600">مطلوبة</span>
            </div>
        </div>
    </div>
</div>

<script>
function sendTestEmail() {
    // هذه دالة تجريبية - يمكن تطويرها لاحقاً
    alert('ميزة إرسال البريد التجريبي ستكون متاحة قريباً');
}
</script>
{% endblock %}
