import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaHome, FaUsers, FaFileContract, FaBroom,
  FaFileAlt, FaChartBar, FaCog, FaSignOutAlt,
  FaPalette
} from 'react-icons/fa';

// Hooks
import { useTheme } from '../../hooks/useTheme';
import { useAuth } from '../../hooks/useAuth';

const Sidebar = ({ mobile = false, closeMobileMenu }) => {
  const { sidebarOpen, toggleSidebar } = useTheme();
  const { logout, user } = useAuth();
  const location = useLocation();

  // Navigation items
  const navItems = [
    { path: '/dashboard', name: 'لوحة التحكم', icon: <FaHome /> },
    { path: '/workers', name: 'العمال', icon: <FaUsers /> },
    { path: '/contracts', name: 'العقود', icon: <FaFileContract /> },
    { path: '/services', name: 'الخدمات', icon: <FaBroom /> },
    { path: '/documents', name: 'المستندات', icon: <FaFileAlt /> },
    { path: '/reports', name: 'التقارير', icon: <FaChartBar /> },
    { path: '/settings', name: 'الإعدادات', icon: <FaCog /> },
    { path: '/ui-components', name: 'مكونات الواجهة', icon: <FaPalette /> },
  ];

  // Handle logout
  const handleLogout = () => {
    logout();
  };

  // Handle mobile menu item click
  const handleMobileItemClick = () => {
    if (mobile && closeMobileMenu) {
      closeMobileMenu();
    }
  };

  return (
    <div className={`h-full flex flex-col bg-primary-dark text-white ${sidebarOpen ? 'w-64' : 'w-20'} transition-all duration-300 ease-in-out`}>
      {/* Logo */}
      <div className="flex items-center justify-center h-16 border-b border-primary">
        {sidebarOpen ? (
          <h1 className="text-xl font-bold">نظام إدارة العمالة</h1>
        ) : (
          <h1 className="text-xl font-bold">ن.ع</h1>
        )}
      </div>

      {/* User info */}
      <div className={`flex items-center p-4 border-b border-primary ${sidebarOpen ? 'justify-start' : 'justify-center'}`}>
        <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
          <span className="text-lg font-bold">{user?.username?.charAt(0).toUpperCase() || 'U'}</span>
        </div>

        {sidebarOpen && (
          <div className="mr-3">
            <p className="font-medium">{user?.full_name || user?.username || 'المستخدم'}</p>
            <p className="text-xs text-primary-light">{user?.is_staff ? 'مشرف' : 'مستخدم'}</p>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-2 px-2">
          {navItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) => `
                  flex items-center p-2 rounded-lg transition-colors
                  ${isActive ? 'bg-secondary text-white font-medium' : 'text-white hover:bg-primary'}
                  ${sidebarOpen ? 'justify-start' : 'justify-center'}
                `}
                onClick={handleMobileItemClick}
              >
                <span className="text-xl">{item.icon}</span>

                {sidebarOpen && (
                  <span className="mr-3">{item.name}</span>
                )}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      {/* Bottom actions */}
      <div className="p-4 border-t border-primary">
        {/* Toggle sidebar button (desktop only) */}
        {!mobile && (
          <button
            onClick={toggleSidebar}
            className="w-full flex items-center justify-center p-2 text-white hover:bg-primary rounded-lg transition-colors"
          >
            <svg
              className={`w-6 h-6 transition-transform duration-300 ${sidebarOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d={sidebarOpen ? "M15 19l-7-7 7-7" : "M9 5l7 7-7 7"}
              />
            </svg>

            {sidebarOpen && (
              <span className="mr-3">تصغير القائمة</span>
            )}
          </button>
        )}

        {/* Logout button */}
        <button
          onClick={handleLogout}
          className={`w-full flex items-center p-2 mt-2 text-white bg-danger hover:bg-danger-dark rounded-lg transition-colors ${sidebarOpen ? 'justify-start' : 'justify-center'}`}
        >
          <FaSignOutAlt className="text-xl" />

          {sidebarOpen && (
            <span className="mr-3">تسجيل الخروج</span>
          )}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
