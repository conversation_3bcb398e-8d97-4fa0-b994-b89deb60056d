# Generated by Django 5.2 on 2025-05-19 08:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('labor_management', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='daily24hservice',
            name='worker',
        ),
        migrations.RemoveField(
            model_name='dailyservice',
            name='worker',
        ),
        migrations.AddField(
            model_name='daily24hservice',
            name='location',
            field=models.TextField(blank=True, null=True, verbose_name='موقع الخدمة'),
        ),
        migrations.AddField(
            model_name='daily24hservice',
            name='start_time',
            field=models.TimeField(default='08:00', verbose_name='وقت بداية الخدمة'),
        ),
        migrations.AddField(
            model_name='daily24hservice',
            name='workers',
            field=models.ManyToManyField(blank=True, related_name='daily_24h_services_assigned', to='labor_management.worker', verbose_name='العمال'),
        ),
        migrations.AddField(
            model_name='daily24hservice',
            name='workers_required',
            field=models.PositiveIntegerField(default=1, verbose_name='عدد العمال المطلوبين'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='booking_type',
            field=models.CharField(choices=[('fixed', 'حجز ثابت'), ('temporary', 'حجز مؤقت')], default='temporary', max_length=20, verbose_name='نوع الحجز'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='location',
            field=models.TextField(blank=True, null=True, verbose_name='موقع الخدمة'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='overtime_fee',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='رسوم العمل الإضافي'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='overtime_hours',
            field=models.PositiveIntegerField(default=0, verbose_name='ساعات العمل الإضافية'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='service_time',
            field=models.TimeField(default='08:00', verbose_name='وقت الخدمة'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='workers',
            field=models.ManyToManyField(blank=True, related_name='daily_services_assigned', to='labor_management.worker', verbose_name='العمال'),
        ),
        migrations.AddField(
            model_name='dailyservice',
            name='workers_required',
            field=models.PositiveIntegerField(default=1, verbose_name='عدد العمال المطلوبين'),
        ),
        migrations.AlterField(
            model_name='dailyservice',
            name='hours',
            field=models.PositiveIntegerField(default=7, verbose_name='عدد الساعات'),
        ),
        migrations.CreateModel(
            name='CustomService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الخدمة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية الخدمة')),
                ('end_date', models.DateField(verbose_name='تاريخ نهاية الخدمة')),
                ('service_days', models.CharField(blank=True, max_length=255, null=True, verbose_name='أيام الخدمة')),
                ('service_time', models.TimeField(default='08:00', verbose_name='وقت الخدمة')),
                ('duration_hours', models.PositiveIntegerField(default=7, verbose_name='مدة الخدمة بالساعات')),
                ('workers_required', models.PositiveIntegerField(default=1, verbose_name='عدد العمال المطلوبين')),
                ('fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='رسوم الخدمة')),
                ('location', models.TextField(blank=True, null=True, verbose_name='موقع الخدمة')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الخدمة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_services', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_custom_services', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('workers', models.ManyToManyField(blank=True, related_name='custom_services_assigned', to='labor_management.worker', verbose_name='العمال')),
            ],
            options={
                'verbose_name': 'خدمة مخصصة',
                'verbose_name_plural': 'الخدمات المخصصة',
                'ordering': ['-start_date'],
            },
        ),
    ]
