<!DOCTYPE html>
<html class="h-full" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'لوحة تحكم المسؤول الأعلى | استقدامي')</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'admin-primary': '#0F2557',
                        'admin-secondary': '#1E5F74',
                        'admin-accent': '#4B8F8C',
                        'admin-light': '#E5F1F1',
                        'admin-dark': '#0A1A3F',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            },
            darkMode: 'class',
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Tajawal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #0F2557;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #1E5F74;
        }
        
        /* Dark mode styles */
        .dark ::-webkit-scrollbar-track {
            background: #1a1a1a;
        }
        
        .dark ::-webkit-scrollbar-thumb {
            background: #4B8F8C;
        }
        
        .dark ::-webkit-scrollbar-thumb:hover {
            background: #1E5F74;
        }
    </style>

    @yield('extra_css')
</head>
<body class="min-h-screen flex flex-col bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200" x-data="{ sidebarOpen: true, darkMode: localStorage.getItem('darkMode') === 'true' }" :class="{ 'dark': darkMode }">
    
    <!-- Top Navigation Bar -->
    <header class="bg-admin-primary dark:bg-admin-dark text-white shadow-md z-30 fixed top-0 inset-x-0">
        <div class="flex justify-between items-center px-4 py-2">
            <!-- Left - Logo and System Name -->
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                <button @click="sidebarOpen = !sidebarOpen" class="p-1 rounded-full hover:bg-admin-secondary transition-colors">
                    <i class="fas fa-bars"></i>
                </button>
                <img src="/static/images/logo.webp" alt="شعار المنصة"
                     class="w-10 h-10 object-contain hover:animate-pulse transition-all duration-300"
                     onerror="this.src='/static/images/logo-placeholder.png'; this.onerror=null;">
                <div class="flex flex-col">
                    <span class="text-lg font-bold text-admin-accent">استقدامي</span>
                    <span class="text-xs text-gray-300">لوحة المسؤول الأعلى v1.0</span>
                </div>
            </div>

            <!-- Right - User Info and Settings -->
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <!-- Dark Mode Toggle -->
                <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)" class="p-2 rounded-full hover:bg-admin-secondary transition-colors" title="تبديل الوضع الليلي">
                    <i class="fas" :class="darkMode ? 'fa-sun text-yellow-300' : 'fa-moon text-gray-300'"></i>
                </button>

                <!-- User Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-2 rtl:space-x-reverse bg-admin-secondary hover:bg-admin-accent rounded-full px-3 py-1 transition-colors">
                        <div class="w-8 h-8 rounded-full bg-admin-accent flex items-center justify-center">
                            <i class="fas fa-user-shield text-white"></i>
                        </div>
                        <span class="font-medium text-sm">{{ auth()->user()->username ?? 'المسؤول الأعلى' }}</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-xl text-xs z-50 border border-gray-200 dark:border-gray-700 overflow-hidden transform origin-top-right transition-all duration-300">
                        <div class="p-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="font-bold text-gray-900 dark:text-gray-100 text-xs">{{ auth()->user()->username ?? 'المسؤول الأعلى' }}</div>
                            <div class="text-gray-500 dark:text-gray-400 text-xs">المسؤول الأعلى</div>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('super_admin.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                <i class="fas fa-user-circle ml-2"></i> الملف الشخصي
                            </a>
                            <a href="{{ route('super_admin.settings') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                <i class="fas fa-cog ml-2"></i> إعدادات النظام
                            </a>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 py-1">
                            <a href="{{ route('super_admin.logout') }}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container with Sidebar -->
    <div class="flex flex-1 pt-14">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 pt-14 bg-admin-primary dark:bg-admin-dark text-white z-20 transition-all duration-300 shadow-lg"
               :class="sidebarOpen ? 'w-64' : 'w-16'" 
               style="transition: width 0.3s ease;">
            <div class="h-full flex flex-col overflow-y-auto">
                <!-- Sidebar Menu -->
                <nav class="flex-1 py-4 px-2">
                    <ul class="space-y-1">
                        <!-- Dashboard -->
                        <li>
                            <a href="{{ route('super_admin.dashboard') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-tachometer-alt text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">لوحة التحكم</span>
                            </a>
                        </li>
                        
                        <!-- Companies Management -->
                        <li>
                            <a href="{{ route('super_admin.companies') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-building text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">إدارة الشركات</span>
                            </a>
                        </li>
                        
                        <!-- Database Management -->
                        <li>
                            <a href="{{ route('super_admin.database') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-database text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">قواعد البيانات</span>
                            </a>
                        </li>
                        
                        <!-- Reports -->
                        <li>
                            <a href="{{ route('super_admin.reports') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-chart-bar text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">تقارير عامة</span>
                            </a>
                        </li>
                        
                        <!-- System Logs -->
                        <li>
                            <a href="{{ route('super_admin.logs') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-history text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">سجل الأحداث</span>
                            </a>
                        </li>
                        
                        <!-- Backup & Scheduling -->
                        <li>
                            <a href="{{ route('super_admin.backup') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-save text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">النسخ الاحتياطي</span>
                            </a>
                        </li>
                        
                        <!-- License Management -->
                        <li>
                            <a href="{{ route('super_admin.licenses') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-key text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">إدارة التراخيص</span>
                            </a>
                        </li>
                        
                        <!-- Settings -->
                        <li>
                            <a href="{{ route('super_admin.settings') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-cog text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="ml-3" x-show="sidebarOpen">الإعدادات</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <!-- Sidebar Footer -->
                <div class="p-3 border-t border-admin-secondary text-xs text-center" x-show="sidebarOpen">
                    <p>استقدامي - الإصدار 1.0</p>
                    <p class="text-admin-accent">{{ date('Y-m-d') }}</p>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 transition-all duration-300" :class="sidebarOpen ? 'mr-64' : 'mr-16'">
            <div class="container mx-auto px-4 py-6">
                <!-- Page Header -->
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-admin-primary dark:text-admin-accent">@yield('page_title', 'لوحة التحكم')</h1>
                    <p class="text-gray-600 dark:text-gray-400">@yield('page_subtitle', 'مرحباً بك في لوحة تحكم المسؤول الأعلى')</p>
                </div>
                
                <!-- Page Content -->
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 text-center text-sm text-gray-600 dark:text-gray-400">
        <div class="container mx-auto">
            <p>جميع الحقوق محفوظة &copy; {{ date('Y') }} استقدامي - نظام إدارة العمالة</p>
            <p>الإصدار 1.0 | تاريخ الإصدار: 2023-12-01</p>
        </div>
    </footer>

    <!-- Notification System -->
    <div id="toast-container" class="fixed top-4 left-4 z-50 flex flex-col space-y-4"></div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden" x-data="{ show: false }" x-show="show" x-cloak>
        <div class="absolute inset-0 bg-black bg-opacity-50" @click="show = false"></div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <h3 class="text-lg font-bold mb-4 text-gray-900 dark:text-white">تأكيد الحذف</h3>
            <p class="mb-6 text-gray-700 dark:text-gray-300">هل أنت متأكد من رغبتك في حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="flex justify-end space-x-3 rtl:space-x-reverse">
                <button @click="show = false" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">إلغاء</button>
                <button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">حذف</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Toast notification system
        function showToast(message, type = 'success', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `px-4 py-3 rounded-lg shadow-lg flex items-center ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            } text-white`;
            
            const icon = document.createElement('i');
            icon.className = `fas ${
                type === 'success' ? 'fa-check-circle' : 
                type === 'error' ? 'fa-exclamation-circle' : 
                type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
            } ml-3`;
            
            const text = document.createElement('span');
            text.textContent = message;
            
            toast.appendChild(icon);
            toast.appendChild(text);
            
            const container = document.getElementById('toast-container');
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, duration);
        }
        
        // Delete confirmation system
        function confirmDelete(callback) {
            const modal = document.getElementById('delete-modal');
            const confirmBtn = document.getElementById('confirm-delete');
            
            modal.__x.$data.show = true;
            
            confirmBtn.onclick = () => {
                callback();
                modal.__x.$data.show = false;
            };
        }
    </script>

    @yield('extra_js')
</body>
</html>
