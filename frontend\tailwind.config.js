/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
  ],
  theme: {
    extend: {
      colors: {
        // الألوان الأساسية
        primary: {
          DEFAULT: '#0781ae', // أزرق متوسط - اللون الرئيسي للنظام
          dark: '#0a384e',    // أزرق داكن - للعناصر الداكنة والتأثيرات
          medium: '#19788c',  // أزرق متوسط آخر - للتنويع
          light: '#cbdbeb',   // أزرق فاتح - للخلفيات الخفيفة
          lighter: '#f0efef', // أبيض مائل للرمادي - للخلفيات الفاتحة جداً
        },

        // الألوان الثانوية
        secondary: {
          DEFAULT: '#ce913d', // ذهبي/برتقالي - للعناصر الثانوية
          dark: '#c05b2a',    // برتقالي داكن - للتأثيرات
          light: '#cfae86',   // بيج فاتح - للخلفيات الخفيفة
          lightest: '#d1ceba', // بيج فاتح جداً - للخلفيات الفاتحة جداً
        },

        // ألوان الحالة
        success: {
          DEFAULT: '#535b2c', // أخضر زيتوني - للنجاح
          dark: '#242611',    // أخضر داكن - للتأثيرات
          light: '#8fbfb6',   // أخضر فاتح - للخلفيات الخفيفة
        },

        danger: {
          DEFAULT: '#74250c', // أحمر بني - للخطر
          dark: '#74250c',    // أحمر بني داكن - للتأثيرات
          light: '#c05b2a',   // برتقالي محمر - للخلفيات الخفيفة
        },

        warning: {
          DEFAULT: '#ce913d', // ذهبي - للتحذير
          dark: '#896f46',    // بني ذهبي - للتأثيرات
          light: '#bd9a78',   // بني فاتح - للخلفيات الخفيفة
        },

        info: {
          DEFAULT: '#065ca5', // أزرق عميق - للمعلومات
          dark: '#075a80',    // أزرق داكن - للتأثيرات
          light: '#7c9cac',   // أزرق رمادي - للخلفيات الخفيفة
        },

        // ألوان محايدة
        neutral: {
          DEFAULT: '#bcb1b2', // رمادي متوسط - للنصوص الثانوية
          dark: '#544025',    // بني داكن - للنصوص الداكنة
          light: '#f0efef',   // أبيض مائل للرمادي - للخلفيات
        },

        // الألوان الإضافية
        blue: {
          '345785': '#345785',
          '898889': '#898889',
          '2c332e': '#2c332e',
          '4b93da': '#4b93da',
          '90c1dc': '#90c1dc',
          'd2dae5': '#d2dae5',
        },
        gray: {
          '294252': '#294252',
          '3773b5': '#3773b5',
          '4f93ad': '#4f93ad',
          '5e6d7d': '#5e6d7d',
          'c4b395': '#c4b395',
          '152851': '#152851',
        },
        accent: {
          '42759b': '#42759b',
          '0c4477': '#0c4477',
          'c08b89': '#c08b89',
          'd34169': '#d34169',
          'c38d61': '#c38d61',
          '8faee1': '#8faee1',
          '3938d0': '#3938d0',
          'bbafb9': '#bbafb9',
        },
      },
      fontFamily: {
        cairo: ['Cairo', 'sans-serif'],
      },
      boxShadow: {
        card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      borderRadius: {
        'card': '0.75rem',
      },
      spacing: {
        '72': '18rem',
        '84': '21rem',
        '96': '24rem',
      },
      zIndex: {
        '-10': '-10',
        '60': '60',
        '70': '70',
      },
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideInDown: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
      },
      animation: {
        fadeIn: 'fadeIn 0.5s ease-in-out',
        slideInRight: 'slideInRight 0.5s ease-in-out',
        slideInLeft: 'slideInLeft 0.5s ease-in-out',
        slideInUp: 'slideInUp 0.5s ease-in-out',
        slideInDown: 'slideInDown 0.5s ease-in-out',
      },
    },
  },
  plugins: [
    require('tailwindcss-rtl'),
  ],
  // تفعيل وضع RTL افتراضيًا
  rtl: true,
}
