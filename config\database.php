<?php

/**
 * ملف تكوين قواعد البيانات
 * 
 * هذا الملف يحتوي على إعدادات قواعد البيانات المختلفة للنظام
 * ويوفر وظائف مساعدة للاتصال بقواعد البيانات المختلفة
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    */

    'connections' => [
        // اتصال قاعدة البيانات الرئيسية (المسؤول الأعلى)
        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'labor_management'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        // اتصال قاعدة بيانات SQLite (للشركات)
        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        // اتصال قاعدة بيانات الشركة (سيتم تكوينه ديناميكيًا)
        'tenant' => [
            'driver' => 'mysql',
            'host' => '127.0.0.1',
            'port' => '3306',
            'database' => 'tenant_db',
            'username' => 'tenant_user',
            'password' => 'tenant_password',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', 'labor_management_'),
        ],
        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],
        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Company Database Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may specify the configuration for company databases.
    | This includes the path to the database files and other settings.
    |
    */

    'company' => [
        // مسار قواعد بيانات الشركات
        'path' => env('COMPANY_DB_PATH', database_path('company_dbs')),
        
        // نوع قاعدة البيانات الافتراضي للشركات
        'driver' => env('COMPANY_DB_DRIVER', 'sqlite'),
        
        // إعدادات قاعدة بيانات MySQL للشركات
        'mysql' => [
            'host' => env('COMPANY_DB_HOST', '127.0.0.1'),
            'port' => env('COMPANY_DB_PORT', '3306'),
            'username' => env('COMPANY_DB_USERNAME', 'company_user'),
            'password' => env('COMPANY_DB_PASSWORD', 'company_password'),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],
        
        // إعدادات قاعدة بيانات SQLite للشركات
        'sqlite' => [
            'foreign_key_constraints' => true,
            'prefix' => '',
        ],
    ],
];
