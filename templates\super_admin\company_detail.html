{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}تفاصيل الشركة: {{ company.name }} | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/backup_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/system_logs_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/backup_logs_specific_fix.css' %}">
<style>
    /* تحسين تباين النصوص والعناوين */
    h2.page-title {
        font-size: 2.25rem;
        color: #1e40af;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    /* تحسين مظهر البطاقات */
    .card-section {
        transition: all 0.3s ease;
    }

    .card-section:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    /* تحسين مظهر الحالة */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
    }

    /* تحسين مظهر الرقم التسلسلي */
    .serial-number-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .serial-number {
        font-family: monospace;
        background-color: #f3f4f6;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid #e5e7eb;
        color: #1e40af;
    }

    .copy-btn {
        color: #6b7280;
        cursor: pointer;
        transition: color 0.2s;
    }

    .copy-btn:hover {
        color: #1e40af;
    }

    /* تحسين مظهر سجل الأحداث */
    .log-item {
        border-right: 3px solid transparent;
        transition: all 0.2s ease;
    }

    .log-item:hover {
        background-color: #f9fafb;
    }

    .log-item.create {
        border-right-color: #10b981;
    }

    .log-item.update {
        border-right-color: #3b82f6;
    }

    .log-item.delete {
        border-right-color: #ef4444;
    }

    .log-item.activate {
        border-right-color: #10b981;
    }

    .log-item.deactivate {
        border-right-color: #f59e0b;
    }

    /* تحسين مظهر النوافذ المنبثقة */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        z-index: 50;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background-color: white;
        border-radius: 0.5rem;
        max-width: 500px;
        width: 90%;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    /* تحسين مظهر كلمة المرور المخفية */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        left: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <!-- رسائل النظام -->
    <div id="toast-container" class="fixed top-4 left-4 right-4 z-50">
        {% if messages %}
            {% for message in messages %}
                <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down" role="alert">
                    <span class="block sm:inline">{{ message }}</span>
                    <button type="button" class="absolute top-0 bottom-0 left-0 px-4 py-3 close-toast">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- CSRF Token للطلبات AJAX -->
    {% csrf_token %}

    <div class="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <h2 class="text-3xl font-bold text-blue-800 page-title">تفاصيل الشركة: {{ company.name }}</h2>
        <div class="flex flex-wrap gap-2">
            <a href="{% url 'super_admin:dashboard' %}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center">
                <i class="fas fa-arrow-right ml-1"></i> العودة للوحة التحكم
            </a>
            <button id="toggle-status-btn" class="{% if company.status == 'active' %}bg-red-600 hover:bg-red-700{% else %}bg-green-600 hover:bg-green-700{% endif %} text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                {% if company.status == 'active' %}
                    <i class="fas fa-ban ml-1"></i> إيقاف الشركة
                {% else %}
                    <i class="fas fa-check ml-1"></i> تفعيل الشركة
                {% endif %}
            </button>
            <button id="export-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-file-export ml-1"></i> تصدير البيانات
            </button>
        </div>
    </div>

    <!-- معلومات الشركة -->
    <div class="bg-gray-50 p-6 rounded-xl border border-gray-200 card-section">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-700">معلومات الشركة</h3>
            <button id="edit-company-btn" class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                <i class="fas fa-edit"></i> تعديل
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">اسم الشركة</h4>
                    <p class="text-lg font-medium company-name">{{ company.name }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">الرقم التسلسلي</h4>
                    <div class="serial-number-container">
                        <code class="serial-number">{{ company.get_formatted_serial }}</code>
                        <button class="copy-btn" data-copy="{{ company.get_formatted_serial }}" data-field="serial_number" title="نسخ الرقم التسلسلي">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                        <i class="fas fa-info-circle"></i>
                        هذا الرقم التسلسلي فريد ويستخدم للتعرف على الشركة في النظام
                    </p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">الحالة</h4>
                    <p>
                        {% if company.status == 'active' %}
                            <span class="status-badge bg-green-100 text-green-800">
                                <i class="fas fa-check-circle"></i> نشطة
                            </span>
                        {% elif company.status == 'inactive' %}
                            <span class="status-badge bg-red-100 text-red-800">
                                <i class="fas fa-times-circle"></i> غير نشطة
                            </span>
                        {% elif company.status == 'suspended' %}
                            <span class="status-badge bg-yellow-100 text-yellow-800">
                                <i class="fas fa-exclamation-circle"></i> معلقة
                            </span>
                        {% elif company.status == 'trial' %}
                            <span class="status-badge bg-blue-100 text-blue-800">
                                <i class="fas fa-flask"></i> تجريبية
                            </span>
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">تاريخ الإنشاء</h4>
                    <p class="flex items-center gap-1">
                        <i class="fas fa-calendar-plus text-gray-400"></i>
                        {{ company.created_at|date:"j F Y - H:i" }}
                    </p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">تاريخ آخر تحديث</h4>
                    <p class="flex items-center gap-1">
                        <i class="fas fa-calendar-check text-gray-400"></i>
                        {{ company.updated_at|date:"j F Y - H:i" }}
                    </p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">تاريخ انتهاء الاشتراك</h4>
                    <p class="flex items-center gap-1 expiry-date">
                        <i class="fas fa-calendar-times text-gray-400"></i>
                        {{ company.expiry_date|date:"j F Y"|default:"غير محدد" }}
                        {% if company.expiry_date %}
                            {% if company.expiry_date|date:"Y-m-d" < now|date:"Y-m-d" %}
                                <span class="text-red-600 text-xs expiry-status">(منتهي)</span>
                            {% elif company.expiry_date|date:"Y-m-d" < now|date:"Y-m-d" %}
                                <span class="text-yellow-600 text-xs expiry-status">(ينتهي قريباً)</span>
                            {% endif %}
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات قاعدة البيانات -->
    <div class="bg-gray-50 p-6 rounded-xl border border-gray-200 card-section">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-700">معلومات قاعدة البيانات</h3>
            <div class="flex items-center gap-2">
                <button id="test-connection-btn" class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                    <i class="fas fa-database"></i> اختبار الاتصال
                </button>
                <button id="backup-db-btn" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                    <i class="fas fa-download"></i> نسخ احتياطي
                </button>
                <button id="restore-db-btn" class="text-orange-600 hover:text-orange-800 transition-colors duration-200">
                    <i class="fas fa-upload"></i> استعادة
                </button>
                <!-- تم تعطيل هذه الميزة مؤقتًا -->
                <button class="text-purple-600 hover:text-purple-800 transition-colors duration-200 disabled" disabled>
                    <i class="fas fa-calendar-alt"></i> جدولة النسخ الاحتياطي
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-1">
                <h4 class="text-sm font-medium text-gray-500">اسم قاعدة البيانات</h4>
                <div class="flex items-center gap-2">
                    <p class="text-lg font-medium database-name company-name">{{ company.database_name }}</p>
                    <button class="copy-btn" data-copy="{{ company.database_name }}" data-field="database_name" title="نسخ اسم قاعدة البيانات">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="space-y-1">
                <h4 class="text-sm font-medium text-gray-500">مضيف قاعدة البيانات</h4>
                <div class="flex items-center gap-2">
                    <p class="database-host">{{ company.database_host }}</p>
                    <button class="copy-btn" data-copy="{{ company.database_host }}" data-field="database_host" title="نسخ مضيف قاعدة البيانات">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="space-y-1">
                <h4 class="text-sm font-medium text-gray-500">مستخدم قاعدة البيانات</h4>
                <div class="flex items-center gap-2">
                    <p class="database-user">{{ company.database_user }}</p>
                    <button class="copy-btn" data-copy="{{ company.database_user }}" data-field="database_user" title="نسخ مستخدم قاعدة البيانات">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="space-y-1 password-field">
                <h4 class="text-sm font-medium text-gray-500">كلمة مرور قاعدة البيانات</h4>
                <div class="flex items-center gap-2">
                    <p id="db-password" class="password-hidden">••••••••</p>
                    <input type="hidden" id="db-password-value" value="{{ company.database_password }}">
                    <button class="copy-btn" data-copy="{{ company.database_password }}" data-field="database_password" title="نسخ كلمة المرور">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="password-toggle" title="إظهار/إخفاء كلمة المرور">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        </div>

        <div id="connection-result" class="mt-4 p-3 rounded-lg hidden"></div>

        <!-- قسم النسخ الاحتياطية -->
        <div id="backups-section" class="mt-6 pt-6 border-t border-gray-200 hidden">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-lg font-medium text-gray-700">النسخ الاحتياطية المتوفرة</h4>
                <div class="flex items-center gap-2">
                    <select id="backup-filter" class="text-sm border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">جميع النسخ</option>
                        <option value="manual">النسخ اليدوية</option>
                        <option value="auto">النسخ التلقائية</option>
                    </select>
                    <button id="refresh-backups-btn" class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="system-backup-logs min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead>
                        <tr class="bg-gradient-to-r from-blue-50 to-blue-100">
                            <th class="py-3 px-4 border-b text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tr-lg">اسم الملف</th>
                            <th class="py-3 px-4 border-b text-right text-xs font-medium text-blue-700 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="py-3 px-4 border-b text-right text-xs font-medium text-blue-700 uppercase tracking-wider">الحجم</th>
                            <th class="py-3 px-4 border-b text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tl-lg">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="backups-table-body">
                        <tr>
                            <td colspan="4" class="py-4 text-center text-gray-500">جاري تحميل النسخ الاحتياطية...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- زر رفع نسخة احتياطية -->
            <div class="mt-4">
                <button id="upload-backup-btn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-upload mr-2"></i> رفع نسخة احتياطية
                </button>
            </div>

            <div id="no-backups-message" class="py-4 text-center text-gray-500 hidden">
                لا توجد نسخ احتياطية متوفرة لهذه الشركة
            </div>
        </div>
    </div>

    <!-- سجل الأحداث -->
    <div class="bg-gray-50 p-6 rounded-xl border border-gray-200 card-section">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-700">سجل الأحداث</h3>
            <div class="flex items-center gap-2">
                <select id="log-filter" class="text-sm border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="all">جميع الأحداث</option>
                    <option value="create">إنشاء</option>
                    <option value="update">تحديث</option>
                    <option value="delete">حذف</option>
                    <option value="activate">تفعيل</option>
                    <option value="deactivate">تعطيل</option>
                </select>
            </div>
        </div>

        <div class="space-y-2">
            {% for log in logs %}
            <div class="p-3 bg-white rounded-lg border border-gray-200 flex flex-col md:flex-row justify-between md:items-center log-item {{ log.action }}">
                <div class="flex items-start gap-2">
                    <div class="mt-1">
                        {% if log.action == 'create' %}
                            <i class="fas fa-plus-circle text-green-500"></i>
                        {% elif log.action == 'update' %}
                            <i class="fas fa-edit text-blue-500"></i>
                        {% elif log.action == 'delete' %}
                            <i class="fas fa-trash-alt text-red-500"></i>
                        {% elif log.action == 'activate' %}
                            <i class="fas fa-check-circle text-green-500"></i>
                        {% elif log.action == 'deactivate' %}
                            <i class="fas fa-ban text-yellow-500"></i>
                        {% else %}
                            <i class="fas fa-info-circle text-gray-500"></i>
                        {% endif %}
                    </div>
                    <div>
                        <div class="font-medium">{{ log.user.username }}</div>
                        <div class="text-gray-600">{{ log.description }}</div>
                        {% if log.ip_address %}
                            <div class="text-xs text-gray-500">IP: {{ log.ip_address }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="text-sm text-gray-500 mt-2 md:mt-0 md:text-left">
                    <div>{{ log.created_at|date:"j F Y" }}</div>
                    <div>{{ log.created_at|date:"H:i:s" }}</div>
                </div>
            </div>
            {% empty %}
            <div class="p-4 text-center text-gray-500">لا توجد أحداث مسجلة لهذه الشركة</div>
            {% endfor %}
        </div>
    </div>

    <!-- خيارات متقدمة -->
    <div class="bg-red-50 p-6 rounded-xl border border-red-200 card-section">
        <h3 class="text-xl font-semibold text-red-800 mb-4">خيارات متقدمة</h3>

        <div class="flex flex-col md:flex-row gap-4">
            <button id="delete-company-btn"
                data-delete-url="{% url 'super_admin:delete_company' company.id %}"
                onclick="window.customizeDeleteModal(
                    this.getAttribute('data-delete-url'),
                    'هل أنت متأكد من حذف الشركة؟',
                    'سيتم حذف الشركة {{ company.name }} وجميع البيانات المرتبطة بها بشكل نهائي ولا يمكن التراجع عن هذه العملية.',
                    'fa-trash-alt',
                    'text-red-600 dark:text-red-400',
                    'تأكيد الحذف',
                    'bg-red-600 hover:bg-red-700'
                );"
                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-center transition-colors duration-200">
                <i class="fas fa-trash-alt ml-1"></i> حذف الشركة
            </button>
        </div>

        <p class="mt-4 text-red-700 text-sm flex items-start gap-2">
            <i class="fas fa-exclamation-triangle mt-1"></i>
            <span>تحذير: حذف الشركة سيؤدي إلى حذف جميع البيانات المرتبطة بها بشكل نهائي ولا يمكن التراجع عن هذه العملية.</span>
        </p>
    </div>
</div>

<!-- النوافذ المنبثقة -->
<!-- نافذة تأكيد تغيير الحالة -->
<div id="toggle-status-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold mb-4">
            {% if company.status == 'active' %}
                تأكيد إيقاف الشركة
            {% else %}
                تأكيد تفعيل الشركة
            {% endif %}
        </h3>
        <p class="mb-6">
            {% if company.status == 'active' %}
                هل أنت متأكد من رغبتك في إيقاف الشركة "{{ company.name }}"؟ لن يتمكن المستخدمون من الوصول إلى النظام بعد الإيقاف.
            {% else %}
                هل أنت متأكد من رغبتك في تفعيل الشركة "{{ company.name }}"؟ سيتمكن المستخدمون من الوصول إلى النظام بعد التفعيل.
            {% endif %}
        </p>
        <div class="flex justify-end gap-2">
            <button class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
            <a href="{% url 'super_admin:toggle_company_status' company.id %}" class="px-4 py-2 {% if company.status == 'active' %}bg-red-600 hover:bg-red-700{% else %}bg-green-600 hover:bg-green-700{% endif %} text-white rounded-lg transition-colors duration-200">
                {% if company.status == 'active' %}
                    تأكيد الإيقاف
                {% else %}
                    تأكيد التفعيل
                {% endif %}
            </a>
        </div>
    </div>
</div>



<!-- نافذة تصدير البيانات -->
<div id="export-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold text-blue-600 mb-4">تصدير بيانات الشركة</h3>
        <p class="mb-4">اختر صيغة تصدير بيانات الشركة "{{ company.name }}":</p>
        <div class="flex flex-col gap-3 mb-6">
            <button class="px-4 py-3 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-file-code"></i> تصدير بصيغة JSON
            </button>
            <button class="px-4 py-3 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-file-pdf"></i> تصدير بصيغة PDF
            </button>
            <button class="px-4 py-3 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-file-excel"></i> تصدير بصيغة Excel
            </button>
        </div>
        <div class="flex justify-end">
            <button class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
        </div>
    </div>
</div>

<!-- نافذة تعديل بيانات الشركة -->
<div id="edit-company-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold text-blue-600 mb-4">تعديل بيانات الشركة</h3>
        <form id="edit-company-form" class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="update_company" value="1">

            <div class="border-b border-gray-200 pb-4 mb-4">
                <h4 class="text-lg font-medium text-gray-700 mb-3">معلومات الشركة</h4>
                <div class="space-y-3">
                    <div>
                        <label for="edit-name" class="block text-sm font-medium text-gray-700 mb-1">اسم الشركة</label>
                        <input type="text" id="edit-name" name="name" value="{{ company.name }}" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="edit-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء الاشتراك</label>
                        <input type="date" id="edit-expiry-date" name="expiry_date" value="{{ company.expiry_date|date:'Y-m-d' }}" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="edit-status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <select id="edit-status" name="status" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="active" {% if company.status == 'active' %}selected{% endif %}>نشطة</option>
                            <option value="inactive" {% if company.status == 'inactive' %}selected{% endif %}>غير نشطة</option>
                            <option value="suspended" {% if company.status == 'suspended' %}selected{% endif %}>معلقة</option>
                            <option value="trial" {% if company.status == 'trial' %}selected{% endif %}>تجريبية</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="border-b border-gray-200 pb-4 mb-4">
                <h4 class="text-lg font-medium text-gray-700 mb-3">معلومات قاعدة البيانات</h4>
                <div class="space-y-3">
                    <div>
                        <label for="edit-db-name" class="block text-sm font-medium text-gray-700 mb-1">اسم قاعدة البيانات</label>
                        <input type="text" id="edit-db-name" name="database_name" value="{{ company.database_name }}" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="edit-db-user" class="block text-sm font-medium text-gray-700 mb-1">مستخدم قاعدة البيانات</label>
                        <input type="text" id="edit-db-user" name="database_user" value="{{ company.database_user }}" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="edit-db-password" class="block text-sm font-medium text-gray-700 mb-1">كلمة مرور قاعدة البيانات</label>
                        <div class="relative">
                            <input type="password" id="edit-db-password" name="database_password" value="********" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <button type="button" id="toggle-password" class="absolute inset-y-0 left-0 px-3 flex items-center text-gray-500 hover:text-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">اترك كلمة المرور كما هي إذا لم ترغب في تغييرها</p>
                    </div>
                    <div>
                        <label for="edit-db-host" class="block text-sm font-medium text-gray-700 mb-1">مضيف قاعدة البيانات</label>
                        <input type="text" id="edit-db-host" name="database_host" value="{{ company.database_host }}" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <button type="button" id="edit-test-connection" class="mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center">
                            <i class="fas fa-database mr-1"></i> اختبار الاتصال
                        </button>
                        <div id="edit-connection-result" class="mt-2 p-2 rounded-lg hidden"></div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end gap-2 pt-4">
                <button type="button" class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
                <button type="submit" id="save-company-btn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <span class="spinner mr-1 hidden"><i class="fas fa-circle-notch fa-spin"></i></span>
                    <span class="btn-text">حفظ التغييرات</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة إنشاء نسخة احتياطية -->
<div id="create-backup-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold text-green-600 mb-4">إنشاء نسخة احتياطية جديدة</h3>
        <form id="create-backup-form" class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="create_backup" value="1">

            <div>
                <label for="backup-description" class="block text-sm font-medium text-gray-700 mb-1">وصف النسخة الاحتياطية</label>
                <textarea id="backup-description" name="backup_description" rows="3" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="أدخل وصفًا للنسخة الاحتياطية (اختياري)"></textarea>
            </div>

            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">معلومات قاعدة البيانات</h4>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 w-32">اسم قاعدة البيانات:</span>
                        <span class="text-sm">{{ company.database_name }}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 w-32">مضيف قاعدة البيانات:</span>
                        <span class="text-sm">{{ company.database_host }}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 w-32">مستخدم قاعدة البيانات:</span>
                        <span class="text-sm">{{ company.database_user }}</span>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-start gap-2">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                    <p class="text-sm text-yellow-800">
                        سيتم إنشاء نسخة احتياطية كاملة لقاعدة البيانات. قد تستغرق هذه العملية بعض الوقت اعتمادًا على حجم قاعدة البيانات.
                    </p>
                </div>
            </div>

            <div class="flex justify-end gap-2 pt-4">
                <button type="button" class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
                <button type="submit" id="create-backup-btn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <span class="spinner mr-1 hidden"><i class="fas fa-circle-notch fa-spin"></i></span>
                    <span class="btn-text">إنشاء نسخة احتياطية</span>
                </button>
            </div>
        </form>
    </div>
</div>

{% include 'super_admin/restore_backup_modal.html' %}

<!-- نافذة حذف نسخة احتياطية -->
<div id="delete-backup-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold text-red-600 mb-4">حذف نسخة احتياطية</h3>
        <p class="mb-4">هل أنت متأكد من رغبتك في حذف النسخة الاحتياطية التالية؟</p>

        <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="space-y-2">
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">اسم الملف:</span>
                    <span class="text-sm backup-filename"></span>
                </div>
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">تاريخ الإنشاء:</span>
                    <span class="text-sm backup-created-at"></span>
                </div>
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">الوصف:</span>
                    <span class="text-sm backup-description"></span>
                </div>
            </div>
        </div>

        <div class="bg-red-50 p-4 rounded-lg mb-4">
            <div class="flex items-start gap-2">
                <i class="fas fa-exclamation-triangle text-red-600 mt-1"></i>
                <p class="text-sm text-red-800">
                    سيتم حذف ملف النسخة الاحتياطية بشكل نهائي ولا يمكن التراجع عن هذه العملية.
                </p>
            </div>
        </div>

        <form id="delete-backup-form" class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="delete_backup" value="1">
            <input type="hidden" id="delete-backup-filename" name="backup_filename" value="">

            <div class="flex justify-end gap-2">
                <button type="button" class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
                <button type="submit" id="delete-backup-btn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                    <span class="spinner mr-1 hidden"><i class="fas fa-circle-notch fa-spin"></i></span>
                    <span class="btn-text">تأكيد الحذف</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة رفع نسخة احتياطية -->
<div id="upload-backup-modal" class="modal">
    <div class="modal-content">
        <h3 class="text-xl font-bold text-blue-600 mb-4">رفع نسخة احتياطية</h3>
        <form id="upload-backup-form" class="space-y-4" enctype="multipart/form-data">
            {% csrf_token %}

            <div>
                <label for="backup-file" class="block text-sm font-medium text-gray-700 mb-1">ملف النسخة الاحتياطية</label>
                <input type="file" id="backup-file" name="backup_file" accept=".sql" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500 mt-1">يجب أن يكون الملف بامتداد .sql</p>
            </div>

            <div>
                <label for="upload-backup-description" class="block text-sm font-medium text-gray-700 mb-1">وصف النسخة الاحتياطية</label>
                <textarea id="upload-backup-description" name="backup_description" rows="3" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="أدخل وصفًا للنسخة الاحتياطية (اختياري)"></textarea>
            </div>

            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">معلومات قاعدة البيانات</h4>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 w-32">اسم قاعدة البيانات:</span>
                        <span class="text-sm">{{ company.database_name }}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 w-32">مضيف قاعدة البيانات:</span>
                        <span class="text-sm">{{ company.database_host }}</span>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-start gap-2">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                    <p class="text-sm text-yellow-800">
                        تأكد من أن ملف النسخة الاحتياطية متوافق مع قاعدة البيانات الحالية. قد يؤدي رفع ملف غير متوافق إلى مشاكل عند محاولة استعادته.
                    </p>
                </div>
            </div>

            <div class="flex justify-end gap-2 pt-4">
                <button type="button" class="modal-close px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200">إلغاء</button>
                <button type="submit" id="submit-upload-backup-btn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <span class="spinner mr-1 hidden"><i class="fas fa-circle-notch fa-spin"></i></span>
                    <span class="btn-text">رفع النسخة الاحتياطية</span>
                </button>
            </div>
        </form>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار رسائل النظام
        const toastMessages = document.querySelectorAll('#toast-container > div');
        toastMessages.forEach(function(toast) {
            setTimeout(function() {
                toast.classList.add('hidden');
            }, 5000);
        });

        // إغلاق رسائل النظام
        document.querySelectorAll('.close-toast').forEach(function(btn) {
            btn.addEventListener('click', function() {
                this.parentElement.classList.add('hidden');
            });
        });

        // نسخ النصوص
        document.querySelectorAll('.copy-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-copy');
                navigator.clipboard.writeText(textToCopy).then(function() {
                    // إظهار رسالة نجاح النسخ
                    const toast = document.createElement('div');
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">تم نسخ النص بنجاح</span>';
                    document.getElementById('toast-container').appendChild(toast);

                    setTimeout(function() {
                        toast.classList.add('hidden');
                    }, 3000);
                });
            });
        });

        // إظهار/إخفاء كلمة المرور
        document.querySelector('.password-toggle').addEventListener('click', function() {
            const passwordField = document.getElementById('db-password');
            const passwordValue = document.getElementById('db-password-value').value;
            const icon = this.querySelector('i');

            if (passwordField.classList.contains('password-hidden')) {
                // إظهار كلمة المرور
                passwordField.textContent = passwordValue;
                passwordField.classList.remove('password-hidden');
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');

                // إضافة تنسيق لكلمة المرور المعروضة
                passwordField.style.fontFamily = 'monospace';
                passwordField.style.backgroundColor = '#f3f4f6';
                passwordField.style.padding = '0.25rem 0.5rem';
                passwordField.style.borderRadius = '0.25rem';
                passwordField.style.border = '1px solid #e5e7eb';
                passwordField.style.color = '#1e40af';
            } else {
                // إخفاء كلمة المرور
                passwordField.textContent = '••••••••';
                passwordField.classList.add('password-hidden');
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');

                // إزالة التنسيق
                passwordField.style.fontFamily = '';
                passwordField.style.backgroundColor = '';
                passwordField.style.padding = '';
                passwordField.style.borderRadius = '';
                passwordField.style.border = '';
                passwordField.style.color = '';
            }
        });

        // تصفية سجل الأحداث
        document.getElementById('log-filter').addEventListener('change', function() {
            const filter = this.value;
            const logItems = document.querySelectorAll('.log-item');

            logItems.forEach(function(item) {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // اختبار الاتصال بقاعدة البيانات
        document.getElementById('test-connection-btn').addEventListener('click', function() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.classList.remove('hidden', 'bg-green-100', 'text-green-700', 'bg-red-100', 'text-red-700');
            resultDiv.classList.add('bg-blue-100', 'text-blue-700');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري اختبار الاتصال بقاعدة البيانات...';

            // إرسال طلب AJAX لاختبار الاتصال
            const formData = new FormData();
            formData.append('test_connection', '1');
            formData.append('database_name', '{{ company.database_name }}');
            formData.append('database_user', '{{ company.database_user }}');
            formData.append('database_password', '{{ company.database_password }}');
            formData.append('database_host', '{{ company.database_host }}');
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.classList.remove('bg-blue-100', 'text-blue-700');
                if (data.success) {
                    resultDiv.classList.add('bg-green-100', 'text-green-700');
                    resultDiv.innerHTML = '<i class="fas fa-check-circle ml-1"></i> ' + data.message;
                } else {
                    resultDiv.classList.add('bg-red-100', 'text-red-700');
                    resultDiv.innerHTML = '<i class="fas fa-times-circle ml-1"></i> ' + data.message;
                }
            })
            .catch(error => {
                resultDiv.classList.remove('bg-blue-100', 'text-blue-700');
                resultDiv.classList.add('bg-red-100', 'text-red-700');
                resultDiv.innerHTML = '<i class="fas fa-times-circle ml-1"></i> حدث خطأ أثناء الاتصال بالخادم';
                console.error('Error:', error);
            });
        });

        // إدارة النوافذ المنبثقة
        const modals = document.querySelectorAll('.modal');
        const modalCloseButtons = document.querySelectorAll('.modal-close');

        // فتح النوافذ المنبثقة
        document.getElementById('toggle-status-btn').addEventListener('click', function() {
            document.getElementById('toggle-status-modal').style.display = 'flex';
        });



        document.getElementById('export-btn').addEventListener('click', function() {
            document.getElementById('export-modal').style.display = 'flex';
        });

        document.getElementById('edit-company-btn').addEventListener('click', function() {
            document.getElementById('edit-company-modal').style.display = 'flex';
        });

        // إغلاق النوافذ المنبثقة
        modalCloseButtons.forEach(function(btn) {
            btn.addEventListener('click', function() {
                modals.forEach(function(modal) {
                    modal.style.display = 'none';
                });
            });
        });

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        modals.forEach(function(modal) {
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // إغلاق النوافذ المنبثقة عند الضغط على زر ESC
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                modals.forEach(function(modal) {
                    modal.style.display = 'none';
                });
            }
        });

        // تحميل النسخ الاحتياطية
        // الحصول على معرف الشركة من URL
        const company_id = window.location.pathname.split('/')[3];

        function loadBackups() {
            const backupsSection = document.getElementById('backups-section');
            const tableBody = document.getElementById('backups-table-body');
            const noBackupsMessage = document.getElementById('no-backups-message');

            // إظهار قسم النسخ الاحتياطية
            backupsSection.classList.remove('hidden');

            // إظهار رسالة التحميل
            tableBody.innerHTML = '<tr><td colspan="7" class="py-4 text-center text-gray-500">جاري تحميل النسخ الاحتياطية...</td></tr>';

            // إرسال طلب AJAX للحصول على قائمة النسخ الاحتياطية
            fetch(window.location.href + '?get_backups=1', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.backups && data.backups.length > 0) {
                    // إنشاء صفوف الجدول
                    let rows = '';
                    data.backups.forEach(function(backup) {
                        // تنسيق حجم الملف
                        const size = formatFileSize(backup.size);

                        // تحديد نوع النسخة الاحتياطية
                        let typeLabel = '';
                        if (backup.backup_type === 'manual') {
                            typeLabel = '<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">يدوية</span>';
                        } else if (backup.backup_type === 'auto') {
                            typeLabel = '<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">تلقائية</span>';
                        }

                        rows += `
                            <tr class="backup-row" data-type="${backup.backup_type}">
                                <td class="py-2 px-4 border-b">${backup.filename}</td>
                                <td class="py-2 px-4 border-b">${backup.created_at}</td>
                                <td class="py-2 px-4 border-b">${size}</td>
                                <td class="py-2 px-4 border-b">${typeLabel}</td>
                                <td class="py-2 px-4 border-b">${backup.created_by}</td>
                                <td class="py-2 px-4 border-b">${backup.description || '-'}</td>
                                <td class="py-2 px-4 border-b">
                                    <div class="flex items-center gap-2">
                                        <a href="/super-admin/companies/${company_id}/backup/download/${backup.filename}/" class="download-backup-btn text-green-600 hover:text-green-800 transition-colors duration-200" title="تنزيل النسخة الاحتياطية">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button class="restore-backup-btn text-orange-600 hover:text-orange-800 transition-colors duration-200"
                                                data-filename="${backup.filename}"
                                                data-created-at="${backup.created_at}"
                                                data-size="${size}"
                                                data-description="${backup.description || '-'}"
                                                title="استعادة النسخة الاحتياطية">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                        <button class="delete-backup-btn text-red-600 hover:text-red-800 transition-colors duration-200"
                                                data-filename="${backup.filename}"
                                                data-created-at="${backup.created_at}"
                                                data-description="${backup.description || '-'}"
                                                title="حذف النسخة الاحتياطية">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    tableBody.innerHTML = rows;
                    noBackupsMessage.classList.add('hidden');

                    // إضافة مستمعي الأحداث لأزرار الاستعادة والحذف
                    document.querySelectorAll('.restore-backup-btn').forEach(function(btn) {
                        btn.addEventListener('click', function() {
                            openRestoreBackupModal(
                                this.getAttribute('data-filename'),
                                this.getAttribute('data-created-at'),
                                this.getAttribute('data-size'),
                                this.getAttribute('data-description')
                            );
                        });
                    });

                    document.querySelectorAll('.delete-backup-btn').forEach(function(btn) {
                        btn.addEventListener('click', function() {
                            openDeleteBackupModal(
                                this.getAttribute('data-filename'),
                                this.getAttribute('data-created-at'),
                                this.getAttribute('data-description')
                            );
                        });
                    });
                } else {
                    // لا توجد نسخ احتياطية
                    tableBody.innerHTML = '';
                    noBackupsMessage.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = '<tr><td colspan="7" class="py-4 text-center text-red-500">حدث خطأ أثناء تحميل النسخ الاحتياطية</td></tr>';
            });
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';

            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // فتح نافذة إنشاء نسخة احتياطية
        function openCreateBackupModal() {
            document.getElementById('create-backup-modal').style.display = 'flex';
        }

        // تم نقل وظيفة فتح نافذة استعادة النسخة الاحتياطية إلى ملف restore_backup_modal.html

        // فتح نافذة حذف نسخة احتياطية
        function openDeleteBackupModal(filename, createdAt, description) {
            const modal = document.getElementById('delete-backup-modal');

            // تعيين معلومات النسخة الاحتياطية
            modal.querySelector('.backup-filename').textContent = filename;
            modal.querySelector('.backup-created-at').textContent = createdAt;
            modal.querySelector('.backup-description').textContent = description;

            // تعيين اسم الملف في النموذج
            document.getElementById('delete-backup-filename').value = filename;

            // إظهار النافذة
            modal.style.display = 'flex';
        }

        // تصفية النسخ الاحتياطية
        document.getElementById('backup-filter').addEventListener('change', function() {
            const filter = this.value;
            const backupRows = document.querySelectorAll('.backup-row');

            backupRows.forEach(function(row) {
                if (filter === 'all' || row.getAttribute('data-type') === filter) {
                    row.style.display = 'table-row';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // تحديث قائمة النسخ الاحتياطية
        document.getElementById('refresh-backups-btn').addEventListener('click', loadBackups);

        // فتح نافذة إنشاء نسخة احتياطية
        document.getElementById('backup-db-btn').addEventListener('click', function() {
            openCreateBackupModal();
        });

        // فتح قسم النسخ الاحتياطية
        document.getElementById('restore-db-btn').addEventListener('click', function() {
            loadBackups();
        });

        // فتح نافذة رفع نسخة احتياطية
        document.getElementById('upload-backup-btn').addEventListener('click', function() {
            document.getElementById('upload-backup-modal').style.display = 'flex';
        });

        // إرسال نموذج رفع نسخة احتياطية
        document.getElementById('upload-backup-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // التحقق من وجود ملف
            const fileInput = document.getElementById('backup-file');
            if (!fileInput.files.length) {
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">يرجى اختيار ملف للرفع</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                return;
            }

            // إظهار حالة التحميل
            const submitBtn = document.getElementById('submit-upload-backup-btn');
            const spinner = submitBtn.querySelector('.spinner');
            const btnText = submitBtn.querySelector('.btn-text');

            spinner.classList.remove('hidden');
            btnText.textContent = 'جاري رفع النسخة الاحتياطية...';
            submitBtn.disabled = true;

            // إرسال طلب AJAX لرفع نسخة احتياطية
            const formData = new FormData(this);

            fetch(`/super-admin/companies/${company_id}/backup/upload/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'رفع النسخة الاحتياطية';
                submitBtn.disabled = false;

                // إظهار رسالة النتيجة
                const toast = document.createElement('div');
                if (data.success) {
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';

                    // إغلاق النافذة المنبثقة
                    document.getElementById('upload-backup-modal').style.display = 'none';

                    // إعادة تعيين النموذج
                    this.reset();

                    // تحديث قائمة النسخ الاحتياطية
                    loadBackups();
                } else {
                    toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';
                }

                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);
            })
            .catch(error => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'رفع النسخة الاحتياطية';
                submitBtn.disabled = false;

                // إظهار رسالة الخطأ
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">حدث خطأ أثناء الاتصال بالخادم</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                console.error('Error:', error);
            });
        });

        // إرسال نموذج إنشاء نسخة احتياطية
        document.getElementById('create-backup-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // إظهار حالة التحميل
            const submitBtn = document.getElementById('create-backup-btn');
            const spinner = submitBtn.querySelector('.spinner');
            const btnText = submitBtn.querySelector('.btn-text');

            spinner.classList.remove('hidden');
            btnText.textContent = 'جاري إنشاء النسخة الاحتياطية...';
            submitBtn.disabled = true;

            // إظهار شريط التقدم
            const progressContainer = document.getElementById('backup-progress-container');
            const progressBar = document.getElementById('backup-progress-bar');
            const progressPercentage = document.getElementById('backup-progress-percentage');
            const progressStatus = document.getElementById('backup-progress-status');

            progressContainer.classList.remove('hidden');
            progressBar.style.width = '0%';
            progressPercentage.textContent = '0%';
            progressStatus.textContent = 'جاري تحضير قاعدة البيانات...';

            // بدء محاكاة تقدم العملية
            let progress = 0;
            const progressInterval = setInterval(function() {
                // زيادة التقدم بشكل عشوائي لمحاكاة العملية الحقيقية
                progress += Math.random() * 5;
                if (progress > 95) {
                    progress = 95; // نتوقف عند 95% حتى تنتهي العملية فعلياً
                    clearInterval(progressInterval);
                }

                // تحديث شريط التقدم
                const progressValue = Math.min(Math.round(progress), 95);
                progressBar.style.width = progressValue + '%';
                progressPercentage.textContent = progressValue + '%';

                // تحديث حالة التقدم
                if (progressValue < 20) {
                    progressStatus.textContent = 'جاري تحضير قاعدة البيانات...';
                } else if (progressValue < 40) {
                    progressStatus.textContent = 'جاري استخراج البيانات...';
                } else if (progressValue < 60) {
                    progressStatus.textContent = 'جاري ضغط البيانات...';
                } else if (progressValue < 80) {
                    progressStatus.textContent = 'جاري حفظ النسخة الاحتياطية...';
                } else {
                    progressStatus.textContent = 'جاري إنهاء العملية...';
                }
            }, 500);

            // إرسال طلب AJAX لإنشاء نسخة احتياطية
            const formData = new FormData(this);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // إكمال شريط التقدم إلى 100%
                progressBar.style.width = '100%';
                progressPercentage.textContent = '100%';
                progressStatus.textContent = 'تم إنشاء النسخة الاحتياطية بنجاح';

                // إيقاف محاكاة التقدم
                clearInterval(progressInterval);

                // إعادة حالة الزر بعد فترة قصيرة
                setTimeout(function() {
                    spinner.classList.add('hidden');
                    btnText.textContent = 'إنشاء نسخة احتياطية';
                    submitBtn.disabled = false;

                    // إخفاء شريط التقدم بعد فترة
                    setTimeout(function() {
                        progressContainer.classList.add('hidden');
                    }, 1000);
                }, 1000);

                // إظهار رسالة النتيجة
                const toast = document.createElement('div');
                if (data.success) {
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';

                    // إغلاق النافذة المنبثقة
                    document.getElementById('create-backup-modal').style.display = 'none';

                    // إعادة تعيين النموذج
                    this.reset();

                    // تحديث قائمة النسخ الاحتياطية
                    loadBackups();
                } else {
                    toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';
                }

                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);
            })
            .catch(error => {
                // إيقاف محاكاة التقدم
                clearInterval(progressInterval);

                // تحديث شريط التقدم ليظهر الخطأ
                progressBar.style.width = '100%';
                progressBar.classList.remove('bg-green-600');
                progressBar.classList.add('bg-red-600');
                progressPercentage.textContent = 'خطأ';
                progressStatus.textContent = 'حدث خطأ أثناء إنشاء النسخة الاحتياطية';

                // إعادة حالة الزر بعد فترة قصيرة
                setTimeout(function() {
                    spinner.classList.add('hidden');
                    btnText.textContent = 'إنشاء نسخة احتياطية';
                    submitBtn.disabled = false;

                    // إعادة تعيين شريط التقدم وإخفاؤه بعد فترة
                    setTimeout(function() {
                        progressBar.classList.remove('bg-red-600');
                        progressBar.classList.add('bg-green-600');
                        progressContainer.classList.add('hidden');
                    }, 3000);
                }, 1000);

                // إظهار رسالة الخطأ
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">حدث خطأ أثناء الاتصال بالخادم</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                console.error('Error:', error);
            });
        });

        // إرسال نموذج استعادة نسخة احتياطية
        document.getElementById('restore-backup-form').addEventListener('submit', function(event) {
            event.preventDefault();
            console.log('تم تقديم نموذج استعادة النسخة الاحتياطية');

            // إظهار حالة التحميل
            const submitBtn = document.getElementById('restore-backup-btn');
            if (!submitBtn) {
                console.error('لم يتم العثور على زر الاستعادة!');
                return;
            }

            // تغيير نص الزر وتعطيله
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin mr-2"></i> جاري استعادة قاعدة البيانات...';
            submitBtn.disabled = true;

            // إرسال طلب AJAX لاستعادة نسخة احتياطية
            const formData = new FormData(this);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // إعادة حالة الزر
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // إظهار رسالة النتيجة
                const toast = document.createElement('div');
                if (data.success) {
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';

                    // إغلاق النافذة المنبثقة
                    document.getElementById('restore-backup-modal').style.display = 'none';
                } else {
                    toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';
                }

                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);
            })
            .catch(error => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'استعادة قاعدة البيانات';
                submitBtn.disabled = false;

                // إظهار رسالة الخطأ
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">حدث خطأ أثناء الاتصال بالخادم</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                console.error('Error:', error);
            });
        });

        // إرسال نموذج حذف نسخة احتياطية
        document.getElementById('delete-backup-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // إظهار حالة التحميل
            const submitBtn = document.getElementById('delete-backup-btn');
            const spinner = submitBtn.querySelector('.spinner');
            const btnText = submitBtn.querySelector('.btn-text');

            spinner.classList.remove('hidden');
            btnText.textContent = 'جاري الحذف...';
            submitBtn.disabled = true;

            // إرسال طلب AJAX لحذف نسخة احتياطية
            const formData = new FormData(this);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'تأكيد الحذف';
                submitBtn.disabled = false;

                // إظهار رسالة النتيجة
                const toast = document.createElement('div');
                if (data.success) {
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';

                    // إغلاق النافذة المنبثقة
                    document.getElementById('delete-backup-modal').style.display = 'none';

                    // تحديث قائمة النسخ الاحتياطية
                    loadBackups();
                } else {
                    toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';
                }

                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);
            })
            .catch(error => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'تأكيد الحذف';
                submitBtn.disabled = false;

                // إظهار رسالة الخطأ
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">حدث خطأ أثناء الاتصال بالخادم</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                console.error('Error:', error);
            });
        });

        // إظهار/إخفاء كلمة المرور في نموذج التعديل
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordField = document.getElementById('edit-db-password');
            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // اختبار الاتصال بقاعدة البيانات في نموذج التعديل
        document.getElementById('edit-test-connection').addEventListener('click', function() {
            const resultDiv = document.getElementById('edit-connection-result');
            const dbName = document.getElementById('edit-db-name').value;
            const dbUser = document.getElementById('edit-db-user').value;
            const dbPassword = document.getElementById('edit-db-password').value;
            const dbHost = document.getElementById('edit-db-host').value;

            // إظهار حالة الاختبار
            resultDiv.classList.remove('hidden', 'bg-green-100', 'text-green-700', 'bg-red-100', 'text-red-700');
            resultDiv.classList.add('bg-blue-100', 'text-blue-700');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري اختبار الاتصال بقاعدة البيانات...';

            // إرسال طلب AJAX لاختبار الاتصال
            const formData = new FormData();
            formData.append('test_connection', '1');
            formData.append('database_name', dbName);
            formData.append('database_user', dbUser);
            formData.append('database_password', dbPassword);
            formData.append('database_host', dbHost);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.classList.remove('bg-blue-100', 'text-blue-700');
                if (data.success) {
                    resultDiv.classList.add('bg-green-100', 'text-green-700');
                    resultDiv.innerHTML = '<i class="fas fa-check-circle ml-1"></i> ' + data.message;
                } else {
                    resultDiv.classList.add('bg-red-100', 'text-red-700');
                    resultDiv.innerHTML = '<i class="fas fa-times-circle ml-1"></i> ' + data.message;
                }
            })
            .catch(error => {
                resultDiv.classList.remove('bg-blue-100', 'text-blue-700');
                resultDiv.classList.add('bg-red-100', 'text-red-700');
                resultDiv.innerHTML = '<i class="fas fa-times-circle ml-1"></i> حدث خطأ أثناء الاتصال بالخادم';
                console.error('Error:', error);
            });
        });

        // إرسال نموذج تعديل الشركة
        document.getElementById('edit-company-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // إظهار حالة التحميل
            const saveBtn = document.getElementById('save-company-btn');
            const spinner = saveBtn.querySelector('.spinner');
            const btnText = saveBtn.querySelector('.btn-text');

            spinner.classList.remove('hidden');
            btnText.textContent = 'جاري الحفظ...';
            saveBtn.disabled = true;

            // إرسال طلب AJAX لتحديث البيانات
            const formData = new FormData(this);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'حفظ التغييرات';
                saveBtn.disabled = false;

                // إظهار رسالة النتيجة
                const toast = document.createElement('div');
                if (data.success) {
                    toast.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';

                    // إغلاق النافذة المنبثقة
                    document.getElementById('edit-company-modal').style.display = 'none';

                    // تحديث البيانات المعروضة في الصفحة بدون إعادة تحميلها
                    updateDisplayedCompanyData();
                } else {
                    toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                    toast.innerHTML = '<span class="block sm:inline">' + data.message + '</span>';
                }

                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);
            })
            .catch(error => {
                // إعادة حالة الزر
                spinner.classList.add('hidden');
                btnText.textContent = 'حفظ التغييرات';
                saveBtn.disabled = false;

                // إظهار رسالة الخطأ
                const toast = document.createElement('div');
                toast.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md relative mb-4 animate-fade-in-down';
                toast.innerHTML = '<span class="block sm:inline">حدث خطأ أثناء الاتصال بالخادم</span>';
                document.getElementById('toast-container').appendChild(toast);

                setTimeout(function() {
                    toast.classList.add('hidden');
                }, 5000);

                console.error('Error:', error);
            });
        });

        // تحديث البيانات المعروضة في الصفحة بدون إعادة تحميلها
        function updateDisplayedCompanyData() {
            // تحديث اسم الشركة
            const companyName = document.getElementById('edit-name').value;
            document.querySelectorAll('.company-name').forEach(function(element) {
                element.textContent = companyName;
            });

            // تحديث الحالة
            const status = document.getElementById('edit-status').value;
            const statusBadge = document.querySelector('.status-badge');

            if (status === 'active') {
                statusBadge.className = 'status-badge bg-green-100 text-green-800';
                statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> نشطة';
            } else if (status === 'inactive') {
                statusBadge.className = 'status-badge bg-red-100 text-red-800';
                statusBadge.innerHTML = '<i class="fas fa-times-circle"></i> غير نشطة';
            } else if (status === 'suspended') {
                statusBadge.className = 'status-badge bg-yellow-100 text-yellow-800';
                statusBadge.innerHTML = '<i class="fas fa-exclamation-circle"></i> معلقة';
            } else if (status === 'trial') {
                statusBadge.className = 'status-badge bg-blue-100 text-blue-800';
                statusBadge.innerHTML = '<i class="fas fa-flask"></i> تجريبية';
            }

            // تحديث تاريخ انتهاء الاشتراك
            const expiryDate = document.getElementById('edit-expiry-date').value;
            if (expiryDate) {
                const formattedDate = new Date(expiryDate).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long', day: 'numeric' });
                const expiryElement = document.querySelector('.expiry-date');

                // حفظ عنصر حالة الانتهاء إذا كان موجودًا
                const expiryStatus = document.querySelector('.expiry-status');
                const statusHtml = expiryStatus ? expiryStatus.outerHTML : '';

                expiryElement.innerHTML =
                    '<i class="fas fa-calendar-times text-gray-400"></i> ' + formattedDate + ' ' + statusHtml;
            }

            // تحديث بيانات قاعدة البيانات
            document.querySelector('p.database-name').textContent = document.getElementById('edit-db-name').value;
            document.querySelector('p.database-user').textContent = document.getElementById('edit-db-user').value;
            document.querySelector('p.database-host').textContent = document.getElementById('edit-db-host').value;

            // تحديث أزرار النسخ
            document.querySelectorAll('.copy-btn').forEach(function(btn) {
                const field = btn.getAttribute('data-field');
                if (field === 'database_name') {
                    btn.setAttribute('data-copy', document.getElementById('edit-db-name').value);
                } else if (field === 'database_user') {
                    btn.setAttribute('data-copy', document.getElementById('edit-db-user').value);
                } else if (field === 'database_host') {
                    btn.setAttribute('data-copy', document.getElementById('edit-db-host').value);
                } else if (field === 'database_password') {
                    const password = document.getElementById('edit-db-password').value;
                    if (password !== '********') {
                        btn.setAttribute('data-copy', password);
                        document.getElementById('db-password-value').value = password;
                    }
                }
            });
        }
    });
</script>

<script src="{% static 'js/system_backup_logs.js' %}"></script>
<script src="{% static 'js/company_backup_table.js' %}"></script>
<script src="{% static 'js/system_logs_table_fix.js' %}"></script>
<script src="{% static 'js/backup_logs_specific_fix.js' %}"></script>
{% endblock %}
