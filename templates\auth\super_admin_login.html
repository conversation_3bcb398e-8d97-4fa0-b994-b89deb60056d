<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول المسؤول الأعلى | استقدامي</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'admin-primary': '#0F2557',
                        'admin-secondary': '#1E5F74',
                        'admin-accent': '#4B8F8C',
                        'admin-light': '#E5F1F1',
                        'admin-dark': '#0A1A3F',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            },
            darkMode: 'class',
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0F2557 0%, #1E5F74 50%, #4B8F8C 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .admin-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .admin-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }
        
        .floating-crown {
            animation: floating 4s ease-in-out infinite;
        }
        
        .pulse-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md" x-data="{ 
        showPassword: false,
        loading: false,
        username: '{{ username|default:'' }}',
        password: ''
    }">
        
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <div class="floating-crown">
                <div class="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-2xl">
                    <i class="fas fa-crown text-3xl text-white"></i>
                </div>
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">منصة استقدامي</h1>
            <div class="pulse-badge">
                <span class="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-1 rounded-full text-sm font-bold">
                    <i class="fas fa-shield-alt ml-1"></i>
                    المسؤول الأعلى
                </span>
            </div>
        </div>

        <!-- Login Form -->
        <div class="glass-effect rounded-3xl p-8 shadow-2xl">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-white mb-2">تسجيل الدخول الآمن</h2>
                <p class="text-admin-light">لوحة تحكم المسؤول الأعلى</p>
            </div>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Username -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                        <i class="fas fa-user-shield text-admin-light"></i>
                    </div>
                    <input 
                        type="text" 
                        name="username"
                        x-model="username"
                        placeholder="اسم المستخدم"
                        class="admin-input w-full pr-12 pl-4 py-4 bg-white/20 border border-white/30 rounded-xl text-white placeholder-admin-light focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent transition-all duration-300"
                        required
                    >
                </div>
                
                <!-- Password -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                        <i class="fas fa-key text-admin-light"></i>
                    </div>
                    <input 
                        :type="showPassword ? 'text' : 'password'"
                        name="password"
                        x-model="password"
                        placeholder="كلمة المرور"
                        class="admin-input w-full pr-12 pl-12 py-4 bg-white/20 border border-white/30 rounded-xl text-white placeholder-admin-light focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent transition-all duration-300"
                        required
                    >
                    <button 
                        type="button"
                        @click="showPassword = !showPassword"
                        class="absolute inset-y-0 left-0 pl-4 flex items-center"
                    >
                        <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-admin-light hover:text-white transition-colors duration-300"></i>
                    </button>
                </div>
                
                <!-- Login Button -->
                <button 
                    type="submit"
                    :disabled="!username || !password || loading"
                    class="admin-btn w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                    @click="loading = true"
                >
                    <span x-show="!loading" class="flex items-center justify-center">
                        <i class="fas fa-sign-in-alt ml-2"></i>
                        دخول لوحة التحكم
                    </span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <i class="fas fa-spinner fa-spin ml-2"></i>
                        جاري التحقق من البيانات...
                    </span>
                </button>
            </form>
            
            <!-- Error Messages -->
            {% if messages %}
                <div class="mt-6">
                    {% for message in messages %}
                        <div class="bg-red-500/20 border border-red-400/50 text-red-100 px-4 py-3 rounded-xl mb-2 backdrop-blur-sm">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <!-- Security Notice -->
            <div class="mt-6 p-4 bg-admin-accent/20 border border-admin-accent/50 rounded-xl backdrop-blur-sm">
                <div class="flex items-center text-admin-light text-sm">
                    <i class="fas fa-shield-alt ml-2 text-yellow-400"></i>
                    <span>هذه منطقة محمية. يتم تسجيل جميع محاولات الدخول.</span>
                </div>
            </div>
            
            <!-- Footer Links -->
            <div class="mt-6 text-center">
                <a href="{% url 'company:login' %}" class="text-admin-light hover:text-white text-sm transition-colors duration-300">
                    <i class="fas fa-building ml-1"></i>
                    تسجيل دخول الشركات
                </a>
            </div>
        </div>
        
        <!-- Copyright -->
        <div class="text-center mt-8">
            <p class="text-admin-light text-sm">
                © {{ now.year }} منصة استقدامي - نظام إدارة العمالة المتقدم
            </p>
            <p class="text-white/70 text-xs mt-1">
                <i class="fas fa-lock ml-1"></i>
                محمي بأعلى معايير الأمان
            </p>
        </div>
    </div>
</body>
</html>
