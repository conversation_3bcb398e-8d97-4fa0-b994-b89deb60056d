/**
 * منصة استقدامي - ملف JavaScript مخصص للشركات
 * يحتوي على وظائف تفاعلية خاصة بواجهة الشركات
 */

// إعدادات عامة
const CompanyApp = {
    // إعدادات SweetAlert2
    swalConfig: {
        confirmButtonColor: '#7c3aed',
        cancelButtonColor: '#ef4444',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-secondary'
        }
    },
    
    // إعدادات Toast
    toastConfig: {
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    }
};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    initializeDarkMode();
    initializeTooltips();
    initializeAnimations();
    initializeFormValidation();
    initializeTableFeatures();
    initializeNotifications();
    
    console.log('🚀 تم تحميل تطبيق الشركة بنجاح');
}

/**
 * إدارة الوضع الليلي
 */
function initializeDarkMode() {
    const darkMode = localStorage.getItem('darkMode') === 'true';
    if (darkMode) {
        document.documentElement.classList.add('dark');
    }
}

function toggleDarkMode() {
    const html = document.documentElement;
    html.classList.toggle('dark');
    localStorage.setItem('darkMode', html.classList.contains('dark'));
    
    // إشعار بالتغيير
    const isDark = html.classList.contains('dark');
    showToast(isDark ? 'تم تفعيل الوضع الليلي' : 'تم تفعيل الوضع النهاري', 'info');
}

/**
 * إعدادات SweetAlert2
 */
const Toast = Swal.mixin(CompanyApp.toastConfig);

function showToast(message, type = 'success') {
    Toast.fire({
        icon: type,
        title: message
    });
}

function showSuccess(message) {
    showToast(message, 'success');
}

function showError(message) {
    showToast(message, 'error');
}

function showWarning(message) {
    showToast(message, 'warning');
}

function showInfo(message) {
    showToast(message, 'info');
}

function confirmAction(title, text, confirmButtonText = 'نعم', cancelButtonText = 'إلغاء') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        ...CompanyApp.swalConfig
    });
}

function showLoading(title = 'جاري التحميل...') {
    Swal.fire({
        title: title,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });
}

function closeLoading() {
    Swal.close();
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const element = event.target;
    const title = element.getAttribute('title');
    if (!title) return;
    
    // إنشاء tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
    tooltip.textContent = title;
    tooltip.id = 'tooltip';
    
    document.body.appendChild(tooltip);
    
    // تحديد الموضع
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    
    // إخفاء title الأصلي
    element.setAttribute('data-original-title', title);
    element.removeAttribute('title');
}

function hideTooltip(event) {
    const element = event.target;
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.remove();
    }
    
    // استعادة title الأصلي
    const originalTitle = element.getAttribute('data-original-title');
    if (originalTitle) {
        element.setAttribute('title', originalTitle);
        element.removeAttribute('data-original-title');
    }
}

/**
 * تهيئة الحركات
 */
function initializeAnimations() {
    // تحريك العناصر عند الظهور
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // مراقبة البطاقات
    document.querySelectorAll('.card-hover').forEach(card => {
        observer.observe(card);
    });
}

/**
 * تحقق من صحة النماذج
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', validateForm);
    });
}

function validateForm(event) {
    const form = event.target;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('border-red-500', 'ring-red-500');
            input.classList.remove('border-gray-300');
            isValid = false;
            
            // إضافة رسالة خطأ
            let errorMsg = input.nextElementSibling;
            if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                errorMsg = document.createElement('p');
                errorMsg.className = 'error-message text-red-500 text-sm mt-1';
                errorMsg.textContent = 'هذا الحقل مطلوب';
                input.parentNode.insertBefore(errorMsg, input.nextSibling);
            }
        } else {
            input.classList.remove('border-red-500', 'ring-red-500');
            input.classList.add('border-gray-300');
            
            // إزالة رسالة الخطأ
            const errorMsg = input.nextElementSibling;
            if (errorMsg && errorMsg.classList.contains('error-message')) {
                errorMsg.remove();
            }
        }
    });
    
    if (!isValid) {
        event.preventDefault();
        showError('يرجى ملء جميع الحقول المطلوبة');
    }
    
    return isValid;
}

/**
 * ميزات الجداول
 */
function initializeTableFeatures() {
    // البحث في الجداول
    const searchInputs = document.querySelectorAll('[id$="-search"]');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const tableId = this.id.replace('-search', '');
            searchTable(this.value, tableId);
        });
    });
}

function searchTable(searchTerm, tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(term)) {
            row.style.display = '';
            row.classList.add('animate-fade-in');
        } else {
            row.style.display = 'none';
            row.classList.remove('animate-fade-in');
        }
    });
}

function sortTable(columnIndex, tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // تحديد اتجاه الترتيب
    const header = table.querySelectorAll('th')[columnIndex];
    const isAscending = !header.classList.contains('sort-desc');
    
    // إزالة كلاسات الترتيب من جميع الرؤوس
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('i');
        if (icon) icon.className = 'fas fa-sort text-gray-400 mr-2';
    });
    
    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // محاولة ترتيب رقمي
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // ترتيب نصي
        return isAscending ? 
            aText.localeCompare(bText, 'ar') : 
            bText.localeCompare(aText, 'ar');
    });
    
    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
    
    // تحديث أيقونة الترتيب
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    const icon = header.querySelector('i');
    if (icon) {
        icon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} text-purple-600 mr-2`;
    }
}

/**
 * تصدير البيانات
 */
function exportToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) {
        showError('لم يتم العثور على الجدول');
        return;
    }
    
    const rows = table.querySelectorAll('tr:not([style*="display: none"])');
    let csv = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = Array.from(cols).map(col => {
            // تنظيف النص من HTML
            const text = col.textContent.trim();
            // إضافة علامات اقتباس إذا كان النص يحتوي على فواصل
            return text.includes(',') ? `"${text}"` : text;
        });
        csv.push(rowData.join(','));
    });
    
    const csvContent = '\uFEFF' + csv.join('\n'); // إضافة BOM للدعم العربي
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    
    showSuccess('تم تصدير البيانات بنجاح');
}

/**
 * إدارة الإشعارات
 */
function initializeNotifications() {
    // إخفاء الإشعارات تلقائياً
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * وظائف مساعدة
 */
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('تم النسخ بنجاح');
    }).catch(() => {
        showError('فشل في النسخ');
    });
}

function printPage() {
    window.print();
}

function goBack() {
    window.history.back();
}

function refreshPage() {
    window.location.reload();
}

// تصدير الوظائف للاستخدام العام
window.CompanyApp = {
    ...CompanyApp,
    toggleDarkMode,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    confirmAction,
    showLoading,
    closeLoading,
    searchTable,
    sortTable,
    exportToCSV,
    formatNumber,
    formatDate,
    formatCurrency,
    copyToClipboard,
    printPage,
    goBack,
    refreshPage
};
