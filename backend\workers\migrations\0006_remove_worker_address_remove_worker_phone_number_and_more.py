# Generated by Django 5.2 on 2025-04-10 15:12

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workers', '0005_recruitmentcompany_worker_recruitment_company'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='worker',
            name='address',
        ),
        migrations.RemoveField(
            model_name='worker',
            name='phone_number',
        ),
        migrations.RemoveField(
            model_name='worker',
            name='skills',
        ),
        migrations.AlterField(
            model_name='worker',
            name='visa_expiry',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ انتهاء التأشيرة / الاستيكر'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='visa_number',
            field=models.CharField(default='V0000001', max_length=20, unique=True, verbose_name='رقم الحالة / سمة الدخول'),
        ),
    ]
