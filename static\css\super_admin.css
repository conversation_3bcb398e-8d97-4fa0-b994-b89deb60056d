/* 
 * ملف CSS مخصص للمسؤول الأعلى
 * يحتوي على أنماط خاصة بواجهة المسؤول الأعلى
 * مختلفة تماماً عن أنماط واجهة الشركات
 */

:root {
    --admin-primary: #0F2557;
    --admin-secondary: #1E5F74;
    --admin-accent: #4B8F8C;
    --admin-light: #E5F1F1;
    --admin-dark: #0A1A3F;
    --admin-text: #FFFFFF;
    --admin-text-secondary: #E5F1F1;
    --admin-border: rgba(255, 255, 255, 0.1);
    --admin-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* تخصيص الخلفية والألوان للمسؤول الأعلى */
.super-admin-bg {
    background-color: var(--admin-primary);
}

.super-admin-bg-secondary {
    background-color: var(--admin-secondary);
}

.super-admin-bg-accent {
    background-color: var(--admin-accent);
}

.super-admin-text {
    color: var(--admin-text);
}

.super-admin-text-accent {
    color: var(--admin-accent);
}

/* تخصيص الأزرار للمسؤول الأعلى */
.super-admin-btn {
    background-color: var(--admin-secondary);
    color: var(--admin-text);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.super-admin-btn:hover {
    background-color: var(--admin-accent);
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow);
}

.super-admin-btn-accent {
    background-color: var(--admin-accent);
    color: var(--admin-text);
}

.super-admin-btn-accent:hover {
    background-color: var(--admin-primary);
}

/* تخصيص البطاقات للمسؤول الأعلى */
.super-admin-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--admin-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.super-admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.super-admin-card-header {
    background-color: var(--admin-primary);
    color: var(--admin-text);
    padding: 1rem;
    font-weight: bold;
}

.super-admin-card-body {
    padding: 1rem;
}

/* تخصيص الجداول للمسؤول الأعلى */
.super-admin-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.super-admin-table th {
    background-color: var(--admin-primary);
    color: var(--admin-text);
    padding: 0.75rem 1rem;
    text-align: right;
    font-weight: 600;
}

.super-admin-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.super-admin-table tr:hover {
    background-color: var(--admin-light);
}

/* تخصيص الشريط الجانبي للمسؤول الأعلى */
.super-admin-sidebar {
    background-color: var(--admin-primary);
    color: var(--admin-text);
    width: 16rem;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 40;
    transition: all 0.3s ease;
}

.super-admin-sidebar-collapsed {
    width: 4rem;
}

.super-admin-sidebar-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--admin-border);
}

.super-admin-sidebar-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--admin-text-secondary);
    transition: all 0.3s ease;
}

.super-admin-sidebar-item:hover {
    background-color: var(--admin-secondary);
    color: var(--admin-text);
}

.super-admin-sidebar-item.active {
    background-color: var(--admin-accent);
    color: var(--admin-text);
    font-weight: bold;
}

.super-admin-sidebar-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
}

/* تخصيص النماذج للمسؤول الأعلى */
.super-admin-form-group {
    margin-bottom: 1rem;
}

.super-admin-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #4a5568;
}

.super-admin-form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.super-admin-form-input:focus {
    outline: none;
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 3px rgba(75, 143, 140, 0.2);
}

/* تخصيص الإشعارات للمسؤول الأعلى */
.super-admin-alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.super-admin-alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border-right: 4px solid #10b981;
}

.super-admin-alert-error {
    background-color: #fee2e2;
    color: #b91c1c;
    border-right: 4px solid #ef4444;
}

.super-admin-alert-warning {
    background-color: #fffbeb;
    color: #92400e;
    border-right: 4px solid #f59e0b;
}

.super-admin-alert-info {
    background-color: #e0f2fe;
    color: #0369a1;
    border-right: 4px solid #0ea5e9;
}

/* تخصيص الشارات للمسؤول الأعلى */
.super-admin-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.super-admin-badge-primary {
    background-color: var(--admin-primary);
    color: var(--admin-text);
}

.super-admin-badge-secondary {
    background-color: var(--admin-secondary);
    color: var(--admin-text);
}

.super-admin-badge-accent {
    background-color: var(--admin-accent);
    color: var(--admin-text);
}

.super-admin-badge-success {
    background-color: #10b981;
    color: white;
}

.super-admin-badge-error {
    background-color: #ef4444;
    color: white;
}

.super-admin-badge-warning {
    background-color: #f59e0b;
    color: white;
}

/* تخصيص الوضع الليلي للمسؤول الأعلى */
.dark .super-admin-bg {
    background-color: var(--admin-dark);
}

.dark .super-admin-card {
    background-color: #1f2937;
    color: #f3f4f6;
}

.dark .super-admin-table th {
    background-color: var(--admin-dark);
}

.dark .super-admin-table td {
    border-bottom: 1px solid #374151;
}

.dark .super-admin-table tr:hover {
    background-color: #374151;
}

.dark .super-admin-form-label {
    color: #e5e7eb;
}

.dark .super-admin-form-input {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #e5e7eb;
}

.dark .super-admin-form-input:focus {
    border-color: var(--admin-accent);
}
