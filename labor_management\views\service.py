from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count, Sum
from django.utils import timezone
from django.http import JsonResponse
from django.core.paginator import Paginator

from ..models import DailyService, Daily24HService, CustomService, Worker, Client, STATUS_CHOICES
from ..forms import DailyServiceForm, Daily24HServiceForm, CustomServiceForm

# Vistas para servicios diarios
@login_required
def daily_service_list(request):
    """Lista de servicios diarios"""
    # Filtros
    status = request.GET.get('status', '')
    client_id = request.GET.get('client', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    booking_type = request.GET.get('booking_type', '')
    search_query = request.GET.get('q', '')

    # Consulta base
    services = DailyService.objects.all()

    # Aplicar filtros
    if status:
        services = services.filter(status=status)

    if client_id:
        services = services.filter(client_id=client_id)

    if date_from:
        services = services.filter(service_date__gte=date_from)

    if date_to:
        services = services.filter(service_date__lte=date_to)

    if booking_type:
        services = services.filter(booking_type=booking_type)

    if search_query:
        services = services.filter(
            Q(service_number__icontains=search_query) |
            Q(client__name__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # Ordenar
    services = services.order_by('-service_date')

    # Paginación
    paginator = Paginator(services, 10)  # 10 servicios por página
    page_number = request.GET.get('page', 1)
    services_page = paginator.get_page(page_number)

    # Datos para filtros
    clients = Client.objects.all().order_by('name')

    context = {
        'services': services_page,
        'clients': clients,
        'status': status,
        'client_id': client_id,
        'date_from': date_from,
        'date_to': date_to,
        'booking_type': booking_type,
        'search_query': search_query,
        'title': 'قائمة الخدمات اليومية',
    }

    return render(request, 'labor_management/services/daily_service_list.html', context)

@login_required
def daily_service_create(request):
    """Añadir un nuevo servicio diario"""
    if request.method == 'POST':
        form = DailyServiceForm(request.POST)
        if form.is_valid():
            service = form.save(commit=False)
            service.created_by = request.user
            service.save()

            # Guardar los trabajadores seleccionados
            form.save_m2m()

            messages.success(request, 'تم إنشاء الخدمة اليومية بنجاح')
            return redirect('labor_management:daily_service_detail', service_id=service.id)
    else:
        form = DailyServiceForm()

    context = {
        'form': form,
        'title': 'إضافة خدمة يومية جديدة',
    }

    return render(request, 'labor_management/services/daily_service_form.html', context)

@login_required
def daily_service_detail(request, service_id):
    """Detalles de un servicio diario"""
    service = get_object_or_404(DailyService, id=service_id)

    context = {
        'service': service,
        'title': f'تفاصيل الخدمة اليومية: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_service_detail.html', context)

@login_required
def daily_service_edit(request, service_id):
    """Editar un servicio diario existente"""
    service = get_object_or_404(DailyService, id=service_id)

    if request.method == 'POST':
        form = DailyServiceForm(request.POST, instance=service)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الخدمة اليومية بنجاح')
            return redirect('labor_management:daily_service_detail', service_id=service.id)
    else:
        form = DailyServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'title': f'تعديل الخدمة اليومية: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_service_form.html', context)

@login_required
def daily_service_delete(request, service_id):
    """Eliminar un servicio diario"""
    service = get_object_or_404(DailyService, id=service_id)

    if request.method == 'POST':
        service.delete()
        messages.success(request, 'تم حذف الخدمة اليومية بنجاح')
        return redirect('labor_management:daily_service_list')

    context = {
        'service': service,
        'title': f'حذف الخدمة اليومية: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_service_confirm_delete.html', context)

@login_required
def daily_service_calendar(request):
    """Calendario de servicios diarios"""
    # Obtener el mes y año de la URL o usar el mes actual
    month = int(request.GET.get('month', timezone.now().month))
    year = int(request.GET.get('year', timezone.now().year))

    # Obtener todos los servicios para el mes seleccionado
    services = DailyService.objects.filter(
        service_date__year=year,
        service_date__month=month
    ).order_by('service_date', 'service_time')

    # Organizar servicios por día
    services_by_day = {}
    for service in services:
        day = service.service_date.day
        if day not in services_by_day:
            services_by_day[day] = []
        services_by_day[day].append(service)

    context = {
        'services_by_day': services_by_day,
        'month': month,
        'year': year,
        'title': 'تقويم الخدمات اليومية',
    }

    return render(request, 'labor_management/services/daily_service_calendar.html', context)

@login_required
def daily_service_summary(request):
    """Resumen y cálculo del neto diario"""
    # Obtener la fecha de la URL o usar la fecha actual
    date_str = request.GET.get('date', timezone.now().date().isoformat())

    # Obtener todos los servicios para la fecha seleccionada
    services = DailyService.objects.filter(service_date=date_str)

    # Calcular totales
    total_services = services.count()
    total_workers = sum(service.workers.count() for service in services)
    total_fees = services.aggregate(Sum('fee'))['fee__sum'] or 0
    total_overtime_fees = services.aggregate(Sum('overtime_fee'))['overtime_fee__sum'] or 0
    total_income = total_fees + total_overtime_fees

    context = {
        'services': services,
        'date': date_str,
        'total_services': total_services,
        'total_workers': total_workers,
        'total_fees': total_fees,
        'total_overtime_fees': total_overtime_fees,
        'total_income': total_income,
        'title': f'ملخص الخدمات اليومية: {date_str}',
    }

    return render(request, 'labor_management/services/daily_service_summary.html', context)

# Vistas para servicios de 24 horas
@login_required
def daily_24h_service_list(request):
    """Lista de servicios de 24 horas"""
    # Filtros
    status = request.GET.get('status', '')
    client_id = request.GET.get('client', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('q', '')

    # Consulta base
    services = Daily24HService.objects.all()

    # Aplicar filtros
    if status:
        services = services.filter(status=status)

    if client_id:
        services = services.filter(client_id=client_id)

    if date_from:
        services = services.filter(start_date__gte=date_from)

    if date_to:
        services = services.filter(end_date__lte=date_to)

    if search_query:
        services = services.filter(
            Q(service_number__icontains=search_query) |
            Q(client__name__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # Ordenar
    services = services.order_by('-start_date')

    # Paginación
    paginator = Paginator(services, 10)  # 10 servicios por página
    page_number = request.GET.get('page', 1)
    services_page = paginator.get_page(page_number)

    # Datos para filtros
    clients = Client.objects.all().order_by('name')

    context = {
        'services': services_page,
        'clients': clients,
        'status': status,
        'client_id': client_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'title': 'قائمة خدمات 24 ساعة',
    }

    return render(request, 'labor_management/services/daily_24h_service_list.html', context)

@login_required
def daily_24h_service_create(request):
    """Añadir un nuevo servicio de 24 horas"""
    if request.method == 'POST':
        form = Daily24HServiceForm(request.POST)
        if form.is_valid():
            service = form.save(commit=False)
            service.created_by = request.user
            service.save()

            # Guardar los trabajadores seleccionados
            form.save_m2m()

            messages.success(request, 'تم إنشاء خدمة 24 ساعة بنجاح')
            return redirect('labor_management:daily_24h_service_detail', service_id=service.id)
    else:
        form = Daily24HServiceForm()

    context = {
        'form': form,
        'title': 'إضافة خدمة 24 ساعة جديدة',
    }

    return render(request, 'labor_management/services/daily_24h_service_form.html', context)

@login_required
def daily_24h_service_detail(request, service_id):
    """Detalles de un servicio de 24 horas"""
    service = get_object_or_404(Daily24HService, id=service_id)

    context = {
        'service': service,
        'title': f'تفاصيل خدمة 24 ساعة: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_24h_service_detail.html', context)

@login_required
def daily_24h_service_edit(request, service_id):
    """Editar un servicio de 24 horas existente"""
    service = get_object_or_404(Daily24HService, id=service_id)

    if request.method == 'POST':
        form = Daily24HServiceForm(request.POST, instance=service)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث خدمة 24 ساعة بنجاح')
            return redirect('labor_management:daily_24h_service_detail', service_id=service.id)
    else:
        form = Daily24HServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'title': f'تعديل خدمة 24 ساعة: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_24h_service_form.html', context)

@login_required
def daily_24h_service_delete(request, service_id):
    """Eliminar un servicio de 24 horas"""
    service = get_object_or_404(Daily24HService, id=service_id)

    if request.method == 'POST':
        service.delete()
        messages.success(request, 'تم حذف خدمة 24 ساعة بنجاح')
        return redirect('labor_management:daily_24h_service_list')

    context = {
        'service': service,
        'title': f'حذف خدمة 24 ساعة: {service.service_number}',
    }

    return render(request, 'labor_management/services/daily_24h_service_confirm_delete.html', context)

@login_required
def daily_24h_service_calendar(request):
    """Calendario de servicios de 24 horas"""
    # Obtener el mes y año de la URL o usar el mes actual
    month = int(request.GET.get('month', timezone.now().month))
    year = int(request.GET.get('year', timezone.now().year))

    # Obtener todos los servicios para el mes seleccionado
    services = Daily24HService.objects.filter(
        Q(start_date__year=year, start_date__month=month) |
        Q(end_date__year=year, end_date__month=month) |
        Q(start_date__lte=f"{year}-{month:02d}-01", end_date__gte=f"{year}-{month+1:02d}-01")
    ).order_by('start_date')

    # Organizar servicios por día
    services_by_day = {}
    for service in services:
        # Determinar los días del mes actual en los que el servicio está activo
        current_month_days = []

        # Si el servicio comienza antes del mes actual y termina después
        if service.start_date.year < year or (service.start_date.year == year and service.start_date.month < month):
            start_day = 1
        else:
            start_day = service.start_date.day

        if service.end_date.year > year or (service.end_date.year == year and service.end_date.month > month):
            # Obtener el último día del mes actual
            if month == 12:
                end_day = 31
            else:
                next_month = timezone.datetime(year, month+1, 1)
                end_day = (next_month - timezone.timedelta(days=1)).day
        else:
            end_day = service.end_date.day

        for day in range(start_day, end_day + 1):
            if day not in services_by_day:
                services_by_day[day] = []
            services_by_day[day].append(service)

    context = {
        'services_by_day': services_by_day,
        'month': month,
        'year': year,
        'title': 'تقويم خدمات 24 ساعة',
    }

    return render(request, 'labor_management/services/daily_24h_service_calendar.html', context)

@login_required
def daily_24h_service_summary(request):
    """Resumen y cálculo del neto diario para servicios de 24 horas"""
    # Obtener la fecha de la URL o usar la fecha actual
    date_str = request.GET.get('date', timezone.now().date().isoformat())
    date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()

    # Obtener todos los servicios activos para la fecha seleccionada
    services = Daily24HService.objects.filter(
        start_date__lte=date_str,
        end_date__gte=date_str
    )

    # Calcular totales
    total_services = services.count()
    total_workers = sum(service.workers.count() for service in services)
    total_fees = services.aggregate(Sum('fee'))['fee__sum'] or 0

    # Calcular el costo diario (dividiendo el costo total por la duración del servicio)
    daily_fees = 0
    for service in services:
        service_duration = (service.end_date - service.start_date).days + 1
        daily_fee = service.fee / service_duration if service_duration > 0 else 0
        daily_fees += daily_fee

    context = {
        'services': services,
        'date': date_str,
        'total_services': total_services,
        'total_workers': total_workers,
        'total_fees': total_fees,
        'daily_fees': daily_fees,
        'title': f'ملخص خدمات 24 ساعة: {date_str}',
    }

    return render(request, 'labor_management/services/daily_24h_service_summary.html', context)

# Vistas para servicios personalizados
@login_required
def custom_service_list(request):
    """Lista de servicios personalizados"""
    # Filtros
    status = request.GET.get('status', '')
    client_id = request.GET.get('client', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('q', '')

    # Consulta base
    services = CustomService.objects.all()

    # Aplicar filtros
    if status:
        services = services.filter(status=status)

    if client_id:
        services = services.filter(client_id=client_id)

    if date_from:
        services = services.filter(start_date__gte=date_from)

    if date_to:
        services = services.filter(end_date__lte=date_to)

    if search_query:
        services = services.filter(
            Q(service_number__icontains=search_query) |
            Q(client__name__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # Ordenar
    services = services.order_by('-start_date')

    # Paginación
    paginator = Paginator(services, 10)  # 10 servicios por página
    page_number = request.GET.get('page', 1)
    services_page = paginator.get_page(page_number)

    # Datos para filtros
    clients = Client.objects.all().order_by('name')

    context = {
        'services': services_page,
        'clients': clients,
        'status': status,
        'client_id': client_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'title': 'قائمة الخدمات المخصصة',
    }

    return render(request, 'labor_management/services/custom_service_list.html', context)

@login_required
def custom_service_create(request):
    """Añadir un nuevo servicio personalizado"""
    if request.method == 'POST':
        form = CustomServiceForm(request.POST)
        if form.is_valid():
            service = form.save(commit=False)
            service.created_by = request.user
            service.save()

            # Guardar los trabajadores seleccionados
            form.save_m2m()

            messages.success(request, 'تم إنشاء الخدمة المخصصة بنجاح')
            return redirect('labor_management:custom_service_detail', service_id=service.id)
    else:
        form = CustomServiceForm()

    context = {
        'form': form,
        'title': 'إضافة خدمة مخصصة جديدة',
    }

    return render(request, 'labor_management/services/custom_service_form.html', context)

@login_required
def custom_service_detail(request, service_id):
    """Detalles de un servicio personalizado"""
    service = get_object_or_404(CustomService, id=service_id)

    # Obtener los días de servicio como lista
    service_days = service.get_service_days_list()

    context = {
        'service': service,
        'service_days': service_days,
        'title': f'تفاصيل الخدمة المخصصة: {service.service_number}',
    }

    return render(request, 'labor_management/services/custom_service_detail.html', context)

@login_required
def custom_service_edit(request, service_id):
    """Editar un servicio personalizado existente"""
    service = get_object_or_404(CustomService, id=service_id)

    if request.method == 'POST':
        form = CustomServiceForm(request.POST, instance=service)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الخدمة المخصصة بنجاح')
            return redirect('labor_management:custom_service_detail', service_id=service.id)
    else:
        form = CustomServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'title': f'تعديل الخدمة المخصصة: {service.service_number}',
    }

    return render(request, 'labor_management/services/custom_service_form.html', context)

@login_required
def custom_service_delete(request, service_id):
    """Eliminar un servicio personalizado"""
    service = get_object_or_404(CustomService, id=service_id)

    if request.method == 'POST':
        service.delete()
        messages.success(request, 'تم حذف الخدمة المخصصة بنجاح')
        return redirect('labor_management:custom_service_list')

    context = {
        'service': service,
        'title': f'حذف الخدمة المخصصة: {service.service_number}',
    }

    return render(request, 'labor_management/services/custom_service_confirm_delete.html', context)

@login_required
def custom_service_calendar(request):
    """Calendario de servicios personalizados"""
    # Obtener el mes y año de la URL o usar el mes actual
    month = int(request.GET.get('month', timezone.now().month))
    year = int(request.GET.get('year', timezone.now().year))

    # Obtener todos los servicios para el mes seleccionado
    services = CustomService.objects.filter(
        Q(start_date__year=year, start_date__month=month) |
        Q(end_date__year=year, end_date__month=month) |
        Q(start_date__lte=f"{year}-{month:02d}-01", end_date__gte=f"{year}-{month+1:02d}-01")
    ).order_by('start_date')

    # Obtener el número de días en el mes actual
    if month == 12:
        last_day = 31
    else:
        next_month = timezone.datetime(year, month+1, 1)
        last_day = (next_month - timezone.timedelta(days=1)).day

    # Organizar servicios por día
    services_by_day = {}
    for day in range(1, last_day + 1):
        services_by_day[day] = []

        # Fecha actual para comparar
        current_date = timezone.datetime(year, month, day).date()

        for service in services:
            # Verificar si el servicio está activo en este día
            if service.start_date <= current_date <= service.end_date:
                # Verificar si este día está en los días de servicio especificados
                service_days = service.get_service_days_list()

                # Si no hay días específicos, el servicio está activo todos los días
                if not service_days or str(day) in service_days:
                    services_by_day[day].append(service)

    context = {
        'services_by_day': services_by_day,
        'month': month,
        'year': year,
        'title': 'تقويم الخدمات المخصصة',
    }

    return render(request, 'labor_management/services/custom_service_calendar.html', context)

# Funciones de utilidad para servicios
@login_required
def available_workers(request):
    """API para obtener los trabajadores disponibles para una fecha específica"""
    date_str = request.GET.get('date', timezone.now().date().isoformat())
    date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()

    # Obtener todos los trabajadores activos
    all_workers = Worker.objects.filter(status='active')
    total_workers = all_workers.count()

    # Obtener servicios diarios para la fecha
    daily_services = DailyService.objects.filter(service_date=date_str)

    # Obtener servicios de 24 horas activos para la fecha
    daily_24h_services = Daily24HService.objects.filter(
        start_date__lte=date_str,
        end_date__gte=date_str
    )

    # Obtener servicios personalizados activos para la fecha
    custom_services = CustomService.objects.filter(
        start_date__lte=date_str,
        end_date__gte=date_str
    )

    # Filtrar servicios personalizados por día específico
    day_of_month = date_obj.day
    filtered_custom_services = []
    for service in custom_services:
        service_days = service.get_service_days_list()
        if not service_days or str(day_of_month) in service_days:
            filtered_custom_services.append(service)

    # Contar trabajadores asignados
    assigned_workers_ids = set()

    # Trabajadores asignados a servicios diarios
    for service in daily_services:
        assigned_workers_ids.update(service.workers.values_list('id', flat=True))

    # Trabajadores asignados a servicios de 24 horas
    for service in daily_24h_services:
        assigned_workers_ids.update(service.workers.values_list('id', flat=True))

    # Trabajadores asignados a servicios personalizados
    for service in filtered_custom_services:
        assigned_workers_ids.update(service.workers.values_list('id', flat=True))

    # Contar trabajadores requeridos pero no asignados
    unassigned_workers_count = 0

    # Servicios diarios con trabajadores requeridos pero no asignados completamente
    for service in daily_services:
        if service.workers.count() < service.workers_required:
            unassigned_workers_count += (service.workers_required - service.workers.count())

    # Servicios de 24 horas con trabajadores requeridos pero no asignados completamente
    for service in daily_24h_services:
        if service.workers.count() < service.workers_required:
            unassigned_workers_count += (service.workers_required - service.workers.count())

    # Servicios personalizados con trabajadores requeridos pero no asignados completamente
    for service in filtered_custom_services:
        if service.workers.count() < service.workers_required:
            unassigned_workers_count += (service.workers_required - service.workers.count())

    # Calcular trabajadores disponibles
    assigned_workers_count = len(assigned_workers_ids)
    available_workers_count = total_workers - assigned_workers_count - unassigned_workers_count

    # Obtener lista de trabajadores disponibles
    available_workers_list = []
    for worker in all_workers:
        if worker.id not in assigned_workers_ids:
            available_workers_list.append({
                'id': worker.id,
                'name': worker.full_name,
                'nationality': worker.get_nationality_display(),
                'worker_type': worker.get_worker_type_display(),
                'status': worker.status
            })

    # Limitar la lista si hay trabajadores requeridos pero no asignados
    if unassigned_workers_count > 0 and len(available_workers_list) > available_workers_count:
        available_workers_list = available_workers_list[:available_workers_count]

    response_data = {
        'date': date_str,
        'total_workers': total_workers,
        'assigned_workers': assigned_workers_count,
        'unassigned_required': unassigned_workers_count,
        'available_workers': available_workers_count,
        'workers': available_workers_list
    }

    return JsonResponse(response_data)

@login_required
def service_statistics(request):
    """API para obtener estadísticas de servicios"""
    # Obtener estadísticas generales
    total_daily_services = DailyService.objects.count()
    total_24h_services = Daily24HService.objects.count()
    total_custom_services = CustomService.objects.count()

    # Servicios por estado
    daily_services_by_status = {
        status[0]: DailyService.objects.filter(status=status[0]).count()
        for status in STATUS_CHOICES
    }

    daily_24h_services_by_status = {
        status[0]: Daily24HService.objects.filter(status=status[0]).count()
        for status in STATUS_CHOICES
    }

    custom_services_by_status = {
        status[0]: CustomService.objects.filter(status=status[0]).count()
        for status in STATUS_CHOICES
    }

    # Servicios para hoy
    today = timezone.now().date()
    daily_services_today = DailyService.objects.filter(service_date=today).count()

    daily_24h_services_today = Daily24HService.objects.filter(
        start_date__lte=today,
        end_date__gte=today
    ).count()

    custom_services_today = 0
    custom_services_active_today = CustomService.objects.filter(
        start_date__lte=today,
        end_date__gte=today
    )

    day_of_month = today.day
    for service in custom_services_active_today:
        service_days = service.get_service_days_list()
        if not service_days or str(day_of_month) in service_days:
            custom_services_today += 1

    response_data = {
        'total_daily_services': total_daily_services,
        'total_24h_services': total_24h_services,
        'total_custom_services': total_custom_services,
        'daily_services_by_status': daily_services_by_status,
        'daily_24h_services_by_status': daily_24h_services_by_status,
        'custom_services_by_status': custom_services_by_status,
        'daily_services_today': daily_services_today,
        'daily_24h_services_today': daily_24h_services_today,
        'custom_services_today': custom_services_today,
    }

    return JsonResponse(response_data)
