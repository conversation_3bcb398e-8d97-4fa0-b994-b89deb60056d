{% comment %}
استخدام المكون:
1. زر حذف بسيط:
{% include 'components/delete-button.html' with url="/delete/123/" %}

2. زر حذف مخصص:
{% include 'components/delete-button.html' with url="/delete/123/" text="إزالة" icon="trash-alt" btn_size="sm" %}

3. زر حذف مع بيانات إضافية:
{% include 'components/delete-button.html' with url=object.get_delete_url data_id=object.id data_type="document" data_name=object.title %}
{% endcomment %}

{% with text=text|default:"حذف" %}
{% with icon=icon|default:"fa-trash" %}
{% with btn_size=btn_size|default:"md" %}

{% if btn_size == "sm" %}
    {% with size_classes="px-2 py-1 text-xs" icon_classes="ml-1" %}
        <button
            type="button"
            data-delete-url="{{ url }}"
            {% if data_id %}data-id="{{ data_id }}"{% endif %}
            {% if data_type %}data-type="{{ data_type }}"{% endif %}
            {% if data_name %}data-name="{{ data_name }}"{% endif %}
            class="text-white bg-red-600 hover:bg-red-700 {{ size_classes }} rounded-lg transition-colors duration-200 flex items-center"
            onclick="window.deleteItem('{{ url }}');"
        >
            <i class="fas {{ icon }} {{ icon_classes }}"></i> {{ text }}
        </button>
    {% endwith %}
{% elif btn_size == "lg" %}
    {% with size_classes="px-6 py-3 text-base" icon_classes="ml-2" %}
        <button
            type="button"
            data-delete-url="{{ url }}"
            {% if data_id %}data-id="{{ data_id }}"{% endif %}
            {% if data_type %}data-type="{{ data_type }}"{% endif %}
            {% if data_name %}data-name="{{ data_name }}"{% endif %}
            class="text-white bg-red-600 hover:bg-red-700 {{ size_classes }} rounded-lg transition-colors duration-200 flex items-center"
            onclick="window.deleteItem('{{ url }}');"
        >
            <i class="fas {{ icon }} {{ icon_classes }}"></i> {{ text }}
        </button>
    {% endwith %}
{% else %}
    {% with size_classes="px-4 py-2 text-sm" icon_classes="ml-1.5" %}
        <button
            type="button"
            data-delete-url="{{ url }}"
            {% if data_id %}data-id="{{ data_id }}"{% endif %}
            {% if data_type %}data-type="{{ data_type }}"{% endif %}
            {% if data_name %}data-name="{{ data_name }}"{% endif %}
            class="text-white bg-red-600 hover:bg-red-700 {{ size_classes }} rounded-lg transition-colors duration-200 flex items-center"
            onclick="window.deleteItem('{{ url }}');"
        >
            <i class="fas {{ icon }} {{ icon_classes }}"></i> {{ text }}
        </button>
    {% endwith %}
{% endif %}
{% endwith %}
{% endwith %}
{% endwith %}