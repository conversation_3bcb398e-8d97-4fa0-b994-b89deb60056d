# Generated by Django 5.2 on 2025-04-10 14:16

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workers', '0003_worker_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='worker',
            name='entry_date',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الدخول'),
        ),
        migrations.AddField(
            model_name='worker',
            name='passport_image',
            field=models.ImageField(blank=True, null=True, upload_to='workers/passports/', verbose_name='صورة جواز السفر'),
        ),
        migrations.AddField(
            model_name='worker',
            name='visa_image',
            field=models.ImageField(blank=True, null=True, upload_to='workers/visas/', verbose_name='صورة التأشيرة/الستيكر'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='workers/photos/', verbose_name='الصورة الشخصية'),
        ),
        migrations.CreateModel(
            name='WorkerDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('passport', 'جواز سفر'), ('visa', 'تأشيرة'), ('id_card', 'بطاقة هوية'), ('contract', 'عقد عمل'), ('medical', 'شهادة طبية'), ('other', 'مستندات أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('title', models.CharField(max_length=100, verbose_name='عنوان المستند')),
                ('file', models.FileField(upload_to='workers/documents/', verbose_name='الملف')),
                ('upload_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'مستند العامل',
                'verbose_name_plural': 'مستندات العمال',
            },
        ),
    ]
