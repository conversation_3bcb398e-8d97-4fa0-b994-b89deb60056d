{% extends 'base_tailwind.html' %}
{% load static %}

{% block title %}حذف العامل | منصة استقدامي السحابية{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">حذف العامل</h2>
            <p class="text-sm text-gray-500 mt-1">
                تأكيد حذف العامل: {{ worker.first_name }} {{ worker.last_name }}
            </p>
        </div>
        <a href="{% url 'workers:dashboard' %}"
           class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors duration-300 flex items-center">
            <i class="fas fa-arrow-right ml-2"></i>
            العودة
        </a>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0 h-16 w-16 ml-4">
                {% if worker.photo %}
                    <img class="h-16 w-16 rounded-full object-cover" src="{{ worker.photo.url }}" alt="{{ worker.first_name }}">
                {% else %}
                    <div class="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                        <i class="fas fa-user-slash text-2xl"></i>
                    </div>
                {% endif %}
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-900">{{ worker.first_name }} {{ worker.last_name }}</h3>
                <p class="text-gray-600">{{ worker.get_nationality_display }} - {{ worker.passport_number }}</p>
                <p class="text-gray-500 text-sm">{{ worker.get_status_display }}</p>
            </div>
        </div>

        <div class="bg-red-50 border-r-4 border-red-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                </div>
                <div class="mr-3">
                    <p class="text-red-700 font-medium">تحذير: هذا الإجراء لا يمكن التراجع عنه!</p>
                    <p class="text-red-600 text-sm">سيتم حذف جميع بيانات العامل بشكل نهائي من النظام.</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
                <h4 class="text-sm font-semibold text-gray-600 mb-1">معلومات العامل</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                    <li><span class="font-medium">الجنس:</span> {{ worker.get_gender_display }}</li>
                    <li><span class="font-medium">تاريخ الميلاد:</span> {{ worker.date_of_birth|date:"Y-m-d" }}</li>
                    <li><span class="font-medium">تاريخ الانضمام:</span> {{ worker.date_joined|date:"Y-m-d" }}</li>
                </ul>
            </div>
            <div>
                <h4 class="text-sm font-semibold text-gray-600 mb-1">معلومات جواز السفر</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                    <li><span class="font-medium">رقم الجواز:</span> {{ worker.passport_number }}</li>
                    <li><span class="font-medium">تاريخ الانتهاء:</span> {{ worker.passport_expiry|date:"Y-m-d" }}</li>
                    <li><span class="font-medium">تاريخ انتهاء التأشيرة:</span> {{ worker.visa_expiry|date:"Y-m-d" }}</li>
                </ul>
            </div>
        </div>

        <form method="POST" class="mt-6">
            {% csrf_token %}
            <div class="flex items-center justify-between">
                <a href="{% if worker_type == 'contract' %}{% url 'workers:contract_workers' %}{% elif worker_type == 'custom' %}{% url 'workers:custom_service_workers' %}{% elif worker_type == 'monthly' %}{% url 'workers:monthly_workers' %}{% else %}{% url 'workers:worker_list' %}{% endif %}"
                   class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors duration-300">
                    إلغاء
                </a>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md shadow-md transition-colors duration-300 flex items-center">
                    <i class="fas fa-trash-alt ml-2"></i>
                    تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
