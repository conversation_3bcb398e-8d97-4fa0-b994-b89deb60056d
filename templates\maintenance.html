<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title|default:"النظام تحت الصيانة" }}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        .maintenance-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .maintenance-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="maintenance-bg">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-2xl w-full">
            <!-- Main Maintenance Card -->
            <div class="maintenance-card rounded-2xl p-8 text-center text-white shadow-2xl">
                <!-- Icon -->
                <div class="mb-8">
                    <div class="inline-flex items-center justify-center w-24 h-24 bg-white bg-opacity-20 rounded-full floating">
                        <i class="fas fa-tools text-4xl text-white"></i>
                    </div>
                </div>

                <!-- Title -->
                <h1 class="text-4xl font-bold mb-4">
                    {{ title|default:"النظام تحت الصيانة" }}
                </h1>

                <!-- Message -->
                <p class="text-xl mb-8 text-gray-100 leading-relaxed">
                    {{ message|default:"نعتذر، النظام تحت الصيانة حالياً. سيعود للعمل قريباً." }}
                </p>

                <!-- Estimated Completion Time -->
                {% if estimated_completion %}
                <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-8">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-clock text-yellow-300 ml-2"></i>
                        <span class="text-lg font-medium">الوقت المتوقع للانتهاء</span>
                    </div>
                    <div class="text-2xl font-bold text-yellow-300" id="countdown">
                        {{ estimated_completion|date:"Y/m/d H:i" }}
                    </div>
                </div>
                {% endif %}

                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="bg-white bg-opacity-20 rounded-full h-2 overflow-hidden">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-full rounded-full pulse-animation" style="width: 65%;"></div>
                    </div>
                    <p class="text-sm text-gray-200 mt-2">جاري العمل على تحسين النظام...</p>
                </div>

                <!-- Contact Information -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-headset ml-2"></i>
                        هل تحتاج مساعدة؟
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-envelope text-blue-300 ml-2"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center justify-center">
                            <i class="fas fa-phone text-green-300 ml-2"></i>
                            <span>+966 50 123 4567</span>
                        </div>
                    </div>
                </div>

                <!-- Social Media Links -->
                <div class="flex justify-center space-x-4 mb-8">
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                        <i class="fab fa-twitter text-white"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                        <i class="fab fa-facebook text-white"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                        <i class="fab fa-linkedin text-white"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                        <i class="fab fa-instagram text-white"></i>
                    </a>
                </div>

                <!-- Refresh Button -->
                <button onclick="location.reload()" 
                        class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث الصفحة
                </button>
            </div>

            <!-- Additional Info -->
            <div class="mt-8 text-center text-white text-opacity-80">
                <p class="text-sm">
                    <i class="fas fa-shield-alt ml-1"></i>
                    جميع بياناتك محفوظة وآمنة
                </p>
                <p class="text-xs mt-2">
                    © 2024 منصة استقدامي. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
    </div>

    <!-- Auto Refresh Script -->
    <script>
        // تحديث الصفحة كل 30 ثانية للتحقق من انتهاء الصيانة
        setInterval(function() {
            location.reload();
        }, 30000);

        {% if estimated_completion %}
        // عداد تنازلي للوقت المتوقع
        function updateCountdown() {
            const targetDate = new Date('{{ estimated_completion|date:"c" }}');
            const now = new Date();
            const difference = targetDate - now;

            if (difference > 0) {
                const hours = Math.floor(difference / (1000 * 60 * 60));
                const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                document.getElementById('countdown').innerHTML = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                document.getElementById('countdown').innerHTML = 'انتهت فترة الصيانة المتوقعة';
                // تحديث الصفحة عند انتهاء الوقت المتوقع
                setTimeout(() => location.reload(), 2000);
            }
        }

        // تحديث العداد كل ثانية
        setInterval(updateCountdown, 1000);
        updateCountdown(); // تشغيل فوري
        {% endif %}

        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            document.querySelector('.maintenance-card').style.opacity = '0';
            document.querySelector('.maintenance-card').style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                document.querySelector('.maintenance-card').style.transition = 'all 0.8s ease-out';
                document.querySelector('.maintenance-card').style.opacity = '1';
                document.querySelector('.maintenance-card').style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
