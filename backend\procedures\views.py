from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count
from backend.workers.models import Worker
from backend.dashboard.models import Activity
from .models import (
    BloodTest, SponsorshipTransfer, Operation,
    WorkPermit, ResidenceID, BranchTransfer,
    ProcedureAttachment
)

@login_required
def procedures_dashboard(request):
    """View function for the procedures dashboard page."""
    return render(request, 'procedures/dashboard.html')

@login_required
def procedure_list(request):
    """View function for the procedures list page."""
    blood_tests_count = BloodTest.objects.count()
    sponsorship_transfers_count = SponsorshipTransfer.objects.count()
    operations_count = Operation.objects.count()
    work_permits_count = WorkPermit.objects.count()
    residence_ids_count = ResidenceID.objects.count()
    branch_transfers_count = BranchTransfer.objects.count()

    # Get recent procedures
    recent_blood_tests = BloodTest.objects.all().order_by('-created_at')[:5]
    recent_sponsorship_transfers = SponsorshipTransfer.objects.all().order_by('-created_at')[:5]
    recent_operations = Operation.objects.all().order_by('-created_at')[:5]
    recent_work_permits = WorkPermit.objects.all().order_by('-created_at')[:5]
    recent_residence_ids = ResidenceID.objects.all().order_by('-created_at')[:5]
    recent_branch_transfers = BranchTransfer.objects.all().order_by('-created_at')[:5]

    context = {
        'page_title': 'إجراءات العمال',
        'blood_tests_count': blood_tests_count,
        'sponsorship_transfers_count': sponsorship_transfers_count,
        'operations_count': operations_count,
        'work_permits_count': work_permits_count,
        'residence_ids_count': residence_ids_count,
        'branch_transfers_count': branch_transfers_count,
        'recent_blood_tests': recent_blood_tests,
        'recent_sponsorship_transfers': recent_sponsorship_transfers,
        'recent_operations': recent_operations,
        'recent_work_permits': recent_work_permits,
        'recent_residence_ids': recent_residence_ids,
        'recent_branch_transfers': recent_branch_transfers,
    }

    return render(request, 'procedures/procedure_list.html', context)

@login_required
def worker_procedures(request, worker_id):
    """View function for the worker procedures page."""
    worker = get_object_or_404(Worker, id=worker_id)

    # Get worker procedures
    blood_tests = BloodTest.objects.filter(worker=worker).order_by('-created_at')
    sponsorship_transfers = SponsorshipTransfer.objects.filter(worker=worker).order_by('-created_at')
    operations = Operation.objects.filter(worker=worker).order_by('-created_at')
    work_permits = WorkPermit.objects.filter(worker=worker).order_by('-created_at')
    residence_ids = ResidenceID.objects.filter(worker=worker).order_by('-created_at')
    branch_transfers = BranchTransfer.objects.filter(worker=worker).order_by('-created_at')

    context = {
        'page_title': f'إجراءات العامل | {worker.first_name} {worker.last_name}',
        'worker': worker,
        'blood_tests': blood_tests,
        'sponsorship_transfers': sponsorship_transfers,
        'operations': operations,
        'work_permits': work_permits,
        'residence_ids': residence_ids,
        'branch_transfers': branch_transfers,
    }

    return render(request, 'procedures/worker_procedures.html', context)

# Blood Test Views
@login_required
def blood_test_create(request, worker_id):
    """View function for creating a blood test."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        reservation_date = request.POST.get('reservation_date') or None
        reservation_time = request.POST.get('reservation_time') or None
        reference_number = request.POST.get('reference_number')
        result = request.POST.get('result')
        result_date = request.POST.get('result_date') or None

        # Create blood test
        blood_test = BloodTest.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            reservation_date=reservation_date,
            reservation_time=reservation_time,
            reference_number=reference_number,
            result=result,
            result_date=result_date,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة فحص دم للعامل {worker.first_name} {worker.last_name}',
            activity_type='blood_test_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة فحص الدم بنجاح')
        return redirect('procedures:blood_test_detail', pk=blood_test.id)

    context = {
        'page_title': f'إضافة فحص دم | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/blood_test_form.html', context)

@login_required
def blood_test_detail(request, pk):
    """View function for viewing a blood test's details."""
    blood_test = get_object_or_404(BloodTest, id=pk)
    attachments = blood_test.attachments.all()

    context = {
        'page_title': f'تفاصيل فحص الدم | {blood_test.worker.first_name} {blood_test.worker.last_name}',
        'blood_test': blood_test,
        'attachments': attachments,
    }

    return render(request, 'procedures/blood_test_detail.html', context)

@login_required
def blood_test_update(request, pk):
    """View function for updating a blood test."""
    blood_test = get_object_or_404(BloodTest, id=pk)

    if request.method == 'POST':
        # Process form data
        blood_test.status = request.POST.get('status')
        blood_test.outgoing_number = request.POST.get('outgoing_number')
        blood_test.outgoing_date = request.POST.get('outgoing_date') or None
        blood_test.reservation_date = request.POST.get('reservation_date') or None
        blood_test.reservation_time = request.POST.get('reservation_time') or None
        blood_test.reference_number = request.POST.get('reference_number')
        blood_test.result = request.POST.get('result')
        blood_test.result_date = request.POST.get('result_date') or None
        blood_test.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث فحص دم للعامل {blood_test.worker.first_name} {blood_test.worker.last_name}',
            activity_type='blood_test_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث فحص الدم بنجاح')
        return redirect('procedures:blood_test_detail', pk=blood_test.id)

    context = {
        'page_title': f'تعديل فحص دم | {blood_test.worker.first_name} {blood_test.worker.last_name}',
        'blood_test': blood_test,
    }

    return render(request, 'procedures/blood_test_form.html', context)

@login_required
def blood_test_delete(request, pk):
    """View function for deleting a blood test."""
    blood_test = get_object_or_404(BloodTest, id=pk)
    worker = blood_test.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف فحص دم للعامل {blood_test.worker.first_name} {blood_test.worker.last_name}',
        activity_type='blood_test_deleted',
        date_created=timezone.now()
    )

    blood_test.delete()
    messages.success(request, 'تم حذف فحص الدم بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Sponsorship Transfer Views
@login_required
def sponsorship_transfer_create(request, worker_id):
    """View function for creating a sponsorship transfer."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        contract_number = request.POST.get('contract_number')
        contract_date = request.POST.get('contract_date') or None
        client_name = request.POST.get('client_name')

        # Create sponsorship transfer
        sponsorship_transfer = SponsorshipTransfer.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            contract_number=contract_number,
            contract_date=contract_date,
            client_name=client_name,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة تحويل كفالة للعامل {worker.first_name} {worker.last_name}',
            activity_type='sponsorship_transfer_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة تحويل الكفالة بنجاح')
        return redirect('procedures:sponsorship_transfer_detail', pk=sponsorship_transfer.id)

    context = {
        'page_title': f'إضافة تحويل كفالة | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/sponsorship_transfer_form.html', context)

@login_required
def sponsorship_transfer_detail(request, pk):
    """View function for viewing a sponsorship transfer's details."""
    sponsorship_transfer = get_object_or_404(SponsorshipTransfer, id=pk)
    attachments = sponsorship_transfer.attachments.all()

    context = {
        'page_title': f'تفاصيل تحويل الكفالة | {sponsorship_transfer.worker.first_name} {sponsorship_transfer.worker.last_name}',
        'sponsorship_transfer': sponsorship_transfer,
        'attachments': attachments,
    }

    return render(request, 'procedures/sponsorship_transfer_detail.html', context)

@login_required
def sponsorship_transfer_update(request, pk):
    """View function for updating a sponsorship transfer."""
    sponsorship_transfer = get_object_or_404(SponsorshipTransfer, id=pk)

    if request.method == 'POST':
        # Process form data
        sponsorship_transfer.status = request.POST.get('status')
        sponsorship_transfer.outgoing_number = request.POST.get('outgoing_number')
        sponsorship_transfer.outgoing_date = request.POST.get('outgoing_date') or None
        sponsorship_transfer.contract_number = request.POST.get('contract_number')
        sponsorship_transfer.contract_date = request.POST.get('contract_date') or None
        sponsorship_transfer.client_name = request.POST.get('client_name')
        sponsorship_transfer.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث تحويل كفالة للعامل {sponsorship_transfer.worker.first_name} {sponsorship_transfer.worker.last_name}',
            activity_type='sponsorship_transfer_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث تحويل الكفالة بنجاح')
        return redirect('procedures:sponsorship_transfer_detail', pk=sponsorship_transfer.id)

    context = {
        'page_title': f'تعديل تحويل كفالة | {sponsorship_transfer.worker.first_name} {sponsorship_transfer.worker.last_name}',
        'sponsorship_transfer': sponsorship_transfer,
    }

    return render(request, 'procedures/sponsorship_transfer_form.html', context)

@login_required
def sponsorship_transfer_delete(request, pk):
    """View function for deleting a sponsorship transfer."""
    sponsorship_transfer = get_object_or_404(SponsorshipTransfer, id=pk)
    worker = sponsorship_transfer.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف تحويل كفالة للعامل {sponsorship_transfer.worker.first_name} {sponsorship_transfer.worker.last_name}',
        activity_type='sponsorship_transfer_deleted',
        date_created=timezone.now()
    )

    sponsorship_transfer.delete()
    messages.success(request, 'تم حذف تحويل الكفالة بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Operation Views
@login_required
def operation_create(request, worker_id):
    """View function for creating an operation."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        contract_number = request.POST.get('contract_number')
        contract_date = request.POST.get('contract_date') or None
        client_name = request.POST.get('client_name')

        # Create operation
        operation = Operation.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            contract_number=contract_number,
            contract_date=contract_date,
            client_name=client_name,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة تشغيل للعامل {worker.first_name} {worker.last_name}',
            activity_type='operation_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة التشغيل بنجاح')
        return redirect('procedures:operation_detail', pk=operation.id)

    context = {
        'page_title': f'إضافة تشغيل | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/operation_form.html', context)

@login_required
def operation_detail(request, pk):
    """View function for viewing an operation's details."""
    operation = get_object_or_404(Operation, id=pk)
    attachments = operation.attachments.all()

    context = {
        'page_title': f'تفاصيل التشغيل | {operation.worker.first_name} {operation.worker.last_name}',
        'operation': operation,
        'attachments': attachments,
    }

    return render(request, 'procedures/operation_detail.html', context)

@login_required
def operation_update(request, pk):
    """View function for updating an operation."""
    operation = get_object_or_404(Operation, id=pk)

    if request.method == 'POST':
        # Process form data
        operation.status = request.POST.get('status')
        operation.outgoing_number = request.POST.get('outgoing_number')
        operation.outgoing_date = request.POST.get('outgoing_date') or None
        operation.contract_number = request.POST.get('contract_number')
        operation.contract_date = request.POST.get('contract_date') or None
        operation.client_name = request.POST.get('client_name')
        operation.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث تشغيل للعامل {operation.worker.first_name} {operation.worker.last_name}',
            activity_type='operation_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث التشغيل بنجاح')
        return redirect('procedures:operation_detail', pk=operation.id)

    context = {
        'page_title': f'تعديل تشغيل | {operation.worker.first_name} {operation.worker.last_name}',
        'operation': operation,
    }

    return render(request, 'procedures/operation_form.html', context)

@login_required
def operation_delete(request, pk):
    """View function for deleting an operation."""
    operation = get_object_or_404(Operation, id=pk)
    worker = operation.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف تشغيل للعامل {operation.worker.first_name} {operation.worker.last_name}',
        activity_type='operation_deleted',
        date_created=timezone.now()
    )

    operation.delete()
    messages.success(request, 'تم حذف التشغيل بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Work Permit Views
@login_required
def work_permit_create(request, worker_id):
    """View function for creating a work permit."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        permit_number = request.POST.get('permit_number')
        issue_date = request.POST.get('issue_date') or None
        expiry_date = request.POST.get('expiry_date') or None

        # Create work permit
        work_permit = WorkPermit.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            permit_number=permit_number,
            issue_date=issue_date,
            expiry_date=expiry_date,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة إجازة عمل للعامل {worker.first_name} {worker.last_name}',
            activity_type='work_permit_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة إجازة العمل بنجاح')
        return redirect('procedures:work_permit_detail', pk=work_permit.id)

    context = {
        'page_title': f'إضافة إجازة عمل | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/work_permit_form.html', context)

@login_required
def work_permit_detail(request, pk):
    """View function for viewing a work permit's details."""
    work_permit = get_object_or_404(WorkPermit, id=pk)
    attachments = work_permit.attachments.all()

    context = {
        'page_title': f'تفاصيل إجازة العمل | {work_permit.worker.first_name} {work_permit.worker.last_name}',
        'work_permit': work_permit,
        'attachments': attachments,
    }

    return render(request, 'procedures/work_permit_detail.html', context)

@login_required
def work_permit_update(request, pk):
    """View function for updating a work permit."""
    work_permit = get_object_or_404(WorkPermit, id=pk)

    if request.method == 'POST':
        # Process form data
        work_permit.status = request.POST.get('status')
        work_permit.outgoing_number = request.POST.get('outgoing_number')
        work_permit.outgoing_date = request.POST.get('outgoing_date') or None
        work_permit.permit_number = request.POST.get('permit_number')
        work_permit.issue_date = request.POST.get('issue_date') or None
        work_permit.expiry_date = request.POST.get('expiry_date') or None
        work_permit.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث إجازة عمل للعامل {work_permit.worker.first_name} {work_permit.worker.last_name}',
            activity_type='work_permit_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث إجازة العمل بنجاح')
        return redirect('procedures:work_permit_detail', pk=work_permit.id)

    context = {
        'page_title': f'تعديل إجازة عمل | {work_permit.worker.first_name} {work_permit.worker.last_name}',
        'work_permit': work_permit,
    }

    return render(request, 'procedures/work_permit_form.html', context)

@login_required
def work_permit_delete(request, pk):
    """View function for deleting a work permit."""
    work_permit = get_object_or_404(WorkPermit, id=pk)
    worker = work_permit.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف إجازة عمل للعامل {work_permit.worker.first_name} {work_permit.worker.last_name}',
        activity_type='work_permit_deleted',
        date_created=timezone.now()
    )

    work_permit.delete()
    messages.success(request, 'تم حذف إجازة العمل بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Residence ID Views
@login_required
def residence_id_create(request, worker_id):
    """View function for creating a residence ID."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        id_number = request.POST.get('id_number')
        issue_date = request.POST.get('issue_date') or None
        expiry_date = request.POST.get('expiry_date') or None

        # Create residence ID
        residence_id = ResidenceID.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            id_number=id_number,
            issue_date=issue_date,
            expiry_date=expiry_date,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة هوية إقامة للعامل {worker.first_name} {worker.last_name}',
            activity_type='residence_id_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة هوية الإقامة بنجاح')
        return redirect('procedures:residence_id_detail', pk=residence_id.id)

    context = {
        'page_title': f'إضافة هوية إقامة | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/residence_id_form.html', context)

@login_required
def residence_id_detail(request, pk):
    """View function for viewing a residence ID's details."""
    residence_id = get_object_or_404(ResidenceID, id=pk)
    attachments = residence_id.attachments.all()

    context = {
        'page_title': f'تفاصيل هوية الإقامة | {residence_id.worker.first_name} {residence_id.worker.last_name}',
        'residence_id': residence_id,
        'attachments': attachments,
    }

    return render(request, 'procedures/residence_id_detail.html', context)

@login_required
def residence_id_update(request, pk):
    """View function for updating a residence ID."""
    residence_id = get_object_or_404(ResidenceID, id=pk)

    if request.method == 'POST':
        # Process form data
        residence_id.status = request.POST.get('status')
        residence_id.outgoing_number = request.POST.get('outgoing_number')
        residence_id.outgoing_date = request.POST.get('outgoing_date') or None
        residence_id.id_number = request.POST.get('id_number')
        residence_id.issue_date = request.POST.get('issue_date') or None
        residence_id.expiry_date = request.POST.get('expiry_date') or None
        residence_id.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث هوية إقامة للعامل {residence_id.worker.first_name} {residence_id.worker.last_name}',
            activity_type='residence_id_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث هوية الإقامة بنجاح')
        return redirect('procedures:residence_id_detail', pk=residence_id.id)

    context = {
        'page_title': f'تعديل هوية إقامة | {residence_id.worker.first_name} {residence_id.worker.last_name}',
        'residence_id': residence_id,
    }

    return render(request, 'procedures/residence_id_form.html', context)

@login_required
def residence_id_delete(request, pk):
    """View function for deleting a residence ID."""
    residence_id = get_object_or_404(ResidenceID, id=pk)
    worker = residence_id.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف هوية إقامة للعامل {residence_id.worker.first_name} {residence_id.worker.last_name}',
        activity_type='residence_id_deleted',
        date_created=timezone.now()
    )

    residence_id.delete()
    messages.success(request, 'تم حذف هوية الإقامة بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Branch Transfer Views
@login_required
def branch_transfer_create(request, worker_id):
    """View function for creating a branch transfer."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        # Process form data
        status = request.POST.get('status')
        outgoing_number = request.POST.get('outgoing_number')
        outgoing_date = request.POST.get('outgoing_date') or None
        transfer_date = request.POST.get('transfer_date') or None
        destination_branch = request.POST.get('destination_branch')
        reason = request.POST.get('reason')
        notes = request.POST.get('notes')

        # Create branch transfer
        branch_transfer = BranchTransfer.objects.create(
            worker=worker,
            status=status,
            outgoing_number=outgoing_number,
            outgoing_date=outgoing_date,
            transfer_date=transfer_date,
            destination_branch=destination_branch,
            reason=reason,
            notes=notes,
            created_by=request.user
        )

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة نقل إلى فرع آخر للعامل {worker.first_name} {worker.last_name}',
            activity_type='branch_transfer_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة النقل إلى فرع آخر بنجاح')
        return redirect('procedures:branch_transfer_detail', pk=branch_transfer.id)

    context = {
        'page_title': f'إضافة نقل إلى فرع آخر | {worker.first_name} {worker.last_name}',
        'worker': worker,
    }

    return render(request, 'procedures/branch_transfer_form.html', context)

@login_required
def branch_transfer_detail(request, pk):
    """View function for viewing a branch transfer's details."""
    branch_transfer = get_object_or_404(BranchTransfer, id=pk)
    attachments = branch_transfer.attachments.all()

    context = {
        'page_title': f'تفاصيل النقل إلى فرع آخر | {branch_transfer.worker.first_name} {branch_transfer.worker.last_name}',
        'branch_transfer': branch_transfer,
        'attachments': attachments,
    }

    return render(request, 'procedures/branch_transfer_detail.html', context)

@login_required
def branch_transfer_update(request, pk):
    """View function for updating a branch transfer."""
    branch_transfer = get_object_or_404(BranchTransfer, id=pk)

    if request.method == 'POST':
        # Process form data
        branch_transfer.status = request.POST.get('status')
        branch_transfer.outgoing_number = request.POST.get('outgoing_number')
        branch_transfer.outgoing_date = request.POST.get('outgoing_date') or None
        branch_transfer.transfer_date = request.POST.get('transfer_date') or None
        branch_transfer.destination_branch = request.POST.get('destination_branch')
        branch_transfer.reason = request.POST.get('reason')
        branch_transfer.notes = request.POST.get('notes')
        branch_transfer.save()

        # Log activity
        Activity.objects.create(
            description=f'تم تحديث نقل إلى فرع آخر للعامل {branch_transfer.worker.first_name} {branch_transfer.worker.last_name}',
            activity_type='branch_transfer_updated',
            date_created=timezone.now()
        )

        messages.success(request, 'تم تحديث النقل إلى فرع آخر بنجاح')
        return redirect('procedures:branch_transfer_detail', pk=branch_transfer.id)

    context = {
        'page_title': f'تعديل نقل إلى فرع آخر | {branch_transfer.worker.first_name} {branch_transfer.worker.last_name}',
        'branch_transfer': branch_transfer,
    }

    return render(request, 'procedures/branch_transfer_form.html', context)

@login_required
def branch_transfer_delete(request, pk):
    """View function for deleting a branch transfer."""
    branch_transfer = get_object_or_404(BranchTransfer, id=pk)
    worker = branch_transfer.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('procedures:worker_procedures', worker_id=worker.id)

    # Log activity
    Activity.objects.create(
        description=f'تم حذف نقل إلى فرع آخر للعامل {branch_transfer.worker.first_name} {branch_transfer.worker.last_name}',
        activity_type='branch_transfer_deleted',
        date_created=timezone.now()
    )

    branch_transfer.delete()
    messages.success(request, 'تم حذف النقل إلى فرع آخر بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('procedures:worker_procedures', worker_id=worker.id)

# Attachment Views
@login_required
def add_attachment(request, procedure_type, procedure_id):
    """View function for adding an attachment to a procedure."""
    # Get the procedure object based on the procedure type
    procedure = None
    if procedure_type == 'blood-test':
        procedure = get_object_or_404(BloodTest, id=procedure_id)
    elif procedure_type == 'sponsorship-transfer':
        procedure = get_object_or_404(SponsorshipTransfer, id=procedure_id)
    elif procedure_type == 'operation':
        procedure = get_object_or_404(Operation, id=procedure_id)
    elif procedure_type == 'work-permit':
        procedure = get_object_or_404(WorkPermit, id=procedure_id)
    elif procedure_type == 'residence-id':
        procedure = get_object_or_404(ResidenceID, id=procedure_id)
    elif procedure_type == 'branch-transfer':
        procedure = get_object_or_404(BranchTransfer, id=procedure_id)
    else:
        messages.error(request, 'نوع الإجراء غير صحيح')
        return redirect('procedures:procedure_list')

    if request.method == 'POST':
        # Process form data
        file = request.FILES.get('file')
        description = request.POST.get('description')

        if not file:
            messages.error(request, 'يرجى تحديد ملف للتحميل')
            return redirect(request.path)

        # Create attachment based on procedure type
        attachment = ProcedureAttachment(uploaded_by=request.user, description=description, file=file)

        if procedure_type == 'blood-test':
            attachment.blood_test = procedure
        elif procedure_type == 'sponsorship-transfer':
            attachment.sponsorship_transfer = procedure
        elif procedure_type == 'operation':
            attachment.operation = procedure
        elif procedure_type == 'work-permit':
            attachment.work_permit = procedure
        elif procedure_type == 'residence-id':
            attachment.residence_id = procedure
        elif procedure_type == 'branch-transfer':
            attachment.branch_transfer = procedure

        attachment.save()

        # Log activity
        Activity.objects.create(
            description=f'تم إضافة مرفق للعامل {procedure.worker.first_name} {procedure.worker.last_name}',
            activity_type='attachment_added',
            date_created=timezone.now()
        )

        messages.success(request, 'تم إضافة المرفق بنجاح')

        # Redirect to the appropriate detail page
        if procedure_type == 'blood-test':
            return redirect('procedures:blood_test_detail', pk=procedure.id)
        elif procedure_type == 'sponsorship-transfer':
            return redirect('procedures:sponsorship_transfer_detail', pk=procedure.id)
        elif procedure_type == 'operation':
            return redirect('procedures:operation_detail', pk=procedure.id)
        elif procedure_type == 'work-permit':
            return redirect('procedures:work_permit_detail', pk=procedure.id)
        elif procedure_type == 'residence-id':
            return redirect('procedures:residence_id_detail', pk=procedure.id)
        elif procedure_type == 'branch-transfer':
            return redirect('procedures:branch_transfer_detail', pk=procedure.id)

    context = {
        'page_title': f'إضافة مرفق | {procedure.worker.first_name} {procedure.worker.last_name}',
        'procedure': procedure,
        'procedure_type': procedure_type,
    }

    return render(request, 'procedures/add_attachment.html', context)

@login_required
def delete_attachment(request, pk):
    """View function for deleting an attachment."""
    attachment = get_object_or_404(ProcedureAttachment, id=pk)

    # Determine the procedure type and ID for redirection
    procedure = None
    redirect_url = 'procedures:procedure_list'

    if attachment.blood_test:
        procedure = attachment.blood_test
        redirect_url = 'procedures:blood_test_detail'
    elif attachment.sponsorship_transfer:
        procedure = attachment.sponsorship_transfer
        redirect_url = 'procedures:sponsorship_transfer_detail'
    elif attachment.operation:
        procedure = attachment.operation
        redirect_url = 'procedures:operation_detail'
    elif attachment.work_permit:
        procedure = attachment.work_permit
        redirect_url = 'procedures:work_permit_detail'
    elif attachment.residence_id:
        procedure = attachment.residence_id
        redirect_url = 'procedures:residence_id_detail'
    elif attachment.branch_transfer:
        procedure = attachment.branch_transfer
        redirect_url = 'procedures:branch_transfer_detail'

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        elif procedure:
            return redirect(redirect_url, pk=procedure.id)
        else:
            return redirect('procedures:procedure_list')

    # Log activity
    Activity.objects.create(
        description=f'تم حذف مرفق للعامل {procedure.worker.first_name} {procedure.worker.last_name}',
        activity_type='attachment_deleted',
        date_created=timezone.now()
    )

    attachment.delete()
    messages.success(request, 'تم حذف المرفق بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    elif procedure:
        return redirect(redirect_url, pk=procedure.id)
    else:
        return redirect('procedures:procedure_list')