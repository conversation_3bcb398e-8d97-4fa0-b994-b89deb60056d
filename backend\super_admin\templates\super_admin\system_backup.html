{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}النسخ الاحتياطي للنظام{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 mb-6">
        <h1 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white">النسخ الاحتياطي للنظام</h1>
        <p class="text-gray-600 dark:text-gray-300 mb-6">إدارة النسخ الاحتياطية لقواعد بيانات الشركات</p>

        <!-- قسم إنشاء نسخة احتياطية جديدة -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h2 class="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2">إنشاء نسخة احتياطية جديدة</h2>
            <form id="create-backup-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الشركة</label>
                        <select id="company_id" name="company_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">اختر الشركة</option>
                            {% for company in companies %}
                            <option value="{{ company.id }}">{{ company.name }} ({{ company.database_name }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وصف النسخة الاحتياطية</label>
                        <input type="text" id="description" name="description" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="وصف اختياري للنسخة الاحتياطية">
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200">
                        <i class="fas fa-database mr-2"></i>إنشاء نسخة احتياطية
                    </button>
                </div>
            </form>
        </div>

        <!-- قسم تصفية النسخ الاحتياطية -->
        <div class="bg-gray-50 dark:bg-gray-700/30 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6">
            <h2 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">تصفية النسخ الاحتياطية</h2>
            <form id="filter-backups-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="filter_company_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الشركة</label>
                        <select id="filter_company_id" name="company_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">جميع الشركات</option>
                            {% for company in companies %}
                            <option value="{{ company.id }}" {% if request.GET.company_id == company.id|stringformat:"i" %}selected{% endif %}>{{ company.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="filter_backup_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع النسخة الاحتياطية</label>
                        <select id="filter_backup_type" name="backup_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">جميع الأنواع</option>
                            {% for key, value in backup_types.items %}
                            <option value="{{ key }}" {% if request.GET.backup_type == key %}selected{% endif %}>{{ value }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="filter_date_range" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نطاق التاريخ</label>
                        <div class="flex space-x-2 rtl:space-x-reverse">
                            <input type="date" id="filter_start_date" name="start_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ request.GET.start_date }}">
                            <input type="date" id="filter_end_date" name="end_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ request.GET.end_date }}">
                        </div>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i>تصفية
                    </button>
                </div>
            </form>
        </div>

        <!-- جدول النسخ الاحتياطية -->
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm rounded-lg overflow-hidden">
                <thead class="bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    <tr>
                        <th class="py-3 px-4 text-right font-semibold">الشركة</th>
                        <th class="py-3 px-4 text-right font-semibold">اسم الملف</th>
                        <th class="py-3 px-4 text-right font-semibold">النوع</th>
                        <th class="py-3 px-4 text-right font-semibold">الحجم</th>
                        <th class="py-3 px-4 text-right font-semibold">تاريخ الإنشاء</th>
                        <th class="py-3 px-4 text-right font-semibold">تم الإنشاء بواسطة</th>
                        <th class="py-3 px-4 text-right font-semibold">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    {% if backups %}
                        {% for backup in backups %}
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.company.name }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.file_name }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.get_backup_type_display }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.get_size_formatted }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{ backup.created_by.username|default:"النظام" }}</td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">
                                <div class="flex space-x-2 rtl:space-x-reverse">
                                    <button onclick="if(confirm('هل أنت متأكد من استعادة قاعدة البيانات من هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) { restoreBackup('{{ backup.id }}'); }" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" title="استعادة">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="download-backup-btn text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300" data-backup-id="{{ backup.id }}" title="تنزيل">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button onclick="if(confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) { deleteBackup('{{ backup.id }}'); }" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="py-4 px-4 text-center text-gray-500 dark:text-gray-400">لا توجد نسخ احتياطية</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إنشاء نسخة احتياطية
        const createBackupForm = document.getElementById('create-backup-form');
        createBackupForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const companyId = document.getElementById('company_id').value;
            const description = document.getElementById('description').value;

            if (!companyId) {
                showAlert('يرجى اختيار الشركة', 'error');
                return;
            }

            // إرسال طلب AJAX لإنشاء نسخة احتياطية
            fetch('{% url "create_backup" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: new URLSearchParams({
                    'create_backup': 'true',
                    'company_id': companyId,
                    'description': description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    // إعادة تحميل الصفحة بعد نجاح العملية
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
            });
        });

        // تصفية النسخ الاحتياطية
        const filterForm = document.getElementById('filter-backups-form');
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const companyId = document.getElementById('filter_company_id').value;
            const backupType = document.getElementById('filter_backup_type').value;
            const startDate = document.getElementById('filter_start_date').value;
            const endDate = document.getElementById('filter_end_date').value;

            // بناء عنوان URL مع معلمات التصفية
            const params = new URLSearchParams();
            if (companyId) params.append('company_id', companyId);
            if (backupType) params.append('backup_type', backupType);
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            // إعادة تحميل الصفحة مع معلمات التصفية
            window.location.href = `{% url "system_backup" %}?${params.toString()}`;
        });

        // دالة حذف نسخة احتياطية
        function deleteBackup(backupId) {
            // إرسال طلب AJAX لحذف النسخة الاحتياطية
            fetch('{% url "system_backup" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: new URLSearchParams({
                    'delete_backup': 'true',
                    'backup_id': backupId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    // إعادة تحميل الصفحة بعد نجاح العملية
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ أثناء حذف النسخة الاحتياطية', 'error');
            });
        }

        // دالة استعادة نسخة احتياطية
        function restoreBackup(backupId) {
            // إرسال طلب AJAX لاستعادة النسخة الاحتياطية
            fetch('{% url "restore_backup" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: new URLSearchParams({
                    'restore_backup': 'true',
                    'backup_id': backupId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ أثناء استعادة النسخة الاحتياطية', 'error');
            });
        }

        // تنزيل نسخة احتياطية
        document.querySelectorAll('.download-backup-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const backupId = this.dataset.backupId;
                window.location.href = `/super_admin/backup/download/${backupId}/`;
            });
        });

        // دالة للحصول على قيمة ملف تعريف الارتباط
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // دالة لعرض رسائل التنبيه
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 left-1/2 transform -translate-x-1/2 p-4 rounded-md shadow-lg z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
    });
</script>
{% endblock %}
