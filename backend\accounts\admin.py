"""
تكوين صفحة المسؤول الافتراضية في Django
"""

from django.contrib import admin
from django.contrib.admin.sites import AdminSite
from django.shortcuts import redirect
from .models import UserCompany

# تعديل صفحة المسؤول الافتراضية لتقييد الوصول إليها
class RestrictedAdminSite(AdminSite):
    """
    موقع إدارة مقيد يسمح فقط للمستخدم المحدد (منشئ النظام) بالوصول إليه
    """

    def has_permission(self, request):
        """
        التحقق من صلاحيات الوصول إلى صفحة المسؤول
        """
        # التحقق من تسجيل الدخول
        if not request.user.is_authenticated:
            return False

        # التحقق من صلاحيات المسؤول الأعلى (منشئ النظام)
        # يمكن تعديل هذا الشرط حسب احتياجاتك لتحديد المستخدم المسموح له بالوصول
        if not (request.user.is_superuser and request.user.username == 'admin' and hasattr(request.user, 'super_admin_profile')):
            # إعادة التوجيه إلى صفحة تسجيل الدخول
            return False

        return True

    def login(self, request, extra_context=None):
        """
        تخصيص صفحة تسجيل الدخول
        """
        if request.method == 'GET' and not request.user.is_authenticated:
            # إعادة التوجيه إلى صفحة تسجيل الدخول الرئيسية
            return redirect('login')

        return super().login(request, extra_context)

# استبدال موقع الإدارة الافتراضي بالموقع المقيد
admin.site = RestrictedAdminSite(name='restricted_admin')

@admin.register(UserCompany)
class UserCompanyAdmin(admin.ModelAdmin):
    """Admin interface for UserCompany model."""
    list_display = ('user', 'company', 'is_default', 'created_at')
    list_filter = ('is_default', 'created_at', 'company__status')
    search_fields = ('user__username', 'company__name')
    raw_id_fields = ('user', 'company')
    date_hierarchy = 'created_at'
