# Generated by Django 5.2 on 2025-04-09 17:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system_settings', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='رمز العملة')),
                ('name', models.Char<PERSON><PERSON>(max_length=50, verbose_name='اسم العملة')),
                ('symbol', models.CharField(max_length=5, verbose_name='رمز العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('is_default', models.BooleanField(default=False, verbose_name='العملة الافتراضية')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعلة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عملة',
                'verbose_name_plural': 'العملات',
                'ordering': ['-is_default', 'code'],
            },
        ),
    ]
