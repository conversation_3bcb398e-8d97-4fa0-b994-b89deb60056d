{% comment %}
مكون الإشعارات المحسن
يستخدم لعرض رسائل النظام بشكل موحد وجذاب
{% endcomment %}

{% if messages %}
<div class="toast-container">
    {% for message in messages %}
        {% if message.tags == 'success' %}
            <div class="toast toast-success toast-in">
                <div class="toast-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="toast-content">{{ message }}</div>
                <button type="button" class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% elif message.tags == 'error' %}
            <div class="toast toast-error toast-in">
                <div class="toast-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="toast-content">{{ message }}</div>
                <button type="button" class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% elif message.tags == 'warning' %}
            <div class="toast toast-warning toast-in">
                <div class="toast-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="toast-content">{{ message }}</div>
                <button type="button" class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% elif message.tags == 'info' %}
            <div class="toast toast-info toast-in">
                <div class="toast-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="toast-content">{{ message }}</div>
                <button type="button" class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% else %}
            <div class="toast toast-info toast-in">
                <div class="toast-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="toast-content">{{ message }}</div>
                <button type="button" class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endif %}
    {% endfor %}
</div>
{% endif %}


