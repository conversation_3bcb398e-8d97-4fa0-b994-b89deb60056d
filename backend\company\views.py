from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.contrib import messages
from django.http import JsonResponse
from datetime import timedelta

# لوحة تحكم الشركة
@login_required
def company_dashboard(request):
    """عرض لوحة تحكم الشركة"""
    # التحقق من أن المستخدم ينتمي إلى شركة
    if not hasattr(request.user, 'company_profile'):
        messages.error(request, 'ليس لديك صلاحيات للوصول إلى لوحة تحكم الشركة')
        return redirect('company:login')

    # الحصول على بيانات الشركة
    company = request.user.company_profile.company

    # الحصول على الإحصائيات الحقيقية من قاعدة البيانات
    from backend.dashboard.views import dashboard_statistics_api, activities_api

    # الحصول على الإحصائيات من API
    stats_response = dashboard_statistics_api(request)
    stats = {}

    if hasattr(stats_response, 'content'):
        import json
        try:
            stats = json.loads(stats_response.content)
        except Exception as e:
            print(f"خطأ في تحليل بيانات الإحصائيات: {str(e)}")

    # الحصول على الأنشطة الأخيرة من API
    activities_response = activities_api(request)
    recent_activities = []

    if hasattr(activities_response, 'content'):
        import json
        try:
            activities_data = json.loads(activities_response.content)
            recent_activities = activities_data.get('activities', [])[:4]  # أخذ آخر 4 أنشطة فقط
        except Exception as e:
            print(f"خطأ في تحليل بيانات الأنشطة: {str(e)}")

    context = {
        'company': company,
        'stats': stats,
        'recent_activities': recent_activities,
        'current_date': timezone.now(),
    }

    return render(request, 'company/dashboard.html', context)

# صفحة العمال
@login_required
def workers(request):
    """عرض صفحة العمال"""
    return render(request, 'company/workers.html')

# صفحة العملاء
@login_required
def clients(request):
    """عرض صفحة العملاء"""
    return render(request, 'company/clients.html')

# صفحة العقود
@login_required
def contracts(request):
    """عرض صفحة العقود"""
    return render(request, 'company/contracts.html')

# صفحة الخدمات
@login_required
def services(request):
    """عرض صفحة الخدمات"""
    return render(request, 'company/services.html')

# صفحة التقارير
@login_required
def reports(request):
    """عرض صفحة التقارير"""
    return render(request, 'company/reports.html')

# صفحة الملف الشخصي
@login_required
def profile(request):
    """عرض صفحة الملف الشخصي"""
    return render(request, 'company/profile.html')

# صفحة الإعدادات
@login_required
def settings(request):
    """عرض صفحة الإعدادات"""
    return render(request, 'company/settings.html')

# تسجيل الخروج
@login_required
def logout(request):
    """تسجيل الخروج من النظام"""
    from django.contrib.auth import logout as auth_logout
    auth_logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('company:login')
