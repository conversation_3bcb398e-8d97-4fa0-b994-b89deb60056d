#!/usr/bin/env python3
"""
مجدول النسخ الاحتياطي التلقائي
يمكن تشغيله كخدمة أو جدولته باستخدام cron

استخدام:
    python scripts/backup_scheduler.py --daily
    python scripts/backup_scheduler.py --weekly  
    python scripts/backup_scheduler.py --monthly
"""

import os
import sys
import django
import schedule
import time
import logging
from datetime import datetime, timedelta
import argparse

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.labor_management.settings')
django.setup()

from django.core.management import call_command
from backend.super_admin.models import Company, SystemSettings, DatabaseBackup
from backend.super_admin.backup_utils import create_comprehensive_backup_for_all_companies

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/backups/backup_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BackupScheduler:
    """مجدول النسخ الاحتياطي التلقائي"""
    
    def __init__(self):
        self.logger = logger
        
    def daily_backup(self):
        """نسخة احتياطية يومية"""
        self.logger.info("🌅 بدء النسخة الاحتياطية اليومية")
        try:
            # تنظيف النسخ القديمة أولاً (الاحتفاظ بـ 7 أيام للنسخ اليومية)
            call_command('backup_companies', '--clean-old', '--retention-days=7')
            
            # إنشاء نسخ احتياطية لجميع الشركات النشطة
            success_count, failed_count, messages = create_comprehensive_backup_for_all_companies()
            
            self.logger.info(f"✅ النسخة الاحتياطية اليومية مكتملة - نجح: {success_count}, فشل: {failed_count}")
            
            # إرسال تقرير إذا كان هناك فشل
            if failed_count > 0:
                self.send_failure_notification(messages)
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في النسخة الاحتياطية اليومية: {str(e)}")
            
    def weekly_backup(self):
        """نسخة احتياطية أسبوعية"""
        self.logger.info("📅 بدء النسخة الاحتياطية الأسبوعية")
        try:
            # تنظيف النسخ القديمة (الاحتفاظ بـ 4 أسابيع للنسخ الأسبوعية)
            call_command('backup_companies', '--clean-old', '--retention-days=28')
            
            # إنشاء نسخ احتياطية أسبوعية
            call_command('backup_companies', '--all-companies', '--backup-type=weekly')
            
            self.logger.info("✅ النسخة الاحتياطية الأسبوعية مكتملة")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في النسخة الاحتياطية الأسبوعية: {str(e)}")
            
    def monthly_backup(self):
        """نسخة احتياطية شهرية"""
        self.logger.info("🗓️ بدء النسخة الاحتياطية الشهرية")
        try:
            # تنظيف النسخ القديمة (الاحتفاظ بـ 12 شهر للنسخ الشهرية)
            call_command('backup_companies', '--clean-old', '--retention-days=365')
            
            # إنشاء نسخ احتياطية شهرية
            call_command('backup_companies', '--all-companies', '--backup-type=monthly')
            
            self.logger.info("✅ النسخة الاحتياطية الشهرية مكتملة")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في النسخة الاحتياطية الشهرية: {str(e)}")
            
    def send_failure_notification(self, messages):
        """إرسال إشعار في حالة فشل النسخ الاحتياطي"""
        try:
            # يمكن إضافة إرسال إيميل أو إشعار هنا
            failure_messages = [msg for msg in messages if msg.startswith('❌')]
            self.logger.warning(f"⚠️ فشل في {len(failure_messages)} نسخة احتياطية")
            for msg in failure_messages:
                self.logger.warning(msg)
        except Exception as e:
            self.logger.error(f"خطأ في إرسال الإشعار: {str(e)}")
            
    def get_backup_statistics(self):
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            total_backups = DatabaseBackup.objects.count()
            total_size = sum(backup.file_size for backup in DatabaseBackup.objects.all())
            
            # النسخ الاحتياطية في آخر 24 ساعة
            yesterday = datetime.now() - timedelta(days=1)
            recent_backups = DatabaseBackup.objects.filter(created_at__gte=yesterday).count()
            
            stats = {
                'total_backups': total_backups,
                'total_size_gb': total_size / (1024*1024*1024),
                'recent_backups': recent_backups
            }
            
            self.logger.info(f"📊 إحصائيات: {total_backups} نسخة، {stats['total_size_gb']:.2f} GB، {recent_backups} نسخة حديثة")
            return stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}
            
    def run_scheduler(self, mode='daily'):
        """تشغيل المجدول"""
        self.logger.info(f"🚀 بدء مجدول النسخ الاحتياطي - وضع: {mode}")
        
        if mode == 'daily':
            # تشغيل يومي في الساعة 2:00 صباحاً
            schedule.every().day.at("02:00").do(self.daily_backup)
            self.logger.info("⏰ تم جدولة النسخ الاحتياطي اليومي في الساعة 2:00 صباحاً")
            
        elif mode == 'weekly':
            # تشغيل أسبوعي يوم الأحد في الساعة 3:00 صباحاً
            schedule.every().sunday.at("03:00").do(self.weekly_backup)
            self.logger.info("⏰ تم جدولة النسخ الاحتياطي الأسبوعي يوم الأحد في الساعة 3:00 صباحاً")
            
        elif mode == 'monthly':
            # تشغيل شهري في اليوم الأول من كل شهر
            schedule.every().month.do(self.monthly_backup)
            self.logger.info("⏰ تم جدولة النسخ الاحتياطي الشهري في اليوم الأول من كل شهر")
            
        elif mode == 'all':
            # جدولة جميع الأنواع
            schedule.every().day.at("02:00").do(self.daily_backup)
            schedule.every().sunday.at("03:00").do(self.weekly_backup)
            schedule.every().month.do(self.monthly_backup)
            self.logger.info("⏰ تم جدولة جميع أنواع النسخ الاحتياطي")
            
        # تشغيل المجدول
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

def main():
    parser = argparse.ArgumentParser(description='مجدول النسخ الاحتياطي التلقائي')
    parser.add_argument('--daily', action='store_true', help='تشغيل النسخ الاحتياطي اليومي')
    parser.add_argument('--weekly', action='store_true', help='تشغيل النسخ الاحتياطي الأسبوعي')
    parser.add_argument('--monthly', action='store_true', help='تشغيل النسخ الاحتياطي الشهري')
    parser.add_argument('--all', action='store_true', help='تشغيل جميع أنواع النسخ الاحتياطي')
    parser.add_argument('--run-once', action='store_true', help='تشغيل مرة واحدة فقط')
    parser.add_argument('--stats', action='store_true', help='عرض الإحصائيات فقط')
    
    args = parser.parse_args()
    
    scheduler = BackupScheduler()
    
    if args.stats:
        scheduler.get_backup_statistics()
        return
        
    if args.run_once:
        if args.daily:
            scheduler.daily_backup()
        elif args.weekly:
            scheduler.weekly_backup()
        elif args.monthly:
            scheduler.monthly_backup()
        else:
            scheduler.daily_backup()  # افتراضي
        return
    
    # تشغيل المجدول
    if args.daily:
        scheduler.run_scheduler('daily')
    elif args.weekly:
        scheduler.run_scheduler('weekly')
    elif args.monthly:
        scheduler.run_scheduler('monthly')
    elif args.all:
        scheduler.run_scheduler('all')
    else:
        print("يرجى تحديد نوع النسخ الاحتياطي: --daily, --weekly, --monthly, أو --all")

if __name__ == '__main__':
    main()
