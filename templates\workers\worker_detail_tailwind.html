{% extends 'base_tailwind.html' %}
{% load static %}

{% block title %}تفاصيل العامل | {{ worker.first_name }} {{ worker.last_name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تفاصيل العامل</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">عرض كافة بيانات العامل</p>
        </div>
        <div class="flex flex-wrap gap-2">
            <a href="{% url 'workers:worker_list' %}"
               class="bg-white dark:bg-[#1E293B] border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-[#0F172A] transition-all duration-300 flex items-center shadow-sm hover:shadow-md">
                <i class="fas fa-arrow-right ml-2"></i>
                <span>العودة</span>
            </a>
            <a href="{% url 'workers:worker_update' worker.id %}"
               class="bg-white dark:bg-[#1E293B] border-2 border-blue-500 dark:border-blue-600 text-blue-500 dark:text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600 transition-all duration-300 flex items-center shadow-sm hover:shadow-md">
                <i class="fas fa-edit ml-2"></i>
                <span>تعديل</span>
            </a>
            <a href="{% url 'workers:worker_documents' worker.id %}"
               class="bg-white dark:bg-[#1E293B] border-2 border-green-500 dark:border-green-600 text-green-500 dark:text-green-400 px-4 py-2 rounded-lg hover:bg-green-500 hover:text-white dark:hover:bg-green-600 transition-all duration-300 flex items-center shadow-sm hover:shadow-md">
                <i class="fas fa-file-alt ml-2"></i>
                <span>المستندات</span>
            </a>
            <a href="{% url 'workers:worker_delete' worker.id %}"
                   class="bg-white dark:bg-[#1E293B] border-2 border-red-500 dark:border-red-600 text-red-500 dark:text-red-400 px-4 py-2 rounded-lg hover:bg-red-500 hover:text-white dark:hover:bg-red-600 transition-all duration-300 flex items-center shadow-sm hover:shadow-md">
                <i class="fas fa-trash ml-2"></i>
                <span>حذف</span>
            </a>
        </div>
    </div>

    <!-- Worker Type Badge -->
    <div class="mb-6">
        {% if worker.worker_type == 'contract' %}
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
            <i class="fas fa-file-contract ml-2"></i>
            <span>عامل عقد دائم</span>
        </div>
        {% elif worker.worker_type == 'custom' %}
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200">
            <i class="fas fa-tools ml-2"></i>
            <span>عامل خدمة مخصصة</span>
        </div>
        {% elif worker.worker_type == 'monthly' %}
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
            <i class="fas fa-calendar-alt ml-2"></i>
            <span>عامل عقد شهري</span>
        </div>
        {% endif %}

        {% if worker.status == 'active' %}
        <div class="inline-flex items-center px-3 py-1 rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 mr-2">
            <i class="fas fa-check-circle ml-1"></i>
            <span>نشط</span>
        </div>
        {% elif worker.status == 'inactive' %}
        <div class="inline-flex items-center px-3 py-1 rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 mr-2">
            <i class="fas fa-times-circle ml-1"></i>
            <span>غير نشط</span>
        </div>
        {% elif worker.status == 'on_leave' %}
        <div class="inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 mr-2">
            <i class="fas fa-plane ml-1"></i>
            <span>في إجازة</span>
        </div>
        {% elif worker.status == 'terminated' %}
        <div class="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 mr-2">
            <i class="fas fa-ban ml-1"></i>
            <span>منتهي الخدمة</span>
        </div>
        {% endif %}
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Personal Information Card -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">
                    <i class="fas fa-user ml-2"></i>المعلومات الشخصية
                </h3>

                <div class="flex flex-col items-center mb-6">
                    {% if worker.photo %}
                    <img src="{{ worker.photo.url }}" alt="{{ worker.first_name }} {{ worker.last_name }}"
                         class="w-32 h-32 rounded-full object-cover border-4 border-blue-100 dark:border-blue-900 mb-3">
                    {% else %}
                    <div class="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mb-3">
                        <i class="fas fa-user text-4xl text-gray-400 dark:text-gray-500"></i>
                    </div>
                    {% endif %}
                    <h4 class="text-xl font-bold text-gray-800 dark:text-white">{{ worker.first_name }} {{ worker.last_name }}</h4>
                    <p class="text-gray-600 dark:text-gray-400">{{ worker.job_title|default:"لا توجد مهنة محددة" }}</p>
                </div>

                <div class="space-y-3">
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">الجنس</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.get_gender_display }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">الجنسية</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.get_nationality_display }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ الميلاد</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.date_of_birth|date:"Y-m-d"|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">رقم الهاتف</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.phone_number|default:"-" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passport Information Card -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">
                    <i class="fas fa-passport ml-2"></i>معلومات جواز السفر والتأشيرة
                </h3>

                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center">
                        {% if worker.passport_image %}
                        <a href="{{ worker.passport_image.url }}" target="_blank" class="block">
                            <img src="{{ worker.passport_image.url }}" alt="صورة جواز السفر"
                                 class="w-full h-24 object-cover rounded-lg mb-2">
                            <span class="text-sm text-gray-600 dark:text-gray-400">صورة جواز السفر</span>
                        </a>
                        {% else %}
                        <div class="w-full h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                            <i class="fas fa-passport text-2xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">لا توجد صورة للجواز</span>
                        {% endif %}
                    </div>
                    <div class="text-center">
                        {% if worker.visa_image %}
                        <a href="{{ worker.visa_image.url }}" target="_blank" class="block">
                            <img src="{{ worker.visa_image.url }}" alt="صورة التأشيرة"
                                 class="w-full h-24 object-cover rounded-lg mb-2">
                            <span class="text-sm text-gray-600 dark:text-gray-400">صورة التأشيرة</span>
                        </a>
                        {% else %}
                        <div class="w-full h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                            <i class="fas fa-id-card text-2xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">لا توجد صورة للتأشيرة</span>
                        {% endif %}
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">رقم جواز السفر</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.passport_number|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ إصدار جواز السفر</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.passport_issue_date|date:"Y-m-d"|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ انتهاء جواز السفر</span>
                        <span class="font-medium text-gray-800 dark:text-white">
                            {{ worker.passport_expiry|date:"Y-m-d"|default:"-" }}
                            {% if worker.passport_expiry and worker.passport_expiry < today %}
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 mr-1">منتهي</span>
                            {% elif worker.passport_expiry and worker.passport_expiry < expiry_warning %}
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 mr-1">قريب من الانتهاء</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ إصدار التأشيرة / الستيكر</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.entry_date|date:"Y-m-d"|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">رقم التأشيرة</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.visa_number|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ انتهاء التأشيرة</span>
                        <span class="font-medium text-gray-800 dark:text-white">
                            {{ worker.visa_expiry|date:"Y-m-d"|default:"-" }}
                            {% if worker.visa_expiry and worker.visa_expiry < today %}
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 mr-1">منتهية</span>
                            {% elif worker.visa_expiry and worker.visa_expiry < expiry_warning %}
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 mr-1">قريبة من الانتهاء</span>
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Information Card -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">
                    <i class="fas fa-briefcase ml-2"></i>معلومات العمل
                </h3>

                <div class="space-y-3">
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ الانضمام</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.date_joined|date:"Y-m-d"|default:"-" }}</span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
                        <span class="text-gray-600 dark:text-gray-400">جهة طلب سمة الدخول</span>
                        <span class="font-medium text-gray-800 dark:text-white">
                            {% if worker.recruitment_company %}
                            {{ worker.recruitment_company.name }} ({{ worker.recruitment_company.country }})
                            {% else %}
                            -
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الراتب</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ worker.salary|default:"-" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    <div class="mt-6 bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">
                <i class="fas fa-sticky-note ml-2"></i>ملاحظات
            </h3>

            <div class="bg-gray-50 dark:bg-[#0F172A] p-4 rounded-lg">
                {% if worker.notes %}
                <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ worker.notes }}</p>
                {% else %}
                <p class="text-gray-500 dark:text-gray-400">لا توجد ملاحظات.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
    // تم إزالة وظائف JavaScript غير المستخدمة
</script>
{% endblock %}
