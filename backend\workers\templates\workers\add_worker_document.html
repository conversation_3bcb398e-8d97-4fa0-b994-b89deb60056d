{% extends 'base_tailwind.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- زر العودة إلى قائمة المستندات -->
    <div class="mb-4">
        <a href="{% url 'workers:worker_documents' worker.id %}" class="bg-white dark:bg-[#1E293B] border-2 border-blue-500 dark:border-blue-600 text-blue-500 dark:text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600 transition-all duration-300 flex items-center shadow-sm hover:shadow-md inline-flex">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة إلى قائمة المستندات</span>
        </a>
    </div>

    <!-- نموذج إضافة مستند -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
        <div class="bg-blue-600 dark:bg-blue-800 p-4 text-white">
            <h5 class="font-bold flex items-center"><i class="fas fa-file-upload ml-2"></i>إضافة مستند جديد</h5>
        </div>
        <div class="p-6">
            <div class="mb-6">
                <div class="bg-blue-50 dark:bg-blue-900/20 border-r-4 border-blue-500 dark:border-blue-600 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-500 dark:text-blue-400"></i>
                        </div>
                        <div class="mr-3">
                            <h5 class="text-blue-700 dark:text-blue-300 font-bold">معلومات العامل</h5>
                            <p class="text-blue-700 dark:text-blue-300">
                                <strong>الاسم:</strong> {{ worker.first_name }} {{ worker.last_name }} |
                                <strong>رقم جواز السفر:</strong> {{ worker.passport_number }} |
                                <strong>الجنسية:</strong> {{ worker.get_nationality_display }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.document_type.id_for_label }}" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ form.document_type.label }}</label>
                        {{ form.document_type }}
                        {% if form.document_type.errors %}
                            <div class="text-red-500 mt-1">
                                {% for error in form.document_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.title.id_for_label }}" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ form.title.label }}</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-red-500 mt-1">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.file.id_for_label }}" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ form.file.label }}</label>
                        {{ form.file }}
                        {% if form.file.errors %}
                            <div class="text-red-500 mt-1">
                                {% for error in form.file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.expiry_date.id_for_label }}" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ form.expiry_date.label }}</label>
                        {{ form.expiry_date }}
                        {% if form.expiry_date.errors %}
                            <div class="text-red-500 mt-1">
                                {% for error in form.expiry_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="md:col-span-2">
                        <label for="{{ form.notes.id_for_label }}" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-red-500 mt-1">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="flex justify-end mt-6 space-x-4 space-x-reverse">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-save ml-2"></i> حفظ
                    </button>
                    <a href="{% url 'workers:worker_documents' worker.id %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-times ml-2"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
