from django import forms
from django.contrib.auth.forms import AuthenticationForm

class DatabaseLoginForm(AuthenticationForm):
    """نموذج تسجيل الدخول مع الرقم التسلسلي لقاعدة البيانات"""

    # حقل الرقم التسلسلي لقاعدة البيانات
    serial_number = forms.CharField(
        label='الرقم التسلسلي لقاعدة البيانات',
        widget=forms.TextInput(attrs={
            'class': 'w-full pr-10 pl-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center',
            'placeholder': 'XXXX-XXXX-XXXX-XXXX',
            'pattern': '[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}',
            'title': 'الرقم التسلسلي يجب أن يكون بتنسيق XXXX-XXXX-XXXX-XXXX',
            'id': 'serial_number',
            'autocomplete': 'off',
            'maxlength': '19',
            'data-mask': 'XXXX-XXXX-XXXX-XXXX',
            'dir': 'ltr',
            'style': 'font-family: monospace; letter-spacing: 2px; font-weight: 600;'
        })
    )

    # حقول معلومات قاعدة البيانات
    db_name = forms.CharField(
        label='اسم قاعدة البيانات',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل اسم قاعدة البيانات',
            'id': 'db_name',
            'autocomplete': 'off',
            'dir': 'ltr'
        })
    )

    db_user = forms.CharField(
        label='اسم مستخدم قاعدة البيانات',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل اسم مستخدم قاعدة البيانات',
            'id': 'db_user',
            'autocomplete': 'off',
            'dir': 'ltr'
        })
    )

    db_password = forms.CharField(
        label='كلمة مرور قاعدة البيانات',
        required=False,
        widget=forms.PasswordInput(attrs={
            'class': 'w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل كلمة مرور قاعدة البيانات',
            'id': 'db_password',
            'autocomplete': 'off',
            'dir': 'ltr'
        })
    )

    db_host = forms.CharField(
        label='مضيف قاعدة البيانات',
        required=False,
        initial='localhost',
        widget=forms.TextInput(attrs={
            'class': 'w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل مضيف قاعدة البيانات',
            'id': 'db_host',
            'autocomplete': 'off',
            'dir': 'ltr'
        })
    )

    # متغيرات إضافية للقالب
    username_label = 'اسم المستخدم'
    password_label = 'كلمة المرور'
    serial_label = 'الرقم التسلسلي'
    login_title = 'تسجيل الدخول'
    login_button_text = 'تسجيل الدخول'
    create_account_text = 'إنشاء حساب جديد'
    help_text = 'يرجى إدخال بيانات تسجيل الدخول الخاصة بك للوصول إلى النظام'
    copyright_text = 'جميع الحقوق محفوظة'
    return_link_text = 'العودة إلى صفحة تسجيل الدخول الأصلية'
    company_name = 'نظام إدارة العمالة'
    company_subtitle = 'Labor Management System'
    info_title = 'نظام إدارة العمالة'
    info_subtitle = 'النظام المتكامل لإدارة العمالة والموارد البشرية'
    info_text1 = 'مرحباً بكم في نظام إدارة العمالة المتكامل'
    info_text2 = 'النظام الأمثل لإدارة الموارد البشرية وشؤون العاملين'
    tab1_text = 'الإحصائيات'
    tab2_text = 'المساعدة'
    tab3_text = 'تسجيل الدخول'
    tab4_text = 'English'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعديل حقول النموذج الأصلي
        self.fields['username'].widget.attrs.update({
            'class': 'w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل اسم المستخدم',
            'id': 'username',
            'autocomplete': 'username',
            'autofocus': True,
            'dir': 'ltr'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'w-full pr-10 pl-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'أدخل كلمة المرور',
            'id': 'password',
            'autocomplete': 'current-password',
            'minlength': '8',
            'dir': 'ltr'
        })

    def clean_serial_number(self):
        """التحقق من تنسيق الرقم التسلسلي"""
        serial_number = self.cleaned_data.get('serial_number')
        if serial_number:
            # إزالة أي مسافات
            serial_number = serial_number.strip()

            # التحقق من التنسيق
            import re
            if not re.match(r'^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$', serial_number):
                # محاولة تنسيق الرقم التسلسلي إذا كان بدون شرطات
                if re.match(r'^[A-Z0-9]{16}$', serial_number.replace('-', '')):
                    clean_serial = serial_number.replace('-', '')
                    serial_number = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])
                else:
                    raise forms.ValidationError('الرقم التسلسلي يجب أن يكون بتنسيق XXXX-XXXX-XXXX-XXXX')

        return serial_number
