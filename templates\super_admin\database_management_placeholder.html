{% extends 'layouts/super_admin.html' %}

{% block title %}إدارة قواعد البيانات{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-database ml-2"></i>
                    إدارة قواعد البيانات
                </h1>
                <p class="text-blue-100">
                    إدارة وصيانة قواعد بيانات الشركات المسجلة في النظام
                </p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold">{{ companies.count }}</div>
                <div class="text-sm text-blue-100">قاعدة بيانات</div>
            </div>
        </div>
    </div>

    <!-- Under Development Notice -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-tools text-yellow-600 text-2xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-yellow-800">
                    قسم تحت التطوير
                </h3>
                <p class="text-yellow-700 mt-1">
                    هذا القسم قيد التطوير حالياً. سيتم إضافة المزيد من الميزات قريباً.
                </p>
                <div class="mt-4">
                    <h4 class="font-medium text-yellow-800 mb-2">الميزات المخططة:</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> عرض هيكل قواعد البيانات</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> تصفير قواعد البيانات</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> نسخ هياكل قواعد البيانات</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> إنشاء قواعد بيانات فارغة</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> إحصائيات مفصلة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="admin-card rounded-xl p-6 shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-2">نسخ احتياطية شاملة</h3>
                    <p class="text-gray-300 text-sm">إنشاء نسخ احتياطية لجميع الشركات</p>
                </div>
                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-download text-white text-xl"></i>
                </div>
            </div>
            <button onclick="createBackupForAllCompanies()"
                    class="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-play ml-2"></i>
                بدء النسخ الاحتياطي
            </button>
        </div>

        <div class="admin-card rounded-xl p-6 shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-2">تنظيف النسخ القديمة</h3>
                    <p class="text-gray-300 text-sm">حذف النسخ الاحتياطية القديمة</p>
                </div>
                <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-trash-alt text-white text-xl"></i>
                </div>
            </div>
            <button onclick="cleanOldBackups()"
                    class="w-full mt-4 bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-broom ml-2"></i>
                تنظيف النسخ القديمة
            </button>
        </div>

        <div class="admin-card rounded-xl p-6 shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-2">إحصائيات النسخ</h3>
                    <p class="text-gray-300 text-sm">عرض تفاصيل النسخ الاحتياطية</p>
                </div>
                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-bar text-white text-xl"></i>
                </div>
            </div>
            <button onclick="showBackupStatistics()"
                    class="w-full mt-4 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-eye ml-2"></i>
                عرض الإحصائيات
            </button>
        </div>
    </div>

    <!-- Companies List -->
    <div class="admin-card rounded-xl shadow-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-700 border-b border-gray-600">
            <h2 class="text-lg font-semibold text-white">
                <i class="fas fa-building ml-2"></i>
                قائمة الشركات المسجلة
            </h2>
        </div>

        {% if companies %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الشركة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            قاعدة البيانات
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            آخر نسخة احتياطية
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            إجراءات النسخ الاحتياطي
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for company in companies %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-building text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">{{ company.name }}</div>
                                    <div class="text-sm text-gray-500">{{ company.serial_number }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ company.database_name }}</div>
                            <div class="text-sm text-gray-500">SQLite</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if company.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    نشطة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle ml-1"></i>
                                    معطلة
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                لم يتم إنشاء نسخة بعد
                            </div>
                            <div class="text-xs text-gray-400">
                                إنشاء أول نسخة احتياطية
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button onclick="createBackupForCompany({{ company.id }})"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
                                        title="إنشاء نسخة احتياطية">
                                    <i class="fas fa-download ml-1"></i>
                                    نسخة احتياطية
                                </button>
                                <button onclick="viewCompanyBackups({{ company.id }})"
                                        class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                                        title="عرض النسخ الاحتياطية">
                                    <i class="fas fa-history ml-1"></i>
                                    النسخ السابقة
                                </button>
                                <button onclick="viewCompanyDetails({{ company.id }})"
                                        class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-xs transition-colors"
                                        title="تفاصيل الشركة">
                                    <i class="fas fa-info-circle ml-1"></i>
                                    تفاصيل
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-database text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد شركات مسجلة</h3>
            <p class="text-gray-500">لم يتم تسجيل أي شركات في النظام بعد</p>
        </div>
        {% endif %}
    </div>

    <!-- Database Management Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="admin-card rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-plus text-white text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">إنشاء قاعدة بيانات</h3>
            <p class="text-gray-300 text-sm mb-4">إنشاء قاعدة بيانات جديدة للشركة</p>
            <button onclick="showCreateDatabaseModal()"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-plus ml-2"></i>
                إنشاء قاعدة بيانات
            </button>
        </div>

        <div class="admin-card rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-copy text-white text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">نسخ الهيكل</h3>
            <p class="text-gray-300 text-sm mb-4">نسخ هيكل قاعدة بيانات موجودة</p>
            <button onclick="showCopyStructureModal()"
                    class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-copy ml-2"></i>
                نسخ الهيكل
            </button>
        </div>

        <div class="admin-card rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-trash text-white text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">تصفير البيانات</h3>
            <p class="text-gray-300 text-sm mb-4">حذف جميع البيانات مع الحفاظ على الهيكل</p>
            <button onclick="showResetDataModal()"
                    class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-trash ml-2"></i>
                تصفير البيانات
            </button>
        </div>
    </div>

    <!-- Modals -->
    <!-- Create Database Modal -->
    <div id="createDatabaseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">إنشاء قاعدة بيانات جديدة</h3>
                <button onclick="hideCreateDatabaseModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createDatabaseForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                    <input type="text" id="companyName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل اسم الشركة" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم قاعدة البيانات</label>
                    <input type="text" id="databaseName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" placeholder="company_xxx" required>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الرقم التسلسلي</label>
                    <input type="text" id="serialNumber" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" placeholder="16 رقم" maxlength="16" required>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء
                    </button>
                    <button type="button" onclick="hideCreateDatabaseModal()" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Copy Structure Modal -->
    <div id="copyStructureModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">نسخ هيكل قاعدة البيانات</h3>
                <button onclick="hideCopyStructureModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="copyStructureForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الشركة المصدر</label>
                    <select id="sourceCompany" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">اختر الشركة المصدر</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}">{{ company.name }} ({{ company.database_name }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الشركة الهدف</label>
                    <select id="targetCompany" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">اختر الشركة الهدف</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}">{{ company.name }} ({{ company.database_name }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button type="submit" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-copy ml-2"></i>
                        نسخ الهيكل
                    </button>
                    <button type="button" onclick="hideCopyStructureModal()" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reset Data Modal -->
    <div id="resetDataModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">تصفير بيانات الشركة</h3>
                <button onclick="hideResetDataModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 ml-2"></i>
                    <span class="text-red-800 font-medium">تحذير!</span>
                </div>
                <p class="text-red-700 text-sm mt-2">
                    سيتم حذف جميع البيانات من قاعدة البيانات مع الحفاظ على الهيكل فقط. هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <form id="resetDataForm">
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختر الشركة</label>
                    <select id="resetCompany" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">اختر الشركة لتصفير بياناتها</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}">{{ company.name }} ({{ company.database_name }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="confirmReset" class="rounded border-gray-300 text-red-600 focus:ring-red-500" required>
                        <span class="mr-2 text-sm text-gray-700">أؤكد أنني أريد حذف جميع البيانات</span>
                    </label>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-trash ml-2"></i>
                        تصفير البيانات
                    </button>
                    <button type="button" onclick="hideResetDataModal()" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إنشاء نسخة احتياطية لشركة محددة
function createBackupForCompany(companyId) {
    if (confirm('هل تريد إنشاء نسخة احتياطية شاملة لهذه الشركة؟\nستشمل النسخة قاعدة البيانات وجميع الملفات والمستندات.')) {
        showLoadingIndicator('جاري إنشاء النسخة الاحتياطية...');

        // محاكاة إنشاء النسخة الاحتياطية
        setTimeout(() => {
            hideLoadingIndicator();
            showSuccessMessage('تم إنشاء النسخة الاحتياطية بنجاح!');
        }, 3000);
    }
}

// إنشاء نسخ احتياطية لجميع الشركات
function createBackupForAllCompanies() {
    if (confirm('هل تريد إنشاء نسخ احتياطية شاملة لجميع الشركات النشطة؟\nقد تستغرق هذه العملية وقتاً طويلاً.')) {
        showLoadingIndicator('جاري إنشاء النسخ الاحتياطية لجميع الشركات...');

        // محاكاة إنشاء النسخ الاحتياطية
        setTimeout(() => {
            hideLoadingIndicator();
            showSuccessMessage('تم إنشاء النسخ الاحتياطية بنجاح لجميع الشركات!');
        }, 5000);
    }
}

// عرض النسخ الاحتياطية لشركة
function viewCompanyBackups(companyId) {
    alert('سيتم فتح صفحة النسخ الاحتياطية للشركة رقم: ' + companyId);
}

// عرض تفاصيل الشركة
function viewCompanyDetails(companyId) {
    alert('سيتم فتح صفحة تفاصيل الشركة رقم: ' + companyId);
}

// تنظيف النسخ القديمة
function cleanOldBackups() {
    const retentionDays = prompt('كم يوماً تريد الاحتفاظ بالنسخ الاحتياطية؟', '30');
    if (retentionDays && !isNaN(retentionDays)) {
        if (confirm(`هل تريد حذف جميع النسخ الاحتياطية الأقدم من ${retentionDays} يوماً؟`)) {
            showLoadingIndicator('جاري تنظيف النسخ القديمة...');

            setTimeout(() => {
                hideLoadingIndicator();
                showSuccessMessage('تم حذف النسخ الاحتياطية القديمة بنجاح!');
            }, 2000);
        }
    }
}

// عرض إحصائيات النسخ الاحتياطية
function showBackupStatistics() {
    const message = `📊 إحصائيات النسخ الاحتياطية:

📦 إجمالي النسخ: 25 نسخة
💾 الحجم الإجمالي: 2.5 GB
🔧 يدوية: 10 نسخ
🤖 تلقائية: 15 نسخة
📅 يومية: 7 نسخ
📆 أسبوعية: 4 نسخ
🗓️ شهرية: 4 نسخ

🕐 أحدث نسخة: اليوم 14:30
🏢 الشركة: شركة الخليج للاستقدام`;

    alert(message);
}

// دوال مساعدة
function showLoadingIndicator(message) {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-indicator';
    loadingDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingDiv.innerHTML = `
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-700">${message}</p>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

function hideLoadingIndicator() {
    const loadingDiv = document.getElementById('loading-indicator');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

function showSuccessMessage(message) {
    alert('✅ ' + message);
}

function showErrorMessage(message) {
    alert('❌ ' + message);
}

// وظائف النوافذ المنبثقة
function showCreateDatabaseModal() {
    document.getElementById('createDatabaseModal').classList.remove('hidden');
    document.getElementById('createDatabaseModal').classList.add('flex');
}

function hideCreateDatabaseModal() {
    document.getElementById('createDatabaseModal').classList.add('hidden');
    document.getElementById('createDatabaseModal').classList.remove('flex');
    document.getElementById('createDatabaseForm').reset();
}

function showCopyStructureModal() {
    document.getElementById('copyStructureModal').classList.remove('hidden');
    document.getElementById('copyStructureModal').classList.add('flex');
}

function hideCopyStructureModal() {
    document.getElementById('copyStructureModal').classList.add('hidden');
    document.getElementById('copyStructureModal').classList.remove('flex');
    document.getElementById('copyStructureForm').reset();
}

function showResetDataModal() {
    document.getElementById('resetDataModal').classList.remove('hidden');
    document.getElementById('resetDataModal').classList.add('flex');
}

function hideResetDataModal() {
    document.getElementById('resetDataModal').classList.add('hidden');
    document.getElementById('resetDataModal').classList.remove('flex');
    document.getElementById('resetDataForm').reset();
}

// معالجة النماذج
document.getElementById('createDatabaseForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const companyName = document.getElementById('companyName').value;
    const databaseName = document.getElementById('databaseName').value;
    const serialNumber = document.getElementById('serialNumber').value;

    if (serialNumber.length !== 16) {
        showErrorMessage('الرقم التسلسلي يجب أن يكون 16 رقماً');
        return;
    }

    if (confirm(`هل تريد إنشاء قاعدة بيانات جديدة للشركة "${companyName}"؟`)) {
        showLoadingIndicator('جاري إنشاء قاعدة البيانات...');

        // محاكاة إنشاء قاعدة البيانات
        setTimeout(() => {
            hideLoadingIndicator();
            hideCreateDatabaseModal();
            showSuccessMessage(`تم إنشاء قاعدة البيانات "${databaseName}" للشركة "${companyName}" بنجاح!`);
            location.reload();
        }, 2000);
    }
});

document.getElementById('copyStructureForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const sourceCompany = document.getElementById('sourceCompany');
    const targetCompany = document.getElementById('targetCompany');

    if (sourceCompany.value === targetCompany.value) {
        showErrorMessage('لا يمكن نسخ الهيكل إلى نفس الشركة');
        return;
    }

    const sourceText = sourceCompany.options[sourceCompany.selectedIndex].text;
    const targetText = targetCompany.options[targetCompany.selectedIndex].text;

    if (confirm(`هل تريد نسخ هيكل قاعدة البيانات من "${sourceText}" إلى "${targetText}"؟\nسيتم استبدال الهيكل الحالي.`)) {
        showLoadingIndicator('جاري نسخ هيكل قاعدة البيانات...');

        // محاكاة نسخ الهيكل
        setTimeout(() => {
            hideLoadingIndicator();
            hideCopyStructureModal();
            showSuccessMessage('تم نسخ هيكل قاعدة البيانات بنجاح!');
        }, 3000);
    }
});

document.getElementById('resetDataForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const resetCompany = document.getElementById('resetCompany');
    const confirmReset = document.getElementById('confirmReset');

    if (!confirmReset.checked) {
        showErrorMessage('يجب تأكيد العملية أولاً');
        return;
    }

    const companyText = resetCompany.options[resetCompany.selectedIndex].text;

    if (confirm(`هل أنت متأكد من تصفير جميع البيانات للشركة "${companyText}"؟\nهذا الإجراء لا يمكن التراجع عنه!`)) {
        showLoadingIndicator('جاري تصفير البيانات...');

        // محاكاة تصفير البيانات
        setTimeout(() => {
            hideLoadingIndicator();
            hideResetDataModal();
            showSuccessMessage('تم تصفير البيانات بنجاح! تم الحفاظ على هيكل قاعدة البيانات.');
        }, 2000);
    }
});

// إغلاق النوافذ المنبثقة عند النقر خارجها
document.addEventListener('click', function(e) {
    if (e.target.id === 'createDatabaseModal') {
        hideCreateDatabaseModal();
    }
    if (e.target.id === 'copyStructureModal') {
        hideCopyStructureModal();
    }
    if (e.target.id === 'resetDataModal') {
        hideResetDataModal();
    }
});

// توليد اسم قاعدة البيانات تلقائياً
document.getElementById('companyName').addEventListener('input', function(e) {
    const companyName = e.target.value;
    const databaseName = 'company_' + companyName.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 10);
    document.getElementById('databaseName').value = databaseName;
});

// توليد رقم تسلسلي عشوائي
document.getElementById('serialNumber').addEventListener('focus', function(e) {
    if (!e.target.value) {
        const randomSerial = Math.random().toString().substring(2, 18);
        e.target.value = randomSerial.padEnd(16, '0');
    }
});
</script>
{% endblock %}
