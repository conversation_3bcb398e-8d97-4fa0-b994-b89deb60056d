/* منصة استقدامي السحابية - ملف CSS مساعد */

/* خطوط مخصصة */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية للمشروع */
:root {
  --primary-blue: #143D8D;
  --secondary-blue: #407BFF;
  --primary-dark: #0a384e;
  --primary-light: #e6f0ff;
  --success-color: #2ecc71;
  --error-color: #e74c3c;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --font-family: 'Tajawal', 'Cairo', sans-serif;
}

/* إعدادات عامة */
* {
  box-sizing: border-box;
}

html {
  direction: rtl;
  font-family: var(--font-family);
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family);
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  background-color: #f8fafc;
}

/* مكونات مخصصة للمشروع */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-main {
  flex: 1;
  padding: 2rem 0;
}

.app-footer {
  background-color: #1f2937;
  color: #d1d5db;
  padding: 1rem 0;
  text-align: center;
}

/* تحسينات للنماذج */
.form-container {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  border: 1px solid #e5e7eb;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  direction: rtl;
  text-align: right;
  font-family: var(--font-family);
}

.form-input:focus {
  outline: none;
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(64, 123, 255, 0.1);
}

.form-input:invalid {
  border-color: var(--error-color);
}

/* أزرار محسنة */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  font-family: var(--font-family);
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--secondary-blue);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--primary-blue);
  border: 1px solid #dee2e6;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #27ae60;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* بطاقات محسنة */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: #f8f9fa;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* تنبيهات محسنة */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  border-right: 4px solid;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: var(--font-family);
}

.alert-icon {
  margin-left: 0.75rem;
  font-size: 1.25rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-message {
  font-size: 0.875rem;
  opacity: 0.9;
}

.alert-success {
  background-color: #d4edda;
  border-color: var(--success-color);
  color: #155724;
}

.alert-error {
  background-color: #f8d7da;
  border-color: var(--error-color);
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: var(--warning-color);
  color: #856404;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: var(--info-color);
  color: #0c5460;
}

/* تحسينات للجداول */
.table-container {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family);
}

.table th {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  font-size: 0.875rem;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  text-align: right;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* تحسينات للتنقل */
.navbar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1rem;
}

.navbar-item {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.navbar-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
    margin: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .table-container {
    overflow-x: auto;
  }
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .btn {
    display: none;
  }
}

/* تحسينات بسيطة بدون تداخل مع Tailwind CDN */

/* تحسينات للخطوط العربية */
body {
  font-family: 'Tajawal', 'Cairo', sans-serif !important;
  direction: rtl !important;
}

/* تحسينات بسيطة للنماذج */
input[type="text"],
input[type="password"],
select,
textarea {
  font-family: 'Tajawal', 'Cairo', sans-serif !important;
  direction: rtl !important;
  text-align: right !important;
}

/* تحسينات للأزرار */
button {
  font-family: 'Tajawal', 'Cairo', sans-serif !important;
}
