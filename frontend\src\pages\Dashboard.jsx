import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaUsers, FaFileContract, FaBroom, FaBell, FaChartLine, FaCalendarAlt } from 'react-icons/fa';
import { Line, Doughnut } from 'react-chartjs-2';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Components
import StatCard from '../components/common/StatCard';
import DataTable from '../components/common/DataTable';

const Dashboard = () => {
  const [stats, setStats] = useState([]);
  const [expiringContracts, setExpiringContracts] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Fetch dashboard data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      // Stats data
      setStats([
        {
          title: 'العمال',
          value: 156,
          icon: <FaUsers className="text-2xl" />,
          color: 'primary',
          change: '+12%',
          changeType: 'increase',
          link: '/workers'
        },
        {
          title: 'العقود',
          value: 43,
          icon: <FaFileContract className="text-2xl" />,
          color: 'success',
          change: '+5%',
          changeType: 'increase',
          link: '/contracts'
        },
        {
          title: 'الخدمات',
          value: 28,
          icon: <FaBroom className="text-2xl" />,
          color: 'info',
          change: '-3%',
          changeType: 'decrease',
          link: '/services'
        },
        {
          title: 'التنبيهات',
          value: 7,
          icon: <FaBell className="text-2xl" />,
          color: 'warning',
          change: '+2',
          changeType: 'increase',
          link: '#notifications'
        }
      ]);
      
      // Expiring contracts data
      setExpiringContracts([
        { id: 1, client_name: 'شركة الأمل للخدمات', end_date: '2023-07-15', days_remaining: 5 },
        { id: 2, client_name: 'مستشفى الرحمة', end_date: '2023-07-20', days_remaining: 10 },
        { id: 3, client_name: 'فندق النجوم', end_date: '2023-07-25', days_remaining: 15 },
        { id: 4, client_name: 'مدرسة المستقبل', end_date: '2023-08-01', days_remaining: 22 },
        { id: 5, client_name: 'مركز التسوق الكبير', end_date: '2023-08-05', days_remaining: 26 }
      ]);
      
      // Notifications data
      setNotifications([
        { id: 1, title: 'انتهاء عقد', message: 'سينتهي عقد العميل أحمد محمد خلال 3 أيام', date_created: '2023-07-10', type: 'عالي' },
        { id: 2, title: 'انتهاء جواز سفر', message: 'سينتهي جواز سفر العامل راجيش كومار خلال أسبوع', date_created: '2023-07-09', type: 'متوسط' },
        { id: 3, title: 'موعد خدمة', message: 'لديك خدمة تنظيف مجدولة غدًا الساعة 9 صباحًا', date_created: '2023-07-09', type: 'منخفض' }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);
  
  // Chart data for revenue
  const revenueData = {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
    datasets: [
      {
        label: 'الإيرادات',
        data: [12000, 19000, 15000, 25000, 22000, 30000],
        fill: true,
        backgroundColor: 'rgba(23, 71, 133, 0.1)',
        borderColor: '#174785',
        tension: 0.4
      }
    ]
  };
  
  // Chart data for workers by nationality
  const workersData = {
    labels: ['الهند', 'باكستان', 'بنغلاديش', 'الفلبين', 'نيبال', 'أخرى'],
    datasets: [
      {
        data: [45, 25, 20, 15, 10, 5],
        backgroundColor: [
          '#174785',
          '#5a96d4',
          '#10b981',
          '#f59e0b',
          '#3b82f6',
          '#6b46c1'
        ],
        borderWidth: 0
      }
    ]
  };
  
  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: {
            family: 'Cairo'
          }
        }
      }
    }
  };
  
  // Table columns
  const contractColumns = [
    { Header: 'العميل', accessor: 'client_name' },
    { Header: 'تاريخ الانتهاء', accessor: 'end_date' },
    { 
      Header: 'الأيام المتبقية', 
      accessor: 'days_remaining',
      Cell: ({ value }) => (
        <span className={`badge ${value <= 7 ? 'badge-danger' : value <= 15 ? 'badge-warning' : 'badge-success'}`}>
          {value} يوم
        </span>
      )
    },
    {
      Header: 'الإجراءات',
      accessor: 'id',
      Cell: ({ value }) => (
        <div className="row-actions">
          <Link to={`/contracts/${value}`} className="action-btn view">
            <i className="fas fa-eye"></i>
          </Link>
        </div>
      )
    }
  ];
  
  const notificationColumns = [
    { Header: 'العنوان', accessor: 'title' },
    { 
      Header: 'النوع', 
      accessor: 'type',
      Cell: ({ value }) => (
        <span className={`badge ${value === 'عالي' ? 'badge-danger' : value === 'متوسط' ? 'badge-warning' : 'badge-info'}`}>
          {value}
        </span>
      )
    },
    { Header: 'التاريخ', accessor: 'date_created' },
    {
      Header: 'الإجراءات',
      accessor: 'id',
      Cell: ({ value }) => (
        <div className="row-actions">
          <button className="action-btn view">
            <i className="fas fa-eye"></i>
          </button>
        </div>
      )
    }
  ];
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">لوحة التحكم</h1>
          <p className="text-gray-600">مرحباً بك في نظام إدارة العمالة</p>
        </div>
        
        <div className="mt-4 md:mt-0 flex items-center">
          <span className="text-gray-500 ml-2">
            <FaCalendarAlt />
          </span>
          <span className="text-gray-600">
            {new Date().toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
          </span>
        </div>
      </div>
      
      {/* Stats cards */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </motion.div>
      
      {/* Charts */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue chart */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="font-bold">الإيرادات الشهرية</h3>
              <div className="flex items-center text-primary">
                <FaChartLine className="ml-1" />
                <span className="text-sm">+15% من الشهر الماضي</span>
              </div>
            </div>
          </div>
          <div className="card-body">
            <div className="h-64">
              <Line data={revenueData} options={chartOptions} />
            </div>
          </div>
        </div>
        
        {/* Workers by nationality chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="font-bold">العمال حسب الجنسية</h3>
          </div>
          <div className="card-body">
            <div className="h-64">
              <Doughnut data={workersData} options={chartOptions} />
            </div>
          </div>
        </div>
      </motion.div>
      
      {/* Tables */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Expiring contracts */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="font-bold">العقود التي ستنتهي قريباً</h3>
              <Link to="/contracts" className="text-primary hover:text-primary-dark text-sm font-medium">
                عرض الكل
              </Link>
            </div>
          </div>
          <div className="card-body p-0">
            <DataTable
              columns={contractColumns}
              data={expiringContracts}
              emptyMessage="لا توجد عقود ستنتهي قريباً"
            />
          </div>
        </div>
        
        {/* Notifications */}
        <div className="card" id="notifications">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="font-bold">آخر الإشعارات</h3>
              <Link to="#" className="text-primary hover:text-primary-dark text-sm font-medium">
                عرض الكل
              </Link>
            </div>
          </div>
          <div className="card-body p-0">
            <DataTable
              columns={notificationColumns}
              data={notifications}
              emptyMessage="لا توجد إشعارات"
            />
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default Dashboard;
