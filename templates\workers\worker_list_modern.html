{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}قائمة العمال | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">{{ title|default:"قائمة العمال" }}</h2>
            {% if worker_type %}
            <p class="text-sm text-gray-500 mt-1">
                {% if worker_type == 'contract' %}
                    العمال المرتبطين بالعقود الدائمية
                {% elif worker_type == 'custom' %}
                    العمال المرتبطين بالخدمات المخصصة
                {% elif worker_type == 'monthly' %}
                    العمال المرتبطين بالعقود الشهرية
                {% endif %}
            </p>
            {% endif %}
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'workers:dashboard' %}"
               class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                <span>العودة</span>
            </a>
            {% if worker_type == 'contract' %}
                <a href="{% url 'workers:create_permanent_worker' %}"
                   class="bg-primary text-white dark:bg-dark-accent dark:text-white px-5 py-2 rounded-lg hover:bg-primary-dark hover:shadow-md dark:hover:bg-blue-700 transition-all duration-300 flex items-center">
                    <i class="fas fa-plus ml-2 text-sm"></i>
                    <span>إضافة عامل عقد دائم</span>
                </a>
            {% elif worker_type == 'custom' %}
                <a href="{% url 'workers:create_custom_worker' %}"
                   class="bg-orange-500 text-white dark:bg-orange-600 dark:text-white px-5 py-2 rounded-lg hover:bg-orange-600 hover:shadow-md dark:hover:bg-orange-700 transition-all duration-300 flex items-center">
                    <i class="fas fa-plus ml-2 text-sm"></i>
                    <span>إضافة عامل خدمة مخصصة</span>
                </a>
            {% elif worker_type == 'monthly' %}
                <a href="{% url 'workers:create_monthly_worker' %}"
                   class="bg-purple-600 text-white dark:bg-purple-700 dark:text-white px-5 py-2 rounded-lg hover:bg-purple-700 hover:shadow-md dark:hover:bg-purple-800 transition-all duration-300 flex items-center">
                    <i class="fas fa-plus ml-2 text-sm"></i>
                    <span>إضافة عامل عقد شهري</span>
                </a>
            {% else %}
                <a href="{% url 'workers:worker_create' %}?type={{ worker_type }}"
                   class="bg-primary text-white dark:bg-dark-accent dark:text-white px-5 py-2 rounded-lg hover:bg-primary-dark hover:shadow-md dark:hover:bg-blue-700 transition-all duration-300 flex items-center">
                    <i class="fas fa-plus ml-2 text-sm"></i>
                    <span>إضافة عامل جديد</span>
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-grow">
                <div class="relative">
                    <input type="text" id="search-input" placeholder="ابحث بالاسم أو رقم الجواز أو رقم الإقامة..."
                           class="w-full px-4 py-2 pr-10 border rounded-md shadow-sm focus:ring focus:ring-blue-200 focus:border-blue-500">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="flex space-x-2 space-x-reverse">
                <select id="nationality-filter" class="px-4 py-2 border rounded-md shadow-sm focus:ring focus:ring-blue-200 focus:border-blue-500">
                    <option value="">كل الجنسيات</option>
                    {% for code, name in nationality_choices %}
                        <option value="{{ code }}">{{ name }}</option>
                    {% endfor %}
                </select>

                <select id="status-filter" class="px-4 py-2 border rounded-md shadow-sm focus:ring focus:ring-blue-200 focus:border-blue-500">
                    <option value="">كل الحالات</option>
                    {% for code, name in status_choices %}
                        <option value="{{ code }}">{{ name }}</option>
                    {% endfor %}
                </select>

                <button id="filter-button" class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-4 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto bg-white rounded-lg shadow-md">
        <table class="min-w-full text-sm text-right">
            <thead class="bg-gray-50 text-gray-600">
                <tr>
                    <th class="py-3 px-4 font-medium">الاسم</th>
                    <th class="py-3 px-4 font-medium">الجنسية</th>
                    <th class="py-3 px-4 font-medium">رقم الجواز</th>
                    <th class="py-3 px-4 font-medium">النوع</th>
                    <th class="py-3 px-4 font-medium">الحالة</th>
                    <th class="py-3 px-4 font-medium">تاريخ الإضافة</th>
                    <th class="py-3 px-4 font-medium">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for worker in workers %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="py-3 px-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8 ml-3">
                                {% if worker.photo %}
                                    <img class="h-8 w-8 rounded-full object-cover" src="{{ worker.photo.url }}" alt="{{ worker.first_name }}">
                                {% else %}
                                    <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">{{ worker.first_name }} {{ worker.last_name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="py-3 px-4">{{ worker.get_nationality_display }}</td>
                    <td class="py-3 px-4">{{ worker.passport_number }}</td>
                    <td class="py-3 px-4">{{ worker.get_gender_display }}</td>
                    <td class="py-3 px-4">
                        <span class="px-2 py-1 text-xs rounded-full
                                   {% if worker.status == 'active' %}bg-green-100 text-green-700
                                   {% elif worker.status == 'on_leave' %}bg-yellow-100 text-yellow-700
                                   {% elif worker.status == 'terminated' %}bg-red-100 text-red-700
                                   {% else %}bg-gray-100 text-gray-700{% endif %}">
                            {{ worker.get_status_display }}
                        </span>
                    </td>
                    <td class="py-3 px-4">{{ worker.date_joined|date:"Y-m-d" }}</td>
                    <td class="py-3 px-4">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <a href="{% url 'workers:worker_detail' worker.id %}" class="text-blue-600 hover:text-blue-800" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'workers:worker_update' worker.id %}?type={{ worker_type }}" class="text-yellow-600 hover:text-yellow-800" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button"
                                    class="text-red-600 hover:text-red-800 border-none bg-transparent p-0"
                                    title="حذف"
                                    data-delete-url="{% url 'workers:worker_delete' worker.id %}?type={{ worker_type }}"
                                    onclick="window.deleteItem(this.getAttribute('data-delete-url'));">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center justify-center">
                            <i class="fas fa-users text-4xl mb-3 text-gray-300"></i>
                            <p class="text-lg font-medium">لا يوجد عمال مسجلين</p>
                            <p class="text-sm mt-1">قم بإضافة عمال جدد باستخدام زر "إضافة عامل جديد"</p>
                            {% if worker_type == 'contract' %}
                                <a href="{% url 'workers:create_permanent_worker' %}" class="mt-4 bg-primary text-white dark:bg-dark-accent dark:text-white px-5 py-2 rounded-lg hover:bg-primary-dark hover:shadow-md dark:hover:bg-blue-700 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-plus ml-2 text-sm"></i>
                                    <span>إضافة عامل عقد دائم</span>
                                </a>
                            {% elif worker_type == 'custom' %}
                                <a href="{% url 'workers:create_custom_worker' %}" class="mt-4 bg-orange-500 text-white dark:bg-orange-600 dark:text-white px-5 py-2 rounded-lg hover:bg-orange-600 hover:shadow-md dark:hover:bg-orange-700 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-plus ml-2 text-sm"></i>
                                    <span>إضافة عامل خدمة مخصصة</span>
                                </a>
                            {% elif worker_type == 'monthly' %}
                                <a href="{% url 'workers:create_monthly_worker' %}" class="mt-4 bg-purple-600 text-white dark:bg-purple-700 dark:text-white px-5 py-2 rounded-lg hover:bg-purple-700 hover:shadow-md dark:hover:bg-purple-800 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-plus ml-2 text-sm"></i>
                                    <span>إضافة عامل عقد شهري</span>
                                </a>
                            {% else %}
                                <a href="{% url 'workers:worker_create' %}?type={{ worker_type }}" class="mt-4 bg-primary text-white dark:bg-dark-accent dark:text-white px-5 py-2 rounded-lg hover:bg-primary-dark hover:shadow-md dark:hover:bg-blue-700 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-plus ml-2 text-sm"></i>
                                    <span>إضافة عامل جديد</span>
                                </a>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if workers.has_other_pages %}
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mt-6 gap-4">
        <div class="text-sm text-gray-700">
            عرض <span class="font-medium">{{ workers.start_index }}</span> إلى <span class="font-medium">{{ workers.end_index }}</span> من <span class="font-medium">{{ workers.paginator.count }}</span> عامل
        </div>
        <div class="flex flex-wrap items-center justify-center space-x-1 space-x-reverse">
            {% if workers.has_previous %}
                <a href="?page={{ workers.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.nationality %}&nationality={{ request.GET.nationality }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                   class="px-3 py-1 rounded-lg bg-white dark:bg-dark-button border border-gray-300 dark:border-gray-700 text-primary dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                    <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    <span>السابق</span>
                </a>
            {% else %}
                <span class="px-3 py-1 rounded-lg bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed flex items-center">
                    <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    <span>السابق</span>
                </span>
            {% endif %}

            {% for i in workers.paginator.page_range %}
                {% if workers.number == i %}
                    <span class="px-3 py-1 rounded-lg bg-primary text-white dark:bg-primary-light dark:text-white flex items-center justify-center min-w-[32px]">{{ i }}</span>
                {% else %}
                    <a href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.nationality %}&nationality={{ request.GET.nationality }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                       class="px-3 py-1 rounded-lg bg-white dark:bg-dark-button border border-gray-300 dark:border-gray-700 text-primary dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-dark-accent transition-all duration-300 flex items-center justify-center min-w-[32px]">{{ i }}</a>
                {% endif %}
            {% endfor %}

            {% if workers.has_next %}
                <a href="?page={{ workers.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.nationality %}&nationality={{ request.GET.nationality }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                   class="px-3 py-1 rounded-lg bg-white dark:bg-dark-button border border-gray-300 dark:border-gray-700 text-primary dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                    <span>التالي</span>
                    <i class="fas fa-chevron-left mr-1 text-xs"></i>
                </a>
            {% else %}
                <span class="px-3 py-1 rounded-lg bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed flex items-center">
                    <span>التالي</span>
                    <i class="fas fa-chevron-left mr-1 text-xs"></i>
                </span>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

{% endblock %}

{% block inner_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-input');
        const nationalityFilter = document.getElementById('nationality-filter');
        const statusFilter = document.getElementById('status-filter');
        const filterButton = document.getElementById('filter-button');

        // Set initial values from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('search')) {
            searchInput.value = urlParams.get('search');
        }
        if (urlParams.has('nationality')) {
            nationalityFilter.value = urlParams.get('nationality');
        }
        if (urlParams.has('status')) {
            statusFilter.value = urlParams.get('status');
        }

        // Server-side filtering with pagination
        function applyFilters() {
            const searchTerm = searchInput.value;
            const nationality = nationalityFilter.value;
            const status = statusFilter.value;

            let url = window.location.pathname + '?';

            if (searchTerm) {
                url += `search=${encodeURIComponent(searchTerm)}&`;
            }

            if (nationality) {
                url += `nationality=${encodeURIComponent(nationality)}&`;
            }

            if (status) {
                url += `status=${encodeURIComponent(status)}&`;
            }

            // Remove trailing & if exists
            if (url.endsWith('&')) {
                url = url.slice(0, -1);
            }

            window.location.href = url;
        }

        // Add event listeners
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                applyFilters(); // Server-side filtering on Enter
            }
        });

        filterButton.addEventListener('click', function() {
            applyFilters(); // Server-side filtering on button click
        });
    });
</script>
{% endblock %}
