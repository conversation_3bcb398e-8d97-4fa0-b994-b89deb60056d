from django.urls import path
from . import views

app_name = 'services'

urlpatterns = [
    # الصفحة الرئيسية للخدمات
    path('', views.services_home, name='services_home'),
    
    # إدارة أنواع الخدمات
    path('types/', views.service_types, name='service_types'),
    path('types/add/', views.add_service_type, name='add_service_type'),
    path('types/<int:type_id>/edit/', views.edit_service_type, name='edit_service_type'),
    path('types/<int:type_id>/delete/', views.delete_service_type, name='delete_service_type'),
    
    # إدارة الخدمات
    path('list/', views.services_list, name='services_list'),
    path('add/', views.add_service, name='add_service'),
    path('edit/<int:service_id>/', views.edit_service, name='edit_service'),
    path('view/<int:service_id>/', views.view_service, name='view_service'),
    path('delete/<int:service_id>/', views.delete_service, name='delete_service'),
    
    # إدارة الحجوزات الثابتة
    path('fixed/', views.fixed_services, name='fixed_services'),
    path('fixed/add/', views.add_fixed_service, name='add_fixed_service'),
    path('fixed/<int:service_id>/edit/', views.edit_fixed_service, name='edit_fixed_service'),
    path('fixed/<int:service_id>/delete/', views.delete_fixed_service, name='delete_fixed_service'),
    
    # إدارة استثناءات الحجوزات الثابتة
    path('fixed/<int:service_id>/exceptions/', views.fixed_service_exceptions, name='fixed_service_exceptions'),
    path('fixed/<int:service_id>/exceptions/add/', views.add_fixed_service_exception, name='add_fixed_service_exception'),
    path('fixed/<int:service_id>/exceptions/<int:exception_id>/delete/', views.delete_fixed_service_exception, name='delete_fixed_service_exception'),
    
    # إدارة العملاء المخصصين
    path('custom-clients/', views.custom_clients, name='custom_clients'),
    path('custom-clients/add/', views.add_custom_client, name='add_custom_client'),
    path('custom-clients/<int:client_id>/edit/', views.edit_custom_client, name='edit_custom_client'),
    path('custom-clients/<int:client_id>/delete/', views.delete_custom_client, name='delete_custom_client'),
    
    # إدارة العمال المخصصين
    path('custom-workers/', views.custom_workers, name='custom_workers'),
    path('custom-workers/add/', views.add_custom_worker, name='add_custom_worker'),
    path('custom-workers/<int:worker_id>/edit/', views.edit_custom_worker, name='edit_custom_worker'),
    path('custom-workers/<int:worker_id>/delete/', views.delete_custom_worker, name='delete_custom_worker'),
    
    # تقويم الخدمات
    path('calendar/', views.services_calendar, name='services_calendar'),
    path('calendar/<int:year>/<int:month>/', views.services_calendar_month, name='services_calendar_month'),
    path('calendar/<int:year>/<int:month>/<int:day>/', views.services_calendar_day, name='services_calendar_day'),
    
    # إعدادات الخدمات - معطلة مؤقتًا
    # path('settings/', views.service_settings, name='service_settings'),
    
    # واجهات برمجة التطبيقات (APIs) - معطلة مؤقتًا
    # path('api/services/', views.api_services, name='api_services'),
    # path('api/services/<int:service_id>/', views.api_service_detail, name='api_service_detail'),
    # path('api/services/<int:service_id>/status/', views.api_update_service_status, name='api_update_service_status'),
    # path('api/services/<int:service_id>/payment/', views.api_update_payment_status, name='api_update_payment_status'),
    # path('api/available-workers/', views.api_available_workers, name='api_available_workers'),
    # path('api/clients/', views.api_clients, name='api_clients'),
    # path('api/workers/', views.api_workers, name='api_workers'),
    # path('api/statistics/daily/', views.api_daily_statistics, name='api_daily_statistics'),
    # path('api/statistics/monthly/', views.api_monthly_statistics, name='api_monthly_statistics'),
]
