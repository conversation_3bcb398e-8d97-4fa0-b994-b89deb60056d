<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SuperAdminController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\DatabaseController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\SystemLogController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\LicenseController;

/*
|--------------------------------------------------------------------------
| Super Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register Super Admin routes for your application.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "super_admin" middleware group.
|
*/

// Authentication Routes
Route::get('/login', [SuperAdminController::class, 'login'])->name('login');
Route::post('/login', [SuperAdminController::class, 'authenticate'])->name('authenticate');
Route::get('/two-factor', [SuperAdminController::class, 'twoFactor'])->name('two_factor');
Route::post('/two-factor', [SuperAdminController::class, 'verifyTwoFactor'])->name('verify_two_factor');
Route::post('/resend-two-factor-code', [SuperAdminController::class, 'resendTwoFactorCode'])->name('resend_two_factor_code');
Route::post('/logout', [SuperAdminController::class, 'logout'])->name('logout');

// Protected Routes (require Super Admin authentication)
Route::middleware(['auth.super_admin'])->group(function () {
    // Dashboard
    Route::get('/', [SuperAdminController::class, 'dashboard'])->name('dashboard');
    
    // Company Management
    Route::prefix('companies')->name('companies.')->group(function () {
        Route::get('/', [CompanyController::class, 'index'])->name('index');
        Route::get('/create', [CompanyController::class, 'create'])->name('create');
        Route::post('/', [CompanyController::class, 'store'])->name('store');
        Route::get('/{company}', [CompanyController::class, 'show'])->name('show');
        Route::get('/{company}/edit', [CompanyController::class, 'edit'])->name('edit');
        Route::put('/{company}', [CompanyController::class, 'update'])->name('update');
        Route::delete('/{company}', [CompanyController::class, 'destroy'])->name('destroy');
        Route::post('/{company}/toggle-status', [CompanyController::class, 'toggleStatus'])->name('toggle_status');
    });
    
    // Database Management
    Route::prefix('database')->name('database.')->group(function () {
        Route::get('/', [DatabaseController::class, 'index'])->name('index');
        Route::get('/{company}/structure', [DatabaseController::class, 'structure'])->name('structure');
        Route::get('/{company}/test-connection', [DatabaseController::class, 'testConnection'])->name('test_connection');
        Route::post('/test-all-connections', [DatabaseController::class, 'testAllConnections'])->name('test_all_connections');
        Route::post('/{company}/backup', [DatabaseController::class, 'backup'])->name('backup');
        Route::post('/backup-all', [DatabaseController::class, 'backupAll'])->name('backup_all');
        Route::post('/system/optimize', [DatabaseController::class, 'optimizeSystem'])->name('optimize_system');
        Route::post('/system/backup', [DatabaseController::class, 'backupSystem'])->name('backup_system');
    });
    
    // Backup Management
    Route::prefix('backup')->name('backup.')->group(function () {
        Route::get('/', [BackupController::class, 'index'])->name('index');
        Route::post('/create', [BackupController::class, 'create'])->name('create');
        Route::get('/download/{backup}', [BackupController::class, 'download'])->name('download');
        Route::delete('/{backup}', [BackupController::class, 'destroy'])->name('destroy');
        Route::post('/restore/{backup}', [BackupController::class, 'restore'])->name('restore');
        Route::post('/schedule', [BackupController::class, 'schedule'])->name('schedule');
    });
    
    // System Logs
    Route::prefix('logs')->name('logs.')->group(function () {
        Route::get('/', [SystemLogController::class, 'index'])->name('index');
        Route::get('/{log}', [SystemLogController::class, 'show'])->name('show');
        Route::get('/export', [SystemLogController::class, 'export'])->name('export');
        Route::post('/clear', [SystemLogController::class, 'clear'])->name('clear');
    });
    
    // License Management
    Route::prefix('licenses')->name('licenses.')->group(function () {
        Route::get('/', [LicenseController::class, 'index'])->name('index');
        Route::get('/create', [LicenseController::class, 'create'])->name('create');
        Route::post('/', [LicenseController::class, 'store'])->name('store');
        Route::get('/{license}', [LicenseController::class, 'show'])->name('show');
        Route::get('/{license}/edit', [LicenseController::class, 'edit'])->name('edit');
        Route::put('/{license}', [LicenseController::class, 'update'])->name('update');
        Route::delete('/{license}', [LicenseController::class, 'destroy'])->name('destroy');
        Route::post('/{license}/toggle-status', [LicenseController::class, 'toggleStatus'])->name('toggle_status');
    });
    
    // System Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::post('/', [SettingsController::class, 'update'])->name('update');
        Route::post('/test-email', [SettingsController::class, 'testEmail'])->name('test_email');
    });
    
    // User Profile
    Route::get('/profile', [SuperAdminController::class, 'profile'])->name('profile');
    Route::put('/profile', [SuperAdminController::class, 'updateProfile'])->name('update_profile');
    Route::put('/change-password', [SuperAdminController::class, 'changePassword'])->name('change_password');
    Route::post('/setup-two-factor', [SuperAdminController::class, 'setupTwoFactor'])->name('setup_two_factor');
    Route::post('/disable-two-factor', [SuperAdminController::class, 'disableTwoFactor'])->name('disable_two_factor');
});

// API Routes (for AJAX requests)
Route::prefix('api')->name('api.')->middleware(['auth.super_admin'])->group(function () {
    Route::get('/companies', [CompanyController::class, 'apiGetCompanies'])->name('get_companies');
    Route::get('/companies/{company}', [CompanyController::class, 'apiGetCompanyDetails'])->name('get_company_details');
    Route::get('/companies/{company}/tables', [DatabaseController::class, 'apiGetCompanyTables'])->name('get_company_tables');
    Route::get('/companies/{company}/tables/{table}/structure', [DatabaseController::class, 'apiGetTableStructure'])->name('get_table_structure');
    Route::get('/companies/{company}/tables/{table}/data', [DatabaseController::class, 'apiGetTableData'])->name('get_table_data');
    Route::post('/companies/{company}/execute-query', [DatabaseController::class, 'apiExecuteQuery'])->name('execute_query');
});
