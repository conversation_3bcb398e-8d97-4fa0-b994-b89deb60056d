"""
أمر إدارة Django لإنشاء شركة جديدة في النظام
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
import logging
from backend.super_admin.models import Company, SystemLog
import random
import string

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'إنشاء شركة جديدة في النظام'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            help='اسم الشركة'
        )
        parser.add_argument(
            '--db-name',
            type=str,
            help='اسم قاعدة البيانات'
        )
        parser.add_argument(
            '--db-user',
            type=str,
            help='اسم مستخدم قاعدة البيانات'
        )
        parser.add_argument(
            '--db-password',
            type=str,
            help='كلمة مرور قاعدة البيانات'
        )
        parser.add_argument(
            '--db-host',
            type=str,
            default='localhost',
            help='مضيف قاعدة البيانات'
        )
        parser.add_argument(
            '--status',
            type=str,
            default='active',
            choices=['active', 'inactive', 'suspended', 'trial'],
            help='حالة الشركة'
        )

    def handle(self, *args, **options):
        # الحصول على معلومات الشركة من الخيارات
        name = options.get('name')
        db_name = options.get('db_name')
        db_user = options.get('db_user')
        db_password = options.get('db_password')
        db_host = options.get('db_host')
        status = options.get('status')
        
        # إذا لم يتم تحديد اسم الشركة، اطلب من المستخدم إدخاله
        if not name:
            name = input('أدخل اسم الشركة: ')
        
        # إذا لم يتم تحديد اسم قاعدة البيانات، استخدم اسم الشركة مع إزالة المسافات والأحرف الخاصة
        if not db_name:
            db_name = ''.join(c for c in name if c.isalnum()).lower()
            self.stdout.write(self.style.SUCCESS(f"تم تعيين اسم قاعدة البيانات تلقائيًا: {db_name}"))
        
        # إذا لم يتم تحديد اسم مستخدم قاعدة البيانات، استخدم 'admin'
        if not db_user:
            db_user = 'admin'
            self.stdout.write(self.style.SUCCESS(f"تم تعيين اسم مستخدم قاعدة البيانات تلقائيًا: {db_user}"))
        
        # إذا لم يتم تحديد كلمة مرور قاعدة البيانات، قم بتوليد كلمة مرور عشوائية
        if not db_password:
            db_password = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(12))
            self.stdout.write(self.style.SUCCESS(f"تم توليد كلمة مرور قاعدة البيانات تلقائيًا: {db_password}"))
        
        try:
            # إنشاء الشركة
            with transaction.atomic():
                company = Company(
                    name=name,
                    database_name=db_name,
                    database_user=db_user,
                    database_password=db_password,
                    database_host=db_host,
                    status=status,
                )
                company.save()
                
                # تسجيل العملية في سجل النظام
                SystemLog.objects.create(
                    action='create_company',
                    details=f"تم إنشاء شركة جديدة: {name}",
                    ip_address='127.0.0.1',
                    user_agent='Django Management Command',
                )
                
                self.stdout.write(self.style.SUCCESS(f"تم إنشاء الشركة بنجاح:"))
                self.stdout.write(self.style.SUCCESS(f"- اسم الشركة: {company.name}"))
                self.stdout.write(self.style.SUCCESS(f"- الرقم التسلسلي: {company.serial_number}"))
                self.stdout.write(self.style.SUCCESS(f"- اسم قاعدة البيانات: {company.database_name}"))
                self.stdout.write(self.style.SUCCESS(f"- مستخدم قاعدة البيانات: {company.database_user}"))
                self.stdout.write(self.style.SUCCESS(f"- كلمة مرور قاعدة البيانات: {company.database_password}"))
                self.stdout.write(self.style.SUCCESS(f"- مضيف قاعدة البيانات: {company.database_host}"))
                self.stdout.write(self.style.SUCCESS(f"- الحالة: {company.status}"))
                self.stdout.write(self.style.SUCCESS(f"- تاريخ الإنشاء: {company.created_at}"))
                
                # اختبار الاتصال بقاعدة البيانات
                from backend.super_admin.database_connection import test_company_db_connection
                connection_test = test_company_db_connection(company)
                
                if connection_test['success']:
                    self.stdout.write(self.style.SUCCESS(f"تم الاتصال بقاعدة البيانات بنجاح."))
                    self.stdout.write(self.style.SUCCESS(f"عدد الجداول: {connection_test['tables_count']}"))
                else:
                    self.stdout.write(self.style.ERROR(f"فشل في الاتصال بقاعدة البيانات: {connection_test['message']}"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء إنشاء الشركة: {str(e)}"))
            logger.error(f"حدث خطأ أثناء إنشاء الشركة: {str(e)}", exc_info=True)
