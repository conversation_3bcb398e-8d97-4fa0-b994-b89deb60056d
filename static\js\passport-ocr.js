/**
 * وحدة معالجة جوازات السفر
 * 
 * هذه الوحدة تقوم بمعالجة صور جوازات السفر واستخراج البيانات منها
 */

// كائن معالجة جوازات السفر
const PassportOCR = {
    /**
     * معالجة صورة جواز السفر
     * @param {String} imageData بيانات الصورة بتنسيق base64
     * @returns {Promise} وعد يحل إلى كائن يحتوي على البيانات المستخرجة
     */
    processImage: function(imageData) {
        return new Promise((resolve, reject) => {
            console.log('معالجة صورة جواز السفر...');
            
            // إرسال الصورة إلى واجهة برمجة التطبيقات
            fetch('/scanner/api/process-passport/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCookie('csrftoken')
                },
                body: JSON.stringify({
                    image: imageData
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('تم استلام البيانات من الخادم:', data);
                resolve(data);
            })
            .catch(error => {
                console.error('خطأ في معالجة الصورة:', error);
                reject(error);
            });
        });
    },
    
    /**
     * ملء نموذج العامل بالبيانات المستخرجة
     * @param {Object} data البيانات المستخرجة
     */
    fillWorkerForm: function(data) {
        if (!data || !data.success) {
            console.error('لا توجد بيانات صالحة لملء النموذج');
            return;
        }
        
        console.log('ملء نموذج العامل بالبيانات المستخرجة:', data);
        
        // ملء حقول النموذج بالبيانات المستخرجة
        try {
            // الاسم الأول
            const firstNameField = document.getElementById('id_first_name');
            if (firstNameField && data.firstName) {
                firstNameField.value = data.firstName;
                console.log('تم ملء الاسم الأول:', data.firstName);
            }
            
            // الاسم الأخير
            const lastNameField = document.getElementById('id_last_name');
            if (lastNameField && data.lastName) {
                lastNameField.value = data.lastName;
                console.log('تم ملء الاسم الأخير:', data.lastName);
            }
            
            // رقم جواز السفر
            const passportNumberField = document.getElementById('id_passport_number');
            if (passportNumberField && data.passportNumber) {
                passportNumberField.value = data.passportNumber;
                console.log('تم ملء رقم جواز السفر:', data.passportNumber);
            }
            
            // الجنسية
            const nationalityField = document.getElementById('id_nationality');
            if (nationalityField && data.nationalityName) {
                // البحث عن الخيار المناسب في القائمة المنسدلة
                const options = nationalityField.options;
                for (let i = 0; i < options.length; i++) {
                    if (options[i].text.includes(data.nationalityName) || 
                        data.nationalityName.includes(options[i].text)) {
                        nationalityField.selectedIndex = i;
                        console.log('تم اختيار الجنسية:', options[i].text);
                        break;
                    }
                }
            }
            
            // الجنس
            const genderField = document.getElementById('id_gender');
            if (genderField && data.gender) {
                // البحث عن الخيار المناسب في القائمة المنسدلة
                const options = genderField.options;
                for (let i = 0; i < options.length; i++) {
                    if (options[i].text === data.gender) {
                        genderField.selectedIndex = i;
                        console.log('تم اختيار الجنس:', options[i].text);
                        break;
                    }
                }
            }
            
            // تاريخ انتهاء جواز السفر
            const passportExpiryField = document.getElementById('id_passport_expiry');
            if (passportExpiryField && data.passportExpiry) {
                passportExpiryField.value = data.passportExpiry;
                console.log('تم ملء تاريخ انتهاء جواز السفر:', data.passportExpiry);
            }
            
            // تاريخ الدخول (إذا كان متاحًا)
            const entryDateField = document.getElementById('id_entry_date');
            if (entryDateField && data.entryDate) {
                entryDateField.value = data.entryDate;
                console.log('تم ملء تاريخ الدخول:', data.entryDate);
            }
            
            // تاريخ انتهاء التأشيرة (إذا كان متاحًا)
            const visaExpiryField = document.getElementById('id_visa_expiry');
            if (visaExpiryField && data.expiryDate) {
                visaExpiryField.value = data.expiryDate;
                console.log('تم ملء تاريخ انتهاء التأشيرة:', data.expiryDate);
            }
            
            // التبديل إلى تبويب جواز السفر لعرض البيانات المستخرجة
            const passportTab = document.getElementById('passport-tab');
            if (passportTab) {
                passportTab.click();
            }
            
            console.log('تم ملء النموذج بنجاح');
        } catch (error) {
            console.error('خطأ في ملء النموذج:', error);
        }
    },
    
    /**
     * الحصول على قيمة ملف تعريف CSRF
     * @param {String} name اسم ملف التعريف
     * @returns {String} قيمة ملف التعريف
     */
    getCookie: function(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                // هل يبدأ هذا الملف بالاسم الذي نريده؟
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
};
