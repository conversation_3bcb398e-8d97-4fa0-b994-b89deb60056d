from django.urls import path
from . import views
from . import views_api

app_name = 'company'

urlpatterns = [
    # تسجيل الدخول والخروج
    path('login/', views.company_login, name='login'),
    path('logout/', views.company_logout, name='logout'),

    # التحقق من اسم الشركة
    path('get-company-name/', views.get_company_name, name='get_company_name'),

    # التحقق البسيط من الرقم التسلسلي
    path('check-serial/', views.check_serial, name='check_serial'),

    # التحقق من الرقم التسلسلي (API)
    path('accounts/api/check-serial-number/', views_api.check_serial_number_api, name='check_serial_number_api'),

    # مسار إضافي للتوافق مع الواجهة الأمامية
    path('api/accounts/api/check-serial-number/', views_api.check_serial_number_api),

    # مسار إضافي للتوافق مع الواجهة الأمامية (مطلق)
    path('../accounts/api/check-serial-number/', views_api.check_serial_number_api),

    # لوحة التحكم
    path('', views.company_dashboard, name='dashboard'),

    # العمال
    path('workers/', views.workers, name='workers'),

    # العملاء
    path('clients/', views.clients, name='clients'),

    # العقود
    path('contracts/', views.contracts, name='contracts'),

    # الخدمات
    path('services/', views.services, name='services'),

    # التقارير
    path('reports/', views.reports, name='reports'),

    # الأنشطة
    path('activities/', views.activities, name='activities'),

    # الملف الشخصي
    path('profile/', views.profile, name='profile'),

    # الإعدادات
    path('settings/', views.settings, name='settings'),
]
