from django.urls import path
from . import views
from . import api

urlpatterns = [
    # تم حذف جميع مسارات إعادة التوجيه واستخدام فقط المسارات الضرورية

    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.profile, name='profile'),
    path('switch-company/<int:company_id>/', views.switch_company, name='switch_company'),
    path('check-serial-number/', views.check_serial_number, name='check_serial_number'),
    path('test-database-connection/', views.test_database_connection, name='test_database_connection'),
    path('save-db-connection/', views.save_db_connection, name='save_db_connection'),

    # واجهة برمجة التطبيقات (API)
    path('api/verify-database/', api.verify_database, name='api_verify_database'),
    path('api/check-serial-number/', api.check_serial_number, name='api_check_serial_number'),
]
