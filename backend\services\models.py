from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

class ServiceType(models.Model):
    """نموذج لأنواع الخدمات"""
    SERVICE_TYPE_CHOICES = [
        ('regular', 'خدمة عادية'),
        ('full_day', 'خدمة يوم كامل'),
        ('custom', 'خدمة مخصصة'),
    ]

    service_type = models.CharField(max_length=20, choices=SERVICE_TYPE_CHOICES, default='regular', verbose_name='نوع الخدمة')
    name = models.CharField(max_length=100, verbose_name='اسم الخدمة')
    description = models.TextField(blank=True, null=True, verbose_name='وصف الخدمة')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='السعر الأساسي')
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='السعر بالساعة')
    overtime_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='سعر الساعات الإضافية')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'نوع خدمة'
        verbose_name_plural = 'أنواع الخدمات'
        ordering = ['name']


class CustomClient(models.Model):
    """نموذج للعملاء المخصصين (غير المسجلين في النظام)"""
    name = models.CharField(max_length=100, verbose_name='اسم العميل')
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'عميل مخصص'
        verbose_name_plural = 'عملاء مخصصين'
        ordering = ['name']


class CustomWorker(models.Model):
    """نموذج للعمال المخصصين (غير المسجلين في النظام)"""
    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]

    first_name = models.CharField(max_length=50, verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=50, verbose_name='الاسم الأخير')
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, default='male', verbose_name='الجنس')
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    nationality = models.CharField(max_length=50, blank=True, null=True, verbose_name='الجنسية')
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الأجر بالساعة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    class Meta:
        verbose_name = 'عامل مخصص'
        verbose_name_plural = 'عمال مخصصين'
        ordering = ['first_name', 'last_name']


class Service(models.Model):
    """نموذج للخدمات"""
    STATUS_CHOICES = [
        ('pending', 'قيد الانتظار'),
        ('booked', 'محجوز'),
        ('confirmed', 'مؤكد'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('unpaid', 'غير مدفوع'),
        ('partial', 'مدفوع جزئياً'),
        ('paid', 'مدفوع'),
    ]

    service_number = models.CharField(max_length=20, unique=True, verbose_name='رقم الخدمة')
    service_type = models.ForeignKey(ServiceType, on_delete=models.PROTECT, related_name='services', verbose_name='نوع الخدمة')

    # يمكن أن تكون الخدمة مرتبطة بعميل مسجل أو عميل مخصص
    client = models.ForeignKey('clients.Client', on_delete=models.PROTECT, related_name='services', null=True, blank=True, verbose_name='العميل')
    custom_client = models.ForeignKey(CustomClient, on_delete=models.PROTECT, related_name='services', null=True, blank=True, verbose_name='عميل مخصص')

    # يمكن أن تكون الخدمة مرتبطة بعامل مسجل أو عامل مخصص
    worker = models.ForeignKey('workers.Worker', on_delete=models.PROTECT, related_name='services', null=True, blank=True, verbose_name='العامل')
    custom_worker = models.ForeignKey(CustomWorker, on_delete=models.PROTECT, related_name='services', null=True, blank=True, verbose_name='عامل مخصص')

    start_date = models.DateField(verbose_name='تاريخ البدء')
    end_date = models.DateField(verbose_name='تاريخ الانتهاء')
    start_time = models.TimeField(verbose_name='وقت البدء')
    end_time = models.TimeField(verbose_name='وقت الانتهاء')

    location = models.CharField(max_length=255, blank=True, null=True, verbose_name='الموقع')
    description = models.TextField(blank=True, null=True, verbose_name='وصف الخدمة')

    price = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='السعر')
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name='ساعات إضافية')
    overtime_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='سعر الساعة الإضافية')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='السعر الإجمالي')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='الحالة')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='unpaid', verbose_name='حالة الدفع')

    is_fixed = models.BooleanField(default=False, verbose_name='حجز ثابت')
    is_recurring = models.BooleanField(default=False, verbose_name='متكرر')

    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='created_services', verbose_name='تم الإنشاء بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def save(self, *args, **kwargs):
        # إنشاء رقم خدمة فريد إذا لم يكن موجوداً
        if not self.service_number:
            self.service_number = f"SRV-{uuid.uuid4().hex[:8].upper()}"

        # حساب السعر الإجمالي
        self.total_price = self.price + (self.overtime_hours * self.overtime_rate)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.service_number} - {self.service_type.name}"

    class Meta:
        verbose_name = 'خدمة'
        verbose_name_plural = 'الخدمات'
        ordering = ['-start_date', 'start_time']


class FixedServiceSchedule(models.Model):
    """نموذج لجداول الخدمات الثابتة"""
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='fixed_schedules', verbose_name='الخدمة')
    day_of_week = models.IntegerField(validators=[MinValueValidator(0), MaxValueValidator(6)], verbose_name='يوم الأسبوع')
    start_time = models.TimeField(verbose_name='وقت البدء')
    end_time = models.TimeField(verbose_name='وقت الانتهاء')
    is_active = models.BooleanField(default=True, verbose_name='نشط')

    def __str__(self):
        days = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        return f"{self.service.service_number} - {days[self.day_of_week]} {self.start_time}-{self.end_time}"

    class Meta:
        verbose_name = 'جدول خدمة ثابت'
        verbose_name_plural = 'جداول الخدمات الثابتة'
        ordering = ['day_of_week', 'start_time']


class FixedServiceException(models.Model):
    """نموذج لاستثناءات جداول الخدمات الثابتة"""
    schedule = models.ForeignKey(FixedServiceSchedule, on_delete=models.CASCADE, related_name='exceptions', verbose_name='الجدول')
    date = models.DateField(verbose_name='التاريخ')
    reason = models.CharField(max_length=255, blank=True, null=True, verbose_name='السبب')

    def __str__(self):
        return f"{self.schedule} - استثناء {self.date}"

    class Meta:
        verbose_name = 'استثناء جدول ثابت'
        verbose_name_plural = 'استثناءات الجداول الثابتة'
        ordering = ['date']


class ServiceSettings(models.Model):
    """نموذج لإعدادات الخدمات"""
    setting_key = models.CharField(max_length=50, unique=True, verbose_name='المفتاح')
    setting_value = models.TextField(verbose_name='القيمة')
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name='الوصف')

    def __str__(self):
        return self.setting_key

    class Meta:
        verbose_name = 'إعداد الخدمة'
        verbose_name_plural = 'إعدادات الخدمات'
        ordering = ['setting_key']


# إضافة نموذج BookingSchedule للتوافق مع الكود القديم
class BookingSchedule(models.Model):
    """نموذج لجداول الحجز (للتوافق مع الكود القديم)"""
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='booking_schedules', verbose_name='الخدمة')
    date = models.DateField(verbose_name='التاريخ')
    status = models.CharField(max_length=20, default='scheduled', verbose_name='الحالة')

    def __str__(self):
        return f"{self.service.service_number} - {self.date}"

    class Meta:
        verbose_name = 'جدول حجز'
        verbose_name_plural = 'جداول الحجز'
        ordering = ['-date']


# إضافة نموذج Booking للتوافق مع الكود القديم
class Booking(models.Model):
    """نموذج للحجوزات (للتوافق مع الكود القديم)"""
    STATUS_CHOICES = [
        ('pending', 'قيد الانتظار'),
        ('confirmed', 'مؤكد'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    booking_number = models.CharField(max_length=20, unique=True, verbose_name='رقم الحجز')
    service_type = models.ForeignKey(ServiceType, on_delete=models.PROTECT, related_name='bookings', verbose_name='نوع الخدمة')
    client = models.ForeignKey('clients.Client', on_delete=models.PROTECT, related_name='bookings', null=True, blank=True, verbose_name='العميل')
    worker = models.ForeignKey('workers.Worker', on_delete=models.PROTECT, related_name='bookings', null=True, blank=True, verbose_name='العامل')
    date = models.DateField(verbose_name='التاريخ')
    start_time = models.TimeField(verbose_name='وقت البدء')
    end_time = models.TimeField(verbose_name='وقت الانتهاء')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='الحالة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.booking_number} - {self.date}"

    class Meta:
        verbose_name = 'حجز'
        verbose_name_plural = 'الحجوزات'
        ordering = ['-date', 'start_time']
