"""
أمر Django لاختبار إعدادات البريد الإلكتروني
"""
from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from backend.accounts.email_service import email_service, send_2fa_code, test_email_setup
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'اختبار إعدادات البريد الإلكتروني وإرسال رمز تجريبي'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='البريد الإلكتروني المراد الإرسال إليه'
        )
        parser.add_argument(
            '--type',
            type=str,
            choices=['simple', '2fa', 'connection'],
            default='connection',
            help='نوع الاختبار: simple (رسالة بسيطة), 2fa (رمز مصادقة), connection (اختبار الاتصال)'
        )

    def handle(self, *args, **options):
        email = options['email']
        test_type = options['type']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔧 بدء اختبار البريد الإلكتروني...')
        )
        
        # عرض الإعدادات الحالية
        self.show_email_settings()
        
        if test_type == 'connection':
            self.test_connection()
        elif test_type == 'simple':
            self.test_simple_email(email)
        elif test_type == '2fa':
            self.test_2fa_email(email)
    
    def show_email_settings(self):
        """عرض إعدادات البريد الإلكتروني الحالية"""
        self.stdout.write('\n📧 إعدادات البريد الإلكتروني:')
        self.stdout.write(f'   EMAIL_BACKEND: {settings.EMAIL_BACKEND}')
        self.stdout.write(f'   EMAIL_HOST: {settings.EMAIL_HOST}')
        self.stdout.write(f'   EMAIL_PORT: {settings.EMAIL_PORT}')
        self.stdout.write(f'   EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}')
        self.stdout.write(f'   EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}')
        self.stdout.write(f'   DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}')
        self.stdout.write(f'   EMAIL_TIMEOUT: {getattr(settings, "EMAIL_TIMEOUT", "غير محدد")}')
        self.stdout.write('')
    
    def test_connection(self):
        """اختبار الاتصال بخدمة البريد الإلكتروني"""
        self.stdout.write('🔗 اختبار الاتصال بخدمة البريد الإلكتروني...')
        
        try:
            success = test_email_setup()
            if success:
                self.stdout.write(
                    self.style.SUCCESS('✅ تم الاتصال بخدمة البريد الإلكتروني بنجاح!')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ فشل في الاتصال بخدمة البريد الإلكتروني')
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في اختبار الاتصال: {str(e)}')
            )
    
    def test_simple_email(self, email):
        """اختبار إرسال رسالة بسيطة"""
        self.stdout.write(f'📤 إرسال رسالة تجريبية إلى {email}...')
        
        try:
            subject = '🧪 رسالة اختبار من منصة استقدامي'
            message = '''
            مرحباً،
            
            هذه رسالة اختبار من منصة استقدامي للتأكد من عمل نظام البريد الإلكتروني.
            
            إذا وصلتك هذه الرسالة، فهذا يعني أن الإعدادات تعمل بشكل صحيح.
            
            الوقت: {time}
            
            منصة استقدامي
            نظام إدارة محمي
            '''.format(time=__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            success = send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                fail_silently=False
            )
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ تم إرسال الرسالة بنجاح إلى {email}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ فشل في إرسال الرسالة')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إرسال الرسالة: {str(e)}')
            )
    
    def test_2fa_email(self, email):
        """اختبار إرسال رمز المصادقة الثنائية"""
        self.stdout.write(f'🔐 إرسال رمز مصادقة ثنائية إلى {email}...')
        
        try:
            code = send_2fa_code(email, 'super_admin')
            
            if code:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ تم إرسال رمز المصادقة بنجاح!')
                )
                self.stdout.write(
                    self.style.WARNING(f'🔑 الرمز المرسل: {code}')
                )
                self.stdout.write(
                    self.style.HTTP_INFO(f'📧 تحقق من البريد الإلكتروني: {email}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ فشل في إرسال رمز المصادقة')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إرسال رمز المصادقة: {str(e)}')
            )
    
    def handle_error(self, error):
        """معالجة الأخطاء"""
        self.stdout.write(
            self.style.ERROR(f'❌ حدث خطأ: {str(error)}')
        )
        
        # اقتراحات لحل المشاكل الشائعة
        self.stdout.write('\n🔧 اقتراحات لحل المشكلة:')
        self.stdout.write('   1. تأكد من صحة بيانات البريد الإلكتروني في settings.py')
        self.stdout.write('   2. تأكد من تفعيل "كلمة مرور التطبيق" في Gmail')
        self.stdout.write('   3. تأكد من الاتصال بالإنترنت')
        self.stdout.write('   4. تحقق من إعدادات الجدار الناري')
        self.stdout.write('   5. جرب استخدام EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend" للاختبار')
        self.stdout.write('')
        self.stdout.write('📖 للمزيد من المساعدة:')
        self.stdout.write('   https://myaccount.google.com/security')
        self.stdout.write('   https://docs.djangoproject.com/en/stable/topics/email/')
