from django.contrib import admin
from .models import Client, ClientDocument, ClientContact


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'client_type', 'status', 'phone', 'email', 'created_at')
    list_filter = ('client_type', 'status', 'created_at')
    search_fields = ('name', 'phone', 'email', 'national_id', 'passport_number', 'commercial_register')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'client_type', 'status')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email', 'address')
        }),
        ('معلومات الهوية', {
            'fields': ('national_id', 'passport_number'),
            'classes': ('collapse',)
        }),
        ('معلومات الشركة', {
            'fields': ('commercial_register', 'tax_number'),
            'classes': ('collapse',)
        }),
        ('معلومات الكفيل', {
            'fields': ('sponsor_name', 'sponsor_phone', 'visa_number'),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('تواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ClientDocument)
class ClientDocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'client', 'document_type', 'uploaded_at')
    list_filter = ('document_type', 'uploaded_at')
    search_fields = ('title', 'client__name', 'description')
    ordering = ('-uploaded_at',)
    readonly_fields = ('uploaded_at',)


@admin.register(ClientContact)
class ClientContactAdmin(admin.ModelAdmin):
    list_display = ('name', 'client', 'position', 'phone', 'is_primary', 'created_at')
    list_filter = ('is_primary', 'created_at')
    search_fields = ('name', 'client__name', 'phone', 'email')
    ordering = ('-is_primary', 'name')
    readonly_fields = ('created_at',)
