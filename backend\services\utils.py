from django.utils import timezone
from backend.workers.models import Worker
from backend.services.models import BookingSchedule, ScheduleWorker, Booking
from datetime import datetime, date

def get_available_workers(date_obj=None, service_type=None, exclude_booking_id=None):
    """
    Calculate available workers for a specific date and service type.
    
    Args:
        date_obj: Date to check availability for (defaults to today)
        service_type: Type of service ('regular', 'full_day', 'custom')
        exclude_booking_id: Optional booking ID to exclude from calculations
        
    Returns:
        dict: {
            'available_workers': list of available worker objects,
            'total_workers': total count of workers of this type,
            'booked_workers': count of booked workers,
            'remaining_workers': count of remaining available workers
        }
    """
    # Default to today if no date provided
    if date_obj is None:
        date_obj = timezone.now().date()
    
    # Get all workers based on service type
    if service_type == 'custom':
        all_workers = Worker.objects.filter(is_active=True, worker_type='custom')
    else:
        all_workers = Worker.objects.filter(is_active=True, worker_type='contract')
    
    total_workers = all_workers.count()
    
    # Get all worker IDs
    all_worker_ids = set(all_workers.values_list('id', flat=True))
    
    # Get booked workers for the date
    booked_worker_ids = set()
    
    # 1. Workers assigned to schedules on this date
    schedule_workers = ScheduleWorker.objects.filter(
        schedule__date=date_obj,
        schedule__status__in=['scheduled', 'in_progress']
    ).select_related('schedule')
    
    # Exclude workers from a specific booking if needed
    if exclude_booking_id:
        schedule_workers = schedule_workers.exclude(schedule__booking_id=exclude_booking_id)
    
    for schedule_worker in schedule_workers:
        booked_worker_ids.add(schedule_worker.worker_id)
    
    # 2. Count workers requested but not yet assigned
    unassigned_workers_count = 0
    
    # Get bookings for this date that are not fully assigned
    bookings_on_date = Booking.objects.filter(
        schedules__date=date_obj,
        schedules__status__in=['scheduled', 'in_progress']
    ).distinct()
    
    # Exclude specific booking if needed
    if exclude_booking_id:
        bookings_on_date = bookings_on_date.exclude(id=exclude_booking_id)
    
    for booking in bookings_on_date:
        # Check if booking is not fully assigned
        assigned_workers = booking.booking_workers.count()
        if assigned_workers < booking.number_of_workers:
            unassigned_workers_count += (booking.number_of_workers - assigned_workers)
    
    # Calculate available worker IDs
    available_worker_ids = all_worker_ids - booked_worker_ids
    
    # Adjust for unassigned workers
    remaining_workers = max(0, len(available_worker_ids) - unassigned_workers_count)
    
    # Get available worker objects
    available_workers = all_workers.filter(id__in=available_worker_ids)
    
    # Limit to remaining count
    if unassigned_workers_count > 0 and available_workers.count() > remaining_workers:
        available_workers = available_workers[:remaining_workers]
    
    return {
        'available_workers': available_workers,
        'total_workers': total_workers,
        'booked_workers': len(booked_worker_ids) + unassigned_workers_count,
        'remaining_workers': remaining_workers
    }

def check_worker_availability(worker_id, check_date, start_time=None, end_time=None, exclude_booking_id=None):
    """
    Check if a specific worker is available on a given date and time range.
    
    Args:
        worker_id: ID of the worker to check
        check_date: Date to check availability for
        start_time: Optional start time to check
        end_time: Optional end time to check
        exclude_booking_id: Optional booking ID to exclude from calculations
        
    Returns:
        bool: True if worker is available, False otherwise
    """
    # Convert string date to date object if needed
    if isinstance(check_date, str):
        check_date = datetime.strptime(check_date, '%Y-%m-%d').date()
    
    # Check if worker exists and is active
    try:
        worker = Worker.objects.get(id=worker_id, is_active=True)
    except Worker.DoesNotExist:
        return False
    
    # Check if worker is already assigned to a schedule on this date
    worker_schedules = ScheduleWorker.objects.filter(
        worker_id=worker_id,
        schedule__date=check_date,
        schedule__status__in=['scheduled', 'in_progress']
    ).select_related('schedule')
    
    # Exclude schedules from a specific booking if needed
    if exclude_booking_id:
        worker_schedules = worker_schedules.exclude(schedule__booking_id=exclude_booking_id)
    
    # If no time range specified, any assignment means worker is not available
    if not start_time or not end_time:
        return worker_schedules.count() == 0
    
    # Check for time conflicts
    for worker_schedule in worker_schedules:
        schedule = worker_schedule.schedule
        
        # Skip cancelled schedules
        if schedule.status == 'cancelled':
            continue
        
        # Check if time ranges overlap
        if (start_time < schedule.end_time and end_time > schedule.start_time):
            return False
    
    return True
