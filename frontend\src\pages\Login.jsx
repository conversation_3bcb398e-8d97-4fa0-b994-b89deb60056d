import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { FaUser, FaLock, FaDatabase, FaSignInAlt } from 'react-icons/fa';

// Hooks
import { useAuth } from '../hooks/useAuth';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthService from '../services/auth.service';
import './styles/login.css';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    
    // التحقق من عدد المحاولات
    if (attempts >= 3) {
      setError('تم تجاوز الحد الأقصى للمحاولات. الرجاء المحاولة بعد دقيقة');
      return;
    }

    setLoading(true);

    if (!username || !password) {
      setError('الرجاء إدخال اسم المستخدم وكلمة المرور');
      setLoading(false);
      return;
    }

    try {
      await AuthService.login(username, password);
      navigate('/dashboard');
    } catch (error) {
      setError('فشل تسجيل الدخول. الرجاء التحقق من بياناتك');
      setAttempts(prev => prev + 1);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <form onSubmit={handleLogin} className="login-form">
        <h2>تسجيل الدخول</h2>
        {error && <div className="error-message">{error}</div>}
        <div className="form-group">
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="اسم المستخدم"
            disabled={loading || attempts >= 3}
            required
          />
        </div>
        <div className="form-group">
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="كلمة المرور"
            disabled={loading || attempts >= 3}
            required
            minLength={6}
          />
        </div>
        <button type="submit" disabled={loading || attempts >= 3}>
          {loading ? 'جاري التحميل...' : 'دخول'}
        </button>
      </form>
    </div>
  );
};

export default Login;
const Login = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const { login, isAuthenticated, loading, error } = useAuth();
  const navigate = useNavigate();

  const [serial1, setSerial1] = useState('');
  const [serial2, setSerial2] = useState('');
  const [serial3, setSerial3] = useState('');
  const [serial4, setSerial4] = useState('');

  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState({
    show: false,
    type: '',
    message: '',
    icon: ''
  });
  const [isCheckingConnection, setIsCheckingConnection] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Handle serial input
  const handleSerialInput = (e, current, next) => {
    const value = e.target.value.toUpperCase();

    // Update current field
    if (current === 'serial1') setSerial1(value);
    if (current === 'serial2') setSerial2(value);
    if (current === 'serial3') setSerial3(value);
    if (current === 'serial4') setSerial4(value);

    // Move to next field if current is filled
    if (value.length === 4 && next) {
      document.getElementById(next).focus();
    }
  };

  // Check database connection
  const checkConnection = () => {
    // Validate serial number
    if (!validateSerial()) {
      setConnectionStatus({
        show: true,
        type: 'error',
        message: 'يرجى إدخال رقم تسلسلي صحيح بتنسيق XXXX-XXXX-XXXX-XXXX',
        icon: 'fas fa-exclamation-circle'
      });
      return;
    }

    setIsCheckingConnection(true);

    // Simulate connection check (in a real app, this would be an API call)
    setTimeout(() => {
      setIsCheckingConnection(false);
      setIsConnected(true);
      setConnectionStatus({
        show: true,
        type: 'connected',
        message: 'تم الاتصال بقاعدة البيانات بنجاح',
        icon: 'fas fa-check-circle'
      });

      // Store connection state in session storage
      sessionStorage.setItem('dbConnected', 'true');
      sessionStorage.setItem('serialNumber', getFullSerial());
    }, 1500);
  };

  // Validate serial number
  const validateSerial = () => {
    const serialPattern = /^[A-Za-z0-9]{4}$/;

    return (
      serialPattern.test(serial1) &&
      serialPattern.test(serial2) &&
      serialPattern.test(serial3) &&
      serialPattern.test(serial4)
    );
  };

  // Get full serial number
  const getFullSerial = () => {
    return `${serial1}-${serial2}-${serial3}-${serial4}`;
  };

  // Handle login form submission
  const onSubmit = async (data) => {
    if (!isConnected) {
      setConnectionStatus({
        show: true,
        type: 'error',
        message: 'يرجى فحص الاتصال بقاعدة البيانات أولاً',
        icon: 'fas fa-exclamation-circle'
      });
      return;
    }

    const serialNumber = getFullSerial();
    const success = await login(data.username, data.password, serialNumber);

    if (success) {
      navigate('/dashboard');
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Information */}
      <div className="hidden lg:flex lg:flex-1 bg-gradient-to-br from-primary-dark to-primary text-white p-8">
        <div className="max-w-lg mx-auto flex flex-col justify-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl font-bold mb-4">نظام إدارة العمالة</h1>
            <p className="text-xl opacity-90 mb-8">نظام متكامل لإدارة العمالة والعقود وخدمات التنظيف</p>

            <div className="space-y-6 mb-12">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center mr-4">
                  <FaUser className="text-white" />
                </div>
                <span>إدارة شاملة للعمال والموظفين</span>
              </div>

              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center mr-4">
                  <FaDatabase className="text-white" />
                </div>
                <span>إدارة العقود والاتفاقيات</span>
              </div>

              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center mr-4">
                  <FaLock className="text-white" />
                </div>
                <span>نظام تنبيهات للمستندات والعقود</span>
              </div>
            </div>

            <div className="mt-auto">
              <img
                src="/assets/images/tech-platform.jpg"
                alt="منصة تقنية"
                className="w-full rounded-lg shadow-lg"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.style.display = 'none';
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Right side - Login form */}
      <div className="w-full lg:flex-1 flex items-center justify-center p-8 bg-primary-lighter">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="w-full max-w-md bg-white p-8 rounded-lg shadow-lg"
        >
          <div className="text-center mb-8">
            <img
              src="/assets/images/logo.png"
              alt="شعار نظام إدارة العمالة"
              className="h-20 mx-auto mb-4"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = 'https://via.placeholder.com/200x80?text=LOGO';
              }}
            />
            <h2 className="text-2xl font-bold text-primary">مرحباً بك في نظام إدارة العمالة</h2>
            <p className="text-neutral mt-2">يرجى تسجيل الدخول للوصول إلى لوحة التحكم</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Connection status */}
            {connectionStatus.show && (
              <div className={`p-4 rounded-lg ${connectionStatus.type === 'connected' ? 'alert-success' : 'alert-danger'}`}>
                <div className="flex items-center">
                  {connectionStatus.type === 'connected' ? (
                    <FaDatabase className="ml-2" />
                  ) : (
                    <FaDatabase className="ml-2" />
                  )}
                  <span>{connectionStatus.message}</span>
                </div>
              </div>
            )}

            {/* Username */}
            <div className="form-group">
              <label htmlFor="username" className="form-label">اسم المستخدم</label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                  <FaUser />
                </div>
                <input
                  type="text"
                  id="username"
                  className={`form-input pr-10 ${errors.username ? 'error' : ''}`}
                  placeholder="أدخل اسم المستخدم"
                  {...register('username', { required: 'يرجى إدخال اسم المستخدم' })}
                />
              </div>
              {errors.username && (
                <div className="form-error">{errors.username.message}</div>
              )}
            </div>

            {/* Password */}
            <div className="form-group">
              <label htmlFor="password" className="form-label">كلمة المرور</label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                  <FaLock />
                </div>
                <input
                  type="password"
                  id="password"
                  className={`form-input pr-10 ${errors.password ? 'error' : ''}`}
                  placeholder="أدخل كلمة المرور"
                  {...register('password', { required: 'يرجى إدخال كلمة المرور' })}
                />
              </div>
              {errors.password && (
                <div className="form-error">{errors.password.message}</div>
              )}
            </div>

            {/* Serial Number */}
            <div className="form-group">
              <label htmlFor="serial" className="form-label">الرقم التسلسلي</label>
              <div className="grid grid-cols-4 gap-2">
                <input
                  type="text"
                  id="serial1"
                  className="form-input text-center"
                  maxLength="4"
                  value={serial1}
                  onChange={(e) => handleSerialInput(e, 'serial1', 'serial2')}
                />
                <input
                  type="text"
                  id="serial2"
                  className="form-input text-center"
                  maxLength="4"
                  value={serial2}
                  onChange={(e) => handleSerialInput(e, 'serial2', 'serial3')}
                />
                <input
                  type="text"
                  id="serial3"
                  className="form-input text-center"
                  maxLength="4"
                  value={serial3}
                  onChange={(e) => handleSerialInput(e, 'serial3', 'serial4')}
                />
                <input
                  type="text"
                  id="serial4"
                  className="form-input text-center"
                  maxLength="4"
                  value={serial4}
                  onChange={(e) => handleSerialInput(e, 'serial4', null)}
                />
              </div>
            </div>

            {/* Check Connection Button */}
            <div className="form-group">
              <button
                type="button"
                className="btn btn-outline-primary w-full"
                onClick={checkConnection}
                disabled={isCheckingConnection}
              >
                {isCheckingConnection ? (
                  <div className="w-5 h-5 border-2 border-neutral border-t-primary rounded-full animate-spin ml-2"></div>
                ) : (
                  <FaDatabase className="ml-2" />
                )}
                <span>فحص الاتصال بقاعدة البيانات</span>
              </button>
            </div>

            {/* Login Button */}
            <div className="form-group">
              <button
                type="submit"
                className="btn btn-secondary w-full"
                disabled={loading || !isConnected}
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                ) : (
                  <FaSignInAlt className="ml-2" />
                )}
                <span>تسجيل الدخول</span>
              </button>
            </div>

            {/* Error message */}
            {error && (
              <div className="alert-danger p-3 rounded-lg text-sm">
                {error}
              </div>
            )}
          </form>

          <div className="mt-8 text-center text-neutral text-sm">
            <p>&copy; {new Date().getFullYear()} نظام إدارة العمالة - الإصدار 2.5.0</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;
