"""
وحدة للتعامل مع الاتصال بقواعد بيانات الشركات
تتضمن وظائف للاتصال بقواعد بيانات الشركات وتنفيذ استعلامات عليها
"""

from django.db import connections, transaction
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging
import os
import sqlite3
try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
from contextlib import contextmanager
from .database_utils import get_database_path

logger = logging.getLogger(__name__)

def get_db_config_from_company(company):
    """
    الحصول على إعدادات قاعدة البيانات من كائن الشركة

    Args:
        company: كائن الشركة

    Returns:
        dict: إعدادات قاعدة البيانات
    """
    # الحصول على معلومات قاعدة البيانات من كائن الشركة
    db_name = company.database_name if hasattr(company, 'database_name') else company.db_name
    db_user = company.database_user if hasattr(company, 'database_user') else company.db_user
    db_password = company.database_password if hasattr(company, 'database_password') else company.db_password
    db_host = company.database_host if hasattr(company, 'database_host') else company.db_host

    # تحديد نوع قاعدة البيانات (SQLite أو MySQL)
    # إذا كان المضيف ليس localhost أو 127.0.0.1، فهو MySQL
    is_mysql = db_host not in ['localhost', '127.0.0.1']

    return {
        'db_name': db_name,
        'db_user': db_user,
        'db_password': db_password,
        'db_host': db_host,
        'is_mysql': is_mysql
    }

def get_connection_name(company):
    """
    الحصول على اسم الاتصال بقاعدة البيانات

    Args:
        company: كائن الشركة

    Returns:
        str: اسم الاتصال
    """
    # استخدام الرقم التسلسلي للشركة كجزء من اسم الاتصال
    serial = company.serial_number if hasattr(company, 'serial_number') else company.id
    return f"company_{str(serial).replace('-', '_')}"

def setup_company_db_connection(company):
    """
    إعداد اتصال قاعدة بيانات الشركة في إعدادات Django

    Args:
        company: كائن الشركة الذي يحتوي على معلومات الاتصال بقاعدة البيانات

    Returns:
        bool: True إذا تم إعداد الاتصال بنجاح، False خلاف ذلك
    """
    try:
        # التحقق من وجود الشركة
        if not company:
            logger.error("لم يتم تحديد الشركة")
            return False

        # التحقق من حالة الشركة
        if company.status != 'active':
            logger.error(f"الشركة {company.name} غير نشطة")
            return False

        # الحصول على اسم الاتصال
        connection_name = get_connection_name(company)

        # الحصول على إعدادات قاعدة البيانات
        db_config_info = get_db_config_from_company(company)

        # تحديد نوع قاعدة البيانات (SQLite أو MySQL)
        if db_config_info['is_mysql']:
            # استخدام MySQL
            db_config = {
                'ENGINE': 'django.db.backends.mysql',
                'NAME': db_config_info['db_name'],
                'USER': db_config_info['db_user'],
                'PASSWORD': db_config_info['db_password'],
                'HOST': db_config_info['db_host'],
                'PORT': '3306',
                'OPTIONS': {
                    'charset': 'utf8mb4',
                    'use_unicode': True,
                    'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                    'autocommit': True,
                },
                'CONN_MAX_AGE': 60,  # إبقاء الاتصال مفتوحًا لمدة 60 ثانية
            }
        else:
            # استخدام SQLite
            db_path = get_database_path(db_config_info['db_name'])

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(db_path):
                logger.error(f"ملف قاعدة البيانات غير موجود: {db_path}")
                return False

            db_config = {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': str(db_path),
                'USER': db_config_info['db_user'],
                'PASSWORD': db_config_info['db_password'],
                'HOST': db_config_info['db_host'],
                'CONN_MAX_AGE': 60,  # إبقاء الاتصال مفتوحًا لمدة 60 ثانية
            }

        # إضافة قاعدة البيانات إلى إعدادات Django
        settings.DATABASES[connection_name] = db_config

        # إعادة تهيئة الاتصال إذا كان موجودًا
        if connection_name in connections:
            try:
                connections[connection_name].close()
                del connections[connection_name]
                logger.debug(f"تم إغلاق وإزالة الاتصال الموجود: {connection_name}")
            except Exception as close_error:
                logger.warning(f"خطأ في إغلاق اتصال قاعدة البيانات: {str(close_error)}")

        # اختبار الاتصال
        try:
            connections[connection_name].ensure_connection()

            # التحقق من صحة الاتصال بتنفيذ استعلام بسيط
            with connections[connection_name].cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    logger.info(f"تم التحقق من صحة اتصال قاعدة البيانات: {connection_name}")
                else:
                    logger.error(f"فشل اختبار اتصال قاعدة البيانات: {connection_name}")
                    return False
        except Exception as test_error:
            logger.error(f"خطأ في اختبار اتصال قاعدة البيانات: {str(test_error)}")
            return False

        logger.info(f"تم إعداد الاتصال بقاعدة بيانات الشركة {company.name} بنجاح")
        return True

    except Exception as e:
        logger.error(f"فشل في إعداد الاتصال بقاعدة بيانات الشركة {company.name}: {str(e)}")
        return False

@contextmanager
def company_db_connection(company):
    """
    سياق للاتصال بقاعدة بيانات الشركة

    Args:
        company: كائن الشركة الذي يحتوي على معلومات الاتصال بقاعدة البيانات

    Yields:
        اسم الاتصال بقاعدة البيانات
    """
    connection_name = get_connection_name(company)

    # إعداد الاتصال
    success = setup_company_db_connection(company)

    if not success:
        raise ImproperlyConfigured(f"فشل في إعداد الاتصال بقاعدة بيانات الشركة {company.name}")

    try:
        yield connection_name
    finally:
        # إغلاق الاتصال
        if connection_name in connections:
            connections[connection_name].close()

def execute_query_on_company_db(company, query, params=None):
    """
    تنفيذ استعلام على قاعدة بيانات الشركة

    Args:
        company: كائن الشركة
        query: استعلام SQL
        params: معلمات الاستعلام (اختياري)

    Returns:
        نتيجة الاستعلام
    """
    with company_db_connection(company) as connection_name:
        with connections[connection_name].cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # إذا كان الاستعلام SELECT، قم بإرجاع النتائج
            if query.strip().upper().startswith('SELECT'):
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]

            # إذا كان الاستعلام INSERT/UPDATE/DELETE، قم بإرجاع عدد الصفوف المتأثرة
            return cursor.rowcount

@contextmanager
def company_db_transaction(company):
    """
    سياق للمعاملات على قاعدة بيانات الشركة

    Args:
        company: كائن الشركة

    Yields:
        اسم الاتصال بقاعدة البيانات
    """
    with company_db_connection(company) as connection_name:
        with transaction.atomic(using=connection_name):
            yield connection_name

def test_company_db_connection(company):
    """
    اختبار الاتصال بقاعدة بيانات الشركة

    Args:
        company: كائن الشركة

    Returns:
        dict: نتيجة الاختبار
    """
    try:
        # الحصول على إعدادات قاعدة البيانات
        db_config_info = get_db_config_from_company(company)
        logger.info(f"اختبار الاتصال بقاعدة بيانات الشركة {company.name} ({db_config_info['db_name']})")

        if db_config_info['is_mysql']:
            if not MYSQL_AVAILABLE:
                logger.error("وحدة mysql.connector غير متوفرة")
                return {
                    'success': False,
                    'message': 'وحدة mysql.connector غير متوفرة. قم بتثبيتها باستخدام pip install mysql-connector-python',
                    'db_type': 'MySQL',
                    'error': 'Module not available',
                    'tables_count': 0,
                    'tables': []
                }

            try:
                # اختبار الاتصال بقاعدة بيانات MySQL
                logger.info(f"محاولة الاتصال بقاعدة بيانات MySQL: {db_config_info['db_name']} على {db_config_info['db_host']}")
                connection = mysql.connector.connect(
                    host=db_config_info['db_host'],
                    user=db_config_info['db_user'],
                    password=db_config_info['db_password'],
                    database=db_config_info['db_name']
                )

                cursor = connection.cursor()

                # التحقق من وجود الجداول
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                logger.info(f"تم العثور على {len(tables)} جدول في قاعدة البيانات")

                # إغلاق الاتصال
                cursor.close()
                connection.close()

                return {
                    'success': True,
                    'message': 'تم الاتصال بقاعدة البيانات بنجاح',
                    'db_type': 'MySQL',
                    'tables_count': len(tables),
                    'tables': [table[0] for table in tables]
                }
            except Exception as mysql_error:
                logger.error(f"فشل في الاتصال بقاعدة بيانات MySQL: {str(mysql_error)}")
                return {
                    'success': False,
                    'message': f'فشل في الاتصال بقاعدة بيانات MySQL: {str(mysql_error)}',
                    'db_type': 'MySQL',
                    'error': str(mysql_error),
                    'tables_count': 0,
                    'tables': []
                }
        else:
            # اختبار الاتصال بقاعدة بيانات SQLite
            db_path = get_database_path(db_config_info['db_name'])
            logger.info(f"محاولة الاتصال بقاعدة بيانات SQLite: {db_path}")

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(db_path):
                logger.warning(f"ملف قاعدة البيانات غير موجود: {db_path}")

                # إنشاء المجلد إذا لم يكن موجودًا
                os.makedirs(os.path.dirname(db_path), exist_ok=True)

                # إنشاء ملف قاعدة بيانات فارغ
                try:
                    logger.info(f"إنشاء ملف قاعدة بيانات جديد: {db_path}")
                    conn = sqlite3.connect(db_path)
                    conn.close()
                    logger.info(f"تم إنشاء ملف قاعدة البيانات بنجاح: {db_path}")
                except Exception as create_error:
                    logger.error(f"فشل في إنشاء ملف قاعدة البيانات: {str(create_error)}")
                    return {
                        'success': False,
                        'message': f'فشل في إنشاء ملف قاعدة البيانات: {str(create_error)}',
                        'db_type': 'SQLite',
                        'error': str(create_error),
                        'tables_count': 0,
                        'tables': []
                    }

            try:
                # الاتصال بقاعدة البيانات
                connection = sqlite3.connect(db_path)
                cursor = connection.cursor()

                # التحقق من وجود الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                logger.info(f"تم العثور على {len(tables)} جدول في قاعدة البيانات")

                # إغلاق الاتصال
                cursor.close()
                connection.close()

                return {
                    'success': True,
                    'message': 'تم الاتصال بقاعدة البيانات بنجاح',
                    'db_type': 'SQLite',
                    'tables_count': len(tables),
                    'tables': [table[0] for table in tables]
                }
            except Exception as sqlite_error:
                logger.error(f"فشل في الاتصال بقاعدة بيانات SQLite: {str(sqlite_error)}")
                return {
                    'success': False,
                    'message': f'فشل في الاتصال بقاعدة بيانات SQLite: {str(sqlite_error)}',
                    'db_type': 'SQLite',
                    'error': str(sqlite_error),
                    'tables_count': 0,
                    'tables': []
                }

    except Exception as e:
        logger.error(f"فشل في اختبار الاتصال بقاعدة بيانات الشركة {company.name}: {str(e)}")
        return {
            'success': False,
            'message': f'فشل في الاتصال بقاعدة البيانات: {str(e)}',
            'error': str(e),
            'tables_count': 0,
            'tables': []
        }

def get_company_tables(company):
    """
    الحصول على قائمة الجداول في قاعدة بيانات الشركة

    Args:
        company: كائن الشركة

    Returns:
        list: قائمة الجداول
    """
    try:
        with company_db_connection(company) as connection_name:
            with connections[connection_name].cursor() as cursor:
                # تحديد نوع قاعدة البيانات
                db_config_info = get_db_config_from_company(company)

                if db_config_info['is_mysql']:
                    # استعلام MySQL
                    cursor.execute("SHOW TABLES")
                else:
                    # استعلام SQLite
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")

                tables = [row[0] for row in cursor.fetchall()]
                return tables

    except Exception as e:
        logger.error(f"فشل في الحصول على قائمة الجداول: {str(e)}")
        return []

def get_table_structure(company, table_name):
    """
    الحصول على هيكل جدول في قاعدة بيانات الشركة

    Args:
        company: كائن الشركة
        table_name: اسم الجدول

    Returns:
        list: قائمة الأعمدة
    """
    try:
        with company_db_connection(company) as connection_name:
            with connections[connection_name].cursor() as cursor:
                # تحديد نوع قاعدة البيانات
                db_config_info = get_db_config_from_company(company)

                if db_config_info['is_mysql']:
                    # استعلام MySQL
                    cursor.execute(f"DESCRIBE `{table_name}`")
                    columns = [{'name': row[0], 'type': row[1], 'null': row[2], 'key': row[3], 'default': row[4], 'extra': row[5]} for row in cursor.fetchall()]
                else:
                    # استعلام SQLite
                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns = [{'name': row[1], 'type': row[2], 'null': 'YES' if row[3] == 0 else 'NO', 'key': 'PRI' if row[5] == 1 else '', 'default': row[4], 'extra': ''} for row in cursor.fetchall()]

                return columns

    except Exception as e:
        logger.error(f"فشل في الحصول على هيكل الجدول {table_name}: {str(e)}")
        return []

def get_table_data(company, table_name, limit=100, offset=0, where=None, order_by=None):
    """
    الحصول على بيانات جدول في قاعدة بيانات الشركة

    Args:
        company: كائن الشركة
        table_name: اسم الجدول
        limit: عدد السجلات (اختياري)
        offset: بداية السجلات (اختياري)
        where: شرط WHERE (اختياري)
        order_by: ترتيب النتائج (اختياري)

    Returns:
        list: قائمة السجلات
    """
    try:
        with company_db_connection(company) as connection_name:
            with connections[connection_name].cursor() as cursor:
                # بناء الاستعلام
                query = f"SELECT * FROM `{table_name}`"

                if where:
                    query += f" WHERE {where}"

                if order_by:
                    query += f" ORDER BY {order_by}"

                query += f" LIMIT {limit} OFFSET {offset}"

                # تنفيذ الاستعلام
                cursor.execute(query)

                # الحصول على أسماء الأعمدة
                columns = [col[0] for col in cursor.description]

                # الحصول على البيانات
                data = [dict(zip(columns, row)) for row in cursor.fetchall()]

                return data

    except Exception as e:
        logger.error(f"فشل في الحصول على بيانات الجدول {table_name}: {str(e)}")
        return []
