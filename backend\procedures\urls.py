from django.urls import path
from . import views

app_name = 'procedures'

urlpatterns = [
    path('', views.procedures_dashboard, name='procedures_dashboard'),
    path('list/', views.procedure_list, name='procedure_list'),
    path('worker/<int:worker_id>/', views.worker_procedures, name='worker_procedures'),

    # Blood Test URLs
    path('blood-test/create/<int:worker_id>/', views.blood_test_create, name='blood_test_create'),
    path('blood-test/<int:pk>/', views.blood_test_detail, name='blood_test_detail'),
    path('blood-test/<int:pk>/update/', views.blood_test_update, name='blood_test_update'),
    path('blood-test/<int:pk>/delete/', views.blood_test_delete, name='blood_test_delete'),

    # Sponsorship Transfer URLs
    path('sponsorship-transfer/create/<int:worker_id>/', views.sponsorship_transfer_create, name='sponsorship_transfer_create'),
    path('sponsorship-transfer/<int:pk>/', views.sponsorship_transfer_detail, name='sponsorship_transfer_detail'),
    path('sponsorship-transfer/<int:pk>/update/', views.sponsorship_transfer_update, name='sponsorship_transfer_update'),
    path('sponsorship-transfer/<int:pk>/delete/', views.sponsorship_transfer_delete, name='sponsorship_transfer_delete'),

    # Operation URLs
    path('operation/create/<int:worker_id>/', views.operation_create, name='operation_create'),
    path('operation/<int:pk>/', views.operation_detail, name='operation_detail'),
    path('operation/<int:pk>/update/', views.operation_update, name='operation_update'),
    path('operation/<int:pk>/delete/', views.operation_delete, name='operation_delete'),

    # Work Permit URLs
    path('work-permit/create/<int:worker_id>/', views.work_permit_create, name='work_permit_create'),
    path('work-permit/<int:pk>/', views.work_permit_detail, name='work_permit_detail'),
    path('work-permit/<int:pk>/update/', views.work_permit_update, name='work_permit_update'),
    path('work-permit/<int:pk>/delete/', views.work_permit_delete, name='work_permit_delete'),

    # Residence ID URLs
    path('residence-id/create/<int:worker_id>/', views.residence_id_create, name='residence_id_create'),
    path('residence-id/<int:pk>/', views.residence_id_detail, name='residence_id_detail'),
    path('residence-id/<int:pk>/update/', views.residence_id_update, name='residence_id_update'),
    path('residence-id/<int:pk>/delete/', views.residence_id_delete, name='residence_id_delete'),

    # Branch Transfer URLs
    path('branch-transfer/create/<int:worker_id>/', views.branch_transfer_create, name='branch_transfer_create'),
    path('branch-transfer/<int:pk>/', views.branch_transfer_detail, name='branch_transfer_detail'),
    path('branch-transfer/<int:pk>/update/', views.branch_transfer_update, name='branch_transfer_update'),
    path('branch-transfer/<int:pk>/delete/', views.branch_transfer_delete, name='branch_transfer_delete'),

    # Attachment URLs
    path('attachment/add/<str:procedure_type>/<int:procedure_id>/', views.add_attachment, name='add_attachment'),
    path('attachment/<int:pk>/delete/', views.delete_attachment, name='delete_attachment'),
]
