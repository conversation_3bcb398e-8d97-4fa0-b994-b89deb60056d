{% extends 'layouts/super_admin.html' %}

{% block title %}تقييدات عناوين IP{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-red-600 to-pink-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-ban ml-2"></i>
                    تقييدات عناوين IP
                </h1>
                <p class="text-red-100">
                    إدارة عناوين IP المسموح لها بالوصول إلى حساب المسؤول الأعلى
                </p>
            </div>
            <div class="text-center">
                <i class="fas fa-shield-alt text-4xl text-red-200"></i>
            </div>
        </div>
    </div>

    <!-- Security Warning -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">
                    تحذير أمني مهم
                </h3>
                <div class="text-yellow-700 space-y-2">
                    <p>• تأكد من إضافة عنوان IP الحالي قبل تفعيل التقييد</p>
                    <p>• عنوان IP الحالي: <strong>{{ request.META.REMOTE_ADDR }}</strong></p>
                    <p>• في حالة عدم إضافة عنوان IP الحالي، قد تفقد الوصول إلى حسابك</p>
                    <p>• يمكن إضافة عدة عناوين IP مفصولة بفواصل</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Status -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-info-circle ml-2"></i>
                الحالة الحالية
            </h2>
            {% if profile.ip_restriction_enabled %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <i class="fas fa-ban ml-1"></i>
                    التقييد مُفعل
                </span>
            {% else %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <i class="fas fa-check-circle ml-1"></i>
                    التقييد معطل
                </span>
            {% endif %}
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Current IP -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-map-marker-alt ml-2"></i>
                    عنوان IP الحالي
                </h3>
                <p class="text-lg font-mono text-blue-600">{{ request.META.REMOTE_ADDR }}</p>
                <p class="text-sm text-gray-500 mt-1">
                    هذا هو عنوان IP الذي تستخدمه حالياً
                </p>
            </div>

            <!-- Restriction Status -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-shield-alt ml-2"></i>
                    حالة التقييد
                </h3>
                {% if profile.ip_restriction_enabled %}
                    <p class="text-lg text-red-600 font-medium">مُفعل</p>
                    <p class="text-sm text-gray-500 mt-1">
                        يُسمح فقط بعناوين IP المحددة
                    </p>
                {% else %}
                    <p class="text-lg text-green-600 font-medium">معطل</p>
                    <p class="text-sm text-gray-500 mt-1">
                        يُسمح بالوصول من أي عنوان IP
                    </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- IP Restrictions Settings -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-cog ml-2"></i>
            إعدادات تقييد IP
        </h2>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Enable/Disable IP Restriction -->
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                    <h3 class="font-medium text-gray-900">تفعيل تقييد عناوين IP</h3>
                    <p class="text-sm text-gray-500">
                        عند التفعيل، سيُسمح فقط بعناوين IP المحددة أدناه
                    </p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" 
                           name="ip_restriction_enabled" 
                           class="sr-only peer"
                           {% if profile.ip_restriction_enabled %}checked{% endif %}>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <!-- Allowed IPs -->
            <div>
                <label for="allowed_ips" class="block text-sm font-medium text-gray-700 mb-2">
                    عناوين IP المسموحة
                    <span class="text-red-500">*</span>
                </label>
                <textarea id="allowed_ips" 
                          name="allowed_ips" 
                          rows="4"
                          placeholder="127.0.0.1&#10;*************&#10;***********/24"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm">{{ profile.allowed_ips|default:'' }}</textarea>
                
                <div class="mt-2 text-sm text-gray-500">
                    <p class="mb-1"><strong>تنسيقات مدعومة:</strong></p>
                    <ul class="list-disc list-inside space-y-1">
                        <li><code>127.0.0.1</code> - عنوان IP واحد</li>
                        <li><code>***********/24</code> - نطاق شبكة</li>
                        <li><code>localhost</code> - اسم المضيف المحلي</li>
                        <li>عنوان واحد في كل سطر أو مفصول بفواصل</li>
                    </ul>
                </div>
            </div>

            <!-- Quick Add Current IP -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-blue-800">إضافة عنوان IP الحالي</h3>
                        <p class="text-sm text-blue-600">
                            إضافة {{ request.META.REMOTE_ADDR }} إلى القائمة المسموحة
                        </p>
                    </div>
                    <button type="button" 
                            onclick="addCurrentIP()"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة
                    </button>
                </div>
            </div>

            <!-- Common IP Ranges -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-800 mb-3">نطاقات IP شائعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <button type="button" 
                            onclick="addIPRange('127.0.0.1')"
                            class="text-left p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
                        <div class="font-mono text-sm">127.0.0.1</div>
                        <div class="text-xs text-gray-500">المضيف المحلي</div>
                    </button>
                    
                    <button type="button" 
                            onclick="addIPRange('***********/24')"
                            class="text-left p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
                        <div class="font-mono text-sm">***********/24</div>
                        <div class="text-xs text-gray-500">شبكة محلية</div>
                    </button>
                    
                    <button type="button" 
                            onclick="addIPRange('10.0.0.0/8')"
                            class="text-left p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
                        <div class="font-mono text-sm">10.0.0.0/8</div>
                        <div class="text-xs text-gray-500">شبكة خاصة</div>
                    </button>
                    
                    <button type="button" 
                            onclick="addIPRange('**********/12')"
                            class="text-left p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
                        <div class="font-mono text-sm">**********/12</div>
                        <div class="text-xs text-gray-500">شبكة خاصة</div>
                    </button>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{% url 'super_admin:user_profile' %}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للملف الشخصي
                </a>
                
                <button type="submit" 
                        onclick="return confirmSave()"
                        class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 class="text-lg font-medium text-blue-800 mb-3">
            <i class="fas fa-question-circle ml-2"></i>
            معلومات مهمة
        </h3>
        <div class="text-blue-700 space-y-2 text-sm">
            <p><strong>• الأمان:</strong> تقييد IP يزيد من أمان حسابك بشكل كبير</p>
            <p><strong>• المرونة:</strong> يمكن إضافة عدة عناوين IP أو نطاقات شبكة</p>
            <p><strong>• الطوارئ:</strong> في حالة فقدان الوصول، اتصل بمدير النظام</p>
            <p><strong>• التحديث:</strong> يمكن تحديث القائمة في أي وقت</p>
        </div>
    </div>
</div>

<script>
function addCurrentIP() {
    const textarea = document.getElementById('allowed_ips');
    const currentIP = '{{ request.META.REMOTE_ADDR }}';
    
    if (textarea.value.includes(currentIP)) {
        alert('عنوان IP الحالي موجود بالفعل في القائمة');
        return;
    }
    
    if (textarea.value.trim()) {
        textarea.value += '\n' + currentIP;
    } else {
        textarea.value = currentIP;
    }
    
    // تمييز النص المضاف
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length - currentIP.length, textarea.value.length);
}

function addIPRange(ipRange) {
    const textarea = document.getElementById('allowed_ips');
    
    if (textarea.value.includes(ipRange)) {
        alert('هذا النطاق موجود بالفعل في القائمة');
        return;
    }
    
    if (textarea.value.trim()) {
        textarea.value += '\n' + ipRange;
    } else {
        textarea.value = ipRange;
    }
    
    // تمييز النص المضاف
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length - ipRange.length, textarea.value.length);
}

function confirmSave() {
    const isEnabled = document.querySelector('input[name="ip_restriction_enabled"]').checked;
    const allowedIPs = document.getElementById('allowed_ips').value.trim();
    const currentIP = '{{ request.META.REMOTE_ADDR }}';
    
    if (isEnabled) {
        if (!allowedIPs) {
            alert('يجب إدخال عناوين IP مسموحة عند تفعيل التقييد');
            return false;
        }
        
        if (!allowedIPs.includes(currentIP) && !allowedIPs.includes('127.0.0.1') && !allowedIPs.includes('localhost')) {
            if (!confirm('تحذير: عنوان IP الحالي (' + currentIP + ') غير موجود في القائمة المسموحة.\n\nقد تفقد الوصول إلى حسابك!\n\nهل تريد المتابعة؟')) {
                return false;
            }
        }
    }
    
    return confirm('هل أنت متأكد من حفظ إعدادات تقييد IP؟');
}
</script>
{% endblock %}
