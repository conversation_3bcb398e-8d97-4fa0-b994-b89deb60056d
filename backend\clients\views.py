from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Client, ClientDocument, ClientContact
from .forms import ClientForm, ClientDocumentForm, ClientContactForm, ClientSearchForm
from backend.utils.modern_messages import ClientMessages, CommonMessages, json_success, json_error


@login_required
def client_list(request):
    """عرض قائمة العملاء مع البحث والتصفية"""

    # البحث والتصفية
    search_form = ClientSearchForm(request.GET)
    clients = Client.objects.all()

    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        client_type = search_form.cleaned_data.get('client_type')
        status = search_form.cleaned_data.get('status')

        if search_query:
            clients = clients.filter(
                Q(name__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(national_id__icontains=search_query) |
                Q(passport_number__icontains=search_query) |
                Q(commercial_register__icontains=search_query)
            )

        if client_type:
            clients = clients.filter(client_type=client_type)

        if status:
            clients = clients.filter(status=status)

    # ترتيب النتائج
    clients = clients.order_by('-created_at')

    # التصفح
    paginator = Paginator(clients, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    stats = {
        'total': Client.objects.count(),
        'active': Client.objects.filter(status='active').count(),
        'inactive': Client.objects.filter(status='inactive').count(),
        'individuals': Client.objects.filter(client_type='individual').count(),
        'companies': Client.objects.filter(client_type='company').count(),
        'government': Client.objects.filter(client_type='government').count(),
    }

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'stats': stats,
        'title': 'إدارة العملاء'
    }

    return render(request, 'clients/client_list.html', context)


@login_required
def client_detail(request, client_id):
    """عرض تفاصيل العميل"""

    client = get_object_or_404(Client, id=client_id)

    # الحصول على المستندات وجهات الاتصال
    documents = ClientDocument.objects.filter(client=client).order_by('-uploaded_at')
    contacts = ClientContact.objects.filter(client=client).order_by('-is_primary', 'name')

    context = {
        'client': client,
        'documents': documents,
        'contacts': contacts,
        'title': f'تفاصيل العميل - {client.name}'
    }

    return render(request, 'clients/client_detail.html', context)


@login_required
def client_create(request):
    """إضافة عميل جديد"""

    if request.method == 'POST':
        form = ClientForm(request.POST)
        if form.is_valid():
            # حفظ العميل الجديد
            client = form.save()

            ClientMessages.created(request, client.name)
            return redirect('clients:client_detail', client_id=client.id)
        else:
            CommonMessages.validation_error(request)
    else:
        form = ClientForm()

    context = {
        'form': form,
        'title': 'إضافة عميل جديد',
        'action': 'create'
    }

    return render(request, 'clients/client_form.html', context)


@login_required
def client_edit(request, client_id):
    """تعديل بيانات العميل"""

    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        form = ClientForm(request.POST, instance=client)
        if form.is_valid():
            form.save()
            ClientMessages.updated(request, client.name)
            return redirect('clients:client_detail', client_id=client.id)
        else:
            CommonMessages.validation_error(request)
    else:
        form = ClientForm(instance=client)

    context = {
        'form': form,
        'client': client,
        'title': f'تعديل العميل - {client.name}',
        'action': 'edit'
    }

    return render(request, 'clients/client_form.html', context)


@login_required
@require_http_methods(["POST"])
def client_delete(request, client_id):
    """حذف العميل"""

    client = get_object_or_404(Client, id=client_id)

    try:
        client_name = client.name
        client.delete()
        return json_success(f'تم حذف العميل {client_name} بنجاح', icon='delete')
    except Exception as e:
        return json_error(f'خطأ في الحذف: {str(e)}')


@login_required
def client_toggle_status(request, client_id):
    """تغيير حالة العميل"""

    if request.method == 'POST':
        client = get_object_or_404(Client, id=client_id)

        # تبديل الحالة
        if client.status == 'active':
            client.status = 'inactive'
            message = f'تم إلغاء تفعيل العميل {client.name}'
        else:
            client.status = 'active'
            message = f'تم تفعيل العميل {client.name}'

        client.save()
        messages.success(request, message)

        return JsonResponse({
            'success': True,
            'new_status': client.status,
            'message': message
        })

    return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})


@login_required
def client_document_upload(request, client_id):
    """رفع مستند للعميل"""

    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        form = ClientDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.client = client
            document.save()

            messages.success(request, 'تم رفع المستند بنجاح')
            return redirect('clients:client_detail', client_id=client.id)
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = ClientDocumentForm()

    context = {
        'form': form,
        'client': client,
        'title': f'رفع مستند - {client.name}'
    }

    return render(request, 'clients/document_upload.html', context)


@login_required
def client_contact_add(request, client_id):
    """إضافة جهة اتصال للعميل"""

    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        form = ClientContactForm(request.POST)
        if form.is_valid():
            contact = form.save(commit=False)
            contact.client = client
            contact.save()

            messages.success(request, 'تم إضافة جهة الاتصال بنجاح')
            return redirect('clients:client_detail', client_id=client.id)
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = ClientContactForm()

    context = {
        'form': form,
        'client': client,
        'title': f'إضافة جهة اتصال - {client.name}'
    }

    return render(request, 'clients/contact_add.html', context)


@login_required
def client_export(request):
    """تصدير قائمة العملاء"""

    import csv
    from django.utils import timezone

    response = HttpResponse(content_type='text/csv; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="clients_{timezone.now().strftime("%Y%m%d")}.csv"'
    response.write('\ufeff')  # BOM for UTF-8

    writer = csv.writer(response)
    writer.writerow([
        'الاسم', 'نوع العميل', 'الحالة', 'الهاتف', 'البريد الإلكتروني',
        'العنوان', 'رقم الهوية', 'رقم جواز السفر', 'السجل التجاري',
        'الرقم الضريبي', 'تاريخ الإنشاء'
    ])

    clients = Client.objects.all().order_by('name')

    for client in clients:
        writer.writerow([
            client.name,
            client.get_client_type_display_ar,
            client.get_status_display_ar,
            client.phone,
            client.email or '',
            client.address,
            client.national_id or '',
            client.passport_number or '',
            client.commercial_register or '',
            client.tax_number or '',
            client.created_at.strftime('%Y-%m-%d')
        ])

    return response


@login_required
def client_api_search(request):
    """API للبحث السريع في العملاء"""

    query = request.GET.get('q', '')
    if len(query) < 2:
        return JsonResponse({'results': []})

    clients = Client.objects.filter(
        Q(name__icontains=query) |
        Q(phone__icontains=query) |
        Q(email__icontains=query)
    )[:10]

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'name': client.name,
            'phone': client.phone,
            'client_type': client.get_client_type_display_ar,
            'status': client.get_status_display_ar
        })

    return JsonResponse({'results': results})
