from django.contrib import admin
from .models import (
    Branch, Worker, Client, Contract, DailyService, Daily24HService, CustomService,
    SecurityClearance, EntryVisa, WorkPermit, WorkerOperation,
    BloodTestRequest, BloodTestRequestWorker, SponsorshipTransfer, WorkerTransfer
)

# Configuración para Branch
@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'address', 'phone', 'manager', 'is_main', 'created_at')
    list_filter = ('is_main', 'created_at')
    search_fields = ('name', 'address', 'phone')
    ordering = ('-is_main', 'name')

# Configuración para Worker
@admin.register(Worker)
class WorkerAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'nationality', 'passport_number', 'iqama_number', 'worker_type', 'status', 'branch')
    list_filter = ('worker_type', 'status', 'nationality', 'branch')
    search_fields = ('full_name', 'passport_number', 'iqama_number')
    ordering = ('full_name',)
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('full_name', 'nationality', 'passport_number', 'iqama_number', 'worker_type', 'status')
        }),
        ('معلومات شخصية', {
            'fields': ('date_of_birth', 'gender', 'phone', 'address', 'photo')
        }),
        ('معلومات إدارية', {
            'fields': ('branch',)
        }),
    )

# Configuración para Client
@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'client_type', 'contact_person', 'phone', 'status', 'branch')
    list_filter = ('client_type', 'status', 'branch')
    search_fields = ('name', 'contact_person', 'phone', 'cr_number')
    ordering = ('name',)
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'client_type', 'status')
        }),
        ('معلومات الاتصال', {
            'fields': ('contact_person', 'phone', 'email', 'address')
        }),
        ('معلومات تجارية', {
            'fields': ('cr_number', 'vat_number')
        }),
        ('معلومات إدارية', {
            'fields': ('branch',)
        }),
    )

# Configuración para Contract
@admin.register(Contract)
class ContractAdmin(admin.ModelAdmin):
    list_display = ('contract_number', 'client', 'worker', 'contract_type', 'start_date', 'end_date', 'status')
    list_filter = ('contract_type', 'status', 'start_date')
    search_fields = ('contract_number', 'client__name', 'worker__full_name')
    ordering = ('-created_at',)
    date_hierarchy = 'start_date'
    raw_id_fields = ('client', 'worker')

# Configuración para DailyService
@admin.register(DailyService)
class DailyServiceAdmin(admin.ModelAdmin):
    list_display = ('service_number', 'client', 'get_workers_count', 'service_date', 'hours', 'fee', 'status')
    list_filter = ('status', 'service_date', 'booking_type')
    search_fields = ('service_number', 'client__name')
    ordering = ('-service_date',)
    date_hierarchy = 'service_date'
    raw_id_fields = ('client',)
    filter_horizontal = ('workers',)

    def get_workers_count(self, obj):
        return obj.workers.count()
    get_workers_count.short_description = 'عدد العمال'

# Configuración para Daily24HService
@admin.register(Daily24HService)
class Daily24HServiceAdmin(admin.ModelAdmin):
    list_display = ('service_number', 'client', 'get_workers_count', 'start_date', 'end_date', 'fee', 'status')
    list_filter = ('status', 'start_date')
    search_fields = ('service_number', 'client__name')
    ordering = ('-start_date',)
    date_hierarchy = 'start_date'
    raw_id_fields = ('client',)
    filter_horizontal = ('workers',)

    def get_workers_count(self, obj):
        return obj.workers.count()
    get_workers_count.short_description = 'عدد العمال'

# Configuración para CustomService
@admin.register(CustomService)
class CustomServiceAdmin(admin.ModelAdmin):
    list_display = ('service_number', 'client', 'get_workers_count', 'start_date', 'end_date', 'duration_hours', 'fee', 'status')
    list_filter = ('status', 'start_date')
    search_fields = ('service_number', 'client__name')
    ordering = ('-start_date',)
    date_hierarchy = 'start_date'
    raw_id_fields = ('client',)
    filter_horizontal = ('workers',)

    def get_workers_count(self, obj):
        return obj.workers.count()
    get_workers_count.short_description = 'عدد العمال'

# Configuración para SecurityClearance
@admin.register(SecurityClearance)
class SecurityClearanceAdmin(admin.ModelAdmin):
    list_display = ('clearance_number', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date', 'expiry_date')
    search_fields = ('clearance_number',)
    ordering = ('-issue_date',)
    date_hierarchy = 'issue_date'

# Configuración para EntryVisa
@admin.register(EntryVisa)
class EntryVisaAdmin(admin.ModelAdmin):
    list_display = ('visa_number', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date', 'expiry_date')
    search_fields = ('visa_number',)
    ordering = ('-issue_date',)
    date_hierarchy = 'issue_date'

# Configuración para WorkPermit
@admin.register(WorkPermit)
class WorkPermitAdmin(admin.ModelAdmin):
    list_display = ('permit_number', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date', 'expiry_date')
    search_fields = ('permit_number',)
    ordering = ('-issue_date',)
    date_hierarchy = 'issue_date'

# Configuración para WorkerOperation
@admin.register(WorkerOperation)
class WorkerOperationAdmin(admin.ModelAdmin):
    list_display = ('operation_number', 'worker', 'client', 'operation_date', 'status')
    list_filter = ('status', 'operation_date')
    search_fields = ('operation_number', 'worker__full_name', 'client__name')
    ordering = ('-operation_date',)
    date_hierarchy = 'operation_date'
    raw_id_fields = ('worker', 'client', 'security_clearance', 'entry_visa', 'work_permit')

# Configuración para BloodTestRequest
@admin.register(BloodTestRequest)
class BloodTestRequestAdmin(admin.ModelAdmin):
    list_display = ('request_number', 'request_date', 'hospital', 'status')
    list_filter = ('status', 'request_date')
    search_fields = ('request_number', 'hospital')
    ordering = ('-request_date',)
    date_hierarchy = 'request_date'

# Configuración para BloodTestRequestWorker
@admin.register(BloodTestRequestWorker)
class BloodTestRequestWorkerAdmin(admin.ModelAdmin):
    list_display = ('worker', 'blood_test_request', 'test_date', 'result', 'result_date')
    list_filter = ('result', 'test_date', 'result_date')
    search_fields = ('worker__full_name', 'blood_test_request__request_number')
    ordering = ('-test_date',)
    date_hierarchy = 'test_date'
    raw_id_fields = ('worker', 'blood_test_request')

# Configuración para SponsorshipTransfer
@admin.register(SponsorshipTransfer)
class SponsorshipTransferAdmin(admin.ModelAdmin):
    list_display = ('transfer_number', 'worker', 'client', 'transfer_date', 'status')
    list_filter = ('status', 'transfer_date')
    search_fields = ('transfer_number', 'worker__full_name', 'client__name')
    ordering = ('-transfer_date',)
    date_hierarchy = 'transfer_date'
    raw_id_fields = ('worker', 'client')

# Configuración para WorkerTransfer
@admin.register(WorkerTransfer)
class WorkerTransferAdmin(admin.ModelAdmin):
    list_display = ('transfer_number', 'worker', 'from_branch', 'to_branch', 'transfer_date', 'status')
    list_filter = ('status', 'transfer_date', 'from_branch', 'to_branch')
    search_fields = ('transfer_number', 'worker__full_name')
    ordering = ('-transfer_date',)
    date_hierarchy = 'transfer_date'
    raw_id_fields = ('worker', 'from_branch', 'to_branch')
