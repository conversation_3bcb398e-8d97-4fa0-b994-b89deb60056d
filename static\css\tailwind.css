/* منصة استقدامي السحابية v18.1 - Tailwind CSS احترافي */
/*! tailwindcss v3.4.0 | MIT License | https://tailwindcss.com */

/* خطوط عربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* متغيرات CSS مخصصة */
:root {
  --primary-blue: #143D8D;
  --secondary-blue: #407BFF;
  --primary-dark: #0a384e;
  --primary-light: #e6f0ff;
  --success-color: #2ecc71;
  --error-color: #e74c3c;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
}

/* إعدادات أساسية */
* {
  box-sizing: border-box;
}

html {
  direction: rtl;
  font-family: 'Tajawal', 'Cairo', sans-serif;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: 'Tajawal', 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* إصلاح backdrop-filter */
.backdrop-blur {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

/* ألوان مخصصة */
.bg-primary-blue { background-color: var(--primary-blue); }
.bg-secondary-blue { background-color: var(--secondary-blue); }
.text-primary-blue { color: var(--primary-blue); }
.text-secondary-blue { color: var(--secondary-blue); }
.border-primary-blue { border-color: var(--primary-blue); }
.border-secondary-blue { border-color: var(--secondary-blue); }

/* أزرار محسنة */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  font-family: inherit;
}

.btn-primary {
  background-color: var(--secondary-blue);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--primary-blue);
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* حقول الإدخال */
.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  direction: rtl;
  text-align: right;
  font-family: inherit;
}

.input-field:focus {
  outline: none;
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(64, 123, 255, 0.1);
}

/* بطاقات */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  overflow: hidden;
}

.card-header {
  background-color: var(--primary-blue);
  color: white;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-body {
  padding: 1.5rem;
}

/* تنبيهات */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  border-right: 4px solid;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background-color: #d4edda;
  border-color: var(--success-color);
  color: #155724;
}

.alert-error {
  background-color: #f8d7da;
  border-color: var(--error-color);
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: var(--warning-color);
  color: #856404;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: var(--info-color);
  color: #0c5460;
}

/* حركات */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

/* تأثيرات التمرير */
.hover-effect {
  transition: all 0.3s ease;
}

.hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* فئات مساعدة */
.text-rtl {
  direction: rtl;
  text-align: right;
}

.text-ltr {
  direction: ltr;
  text-align: left;
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

/* ألوان الخلفية */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-green-100 { background-color: #dcfce7; }
.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-yellow-50 { background-color: #fffbeb; }
.bg-yellow-100 { background-color: #fef3c7; }

/* ألوان النص */
.text-white { color: #ffffff; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }
.text-green-600 { color: #16a34a; }
.text-green-700 { color: #15803d; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }
.text-yellow-600 { color: #d97706; }
.text-yellow-700 { color: #a16207; }

/* ألوان الحدود */
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-blue-100 { border-color: #dbeafe; }
.border-blue-500 { border-color: #3b82f6; }
.border-green-100 { border-color: #dcfce7; }
.border-green-500 { border-color: #22c55e; }
.border-red-100 { border-color: #fee2e2; }
.border-red-500 { border-color: #ef4444; }
.border-yellow-100 { border-color: #fef3c7; }
.border-yellow-500 { border-color: #eab308; }

/* تباعد */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

/* عرض وارتفاع */
.w-full { width: 100%; }
.w-1/2 { width: 50%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* فليكس */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-col { flex-direction: column; }

/* نص */
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }

/* انتقالات */
.transition { transition: all 0.15s ease-in-out; }
.transition-all { transition: all 0.3s ease; }
.duration-300 { transition-duration: 300ms; }

/* تحويلات */
.transform { transform: translateZ(0); }
.hover\:-translate-y-0\.5:hover { transform: translateY(-0.125rem); }
.hover\:-translate-y-px:hover { transform: translateY(-1px); }

/* عرض */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* موضع */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* فهرس z */
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* حدود */
.border { border-width: 1px; }
.border-r-4 { border-right-width: 4px; }
.border-t { border-top-width: 1px; }

/* تجاوز */
.overflow-hidden { overflow: hidden; }

/* مؤشر */
.cursor-pointer { cursor: pointer; }

/* تركيز */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(64, 123, 255, 0.1); }

/* استجابة */
@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:w-1\/2 { width: 50%; }
  .md\:flex { display: flex; }
  .md\:p-10 { padding: 2.5rem; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:w-1\/2 { width: 50%; }
}
