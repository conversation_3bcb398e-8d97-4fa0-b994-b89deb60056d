from django.core.management.base import BaseCommand
from system_settings.models import Currency

class Command(BaseCommand):
    help = 'Creates initial currencies'

    def handle(self, *args, **kwargs):
        # Default currencies
        currencies = [
            {
                'code': 'SAR',
                'name': 'ريال سعودي',
                'symbol': 'ر.س',
                'exchange_rate': 1.0,
                'is_default': True,
                'is_active': True
            },
            {
                'code': 'USD',
                'name': 'دولار أمريكي',
                'symbol': '$',
                'exchange_rate': 0.27,
                'is_default': False,
                'is_active': True
            },
            {
                'code': 'EUR',
                'name': 'يورو',
                'symbol': '€',
                'exchange_rate': 0.24,
                'is_default': False,
                'is_active': True
            },
            {
                'code': 'GBP',
                'name': 'جنيه إسترليني',
                'symbol': '£',
                'exchange_rate': 0.21,
                'is_default': False,
                'is_active': True
            },
            {
                'code': 'AED',
                'name': 'درهم إماراتي',
                'symbol': 'د.إ',
                'exchange_rate': 0.98,
                'is_default': False,
                'is_active': True
            },
        ]
        
        # Create currencies
        for currency_data in currencies:
            Currency.objects.get_or_create(
                code=currency_data['code'],
                defaults={
                    'name': currency_data['name'],
                    'symbol': currency_data['symbol'],
                    'exchange_rate': currency_data['exchange_rate'],
                    'is_default': currency_data['is_default'],
                    'is_active': currency_data['is_active']
                }
            )
        
        self.stdout.write(self.style.SUCCESS('Successfully created initial currencies'))
