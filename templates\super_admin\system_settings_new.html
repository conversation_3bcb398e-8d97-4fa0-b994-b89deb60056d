{% extends 'super_admin/base.html' %}

{% block title %}إعدادات النظام | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق التبويبات */
    .tabs-container {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .tab-button {
        transition: all 0.3s ease;
        position: relative;
        padding: 1rem 1.5rem;
        font-weight: 500;
    }

    .tab-button.active {
        background-color: #3b82f6;
        color: white;
        font-weight: 600;
    }

    .tab-button:not(.active):hover {
        background-color: #e5e7eb;
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #3b82f6;
    }

    .tab-content {
        display: none;
        padding: 1.5rem;
    }

    .tab-content.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* تنسيق البطاقات */
    .settings-card {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .settings-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    /* تنسيق الأيقونات */
    .settings-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1.25rem;
    }

    /* تنسيق التحذيرات */
    .security-warning {
        background-color: #fff5f5;
        border-right: 4px solid #f56565;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
    }

    /* تنسيق جدول النسخ الاحتياطية */
    .backup-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .backup-table th, .backup-table td {
        padding: 0.75rem 1rem;
        text-align: right;
    }

    .backup-table th {
        background-color: #f9fafb;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .backup-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e5e7eb;
    }

    .backup-table tbody tr:hover {
        background-color: #f3f4f6;
    }

    /* تنسيق الأزرار */
    button, .btn {
        transition: all 0.3s ease;
    }

    button:hover, .btn:hover {
        transform: translateY(-1px);
    }

    /* تنسيق الرسائل */
    .alert {
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-blue-800 flex items-center">
            <i class="fas fa-cogs text-blue-600 ml-3 text-2xl"></i>
            إعدادات النظام
        </h2>
        <a href="{% url 'super_admin:dashboard' %}" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-colors shadow-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة للوحة التحكم
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md alert" role="alert">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
                {% endif %}
                <span class="block sm:inline">{{ message }}</span>
                <button type="button" class="absolute top-0 left-0 mt-3 ml-3 text-{{ message.tags }}-500 hover:text-{{ message.tags }}-700 close-message">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- تبويبات الإعدادات -->
    <div class="tabs-container bg-white rounded-lg shadow-md overflow-hidden">
        <div class="flex border-b border-gray-200 bg-gray-50">
            <button id="tab-general" class="tab-button flex items-center text-gray-700 focus:outline-none active px-6 py-4 font-medium">
                <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3 shadow-sm">
                    <i class="fas fa-cog"></i>
                </div>
                الإعدادات العامة
            </button>
            <button id="tab-security" class="tab-button flex items-center text-gray-700 focus:outline-none px-6 py-4 font-medium">
                <div class="bg-red-100 text-red-600 p-2 rounded-full ml-3 shadow-sm">
                    <i class="fas fa-shield-alt"></i>
                </div>
                إعدادات الأمان
            </button>
            <button id="tab-backup" class="tab-button flex items-center text-gray-700 focus:outline-none px-6 py-4 font-medium">
                <div class="bg-green-100 text-green-600 p-2 rounded-full ml-3 shadow-sm">
                    <i class="fas fa-database"></i>
                </div>
                النسخ الاحتياطي
            </button>
        </div>

        <!-- محتوى التبويبات -->
            <!-- تبويب الإعدادات العامة -->
            <div id="content-general" class="tab-content active p-6">
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md mb-6">
                    <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-md">
                            <i class="fas fa-cog text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">الإعدادات العامة</h3>
                            <p class="text-gray-500 text-sm mt-1">إعدادات النظام الأساسية</p>
                        </div>
                    </div>

                    <form method="post" class="space-y-8">
                        {% csrf_token %}
                        <input type="hidden" name="save_general" value="1">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- اسم التطبيق -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                <label for="app_name" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    اسم التطبيق
                                </label>
                                <input type="text" name="app_name" id="app_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" value="{{ settings.app_name }}" placeholder="أدخل اسم التطبيق">
                                <p class="text-xs text-gray-500 mt-2">سيظهر هذا الاسم في عنوان الصفحة وفي الواجهة</p>
                            </div>

                            <!-- اللغة الافتراضية -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                <label for="default_language" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-language"></i>
                                    </div>
                                    اللغة الافتراضية
                                </label>
                                <select name="default_language" id="default_language" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="ar" {% if settings.default_language == 'ar' %}selected{% endif %}>العربية</option>
                                    <option value="en" {% if settings.default_language == 'en' %}selected{% endif %}>الإنجليزية</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-2">اللغة الافتراضية للنظام</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
                            <!-- تفعيل التسجيل للشركات الجديدة -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 flex items-center">
                                <div class="flex items-center">
                                    <input type="checkbox" name="enable_registration" id="enable_registration" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" {% if settings.enable_registration %}checked{% endif %}>
                                </div>
                                <div class="mr-3">
                                    <label for="enable_registration" class="block text-sm font-medium text-gray-700">
                                        تفعيل التسجيل للشركات الجديدة
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">السماح للشركات الجديدة بالتسجيل في النظام</p>
                                </div>
                                <div class="mr-auto">
                                    <div class="bg-green-100 text-green-600 p-2 rounded-full">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- وضع الصيانة -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 flex items-center">
                                <div class="flex items-center">
                                    <input type="checkbox" name="maintenance_mode" id="maintenance_mode" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" {% if settings.maintenance_mode %}checked{% endif %}>
                                </div>
                                <div class="mr-3">
                                    <label for="maintenance_mode" class="block text-sm font-medium text-gray-700">
                                        تفعيل وضع الصيانة
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">إغلاق النظام مؤقتًا للصيانة</p>
                                </div>
                                <div class="mr-auto">
                                    <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full">
                                        <i class="fas fa-tools"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="pt-6 border-t border-gray-200 flex justify-end">
                            <button type="submit" class="flex justify-center py-3 px-8 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-save ml-2"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- معلومات النظام -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md">
                    <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div class="bg-gradient-to-br from-purple-400 to-purple-600 text-white p-3 rounded-full ml-4 shadow-md">
                            <i class="fas fa-info-circle text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">معلومات النظام</h3>
                            <p class="text-gray-500 text-sm mt-1">معلومات عامة عن النظام</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                            <div class="flex items-center mb-2">
                                <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-2">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-700">تاريخ التثبيت</h4>
                            </div>
                            <p class="text-lg font-semibold text-gray-800">{{ settings.created_at|date:"j F Y" }}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                            <div class="flex items-center mb-2">
                                <div class="bg-green-100 text-green-600 p-2 rounded-full ml-2">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-700">آخر تحديث</h4>
                            </div>
                            <p class="text-lg font-semibold text-gray-800">{{ settings.updated_at|date:"j F Y - H:i" }}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                            <div class="flex items-center mb-2">
                                <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full ml-2">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <h4 class="text-sm font-medium text-gray-700">المسؤول</h4>
                            </div>
                            <p class="text-lg font-semibold text-gray-800">{{ settings.updated_by.username|default:"غير محدد" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب إعدادات الأمان -->
            <div id="content-security" class="tab-content p-6">
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md mb-6">
                    <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div class="bg-gradient-to-br from-red-400 to-red-600 text-white p-3 rounded-full ml-4 shadow-md">
                            <i class="fas fa-shield-alt text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">إعدادات الأمان</h3>
                            <p class="text-gray-500 text-sm mt-1">إعدادات أمان النظام والمستخدمين</p>
                        </div>
                    </div>

                    <div class="bg-red-50 p-4 rounded-lg shadow-sm border border-red-200 mb-8">
                        <div class="flex items-start">
                            <div class="bg-red-100 p-3 rounded-full text-red-600 ml-4 shadow-sm">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-red-700 text-lg">تنبيه أمني</h4>
                                <p class="text-sm text-red-600 mt-2">تغيير إعدادات الأمان قد يؤثر على وصول المستخدمين للنظام. يرجى التأكد من فهم التغييرات قبل حفظها.</p>
                            </div>
                        </div>
                    </div>

                    <form method="post" class="space-y-8">
                        {% csrf_token %}
                        <input type="hidden" name="save_security" value="1">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- تفعيل التحقق الثنائي للمسؤولين -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 flex items-center">
                                <div class="flex items-center">
                                    <input type="checkbox" name="two_factor_for_admins" id="two_factor_for_admins" class="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300 rounded" {% if settings.two_factor_for_admins %}checked{% endif %}>
                                </div>
                                <div class="mr-3">
                                    <label for="two_factor_for_admins" class="block text-sm font-medium text-gray-700">
                                        تفعيل التحقق الثنائي للمسؤولين
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">تأمين حسابات المسؤولين بطبقة إضافية من الحماية</p>
                                </div>
                                <div class="mr-auto">
                                    <div class="bg-purple-100 text-purple-600 p-2 rounded-full">
                                        <i class="fas fa-key"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- نوع المصادقة الثنائية -->
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                <label for="two_factor_type" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    نوع المصادقة الثنائية
                                </label>
                                <select name="two_factor_type" id="two_factor_type" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                                    <option value="otp" {% if settings.two_factor_type == 'otp' %}selected{% endif %}>رمز OTP لمرة واحدة</option>
                                    <option value="google_auth" {% if settings.two_factor_type == 'google_auth' %}selected{% endif %}>Google Authenticator</option>
                                    <option value="sms" {% if settings.two_factor_type == 'sms' %}selected{% endif %}>رسائل SMS</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-2">اختر طريقة التحقق الثنائي المفضلة</p>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="session_timeout" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                <div class="bg-orange-100 text-orange-600 p-2 rounded-full ml-3">
                                    <i class="fas fa-clock"></i>
                                </div>
                                مدة صلاحية الجلسة (بالدقائق)
                            </label>
                            <div class="flex items-center">
                                <input type="number" name="session_timeout" id="session_timeout" min="5" max="1440" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm" value="{{ settings.session_timeout }}">
                                <div class="mr-4 bg-gray-100 px-3 py-3 rounded-md border border-gray-200">
                                    <span class="text-sm text-gray-600">دقيقة</span>
                                </div>
                            </div>
                            <div class="flex items-center mt-3">
                                <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full ml-2">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <p class="text-xs text-gray-500">الحد الأدنى 5 دقائق والحد الأقصى 1440 دقيقة (24 ساعة)</p>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-5 rounded-lg shadow-sm border border-gray-200">
                            <h4 class="font-medium text-gray-700 mb-4 flex items-center">
                                <div class="bg-gray-200 text-gray-600 p-2 rounded-full ml-3">
                                    <i class="fas fa-history"></i>
                                </div>
                                آخر تحديث لإعدادات الأمان
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-white p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500">تاريخ التحديث</p>
                                            <p class="font-semibold text-gray-700">{{ settings.updated_at|date:"j F Y - H:i" }}</p>
                                        </div>
                                    </div>
                                </div>

                                {% if settings.updated_by %}
                                <div class="bg-white p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 text-green-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500">تم التحديث بواسطة</p>
                                            <p class="font-semibold text-gray-700">{{ settings.updated_by.username }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="pt-6 border-t border-gray-200 flex justify-end">
                            <button type="submit" class="flex justify-center py-3 px-8 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                <i class="fas fa-shield-alt ml-2"></i> حفظ إعدادات الأمان
                            </button>
                        </div>
                    </form>
                </div>

                <!-- نصائح أمنية -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md">
                    <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div class="bg-gradient-to-br from-yellow-400 to-yellow-600 text-white p-3 rounded-full ml-4 shadow-md">
                            <i class="fas fa-lightbulb text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">نصائح أمنية</h3>
                            <p class="text-gray-500 text-sm mt-1">إرشادات لتعزيز أمان النظام</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-start p-3 bg-blue-50 rounded-lg border border-blue-100">
                            <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3 mt-1">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-blue-800">تفعيل المصادقة الثنائية</h5>
                                <p class="text-sm text-blue-600 mt-1">يوصى بتفعيل المصادقة الثنائية لجميع حسابات المسؤولين لتعزيز الأمان.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-green-50 rounded-lg border border-green-100">
                            <div class="bg-green-100 text-green-600 p-2 rounded-full ml-3 mt-1">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-green-800">كلمات مرور قوية</h5>
                                <p class="text-sm text-green-600 mt-1">تأكد من استخدام كلمات مرور قوية تحتوي على أحرف وأرقام ورموز خاصة.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-purple-50 rounded-lg border border-purple-100">
                            <div class="bg-purple-100 text-purple-600 p-2 rounded-full ml-3 mt-1">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-purple-800">تغيير كلمات المرور دوريًا</h5>
                                <p class="text-sm text-purple-600 mt-1">قم بتغيير كلمات المرور بشكل دوري كل 90 يومًا على الأقل.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب النسخ الاحتياطي -->
            <div id="content-backup" class="tab-content p-6">
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md mb-6">
                    <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div class="bg-gradient-to-br from-green-400 to-green-600 text-white p-3 rounded-full ml-4 shadow-md">
                            <i class="fas fa-database text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">النسخ الاحتياطي</h3>
                            <p class="text-gray-500 text-sm mt-1">إدارة النسخ الاحتياطية للنظام</p>
                        </div>
                    </div>

                    <p class="text-gray-700 mb-6 bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">يمكنك إنشاء نسخة احتياطية لجميع قواعد البيانات أو استعادة نسخة سابقة. يوصى بإنشاء نسخة احتياطية بشكل دوري للحفاظ على بياناتك.</p>

                    {% if settings.last_backup_date %}
                    <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-5 rounded-lg mb-8 shadow-sm border border-blue-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="bg-blue-500 text-white p-3 rounded-full ml-4 shadow-sm">
                                    <i class="fas fa-info-circle text-lg"></i>
                                </div>
                                <div>
                                    <p class="text-blue-700 font-medium">آخر نسخة احتياطية</p>
                                    <p class="text-blue-800 font-bold text-xl">{{ settings.last_backup_date|date:"j F Y - H:i" }}</p>
                                </div>
                            </div>
                            <div class="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg border border-blue-200 shadow-sm">
                                <div class="flex items-center">
                                    <i class="fas fa-clock ml-2"></i>
                                    <span>منذ {{ settings.last_backup_date|timesince }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- جدولة النسخ الاحتياطي -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8 shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                            <div class="bg-gradient-to-br from-indigo-400 to-indigo-600 text-white p-3 rounded-full ml-4 shadow-sm">
                                <i class="fas fa-calendar-alt text-lg"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-gray-800">جدولة النسخ الاحتياطي التلقائي</h4>
                                <p class="text-gray-500 text-sm mt-1">إعداد جدولة تلقائية للنسخ الاحتياطي</p>
                            </div>
                        </div>

                        <form method="post" class="space-y-6">
                            {% csrf_token %}
                            <input type="hidden" name="save_backup_schedule" value="1">

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                    <label for="backup_frequency" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-sync-alt"></i>
                                        </div>
                                        التكرار
                                    </label>
                                    <select name="backup_frequency" id="backup_frequency" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        <option value="daily" {% if system_backup_schedule and system_backup_schedule.frequency == 'daily' %}selected{% endif %}>يومي</option>
                                        <option value="weekly" {% if system_backup_schedule and system_backup_schedule.frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                                        <option value="monthly" {% if system_backup_schedule and system_backup_schedule.frequency == 'monthly' %}selected{% endif %}>شهري</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-2">حدد عدد مرات إنشاء النسخ الاحتياطية</p>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                    <label for="backup_time" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        وقت النسخ الاحتياطي
                                    </label>
                                    <input type="time" name="backup_time" id="backup_time" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" value="{% if system_backup_schedule %}{{ system_backup_schedule.time|time:'H:i' }}{% else %}00:00{% endif %}">
                                    <p class="text-xs text-gray-500 mt-2">الوقت الذي سيتم فيه إنشاء النسخة الاحتياطية</p>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100">
                                    <label for="backup_retention" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-history"></i>
                                        </div>
                                        فترة الاحتفاظ (بالأيام)
                                    </label>
                                    <input type="number" name="backup_retention" id="backup_retention" min="1" max="365" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" value="{% if system_backup_schedule %}{{ system_backup_schedule.retention_period }}{% else %}30{% endif %}">
                                    <p class="text-xs text-gray-500 mt-2">عدد الأيام التي سيتم الاحتفاظ بالنسخ الاحتياطية خلالها</p>
                                </div>
                            </div>

                            <div id="weekly_options" class="mt-6 bg-indigo-50 p-5 rounded-lg border border-indigo-100 shadow-sm {% if not system_backup_schedule or system_backup_schedule.frequency != 'weekly' %}hidden{% endif %}">
                                <div class="flex items-center mb-4">
                                    <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <h5 class="font-medium text-indigo-800">إعدادات الجدولة الأسبوعية</h5>
                                </div>
                                <div class="bg-white p-4 rounded-lg shadow-sm border border-indigo-100">
                                    <label for="day_of_week" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-calendar-day"></i>
                                        </div>
                                        يوم الأسبوع
                                    </label>
                                    <select name="day_of_week" id="day_of_week" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        <option value="0" {% if system_backup_schedule and system_backup_schedule.day_of_week == 0 %}selected{% endif %}>الاثنين</option>
                                        <option value="1" {% if system_backup_schedule and system_backup_schedule.day_of_week == 1 %}selected{% endif %}>الثلاثاء</option>
                                        <option value="2" {% if system_backup_schedule and system_backup_schedule.day_of_week == 2 %}selected{% endif %}>الأربعاء</option>
                                        <option value="3" {% if system_backup_schedule and system_backup_schedule.day_of_week == 3 %}selected{% endif %}>الخميس</option>
                                        <option value="4" {% if system_backup_schedule and system_backup_schedule.day_of_week == 4 %}selected{% endif %}>الجمعة</option>
                                        <option value="5" {% if system_backup_schedule and system_backup_schedule.day_of_week == 5 %}selected{% endif %}>السبت</option>
                                        <option value="6" {% if system_backup_schedule and system_backup_schedule.day_of_week == 6 %}selected{% endif %}>الأحد</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-2">اليوم الذي سيتم فيه إنشاء النسخة الاحتياطية كل أسبوع</p>
                                </div>
                            </div>

                            <div id="monthly_options" class="mt-6 bg-indigo-50 p-5 rounded-lg border border-indigo-100 shadow-sm {% if not system_backup_schedule or system_backup_schedule.frequency != 'monthly' %}hidden{% endif %}">
                                <div class="flex items-center mb-4">
                                    <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h5 class="font-medium text-indigo-800">إعدادات الجدولة الشهرية</h5>
                                </div>
                                <div class="bg-white p-4 rounded-lg shadow-sm border border-indigo-100">
                                    <label for="day_of_month" class="flex items-center text-sm font-medium text-gray-700 mb-3">
                                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-calendar-day"></i>
                                        </div>
                                        يوم الشهر
                                    </label>
                                    <select name="day_of_month" id="day_of_month" class="block w-full border border-gray-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        <option value="1" {% if system_backup_schedule and system_backup_schedule.day_of_month == 1 %}selected{% endif %}>1</option>
                                        <option value="2" {% if system_backup_schedule and system_backup_schedule.day_of_month == 2 %}selected{% endif %}>2</option>
                                        <option value="3" {% if system_backup_schedule and system_backup_schedule.day_of_month == 3 %}selected{% endif %}>3</option>
                                        <option value="4" {% if system_backup_schedule and system_backup_schedule.day_of_month == 4 %}selected{% endif %}>4</option>
                                        <option value="5" {% if system_backup_schedule and system_backup_schedule.day_of_month == 5 %}selected{% endif %}>5</option>
                                        <option value="6" {% if system_backup_schedule and system_backup_schedule.day_of_month == 6 %}selected{% endif %}>6</option>
                                        <option value="7" {% if system_backup_schedule and system_backup_schedule.day_of_month == 7 %}selected{% endif %}>7</option>
                                        <option value="8" {% if system_backup_schedule and system_backup_schedule.day_of_month == 8 %}selected{% endif %}>8</option>
                                        <option value="9" {% if system_backup_schedule and system_backup_schedule.day_of_month == 9 %}selected{% endif %}>9</option>
                                        <option value="10" {% if system_backup_schedule and system_backup_schedule.day_of_month == 10 %}selected{% endif %}>10</option>
                                        <option value="11" {% if system_backup_schedule and system_backup_schedule.day_of_month == 11 %}selected{% endif %}>11</option>
                                        <option value="12" {% if system_backup_schedule and system_backup_schedule.day_of_month == 12 %}selected{% endif %}>12</option>
                                        <option value="13" {% if system_backup_schedule and system_backup_schedule.day_of_month == 13 %}selected{% endif %}>13</option>
                                        <option value="14" {% if system_backup_schedule and system_backup_schedule.day_of_month == 14 %}selected{% endif %}>14</option>
                                        <option value="15" {% if system_backup_schedule and system_backup_schedule.day_of_month == 15 %}selected{% endif %}>15</option>
                                        <option value="16" {% if system_backup_schedule and system_backup_schedule.day_of_month == 16 %}selected{% endif %}>16</option>
                                        <option value="17" {% if system_backup_schedule and system_backup_schedule.day_of_month == 17 %}selected{% endif %}>17</option>
                                        <option value="18" {% if system_backup_schedule and system_backup_schedule.day_of_month == 18 %}selected{% endif %}>18</option>
                                        <option value="19" {% if system_backup_schedule and system_backup_schedule.day_of_month == 19 %}selected{% endif %}>19</option>
                                        <option value="20" {% if system_backup_schedule and system_backup_schedule.day_of_month == 20 %}selected{% endif %}>20</option>
                                        <option value="21" {% if system_backup_schedule and system_backup_schedule.day_of_month == 21 %}selected{% endif %}>21</option>
                                        <option value="22" {% if system_backup_schedule and system_backup_schedule.day_of_month == 22 %}selected{% endif %}>22</option>
                                        <option value="23" {% if system_backup_schedule and system_backup_schedule.day_of_month == 23 %}selected{% endif %}>23</option>
                                        <option value="24" {% if system_backup_schedule and system_backup_schedule.day_of_month == 24 %}selected{% endif %}>24</option>
                                        <option value="25" {% if system_backup_schedule and system_backup_schedule.day_of_month == 25 %}selected{% endif %}>25</option>
                                        <option value="26" {% if system_backup_schedule and system_backup_schedule.day_of_month == 26 %}selected{% endif %}>26</option>
                                        <option value="27" {% if system_backup_schedule and system_backup_schedule.day_of_month == 27 %}selected{% endif %}>27</option>
                                        <option value="28" {% if system_backup_schedule and system_backup_schedule.day_of_month == 28 %}selected{% endif %}>28</option>
                                        <option value="29" {% if system_backup_schedule and system_backup_schedule.day_of_month == 29 %}selected{% endif %}>29</option>
                                        <option value="30" {% if system_backup_schedule and system_backup_schedule.day_of_month == 30 %}selected{% endif %}>30</option>
                                        <option value="31" {% if system_backup_schedule and system_backup_schedule.day_of_month == 31 %}selected{% endif %}>31</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-2">اليوم الذي سيتم فيه إنشاء النسخة الاحتياطية كل شهر</p>
                                </div>
                            </div>

                            <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 flex items-center">
                                <div class="flex items-center">
                                    <input type="checkbox" name="backup_active" id="backup_active" class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" {% if system_backup_schedule and system_backup_schedule.is_active %}checked{% endif %}>
                                </div>
                                <div class="mr-3">
                                    <label for="backup_active" class="block text-sm font-medium text-gray-700">
                                        تفعيل النسخ الاحتياطي التلقائي
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">تفعيل أو تعطيل جدولة النسخ الاحتياطي التلقائي</p>
                                </div>
                                <div class="mr-auto">
                                    <div class="bg-green-100 text-green-600 p-2 rounded-full">
                                        <i class="fas fa-toggle-on"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="pt-6 border-t border-gray-200 flex justify-end mt-6">
                                <button type="submit" class="flex justify-center py-3 px-8 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                                    <i class="fas fa-calendar-check ml-2"></i> حفظ جدولة النسخ الاحتياطي
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- قائمة النسخ الاحتياطية -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8 shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                            <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-sm">
                                <i class="fas fa-history text-lg"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-gray-800">النسخ الاحتياطية السابقة</h4>
                                <p class="text-gray-500 text-sm mt-1">قائمة النسخ الاحتياطية المحفوظة في النظام</p>
                            </div>
                            <div class="mr-auto">
                                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full shadow-sm">
                                    {% if backups %}{{ backups|length }} نسخة{% else %}لا توجد نسخ{% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="overflow-x-auto rounded-lg shadow">
                            <table class="backup-table min-w-full divide-y divide-gray-200">
                                <thead class="bg-gradient-to-r from-blue-50 to-blue-100">
                                    <tr>
                                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tr-lg py-3 px-4">الاسم</th>
                                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">التاريخ</th>
                                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">الحجم</th>
                                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tl-lg py-3 px-4">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% if backups %}
                                        {% for backup in backups %}
                                        <tr class="hover:bg-blue-50 transition-colors">
                                            <td class="text-sm font-medium text-gray-900 py-4 px-4">
                                                <div class="flex items-center">
                                                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                                        <i class="fas fa-file-archive"></i>
                                                    </div>
                                                    {{ backup.filename }}
                                                </div>
                                            </td>
                                            <td class="text-sm text-gray-600 py-4 px-4">
                                                <div class="flex items-center">
                                                    <i class="fas fa-calendar-alt text-gray-400 ml-2"></i>
                                                    {{ backup.created_at }}
                                                </div>
                                            </td>
                                            <td class="text-sm text-gray-600 py-4 px-4">
                                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-md">
                                                    {{ backup.size_formatted }}
                                                </span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div class="flex space-x-3 space-x-reverse">
                                                    <a href="{% url 'super_admin:download_system_backup' backup.filename %}" class="text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 p-2 rounded-full transition-colors shadow-sm" title="تنزيل">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    <form method="post" action="{% url 'super_admin:delete_system_backup' backup.filename %}" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                        {% csrf_token %}
                                                        <button type="submit" class="text-red-600 hover:text-red-800 bg-red-50 hover:bg-red-100 p-2 rounded-full transition-colors shadow-sm" title="حذف">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                    <button type="button" class="text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 p-2 rounded-full transition-colors shadow-sm" title="استعادة" onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')">
                                                        <i class="fas fa-undo-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center py-12 text-gray-500">
                                                <div class="flex flex-col items-center bg-gray-50 p-8 rounded-lg">
                                                    <div class="bg-blue-100 text-blue-500 p-4 rounded-full mb-4 shadow-sm">
                                                        <i class="fas fa-database text-3xl"></i>
                                                    </div>
                                                    <p class="text-lg font-medium text-gray-700">لا توجد نسخ احتياطية متاحة</p>
                                                    <p class="text-sm text-gray-500 mt-2 max-w-md">قم بإنشاء نسخة احتياطية جديدة باستخدام الزر أدناه للحفاظ على بياناتك</p>
                                                    <button type="button" class="mt-4 flex items-center py-2 px-4 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors" onclick="document.getElementById('create-backup-btn').click()">
                                                        <i class="fas fa-plus-circle ml-2"></i> إنشاء نسخة احتياطية الآن
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg border border-gray-200 p-6 shadow-md">
                        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                            <div class="bg-gradient-to-br from-green-400 to-green-600 text-white p-3 rounded-full ml-4 shadow-sm">
                                <i class="fas fa-cogs text-lg"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-gray-800">إجراءات النسخ الاحتياطي</h4>
                                <p class="text-gray-500 text-sm mt-1">إنشاء واستعادة النسخ الاحتياطية</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-5 rounded-lg border border-blue-200 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <div class="bg-blue-500 text-white p-3 rounded-full ml-3 shadow-sm">
                                        <i class="fas fa-download"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-blue-800">إنشاء نسخة احتياطية</h5>
                                        <p class="text-sm text-blue-600 mt-1">إنشاء نسخة احتياطية جديدة من قاعدة البيانات</p>
                                    </div>
                                </div>
                                <form method="post" action="{% url 'super_admin:create_backup' %}">
                                    {% csrf_token %}
                                    <button id="create-backup-btn" type="submit" class="w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                        <i class="fas fa-download ml-2 text-lg"></i> إنشاء نسخة احتياطية جديدة
                                    </button>
                                </form>
                            </div>

                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-5 rounded-lg border border-yellow-200 shadow-sm">
                                <div class="flex items-center mb-4">
                                    <div class="bg-yellow-500 text-white p-3 rounded-full ml-3 shadow-sm">
                                        <i class="fas fa-upload"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-yellow-800">استعادة نسخة احتياطية</h5>
                                        <p class="text-sm text-yellow-600 mt-1">استعادة قاعدة البيانات من نسخة احتياطية سابقة</p>
                                    </div>
                                </div>
                                <form method="post" action="{% url 'super_admin:restore_backup' %}" id="restore-backup-form">
                                    {% csrf_token %}
                                    <button type="button"
                                        onclick="window.customizeDeleteModal(
                                            document.getElementById('restore-backup-form').getAttribute('action'),
                                            'تأكيد استعادة النسخة الاحتياطية',
                                            'تحذير: هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.',
                                            'fa-upload',
                                            'text-yellow-600',
                                            'تأكيد الاستعادة',
                                            'bg-yellow-600 hover:bg-yellow-700'
                                        );"
                                        class="w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors">
                                        <i class="fas fa-upload ml-2 text-lg"></i> استعادة نسخة احتياطية
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إغلاق رسائل التنبيه
        const closeButtons = document.querySelectorAll('.close-message');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const alert = this.closest('.alert');
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            });
        });

        // إخفاء رسائل التنبيه تلقائيًا بعد 5 ثوانٍ
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }, 5000);
        });

        // تفعيل التبويبات مع تأثيرات حركية
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        // إضافة أنماط CSS للتبويبات
        const style = document.createElement('style');
        style.textContent = `
            .tab-button {
                position: relative;
                transition: all 0.3s ease;
            }
            .tab-button.active {
                background-color: #f8fafc;
                font-weight: 600;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }
            .tab-button.active::after {
                content: '';
                position: absolute;
                bottom: -1px;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(to right, #3b82f6, #2563eb);
                border-radius: 3px 3px 0 0;
            }
            .tab-content {
                display: none;
                opacity: 0;
                transition: opacity 0.4s ease-in-out;
            }
            .tab-content.active {
                display: block;
                opacity: 1;
            }
        `;
        document.head.appendChild(style);

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));

                // إضافة الفئة النشطة للزر المضغوط
                this.classList.add('active');

                // إخفاء جميع محتويات التبويبات
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // إظهار محتوى التبويب المطلوب مع تأثير حركي
                const tabId = this.id.replace('tab-', 'content-');
                const targetContent = document.getElementById(tabId);

                // تأخير بسيط لإظهار التأثير الحركي
                setTimeout(() => {
                    targetContent.classList.add('active');
                }, 50);

                // حفظ التبويب النشط في التخزين المحلي
                localStorage.setItem('activeSettingsTab', this.id);
            });
        });

        // استعادة التبويب النشط من التخزين المحلي
        const activeTab = localStorage.getItem('activeSettingsTab');
        if (activeTab) {
            document.getElementById(activeTab).click();
        }

        // تفعيل تحذير تغيير إعدادات الأمان
        const securityForm = document.querySelector('#content-security form');
        securityForm.addEventListener('submit', function(e) {
            e.preventDefault();
            window.customizeDeleteModal(
                securityForm.getAttribute('action') || window.location.href,
                'تأكيد تغيير إعدادات الأمان',
                'هل أنت متأكد من تغيير إعدادات الأمان؟ قد يؤثر ذلك على وصول المستخدمين للنظام.',
                'fa-shield-alt',
                'text-red-600',
                'تأكيد التغيير',
                'bg-red-600 hover:bg-red-700',
                function() {
                    securityForm.submit();
                }
            );
        });

        // تفعيل اختيار نوع المصادقة الثنائية
        const twoFactorCheckbox = document.getElementById('two_factor_for_admins');
        const twoFactorTypeSelect = document.getElementById('two_factor_type');

        function updateTwoFactorTypeState() {
            twoFactorTypeSelect.disabled = !twoFactorCheckbox.checked;
            if (!twoFactorCheckbox.checked) {
                twoFactorTypeSelect.parentElement.classList.add('opacity-50');
            } else {
                twoFactorTypeSelect.parentElement.classList.remove('opacity-50');
            }
        }

        twoFactorCheckbox.addEventListener('change', updateTwoFactorTypeState);
        updateTwoFactorTypeState(); // تحديث الحالة الأولية

        // تفعيل اختيار تكرار النسخ الاحتياطي
        const backupFrequencySelect = document.getElementById('backup_frequency');
        const weeklyOptions = document.getElementById('weekly_options');
        const monthlyOptions = document.getElementById('monthly_options');

        function updateBackupOptions() {
            const frequency = backupFrequencySelect.value;

            // إخفاء جميع الخيارات أولاً
            weeklyOptions.classList.add('hidden');
            monthlyOptions.classList.add('hidden');

            // إظهار الخيارات المناسبة بناءً على التكرار المحدد
            if (frequency === 'weekly') {
                // إظهار خيارات الجدولة الأسبوعية مع تأثير حركي
                weeklyOptions.classList.remove('hidden');
                weeklyOptions.style.opacity = '0';
                setTimeout(() => {
                    weeklyOptions.style.opacity = '1';
                    weeklyOptions.style.transition = 'opacity 0.3s ease-in-out';
                }, 10);
            } else if (frequency === 'monthly') {
                // إظهار خيارات الجدولة الشهرية مع تأثير حركي
                monthlyOptions.classList.remove('hidden');
                monthlyOptions.style.opacity = '0';
                setTimeout(() => {
                    monthlyOptions.style.opacity = '1';
                    monthlyOptions.style.transition = 'opacity 0.3s ease-in-out';
                }, 10);
            }
        }

        backupFrequencySelect.addEventListener('change', updateBackupOptions);

        // تحديث الحالة الأولية مع تأخير بسيط للتأكد من تحميل الصفحة بالكامل
        setTimeout(updateBackupOptions, 100);

        // تحسين تجربة المستخدم عند تغيير التبويبات
        tabButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                }
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
