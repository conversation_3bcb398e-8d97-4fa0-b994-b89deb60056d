# Generated by Django 5.2 on 2025-04-29 20:27

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0002_systemsettings'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='مفعل')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='weekly', max_length=10, verbose_name='التكرار')),
                ('time', models.TimeField(default=datetime.time(0, 0), verbose_name='وقت النسخ الاحتياطي')),
                ('day_of_week', models.IntegerField(choices=[(0, 'الاثنين'), (1, 'الثلاثاء'), (2, 'الأربعاء'), (3, 'الخميس'), (4, 'الجمعة'), (5, 'السبت'), (6, 'الأحد')], default=0, verbose_name='يوم الأسبوع')),
                ('day_of_month', models.IntegerField(default=1, verbose_name='يوم الشهر')),
                ('retention_period', models.IntegerField(default=30, verbose_name='فترة الاحتفاظ (بالأيام)')),
                ('last_backup', models.DateTimeField(blank=True, null=True, verbose_name='آخر نسخة احتياطية')),
                ('next_backup', models.DateTimeField(blank=True, null=True, verbose_name='النسخة الاحتياطية التالية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='backup_schedule', to='super_admin.company', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_backup_schedules', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_backup_schedules', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'جدولة النسخ الاحتياطي',
                'verbose_name_plural': 'جدولات النسخ الاحتياطي',
            },
        ),
    ]
