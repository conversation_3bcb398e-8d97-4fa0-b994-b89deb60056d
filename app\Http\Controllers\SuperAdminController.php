<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Company;
use App\Models\SystemLog;
use App\Models\SuperAdmin;
use App\Models\SystemSettings;
use App\Models\Backup;
use App\Models\User;

class SuperAdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth.super_admin')->except(['login', 'authenticate', 'verifyTwoFactor', 'resendTwoFactorCode']);
    }

    /**
     * Show the Super Admin login form.
     *
     * @return \Illuminate\Http\Response
     */
    public function login()
    {
        // If already logged in as super admin, redirect to dashboard
        if (Auth::check() && Auth::user()->is_super_admin) {
            return redirect()->route('super_admin.dashboard');
        }

        return view('super_admin.auth.login');
    }

    /**
     * Handle the Super Admin login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function authenticate(Request $request)
    {
        $credentials = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // Check if user exists and is a super admin
        $user = User::where('username', $credentials['username'])->first();

        if (!$user || !$user->is_super_admin) {
            return back()->withErrors([
                'username' => 'اسم المستخدم أو كلمة المرور غير صحيحة',
            ])->withInput($request->only('username'));
        }

        // Verify password
        if (!Hash::check($credentials['password'], $user->password)) {
            // Log failed login attempt
            SystemLog::create([
                'user_id' => null,
                'action' => 'login_failed',
                'description' => 'محاولة تسجيل دخول فاشلة للمسؤول الأعلى: ' . $credentials['username'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'username' => 'اسم المستخدم أو كلمة المرور غير صحيحة',
            ])->withInput($request->only('username'));
        }

        // Check if two-factor authentication is enabled
        if ($user->super_admin && $user->super_admin->two_factor_enabled) {
            // Store user ID in session for two-factor verification
            $request->session()->put('super_admin_2fa_user_id', $user->id);
            
            // Send verification code
            $user->super_admin->sendTwoFactorCode();
            
            return redirect()->route('super_admin.two_factor');
        }

        // Log in the user
        Auth::login($user);

        // Update last login info
        if ($user->super_admin) {
            $user->super_admin->update([
                'last_login_ip' => $request->ip(),
                'last_login_at' => now(),
            ]);
        }

        // Log successful login
        SystemLog::create([
            'user_id' => $user->id,
            'action' => 'login',
            'description' => 'تسجيل دخول المسؤول الأعلى',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('super_admin.dashboard');
    }

    /**
     * Show the two-factor authentication form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function twoFactor(Request $request)
    {
        if (!$request->session()->has('super_admin_2fa_user_id')) {
            return redirect()->route('super_admin.login');
        }

        return view('super_admin.auth.two_factor');
    }

    /**
     * Verify the two-factor authentication code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyTwoFactor(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        if (!$request->session()->has('super_admin_2fa_user_id')) {
            return redirect()->route('super_admin.login');
        }

        $userId = $request->session()->get('super_admin_2fa_user_id');
        $user = User::findOrFail($userId);

        if (!$user->super_admin) {
            return redirect()->route('super_admin.login');
        }

        if ($user->super_admin->verifyTwoFactorCode($request->code)) {
            // Log in the user
            Auth::login($user);

            // Update last login info
            $user->super_admin->update([
                'last_login_ip' => $request->ip(),
                'last_login_at' => now(),
            ]);

            // Log successful login
            SystemLog::create([
                'user_id' => $user->id,
                'action' => 'login',
                'description' => 'تسجيل دخول المسؤول الأعلى باستخدام المصادقة الثنائية',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Clear the session
            $request->session()->forget('super_admin_2fa_user_id');

            return redirect()->route('super_admin.dashboard');
        }

        // Log failed verification attempt
        SystemLog::create([
            'user_id' => null,
            'action' => 'login_failed',
            'description' => 'محاولة تحقق ثنائي فاشلة للمسؤول الأعلى',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return back()->withErrors([
            'code' => 'الرمز غير صحيح أو انتهت صلاحيته',
        ]);
    }

    /**
     * Resend the two-factor authentication code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resendTwoFactorCode(Request $request)
    {
        if (!$request->session()->has('super_admin_2fa_user_id')) {
            return redirect()->route('super_admin.login');
        }

        $userId = $request->session()->get('super_admin_2fa_user_id');
        $user = User::findOrFail($userId);

        if (!$user->super_admin) {
            return redirect()->route('super_admin.login');
        }

        // Send verification code
        $user->super_admin->sendTwoFactorCode();

        return back()->with('status', 'تم إرسال رمز جديد إلى بريدك الإلكتروني');
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        // Log logout
        if (Auth::check()) {
            SystemLog::create([
                'user_id' => Auth::id(),
                'action' => 'logout',
                'description' => 'تسجيل خروج المسؤول الأعلى',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('super_admin.login');
    }

    /**
     * Show the Super Admin dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        // Get statistics
        $stats = [
            'companies_count' => Company::count(),
            'active_companies' => Company::where('status', 'active')->count(),
            'inactive_companies' => Company::where('status', '!=', 'active')->count(),
            'databases_count' => Company::count(), // Assuming each company has one database
            'total_db_size' => $this->getTotalDatabaseSize(),
            'backups_count' => Backup::count(),
            'last_backup' => Backup::latest('created_at')->first()?->created_at->format('Y-m-d H:i'),
            'logs_count' => SystemLog::count(),
            'warnings_count' => SystemLog::where('action', 'warning')->count(),
            'errors_count' => SystemLog::where('action', 'error')->count(),
        ];

        // Get latest companies
        $latest_companies = Company::latest('created_at')->take(5)->get();

        // Get latest backups
        $latest_backups = Backup::with('company')->latest('created_at')->take(5)->get();

        // Get latest logs
        $latest_logs = SystemLog::with('user')->latest('created_at')->take(10)->get();

        return view('super_admin.dashboard', compact('stats', 'latest_companies', 'latest_backups', 'latest_logs'));
    }

    /**
     * Get the total size of all databases.
     *
     * @return string
     */
    private function getTotalDatabaseSize()
    {
        // This is a placeholder. In a real application, you would calculate the actual size.
        // For MySQL, you might query information_schema.tables
        return '100 MB';
    }
}
