# دليل استخدام نظام الإشعارات المحسن

## مقدمة

تم تطوير نظام إشعارات محسن لمنصة استقدامي يوفر تجربة مستخدم أفضل وأكثر تفاعلية. يتضمن النظام:

1. إشعارات منبثقة (Toast Notifications) بتصميم جذاب
2. نافذة تأكيد الحذف (Delete Confirmation Modal)
3. دعم كامل للوضع الليلي
4. تحريكات سلسة

## استخدام الإشعارات في وظائف العرض (Views)

### إرسال إشعارات من وظائف العرض

```python
from django.contrib import messages
from django.shortcuts import redirect

def save_item(request):
    # ... كود الحفظ ...
    
    # إرسال رسالة نجاح
    messages.success(request, 'تم حفظ البيانات بنجاح')
    
    # إرسال رسالة خطأ
    messages.error(request, 'حدث خطأ أثناء حفظ البيانات')
    
    # إرسال رسالة تحذير
    messages.warning(request, 'يرجى التحقق من البيانات المدخلة')
    
    # إرسال رسالة معلومات
    messages.info(request, 'تم تحديث النظام إلى الإصدار الجديد')
    
    return redirect('some_url')
```

### أنواع الإشعارات

1. **نجاح** (`success`): للعمليات الناجحة مثل الحفظ أو التحديث
2. **خطأ** (`error`): للأخطاء التي تحدث أثناء العمليات
3. **تحذير** (`warning`): للتنبيهات التي تحتاج انتباه المستخدم
4. **معلومات** (`info`): لتقديم معلومات عامة للمستخدم

## استخدام الإشعارات في JavaScript

### إظهار إشعار من JavaScript

```javascript
// إظهار إشعار نجاح
showToast('تم حفظ البيانات بنجاح', 'success');

// إظهار إشعار خطأ
showToast('حدث خطأ أثناء حفظ البيانات', 'error');

// إظهار إشعار تحذير
showToast('يرجى التحقق من البيانات المدخلة', 'warning');

// إظهار إشعار معلومات
showToast('تم تحديث النظام إلى الإصدار الجديد', 'info');

// إظهار إشعار مع مدة محددة (بالمللي ثانية)
showToast('تم تسجيل الدخول بنجاح', 'success', 3000);
```

## استخدام نافذة تأكيد الحذف

### إضافة زر حذف في القالب

```html
<button type="button" 
        data-delete-id="123" 
        data-delete-type="العميل" 
        data-delete-name="أحمد محمد" 
        data-delete-url="/clients/123/delete/">
    <i class="fas fa-trash"></i> حذف
</button>
```

### الخصائص المتاحة

- `data-delete-id`: معرف العنصر المراد حذفه (مطلوب)
- `data-delete-type`: نوع العنصر مثل "العميل" أو "العامل" (اختياري)
- `data-delete-name`: اسم العنصر المراد حذفه (اختياري)
- `data-delete-url`: رابط الحذف (اختياري)

### التعامل مع حدث تأكيد الحذف في JavaScript

```javascript
document.addEventListener('deleteConfirmed', function(e) {
    const { itemId, itemType, itemName } = e.detail;
    
    // يمكنك إرسال طلب AJAX لحذف العنصر
    fetch(`/api/items/${itemId}/delete/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`تم حذف ${itemType} "${itemName}" بنجاح`, 'success');
            // تحديث واجهة المستخدم
        } else {
            showToast(`حدث خطأ أثناء حذف ${itemType}`, 'error');
        }
    });
});
```

## تخصيص مظهر الإشعارات

يمكن تخصيص مظهر الإشعارات من خلال تعديل ملف CSS الخاص بها:

```css
/* تغيير موضع الإشعارات */
.toast-container {
    top: 2rem;
    left: 2rem;
    right: auto;
}

/* تغيير حجم الإشعارات */
.toast {
    max-width: 20rem;
}

/* تغيير ألوان إشعار النجاح */
.toast-success {
    background-color: #f0fff4;
    border-right-color: #38a169;
    color: #276749;
}
```

## أفضل الممارسات

1. استخدم الإشعارات بشكل مناسب وفقًا لنوع الرسالة
2. اجعل نص الإشعار قصيرًا ومفهومًا
3. استخدم نافذة تأكيد الحذف لجميع عمليات الحذف
4. تجنب استخدام الكثير من الإشعارات في وقت واحد
5. استخدم الأيقونات المناسبة لكل نوع من الإشعارات

## مثال كامل

```python
def update_client(request, client_id):
    client = get_object_or_404(Client, id=client_id)
    
    if request.method == 'POST':
        form = ClientForm(request.POST, instance=client)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات العميل {client.name} بنجاح')
            return redirect('client_detail', client_id=client.id)
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = ClientForm(instance=client)
    
    return render(request, 'clients/edit.html', {'form': form, 'client': client})
```
