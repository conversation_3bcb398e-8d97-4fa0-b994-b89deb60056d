@extends('layouts.master_admin')

@section('title', 'إدارة الشركات | المسؤول الأعلى')

@section('page_title', 'إدارة الشركات')
@section('page_subtitle', 'عرض وإدارة جميع الشركات في النظام')

@section('extra_css')
<style>
    .company-card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all duration-300;
    }
    
    .company-card:hover {
        @apply shadow-lg transform -translate-y-1;
    }
    
    .company-header {
        @apply p-4 border-b border-gray-200 dark:border-gray-700;
    }
    
    .company-body {
        @apply p-4;
    }
    
    .company-footer {
        @apply p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600;
    }
    
    .company-logo {
        @apply w-16 h-16 rounded-full bg-admin-light flex items-center justify-center text-admin-primary text-2xl overflow-hidden;
    }
    
    .status-badge {
        @apply px-3 py-1 rounded-full text-xs font-medium;
    }
    
    .status-active {
        @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
    }
    
    .status-inactive {
        @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
    }
    
    .status-pending {
        @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
    }
</style>
@endsection

@section('content')
<!-- Filters and Actions -->
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
    <!-- Search and Filters -->
    <div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
        <div class="relative">
            <input type="text" id="company-search" placeholder="بحث عن شركة..." class="pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 w-full">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>
        
        <select id="status-filter" class="rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2">
            <option value="all">جميع الحالات</option>
            <option value="active">نشطة</option>
            <option value="inactive">غير نشطة</option>
            <option value="pending">قيد الإنشاء</option>
        </select>
    </div>
    
    <!-- Actions -->
    <div class="flex gap-3 w-full md:w-auto">
        <a href="{{ route('super_admin.companies.create') }}" class="bg-admin-primary hover:bg-admin-dark text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus ml-2"></i>
            إنشاء شركة جديدة
        </a>
        
        <button id="export-btn" class="bg-admin-secondary hover:bg-admin-accent text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-file-export ml-2"></i>
            تصدير
        </button>
    </div>
</div>

<!-- Companies Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @if(isset($companies) && count($companies) > 0)
        @foreach($companies as $company)
        <div class="company-card">
            <div class="company-header flex items-center">
                <div class="company-logo ml-4">
                    @if($company->logo)
                        <img src="{{ asset('storage/' . $company->logo) }}" alt="{{ $company->name }}" class="w-full h-full object-cover">
                    @else
                        <i class="fas fa-building"></i>
                    @endif
                </div>
                <div>
                    <h3 class="text-lg font-bold text-admin-primary dark:text-admin-accent">{{ $company->name }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $company->serial_number }}</p>
                </div>
                <div class="mr-auto">
                    <span class="status-badge {{ $company->status == 'active' ? 'status-active' : ($company->status == 'pending' ? 'status-pending' : 'status-inactive') }}">
                        {{ $company->status == 'active' ? 'نشطة' : ($company->status == 'pending' ? 'قيد الإنشاء' : 'غير نشطة') }}
                    </span>
                </div>
            </div>
            
            <div class="company-body">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="text-center p-3 bg-admin-light dark:bg-gray-700 rounded-lg">
                        <div class="text-xs text-gray-500 dark:text-gray-400">قاعدة البيانات</div>
                        <div class="font-medium">{{ $company->database_name }}</div>
                    </div>
                    <div class="text-center p-3 bg-admin-light dark:bg-gray-700 rounded-lg">
                        <div class="text-xs text-gray-500 dark:text-gray-400">تاريخ الإنشاء</div>
                        <div class="font-medium">{{ $company->created_at->format('Y-m-d') }}</div>
                    </div>
                </div>
                
                <div class="flex items-center text-sm mb-2">
                    <i class="fas fa-envelope text-admin-accent ml-2"></i>
                    <span>{{ $company->email ?? 'لا يوجد بريد إلكتروني' }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-phone text-admin-accent ml-2"></i>
                    <span>{{ $company->phone ?? 'لا يوجد رقم هاتف' }}</span>
                </div>
            </div>
            
            <div class="company-footer flex justify-between">
                <a href="{{ route('super_admin.companies.show', $company->id) }}" class="text-admin-primary dark:text-admin-accent hover:underline">
                    <i class="fas fa-eye ml-1"></i>
                    عرض
                </a>
                
                <a href="{{ route('super_admin.companies.edit', $company->id) }}" class="text-admin-secondary dark:text-admin-accent hover:underline">
                    <i class="fas fa-edit ml-1"></i>
                    تعديل
                </a>
                
                <button class="text-red-600 dark:text-red-400 hover:underline delete-company" data-id="{{ $company->id }}" data-name="{{ $company->name }}">
                    <i class="fas fa-trash-alt ml-1"></i>
                    حذف
                </button>
            </div>
        </div>
        @endforeach
    @else
        <div class="col-span-3 text-center py-12">
            <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
                <i class="fas fa-building"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-500 dark:text-gray-400 mb-2">لا توجد شركات</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">لم يتم العثور على أي شركات في النظام</p>
            <a href="{{ route('super_admin.companies.create') }}" class="bg-admin-primary hover:bg-admin-dark text-white px-6 py-2 rounded-lg inline-flex items-center">
                <i class="fas fa-plus ml-2"></i>
                إنشاء شركة جديدة
            </a>
        </div>
    @endif
</div>

<!-- Pagination -->
@if(isset($companies) && $companies->hasPages())
<div class="mt-6">
    {{ $companies->links() }}
</div>
@endif
@endsection

@section('extra_js')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('company-search');
        searchInput.addEventListener('input', function() {
            // Implement search functionality
            const searchTerm = this.value.toLowerCase();
            filterCompanies();
        });
        
        // Status filter
        const statusFilter = document.getElementById('status-filter');
        statusFilter.addEventListener('change', function() {
            filterCompanies();
        });
        
        // Delete company
        const deleteButtons = document.querySelectorAll('.delete-company');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const companyId = this.dataset.id;
                const companyName = this.dataset.name;
                
                confirmDelete(() => {
                    // Send delete request
                    fetch(`/super_admin/companies/${companyId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(`تم حذف الشركة "${companyName}" بنجاح`, 'success');
                            // Remove the company card from the DOM
                            this.closest('.company-card').remove();
                        } else {
                            showToast(data.message || 'حدث خطأ أثناء حذف الشركة', 'error');
                        }
                    })
                    .catch(error => {
                        showToast('حدث خطأ أثناء حذف الشركة', 'error');
                        console.error(error);
                    });
                });
            });
        });
        
        // Filter companies function
        function filterCompanies() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            
            const companyCards = document.querySelectorAll('.company-card');
            
            companyCards.forEach(card => {
                const companyName = card.querySelector('h3').textContent.toLowerCase();
                const companySerial = card.querySelector('p').textContent.toLowerCase();
                const statusBadge = card.querySelector('.status-badge');
                const companyStatus = statusBadge.textContent.trim();
                
                const matchesSearch = companyName.includes(searchTerm) || companySerial.includes(searchTerm);
                const matchesStatus = statusValue === 'all' || 
                                     (statusValue === 'active' && companyStatus === 'نشطة') ||
                                     (statusValue === 'inactive' && companyStatus === 'غير نشطة') ||
                                     (statusValue === 'pending' && companyStatus === 'قيد الإنشاء');
                
                if (matchesSearch && matchesStatus) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        // Export button
        document.getElementById('export-btn').addEventListener('click', function() {
            showToast('جاري تصدير بيانات الشركات...', 'info');
            // Implement export functionality
        });
    });
</script>
@endsection
