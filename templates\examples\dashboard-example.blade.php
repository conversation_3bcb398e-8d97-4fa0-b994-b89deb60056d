{{--
    مثال على استخدام المكونات الجديدة في لوحة التحكم
    
    هذا الملف يوضح كيفية استخدام جميع المكونات المطورة
--}}

@extends('layouts.company')

@section('title', 'مثال على لوحة التحكم - منصة استقدامي')

@section('content')
<div class="p-6 space-y-8">

    {{-- استخدام مكون نظرة عامة كاملة --}}
    @include('components.dashboard-overview', [
        'company' => (object)[
            'name' => 'شركة الخليج للاستقدام',
            'serial_number' => 'HT3Z-8DXS-YMXX-D8DD'
        ],
        'stats' => [
            'total_workers' => 150,
            'total_clients' => 89,
            'total_contracts' => 25,
            'total_services' => 12,
            'notifications' => 5,
            'workers_growth' => 12,
            'clients_growth' => 8,
            'contracts_growth' => 15,
            'services_growth' => 5
        ],
        'activities' => [
            [
                'type' => 'worker',
                'description' => 'تمت إضافة عامل جديد: أحمد محمد علي',
                'time' => 'قبل 5 دقائق',
                'user' => 'مدير الشركة',
                'link' => '/workers/1'
            ],
            [
                'type' => 'client',
                'description' => 'تم تسجيل عميل جديد: شركة النور التجارية',
                'time' => 'قبل 15 دقيقة',
                'details' => 'عميل من فئة الشركات الكبيرة',
                'user' => 'موظف المبيعات'
            ],
            [
                'type' => 'contract',
                'description' => 'تم إنشاء عقد جديد رقم #2024001',
                'time' => 'قبل ساعة',
                'user' => 'مدير العقود',
                'link' => '/contracts/2024001'
            ],
            [
                'type' => 'service',
                'description' => 'تم تحديث خدمة التنظيف المنزلي',
                'time' => 'قبل ساعتين',
                'user' => 'مدير الخدمات'
            ],
            [
                'type' => 'delete',
                'description' => 'تم حذف عامل منتهي الصلاحية',
                'time' => 'قبل 3 ساعات',
                'user' => 'مدير الموارد البشرية'
            ]
        ]
    ])

    {{-- مثال على استخدام البطاقات الإحصائية منفردة --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        
        @include('components.stat-card', [
            'label' => 'العمال النشطون',
            'value' => '142',
            'icon' => 'fas fa-hard-hat',
            'borderColor' => 'border-green-500',
            'iconColor' => 'text-green-500',
            'trend' => '+8%',
            'trendUp' => true,
            'link' => '/workers?status=active'
        ])
        
        @include('components.stat-card', [
            'label' => 'العقود المنتهية',
            'value' => '8',
            'icon' => 'fas fa-file-times',
            'borderColor' => 'border-red-500',
            'iconColor' => 'text-red-500',
            'trend' => '-3%',
            'trendUp' => false,
            'link' => '/contracts?status=expired'
        ])
        
        @include('components.stat-card', [
            'label' => 'الإيرادات الشهرية',
            'value' => '125,000 ريال',
            'icon' => 'fas fa-money-bill-wave',
            'borderColor' => 'border-blue-800',
            'iconColor' => 'text-blue-800',
            'trend' => '+15%',
            'trendUp' => true,
            'link' => '/reports/revenue'
        ])
        
        @include('components.stat-card', [
            'label' => 'متوسط التقييم',
            'value' => '4.8/5',
            'icon' => 'fas fa-star',
            'borderColor' => 'border-yellow-500',
            'iconColor' => 'text-yellow-500',
            'link' => '/reviews'
        ])
    </div>

    {{-- مثال على الإجراءات السريعة المخصصة --}}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <div>
            @include('components.quick-actions', [
                'title' => 'إدارة العمال',
                'columns' => 2,
                'actions' => [
                    [
                        'label' => 'إضافة عامل',
                        'icon' => 'fas fa-user-plus',
                        'color' => 'green',
                        'link' => '/workers/create',
                        'description' => 'تسجيل عامل جديد'
                    ],
                    [
                        'label' => 'تجديد العقود',
                        'icon' => 'fas fa-sync-alt',
                        'color' => 'blue',
                        'onclick' => 'renewContracts()',
                        'description' => 'تجديد العقود المنتهية'
                    ],
                    [
                        'label' => 'تقرير العمال',
                        'icon' => 'fas fa-chart-bar',
                        'color' => 'purple',
                        'link' => '/reports/workers',
                        'description' => 'إحصائيات مفصلة'
                    ],
                    [
                        'label' => 'نسخ احتياطي',
                        'icon' => 'fas fa-download',
                        'color' => 'cyan',
                        'onclick' => 'createBackup()',
                        'description' => 'حفظ البيانات'
                    ]
                ]
            ])
        </div>
        
        <div>
            @include('components.quick-actions', [
                'title' => 'إدارة العملاء',
                'columns' => 1,
                'actions' => [
                    [
                        'label' => 'إضافة عميل جديد',
                        'icon' => 'fas fa-user-tie',
                        'color' => 'cyan',
                        'link' => '/clients/create',
                        'description' => 'تسجيل عميل جديد في النظام'
                    ],
                    [
                        'label' => 'متابعة الطلبات',
                        'icon' => 'fas fa-tasks',
                        'color' => 'yellow',
                        'link' => '/orders/pending',
                        'description' => 'مراجعة الطلبات المعلقة'
                    ],
                    [
                        'label' => 'إرسال تقرير شهري',
                        'icon' => 'fas fa-paper-plane',
                        'color' => 'indigo',
                        'onclick' => 'sendMonthlyReport()',
                        'description' => 'إرسال التقرير للعملاء'
                    ]
                ],
                'showMore' => true
            ])
        </div>
    </div>

    {{-- مثال على الأنشطة المخصصة --}}
    @include('components.recent-activity', [
        'title' => 'سجل العمليات المالية',
        'activities' => [
            [
                'type' => 'create',
                'description' => 'تم استلام دفعة مالية من شركة النور',
                'time' => 'قبل 10 دقائق',
                'details' => 'مبلغ: 50,000 ريال - عقد رقم #2024001',
                'user' => 'المحاسب'
            ],
            [
                'type' => 'edit',
                'description' => 'تم تعديل فاتورة الخدمات الشهرية',
                'time' => 'قبل 30 دقيقة',
                'user' => 'مدير المالية',
                'link' => '/invoices/2024-03-001'
            ],
            [
                'type' => 'delete',
                'description' => 'تم إلغاء فاتورة مرتجعة',
                'time' => 'قبل ساعة',
                'details' => 'فاتورة رقم #INV-2024-150',
                'user' => 'المحاسب'
            ]
        ],
        'maxItems' => 3,
        'showAll' => false
    ])

    {{-- إحصائيات إضافية --}}
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-xl p-6">
        <h3 class="font-bold text-gray-700 dark:text-gray-200 text-lg mb-6">
            <i class="fas fa-chart-pie mr-2 text-blue-600"></i>
            إحصائيات تفصيلية
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-3xl font-bold text-green-600 mb-1">98%</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">معدل رضا العملاء</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-3xl font-bold text-blue-600 mb-1">15</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">يوم متوسط المعالجة</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-3xl font-bold text-purple-600 mb-1">24/7</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">ساعات الدعم</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-3xl font-bold text-cyan-600 mb-1">5</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">سنوات الخبرة</div>
            </div>
        </div>
    </div>

</div>

{{-- JavaScript للوظائف المخصصة --}}
<script>
function renewContracts() {
    Swal.fire({
        title: 'تجديد العقود',
        text: 'هل تريد تجديد جميع العقود المنتهية؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#ef4444',
        confirmButtonText: 'نعم، جدد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('تم!', 'تم تجديد العقود بنجاح', 'success');
        }
    });
}

function createBackup() {
    Swal.fire({
        title: 'جاري إنشاء النسخة الاحتياطية...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    setTimeout(() => {
        Swal.fire('تم!', 'تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    }, 3000);
}

function sendMonthlyReport() {
    Swal.fire({
        title: 'إرسال التقرير الشهري',
        text: 'سيتم إرسال التقرير لجميع العملاء المسجلين',
        icon: 'info',
        showCancelButton: true,
        confirmButtonColor: '#7c3aed',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'إرسال',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('تم الإرسال!', 'تم إرسال التقرير بنجاح', 'success');
        }
    });
}
</script>

@endsection
