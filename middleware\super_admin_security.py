"""
Middleware للحماية المتقدمة لصفحة المسؤول الأعلى
"""
import logging
from django.http import Http404, HttpResponseForbidden
from django.utils import timezone
from django.core.cache import cache
import hashlib

logger = logging.getLogger('security')

class SuperAdminSecurityMiddleware:
    """
    Middleware لحماية صفحة المسؤول الأعلى
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.super_admin_paths = [
            '/super_admin/',
            '/super_admin/login/',
            '/super_admin/dashboard/',
            '/super_admin/send-verification-code/',
        ]

        # عناوين IP المسموحة (يمكن تخصيصها)
        self.allowed_ips = [
            '127.0.0.1',
            'localhost',
            '::1',
        ]

        # الحد الأقصى لمحاولات تسجيل الدخول
        self.max_login_attempts = 3
        self.lockout_duration = 300  # 5 دقائق

    def __call__(self, request):
        # التحقق من المسار
        if any(request.path.startswith(path) for path in self.super_admin_paths):
            # 1. التحقق من عنوان IP
            if not self._is_ip_allowed(request):
                self._log_security_event(request, 'UNAUTHORIZED_IP_ACCESS')
                raise Http404("الصفحة غير موجودة")

            # 2. التحقق من محاولات تسجيل الدخول
            if request.method == 'POST' and 'login' in request.path:
                if self._is_ip_locked(request):
                    self._log_security_event(request, 'BLOCKED_DUE_TO_MULTIPLE_ATTEMPTS')
                    return HttpResponseForbidden("تم حظر عنوان IP مؤقتاً بسبب محاولات متعددة")

            # 3. التحقق من User-Agent المشبوه
            if self._is_suspicious_user_agent(request):
                self._log_security_event(request, 'SUSPICIOUS_USER_AGENT')
                raise Http404("الصفحة غير موجودة")

            # 4. إخفاء الصفحة عن محركات البحث والبوتات
            if self._is_bot_request(request):
                self._log_security_event(request, 'BOT_ACCESS_ATTEMPT')
                raise Http404("الصفحة غير موجودة")

            # 5. تسجيل محاولة الوصول
            self._log_access_attempt(request)

        response = self.get_response(request)

        # إضافة headers أمنية
        if any(request.path.startswith(path) for path in self.super_admin_paths):
            response['X-Robots-Tag'] = 'noindex, nofollow, noarchive, nosnippet'
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

        return response

    def _is_ip_allowed(self, request):
        """التحقق من عنوان IP المسموح"""
        client_ip = self._get_client_ip(request)

        # في بيئة الإنتاج، يجب تخصيص عناوين IP محددة
        # حالياً نسمح بالوصول المحلي فقط
        return client_ip in self.allowed_ips or client_ip.startswith('192.168.')

    def _get_client_ip(self, request):
        """الحصول على عنوان IP الحقيقي للعميل"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _is_ip_locked(self, request):
        """التحقق من حظر عنوان IP"""
        client_ip = self._get_client_ip(request)
        cache_key = f"login_attempts_{hashlib.md5(client_ip.encode()).hexdigest()}"
        attempts = cache.get(cache_key, 0)
        return attempts >= self.max_login_attempts

    def _increment_login_attempts(self, request):
        """زيادة عداد محاولات تسجيل الدخول"""
        client_ip = self._get_client_ip(request)
        cache_key = f"login_attempts_{hashlib.md5(client_ip.encode()).hexdigest()}"
        attempts = cache.get(cache_key, 0) + 1
        cache.set(cache_key, attempts, self.lockout_duration)
        return attempts

    def _is_suspicious_user_agent(self, request):
        """التحقق من User-Agent المشبوه"""
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        suspicious_agents = [
            'curl', 'wget', 'python', 'bot', 'crawler', 'spider',
            'scraper', 'scanner', 'hack', 'exploit'
        ]
        return any(agent in user_agent for agent in suspicious_agents)

    def _is_bot_request(self, request):
        """التحقق من طلبات البوتات"""
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        bot_indicators = [
            'googlebot', 'bingbot', 'slurp', 'duckduckbot',
            'baiduspider', 'yandexbot', 'facebookexternalhit'
        ]
        return any(bot in user_agent for bot in bot_indicators)

    def _log_security_event(self, request, event_type):
        """تسجيل الأحداث الأمنية"""
        client_ip = self._get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')

        logger.warning(
            f"SECURITY EVENT: {event_type} | "
            f"IP: {client_ip} | "
            f"Path: {request.path} | "
            f"User-Agent: {user_agent} | "
            f"Time: {timezone.now()}"
        )

    def _log_access_attempt(self, request):
        """تسجيل محاولات الوصول"""
        client_ip = self._get_client_ip(request)

        logger.info(
            f"SUPER_ADMIN_ACCESS: "
            f"IP: {client_ip} | "
            f"Path: {request.path} | "
            f"Method: {request.method} | "
            f"Time: {timezone.now()}"
        )


class SuperAdminLoginAttemptMiddleware:
    """
    Middleware لمراقبة محاولات تسجيل الدخول
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # مراقبة محاولات تسجيل الدخول الفاشلة
        if (request.path == '/super_admin/login/' and
            request.method == 'POST' and
            response.status_code in [200, 302]):

            if 'error' in str(response.content) or response.status_code == 200:
                # محاولة فاشلة
                self._handle_failed_login(request)
            else:
                # محاولة ناجحة
                self._handle_successful_login(request)

        return response

    def _handle_failed_login(self, request):
        """التعامل مع محاولات تسجيل الدخول الفاشلة"""
        client_ip = self._get_client_ip(request)
        username = request.POST.get('username', 'Unknown')

        logger.warning(
            f"FAILED_LOGIN_ATTEMPT: "
            f"Username: {username} | "
            f"IP: {client_ip} | "
            f"Time: {timezone.now()}"
        )

        # زيادة عداد المحاولات
        cache_key = f"failed_login_{hashlib.md5(client_ip.encode()).hexdigest()}"
        attempts = cache.get(cache_key, 0) + 1
        cache.set(cache_key, attempts, 3600)  # ساعة واحدة

    def _handle_successful_login(self, request):
        """التعامل مع محاولات تسجيل الدخول الناجحة"""
        client_ip = self._get_client_ip(request)
        username = request.POST.get('username', 'Unknown')

        logger.info(
            f"SUCCESSFUL_LOGIN: "
            f"Username: {username} | "
            f"IP: {client_ip} | "
            f"Time: {timezone.now()}"
        )

        # مسح عداد المحاولات الفاشلة
        cache_key = f"failed_login_{hashlib.md5(client_ip.encode()).hexdigest()}"
        cache.delete(cache_key)

    def _get_client_ip(self, request):
        """الحصول على عنوان IP الحقيقي للعميل"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
