from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>

from .views import DocumentViewSet, DocumentAttachmentViewSet

# إنشاء الموجه
router = DefaultRouter()
router.register(r'documents', DocumentViewSet, basename='document')
router.register(r'document-attachments', DocumentAttachmentViewSet, basename='document-attachment')

# تعريف مسارات المستندات
urlpatterns = [
    # مسارات المستندات
    path('', include(router.urls)),
]
