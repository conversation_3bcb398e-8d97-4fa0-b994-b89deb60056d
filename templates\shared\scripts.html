<!-- Alpine.js للتفاعلات -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

<!-- تهيئة Alpine.js -->
<script src="/static/js/alpine-init.js"></script>

<!-- نظام تأكيد الحذف -->
<script src="/static/js/delete-modal.js"></script>

<!-- JavaScript للبحث الديناميكي والوضع الليلي -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل البحث حسب القسم الحالي
        const currentPath = window.location.pathname;
        const searchInput = document.getElementById('search-input');
        const searchContainer = document.getElementById('search-container');

        // تحديد الأقسام التي تحتاج إلى البحث
        const searchableSections = [
            '/workers/',
            '/clients/',
            '/contracts/',
            '/services/',
            '/reports/'
        ];

        // تفعيل البحث إذا كان القسم الحالي يحتاج إلى البحث
        let searchEnabled = false;
        let currentSection = '';

        // تحقق أكثر دقة من المسار الحالي
        for (const section of searchableSections) {
            if (currentPath.startsWith(section) || currentPath.includes(section)) {
                console.log("Found searchable section:", section);
                searchEnabled = true;
                currentSection = section;
                break;
            }
        }

        // تحقق إضافي للمسارات المحددة
        if (currentPath.match(/\/workers($|\/.*)/i) ||
            currentPath.match(/\/clients($|\/.*)/i) ||
            currentPath.match(/\/contracts($|\/.*)/i) ||
            currentPath.match(/\/services($|\/.*)/i) ||
            currentPath.match(/\/reports($|\/.*)/i)) {
            console.log("Regex match found for searchable section");
            searchEnabled = true;
        }

        // الحصول على زر البحث
        const searchToggleBtn = document.getElementById('search-toggle-btn');

        // تفعيل أو تعطيل حقل البحث
        if (searchInput && searchContainer) {
            console.log("Search enabled:", searchEnabled);
            console.log("Current path:", currentPath);

            if (searchEnabled) {
                // إظهار وتفعيل حقل البحث
                searchContainer.style.display = 'block';
                searchInput.disabled = false;
                searchInput.placeholder = "ابحث في " + getCurrentSectionName(currentPath) + "...";
                searchInput.focus();
                searchInput.addEventListener('keyup', performSearch);

                // إضافة تأثير بصري لتمييز حقل البحث
                searchInput.classList.add('ring-2', 'ring-blue-300');

                // تم إزالة رسالة "اكتب للبحث في العمال" بناءً على طلب المستخدم

                // إضافة أيقونة البحث في شريط الروابط
                highlightNavLink(currentSection);

                // إخفاء زر البحث
                if (searchToggleBtn) {
                    searchToggleBtn.classList.add('hidden');
                }

                console.log("Search container displayed");
            } else {
                // إخفاء حقل البحث
                searchContainer.style.display = 'none';

                // إظهار زر البحث
                if (searchToggleBtn) {
                    searchToggleBtn.classList.remove('hidden');


                }

                console.log("Search container hidden");
            }
        } else {
            console.log("Search input or container not found");
        }

        // تمييز رابط القسم الحالي في شريط التنقل
        function highlightNavLink(section) {
            // إزالة أيقونات البحث من جميع الروابط أولاً
            const allSearchIcons = document.querySelectorAll('.search-icon');
            allSearchIcons.forEach(icon => {
                icon.remove();
            });

            // إضافة أيقونة البحث فقط للقسم الحالي
            const navLinks = document.querySelectorAll('.bg-blue-500 a');
            navLinks.forEach(link => {
                if (link.getAttribute('href').includes(section)) {
                    // إضافة أيقونة البحث بجانب الرابط
                    if (!link.querySelector('.search-icon')) {
                        const searchIcon = document.createElement('i');
                        searchIcon.className = 'fas fa-search text-yellow-300 mr-1 search-icon';
                        searchIcon.title = 'البحث متاح في هذا القسم';
                        link.appendChild(searchIcon);
                    }
                }
            });
        }

        // الحصول على اسم القسم الحالي
        function getCurrentSectionName(path) {
            if (path.includes('/workers/')) return "العمال";
            if (path.includes('/clients/')) return "العملاء";
            if (path.includes('/contracts/')) return "العقود";
            if (path.includes('/services/')) return "الخدمات";
            if (path.includes('/reports/')) return "التقارير";
            return "هذا القسم";
        }

        // وظيفة البحث الديناميكي
        function performSearch(e) {
            const searchTerm = e.target.value.toLowerCase();

            // البحث في الجداول
            const table = document.querySelector('table');
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                let matchCount = 0;

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                        matchCount++;

                        // تمييز النص المطابق (اختياري)
                        if (searchTerm.length > 0) {
                            highlightMatches(row, searchTerm);
                        } else {
                            removeHighlights(row);
                        }
                    } else {
                        row.style.display = 'none';
                    }
                });

                // عرض رسالة إذا لم يتم العثور على نتائج
                const noResultsMessage = document.getElementById('no-results-message');
                if (searchTerm.length > 0 && matchCount === 0) {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.id = 'no-results-message';
                        message.className = 'text-center py-4 text-gray-500';
                        message.innerHTML = `<i class="fas fa-search mr-2"></i> لا توجد نتائج تطابق "${searchTerm}"`;
                        table.parentNode.appendChild(message);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }

            // البحث في البطاقات (إذا كانت موجودة)
            const cards = document.querySelectorAll('.card, .dashboard-card');
            if (cards.length > 0) {
                cards.forEach(card => {
                    const text = card.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }
        }

        // تمييز النص المطابق
        function highlightMatches(element, term) {
            removeHighlights(element);

            const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null, false);
            const textNodes = [];
            let node;

            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const text = textNode.nodeValue.toLowerCase();
                const index = text.indexOf(term);

                if (index >= 0) {
                    const span = document.createElement('span');
                    span.className = 'search-highlight';

                    const before = textNode.nodeValue.substring(0, index);
                    const match = textNode.nodeValue.substring(index, index + term.length);
                    const after = textNode.nodeValue.substring(index + term.length);

                    span.textContent = match;

                    const fragment = document.createDocumentFragment();
                    fragment.appendChild(document.createTextNode(before));
                    fragment.appendChild(span);
                    fragment.appendChild(document.createTextNode(after));

                    textNode.parentNode.replaceChild(fragment, textNode);
                }
            });
        }

        // إزالة التمييز
        function removeHighlights(element) {
            const highlights = element.querySelectorAll('.search-highlight');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                const text = document.createTextNode(highlight.textContent);
                parent.replaceChild(text, highlight);
                parent.normalize();
            });
        }

        // وظيفة الوضع الليلي المحسنة
        const darkModeToggle = document.getElementById('dark-mode-toggle');
        const htmlElement = document.documentElement;

        // التحقق من وجود إعدادات الوضع الليلي في التخزين المحلي
        const isDarkMode = localStorage.getItem('theme') === 'dark';

        // تطبيق الوضع الليلي عند تحميل الصفحة إذا كان مفعلاً
        if (isDarkMode) {
            htmlElement.classList.add('dark');
            updateDarkModeIcon(true);
        }

        // تعريف وظيفة تبديل الوضع الليلي
        window.toggleDarkMode = function() {
            const isDark = htmlElement.classList.toggle('dark');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
            updateDarkModeIcon(isDark);

            // إرسال حدث لجميع الصفحات المفتوحة
            try {
                const darkModeEvent = new CustomEvent('darkModeChanged', { detail: { isDark } });
                window.dispatchEvent(darkModeEvent);
            } catch (e) {
                console.error('Error dispatching dark mode event:', e);
            }

            // تطبيق الوضع الليلي على جميع العناصر
            applyDarkModeToElements(isDark);
        };

        // تبديل الوضع الليلي عند النقر على الزر
        if (darkModeToggle) {
            // إزالة أي مستمعي أحداث سابقة
            darkModeToggle.removeEventListener('click', window.toggleDarkMode);

            // إضافة مستمع جديد
            darkModeToggle.addEventListener('click', function() {
                console.log("Dark mode toggle clicked");
                window.toggleDarkMode();
            });

            // إضافة تأثير نقر للزر
            darkModeToggle.addEventListener('mousedown', function() {
                this.classList.add('scale-90');
            });

            darkModeToggle.addEventListener('mouseup', function() {
                this.classList.remove('scale-90');
            });

            // إضافة تلميح للزر
            darkModeToggle.title = isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي';
        }

        // تحديث أيقونة الوضع الليلي مع تأثيرات حركية
        function updateDarkModeIcon(isDark) {
            const darkModeToggles = document.querySelectorAll('[id^="dark-mode-toggle"]');
            darkModeToggles.forEach(toggle => {
                // إضافة تأثير الدوران عند التبديل
                toggle.style.transition = 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
                toggle.style.transform = 'rotate(360deg)';

                // إعادة التعيين بعد انتهاء الحركة
                setTimeout(() => {
                    toggle.style.transform = 'rotate(0deg)';
                }, 300);

                if (isDark) {
                    toggle.innerHTML = '<i class="fas fa-sun"></i>';
                    toggle.title = 'تفعيل الوضع النهاري';
                    toggle.classList.remove('bg-indigo-700');
                    toggle.classList.add('bg-yellow-600');
                } else {
                    toggle.innerHTML = '<i class="fas fa-moon"></i>';
                    toggle.title = 'تفعيل الوضع الليلي';
                    toggle.classList.remove('bg-yellow-600');
                    toggle.classList.add('bg-indigo-700');
                }
            });
        }

        // تطبيق الوضع الليلي على جميع العناصر
        function applyDarkModeToElements(isDark) {
            console.log("Applying dark mode to all elements:", isDark);

            // تطبيق على الجداول
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                if (isDark) {
                    table.classList.add('dark-table');
                } else {
                    table.classList.remove('dark-table');
                }
            });

            // تطبيق على البطاقات
            const cards = document.querySelectorAll('.card, [class*="bg-white"], [class*="shadow"], .dashboard-card, .stats-card');
            cards.forEach(card => {
                if (isDark) {
                    card.classList.add('dark-card');
                } else {
                    card.classList.remove('dark-card');
                }
            });

            // الحفاظ على ألوان الأيقونات في الوضع الليلي
            // تم نقل هذا الكود إلى ملف CSS منفصل (dark-mode-colors.css)
            // للتأكد من تطبيق الألوان بشكل متسق

            // تطبيق الألوان على جميع الأيقونات
            const allIcons = document.querySelectorAll('i, svg');
            allIcons.forEach(icon => {
                if (isDark) {
                    // إزالة أي تأثيرات قد تغير لون الأيقونة في الوضع الليلي
                    icon.style.opacity = '1';
                    icon.style.filter = 'none';
                }
            });

            // تطبيق على الأزرار
            const buttons = document.querySelectorAll('button, .btn, [class*="bg-blue"], [class*="bg-red"], [class*="bg-green"], [class*="bg-yellow"], [class*="bg-purple"]');
            buttons.forEach(button => {
                if (isDark) {
                    button.classList.add('dark-button');
                } else {
                    button.classList.remove('dark-button');
                }
            });

            // تطبيق على حقول الإدخال
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (isDark) {
                    input.classList.add('dark-input');
                } else {
                    input.classList.remove('dark-input');
                }
            });

            // تطبيق على القوائم المنسدلة
            const dropdowns = document.querySelectorAll('.dropdown-menu, #userDropdown');
            dropdowns.forEach(dropdown => {
                if (isDark) {
                    dropdown.classList.add('dark-dropdown');
                } else {
                    dropdown.classList.remove('dark-dropdown');
                }
            });

            // تطبيق على الشريط العلوي والسفلي
            const headers = document.querySelectorAll('header, footer');
            headers.forEach(header => {
                if (isDark) {
                    header.classList.add('dark-header');
                } else {
                    header.classList.remove('dark-header');
                }
            });

            // تطبيق على شريط الأدوات
            const navbars = document.querySelectorAll('.bg-blue-500, .bg-blue-600');
            navbars.forEach(navbar => {
                if (isDark) {
                    navbar.classList.add('dark-navbar');
                } else {
                    navbar.classList.remove('dark-navbar');
                }
            });

            // تطبيق على العناصر الأخرى
            const others = document.querySelectorAll('div, span, p, h1, h2, h3, h4, h5, h6, a');
            others.forEach(element => {
                if (isDark) {
                    element.classList.add('dark-element');
                } else {
                    element.classList.remove('dark-element');
                }
            });
        }

        // تطبيق الوضع الليلي عند تحميل الصفحة
        if (isDarkMode) {
            applyDarkModeToElements(true);
        }

        // الاستماع لتغييرات الوضع الليلي من صفحات أخرى
        window.addEventListener('darkModeChanged', function(event) {
            const isDark = event.detail.isDark;
            if (isDark) {
                htmlElement.classList.add('dark');
            } else {
                htmlElement.classList.remove('dark');
            }
            updateDarkModeIcon(isDark);
            applyDarkModeToElements(isDark);
        });

        // وظيفة فتح وإغلاق قائمة المستخدم المنسدلة مع تأثيرات حركية محسنة
        window.toggleDropdown = function() {
            const dropdown = document.getElementById('userDropdown');
            if (!dropdown) {
                console.error('User dropdown element not found!');
                return;
            }

            const button = document.querySelector('button[onclick="toggleDropdown()"]');
            const arrow = document.getElementById('dropdown-arrow');

            if (dropdown.classList.contains('hidden')) {
                // فتح القائمة
                dropdown.classList.remove('hidden');

                // تحديث موضع القائمة المنسدلة بناءً على موضع الزر
                if (button) {
                    const buttonRect = button.getBoundingClientRect();
                    dropdown.style.top = (buttonRect.bottom + 7) + 'px';
                    dropdown.style.right = (window.innerWidth - buttonRect.right) + 'px';
                } else {
                    // موضع افتراضي إذا لم يتم العثور على الزر
                    dropdown.style.top = '60px';
                    dropdown.style.right = '20px';
                }

                // تأثير حركي للقائمة - تظهر من الأسفل إلى الأعلى
                dropdown.style.opacity = '0';
                dropdown.style.transform = 'translateY(-5px)';

                setTimeout(() => {
                    dropdown.style.opacity = '1';
                    dropdown.style.transform = 'translateY(0)';
                }, 10);

                // تدوير السهم للأسفل
                if (arrow) {
                    arrow.style.transform = 'rotate(180deg)';
                }

                // تغيير لون الزر
                if (button) {
                    button.classList.add('bg-indigo-800');
                    button.classList.remove('bg-indigo-600');

                    // تأثير ظل أكبر
                    button.classList.add('shadow-md');
                    button.classList.remove('shadow-sm');
                }
            } else {
                // إغلاق القائمة
                dropdown.style.opacity = '0';
                dropdown.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    dropdown.classList.add('hidden');
                }, 200);

                // إعادة السهم للأعلى
                if (arrow) {
                    arrow.style.transform = 'rotate(0deg)';
                }

                // إعادة لون الزر
                if (button) {
                    button.classList.remove('bg-indigo-800');
                    button.classList.add('bg-indigo-600');

                    // إعادة الظل الأصلي
                    button.classList.remove('shadow-md');
                    button.classList.add('shadow-sm');
                }
            }
        }

        // إضافة تأثير النبض للأزرار
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(0.95); }
                100% { transform: scale(1); }
            }
            .pulse-effect {
                animation: pulse 0.3s ease-in-out;
            }
        `;
        document.head.appendChild(style);

        // إغلاق القائمة عند الضغط خارجها
        window.addEventListener('click', function(e) {
            const dropdown = document.getElementById('userDropdown');
            const button = document.querySelector('button[onclick="toggleDropdown()"]');
            if (dropdown && button && !dropdown.contains(e.target) && !button.contains(e.target)) {
                dropdown.classList.add('hidden');
                button.classList.remove('bg-indigo-700');
                button.classList.add('bg-indigo-600');
            }
        });
    });
</script>
