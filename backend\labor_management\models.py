from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

# Choices para los diferentes tipos de entidades
WORKER_TYPE_CHOICES = (
    ('contract', 'عقد'),
    ('monthly_service', 'خدمة شهرية'),
    ('daily_service', 'خدمة يومية'),
)

CLIENT_TYPE_CHOICES = (
    ('permanent', 'دائم'),
    ('monthly', 'شهري'),
    ('daily', 'يومي'),
)

CONTRACT_TYPE_CHOICES = (
    ('permanent', 'دائم'),
    ('monthly', 'شهري'),
    ('custom', 'مخصص'),
)

STATUS_CHOICES = (
    ('active', 'نشط'),
    ('inactive', 'غير نشط'),
    ('pending', 'معلق'),
    ('completed', 'مكتمل'),
    ('cancelled', 'ملغي'),
)

# Modelo para las sucursales
class Branch(models.Model):
    """نموذج فروع الشركة"""
    name = models.CharField(max_length=100, verbose_name="اسم الفرع")
    address = models.TextField(verbose_name="العنوان")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_branches', verbose_name="مدير الفرع")
    is_main = models.BooleanField(default=False, verbose_name="فرع رئيسي")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "فرع"
        verbose_name_plural = "الفروع"
        ordering = ['-is_main', 'name']

# Modelo para los trabajadores
class Worker(models.Model):
    """نموذج العمال"""
    full_name = models.CharField(max_length=100, verbose_name="الاسم الكامل")
    nationality = models.CharField(max_length=50, verbose_name="الجنسية")
    passport_number = models.CharField(max_length=20, unique=True, verbose_name="رقم جواز السفر")
    iqama_number = models.CharField(max_length=20, blank=True, null=True, unique=True, verbose_name="رقم الإقامة")
    worker_type = models.CharField(max_length=20, choices=WORKER_TYPE_CHOICES, verbose_name="نوع العامل")
    date_of_birth = models.DateField(verbose_name="تاريخ الميلاد")
    gender = models.CharField(max_length=10, choices=(('male', 'ذكر'), ('female', 'أنثى')), verbose_name="الجنس")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    branch = models.ForeignKey(Branch, on_delete=models.SET_NULL, null=True, blank=True, related_name='workers', verbose_name="الفرع")
    photo = models.ImageField(upload_to='workers/', blank=True, null=True, verbose_name="الصورة الشخصية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"{self.full_name} - {self.passport_number}"

    class Meta:
        verbose_name = "عامل"
        verbose_name_plural = "العمال"
        ordering = ['full_name']

# Modelo para los clientes
class Client(models.Model):
    """نموذج العملاء"""
    name = models.CharField(max_length=100, verbose_name="اسم العميل")
    client_type = models.CharField(max_length=20, choices=CLIENT_TYPE_CHOICES, verbose_name="نوع العميل")
    contact_person = models.CharField(max_length=100, blank=True, null=True, verbose_name="الشخص المسؤول")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    address = models.TextField(verbose_name="العنوان")
    cr_number = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم السجل التجاري")
    vat_number = models.CharField(max_length=20, blank=True, null=True, verbose_name="الرقم الضريبي")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    branch = models.ForeignKey(Branch, on_delete=models.SET_NULL, null=True, blank=True, related_name='clients', verbose_name="الفرع")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "عميل"
        verbose_name_plural = "العملاء"
        ordering = ['name']

# Modelo para los contratos
class Contract(models.Model):
    """نموذج العقود"""
    contract_number = models.CharField(max_length=50, unique=True, verbose_name="رقم العقد")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='contracts', verbose_name="العميل")
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='contracts', verbose_name="العامل")
    contract_type = models.CharField(max_length=20, choices=CONTRACT_TYPE_CHOICES, verbose_name="نوع العقد")
    start_date = models.DateField(verbose_name="تاريخ بداية العقد")
    end_date = models.DateField(blank=True, null=True, verbose_name="تاريخ نهاية العقد")
    monthly_fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الرسوم الشهرية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="حالة العقد")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_contracts', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"عقد {self.contract_number} - {self.client.name} - {self.worker.full_name}"

    class Meta:
        verbose_name = "عقد"
        verbose_name_plural = "العقود"
        ordering = ['-created_at']

# Modelo para los servicios diarios
class DailyService(models.Model):
    """نموذج الخدمات اليومية"""
    BOOKING_TYPE_CHOICES = (
        ('fixed', 'حجز ثابت'),
        ('temporary', 'حجز مؤقت'),
    )

    service_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الخدمة")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='daily_services', verbose_name="العميل")
    workers = models.ManyToManyField(Worker, related_name='daily_services_assigned', verbose_name="العمال", blank=True)
    service_date = models.DateField(verbose_name="تاريخ الخدمة")
    service_time = models.TimeField(default='08:00', verbose_name="وقت الخدمة")
    hours = models.PositiveIntegerField(default=7, verbose_name="عدد الساعات")
    overtime_hours = models.PositiveIntegerField(default=0, verbose_name="ساعات العمل الإضافية")
    workers_required = models.PositiveIntegerField(default=1, verbose_name="عدد العمال المطلوبين")
    booking_type = models.CharField(max_length=20, choices=BOOKING_TYPE_CHOICES, default='temporary', verbose_name="نوع الحجز")
    fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="رسوم الخدمة")
    overtime_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="رسوم العمل الإضافي")
    location = models.TextField(blank=True, null=True, verbose_name="موقع الخدمة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الخدمة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_daily_services', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"خدمة يومية {self.service_number} - {self.client.name} - {self.service_date}"

    def get_total_fee(self):
        """حساب إجمالي الرسوم بما في ذلك الأوفر تايم"""
        return self.fee + self.overtime_fee

    def get_assigned_workers_count(self):
        """الحصول على عدد العمال المعينين"""
        return self.workers.count()

    def is_fully_assigned(self):
        """التحقق مما إذا كان تم تعيين جميع العمال المطلوبين"""
        return self.get_assigned_workers_count() >= self.workers_required

    class Meta:
        verbose_name = "خدمة يومية"
        verbose_name_plural = "الخدمات اليومية"
        ordering = ['-service_date']

# Modelo para los servicios de 24 horas
class Daily24HService(models.Model):
    """نموذج خدمات 24 ساعة"""
    service_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الخدمة")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='daily_24h_services', verbose_name="العميل")
    workers = models.ManyToManyField(Worker, related_name='daily_24h_services_assigned', verbose_name="العمال", blank=True)
    start_date = models.DateField(verbose_name="تاريخ بداية الخدمة")
    start_time = models.TimeField(default='08:00', verbose_name="وقت بداية الخدمة")
    end_date = models.DateField(verbose_name="تاريخ نهاية الخدمة")
    workers_required = models.PositiveIntegerField(default=1, verbose_name="عدد العمال المطلوبين")
    fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="رسوم الخدمة")
    location = models.TextField(blank=True, null=True, verbose_name="موقع الخدمة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الخدمة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_24h_services', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"خدمة 24 ساعة {self.service_number} - {self.client.name} - {self.start_date}"

    def get_assigned_workers_count(self):
        """الحصول على عدد العمال المعينين"""
        return self.workers.count()

    def is_fully_assigned(self):
        """التحقق مما إذا كان تم تعيين جميع العمال المطلوبين"""
        return self.get_assigned_workers_count() >= self.workers_required

    class Meta:
        verbose_name = "خدمة 24 ساعة"
        verbose_name_plural = "خدمات 24 ساعة"
        ordering = ['-start_date']

# Modelo para los servicios personalizados
class CustomService(models.Model):
    """نموذج الخدمات المخصصة"""
    service_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الخدمة")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='custom_services', verbose_name="العميل")
    workers = models.ManyToManyField(Worker, related_name='custom_services_assigned', verbose_name="العمال", blank=True)
    start_date = models.DateField(verbose_name="تاريخ بداية الخدمة")
    end_date = models.DateField(verbose_name="تاريخ نهاية الخدمة")
    service_days = models.CharField(max_length=255, blank=True, null=True, verbose_name="أيام الخدمة")
    service_time = models.TimeField(default='08:00', verbose_name="وقت الخدمة")
    duration_hours = models.PositiveIntegerField(default=7, verbose_name="مدة الخدمة بالساعات")
    workers_required = models.PositiveIntegerField(default=1, verbose_name="عدد العمال المطلوبين")
    fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="رسوم الخدمة")
    location = models.TextField(blank=True, null=True, verbose_name="موقع الخدمة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الخدمة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_custom_services', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"خدمة مخصصة {self.service_number} - {self.client.name} - {self.start_date}"

    def get_assigned_workers_count(self):
        """الحصول على عدد العمال المعينين"""
        return self.workers.count()

    def is_fully_assigned(self):
        """التحقق مما إذا كان تم تعيين جميع العمال المطلوبين"""
        return self.get_assigned_workers_count() >= self.workers_required

    def get_service_days_list(self):
        """الحصول على قائمة أيام الخدمة"""
        if not self.service_days:
            return []
        return self.service_days.split(',')

    class Meta:
        verbose_name = "خدمة مخصصة"
        verbose_name_plural = "الخدمات المخصصة"
        ordering = ['-start_date']

# Modelos para los procedimientos oficiales
class SecurityClearance(models.Model):
    """نموذج الموافقات الأمنية"""
    clearance_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الموافقة")
    issue_date = models.DateField(verbose_name="تاريخ الإصدار")
    expiry_date = models.DateField(verbose_name="تاريخ الانتهاء")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    document = models.FileField(upload_to='security_clearances/', blank=True, null=True, verbose_name="المستند")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"موافقة أمنية {self.clearance_number}"

    class Meta:
        verbose_name = "موافقة أمنية"
        verbose_name_plural = "الموافقات الأمنية"
        ordering = ['-issue_date']

class EntryVisa(models.Model):
    """نموذج سمات الدخول"""
    visa_number = models.CharField(max_length=50, unique=True, verbose_name="رقم السمة")
    issue_date = models.DateField(verbose_name="تاريخ الإصدار")
    expiry_date = models.DateField(verbose_name="تاريخ الانتهاء")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    document = models.FileField(upload_to='entry_visas/', blank=True, null=True, verbose_name="المستند")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"سمة دخول {self.visa_number}"

    class Meta:
        verbose_name = "سمة دخول"
        verbose_name_plural = "سمات الدخول"
        ordering = ['-issue_date']

class WorkPermit(models.Model):
    """نموذج إذن العمل"""
    permit_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الإذن")
    issue_date = models.DateField(verbose_name="تاريخ الإصدار")
    expiry_date = models.DateField(verbose_name="تاريخ الانتهاء")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    document = models.FileField(upload_to='work_permits/', blank=True, null=True, verbose_name="المستند")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"إذن عمل {self.permit_number}"

    class Meta:
        verbose_name = "إذن عمل"
        verbose_name_plural = "أذونات العمل"
        ordering = ['-issue_date']

# Modelo para las operaciones de trabajadores
class WorkerOperation(models.Model):
    """نموذج عمليات التشغيل للعمال"""
    operation_number = models.CharField(max_length=50, unique=True, verbose_name="رقم العملية")
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='operations', verbose_name="العامل")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='worker_operations', verbose_name="العميل")
    security_clearance = models.OneToOneField(SecurityClearance, on_delete=models.SET_NULL, null=True, blank=True, related_name='worker_operation', verbose_name="الموافقة الأمنية")
    entry_visa = models.OneToOneField(EntryVisa, on_delete=models.SET_NULL, null=True, blank=True, related_name='worker_operation', verbose_name="سمة الدخول")
    work_permit = models.ForeignKey(WorkPermit, on_delete=models.SET_NULL, null=True, blank=True, related_name='worker_operations', verbose_name="إذن العمل")
    operation_date = models.DateField(verbose_name="تاريخ العملية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة العملية")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_operations', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"عملية {self.operation_number} - {self.worker.full_name}"

    class Meta:
        verbose_name = "عملية تشغيل"
        verbose_name_plural = "عمليات التشغيل"
        ordering = ['-operation_date']

# Modelo para las solicitudes de pruebas de sangre
class BloodTestRequest(models.Model):
    """نموذج طلبات فحص الدم"""
    request_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    request_date = models.DateField(verbose_name="تاريخ الطلب")
    hospital = models.CharField(max_length=100, verbose_name="المستشفى")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الطلب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_blood_tests', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"طلب فحص دم {self.request_number}"

    class Meta:
        verbose_name = "طلب فحص دم"
        verbose_name_plural = "طلبات فحص الدم"
        ordering = ['-request_date']

# Modelo para los detalles de las solicitudes de pruebas de sangre
class BloodTestRequestWorker(models.Model):
    """نموذج تفاصيل طلبات فحص الدم للعمال"""
    blood_test_request = models.ForeignKey(BloodTestRequest, on_delete=models.CASCADE, related_name='workers', verbose_name="طلب فحص الدم")
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='blood_tests', verbose_name="العامل")
    test_date = models.DateField(verbose_name="تاريخ الفحص")
    result = models.CharField(max_length=20, choices=(('positive', 'إيجابي'), ('negative', 'سلبي'), ('pending', 'معلق')), default='pending', verbose_name="نتيجة الفحص")
    result_date = models.DateField(null=True, blank=True, verbose_name="تاريخ النتيجة")
    document = models.FileField(upload_to='blood_tests/', blank=True, null=True, verbose_name="مستند النتيجة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"فحص دم للعامل {self.worker.full_name} - {self.test_date}"

    class Meta:
        verbose_name = "فحص دم للعامل"
        verbose_name_plural = "فحوصات الدم للعمال"
        ordering = ['-test_date']

# Modelo para las transferencias de patrocinio
class SponsorshipTransfer(models.Model):
    """نموذج تحويل الكفالة"""
    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='sponsorship_transfers', verbose_name="العامل")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='sponsorship_transfers', verbose_name="العميل")
    transfer_date = models.DateField(verbose_name="تاريخ التحويل")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة التحويل")
    document = models.FileField(upload_to='sponsorship_transfers/', blank=True, null=True, verbose_name="مستند التحويل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_transfers', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"تحويل كفالة {self.transfer_number} - {self.worker.full_name}"

    class Meta:
        verbose_name = "تحويل كفالة"
        verbose_name_plural = "تحويلات الكفالة"
        ordering = ['-transfer_date']

# Modelo para las transferencias de trabajadores entre sucursales
class WorkerTransfer(models.Model):
    """نموذج تحويل العمال بين الفروع"""
    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='branch_transfers', verbose_name="العامل")
    from_branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='transfers_from', verbose_name="من فرع")
    to_branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='transfers_to', verbose_name="إلى فرع")
    transfer_date = models.DateField(verbose_name="تاريخ التحويل")
    reason = models.TextField(verbose_name="سبب التحويل")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة التحويل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_branch_transfers', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return f"تحويل العامل {self.worker.full_name} من {self.from_branch.name} إلى {self.to_branch.name}"

    class Meta:
        verbose_name = "تحويل عامل بين الفروع"
        verbose_name_plural = "تحويلات العمال بين الفروع"
        ordering = ['-transfer_date']
