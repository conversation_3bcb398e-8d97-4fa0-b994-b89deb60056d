# Generated by Django 5.2 on 2025-04-08 21:44

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0002_remove_contract_contract_type_and_more'),
        ('reports', '0001_initial'),
        ('services', '0002_alter_cleaningservice_service_type_and_more'),
        ('workers', '0002_remove_worker_company_remove_worker_visa_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='', max_length=200, verbose_name='العنوان')),
                ('report_type', models.CharField(choices=[('income', 'تقرير الدخل'), ('expense', 'تقرير المصروفات'), ('profit_loss', 'تقرير الربح والخسارة'), ('tax', 'تقرير الضرائب'), ('salary', 'تقرير الرواتب'), ('custom', 'تقرير مخصص')], default='income', max_length=20, verbose_name='نوع التقرير')),
                ('start_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ النهاية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('date_created', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'تقرير مالي',
                'verbose_name_plural': 'التقارير المالية',
            },
        ),
        migrations.RemoveField(
            model_name='savedreport',
            name='report_type',
        ),
        migrations.RemoveField(
            model_name='savedreport',
            name='user',
        ),
        migrations.CreateModel(
            name='ContractReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='', max_length=200, verbose_name='العنوان')),
                ('start_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ النهاية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('date_created', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('contracts', models.ManyToManyField(related_name='reports', to='contracts.contract', verbose_name='العقود')),
            ],
            options={
                'verbose_name': 'تقرير العقود',
                'verbose_name_plural': 'تقارير العقود',
            },
        ),
        migrations.CreateModel(
            name='ServiceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='', max_length=200, verbose_name='العنوان')),
                ('start_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ النهاية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('date_created', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('services', models.ManyToManyField(related_name='reports', to='services.cleaningservice', verbose_name='الخدمات')),
            ],
            options={
                'verbose_name': 'تقرير الخدمات',
                'verbose_name_plural': 'تقارير الخدمات',
            },
        ),
        migrations.CreateModel(
            name='WorkerReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='', max_length=200, verbose_name='العنوان')),
                ('start_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ النهاية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('date_created', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('workers', models.ManyToManyField(related_name='reports', to='workers.worker', verbose_name='العمال')),
            ],
            options={
                'verbose_name': 'تقرير العمال',
                'verbose_name_plural': 'تقارير العمال',
            },
        ),
        migrations.DeleteModel(
            name='Notification',
        ),
        migrations.DeleteModel(
            name='ReportType',
        ),
        migrations.DeleteModel(
            name='SavedReport',
        ),
    ]
