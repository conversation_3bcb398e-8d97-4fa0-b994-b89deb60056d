from django.contrib import admin
from .models import CompanyUser, CompanyLoginAttempt

@admin.register(CompanyUser)
class CompanyUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'company_serial', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'company_serial')
    ordering = ('-created_at',)

@admin.register(CompanyLoginAttempt)
class CompanyLoginAttemptAdmin(admin.ModelAdmin):
    list_display = ('username', 'company_serial', 'success', 'ip_address', 'timestamp')
    list_filter = ('success', 'timestamp')
    search_fields = ('username', 'company_serial', 'ip_address')
    ordering = ('-timestamp',)
    readonly_fields = ('timestamp',)
