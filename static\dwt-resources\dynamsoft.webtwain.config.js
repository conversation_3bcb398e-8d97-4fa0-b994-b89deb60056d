/**
 * ملف تكوين Dynamic Web TWAIN
 * 
 * هذا الملف يحتوي على إعدادات تكوين مكتبة Dynamic Web TWAIN
 * لتسهيل الوصول إلى الماسح الضوئي من المتصفح.
 */

// تكوين Dynamsoft
var Dynamsoft = Dynamsoft || {};

// إعدادات بيئة Dynamic Web TWAIN
Dynamsoft.WebTwainEnv = {
    // مفتاح المنتج
    ProductKey: 't0068MQAAAGNcO61He7rFtA/4bA5WRwfN0ONXUn8BYdGsOOm8Stkg39Q3GrOJGTgQxPXlR2yYBDXrjV1+Jrmm4VPdGQ==',
    
    // مسار الموارد
    ResourcesPath: '/static/dwt-resources/',
    
    // إعدادات التحميل
    Containers: [{
        ContainerId: 'dwtcontrolContainer',
        Width: '100%',
        Height: '400px'
    }],
    
    // إعدادات التنزيل
    IfAddMD5InUploadHeader: true,
    
    // إعدادات التثبيت
    IfCheckDWTVersion: true,
    
    // رسائل مخصصة
    CustomizableDisplayInfo: {
        errorMessages: {
            // رسائل الخطأ
            InitializeFailed: 'فشل في تهيئة Dynamic Web TWAIN. يرجى تحديث الصفحة وإعادة المحاولة.',
            InstallFailed: 'فشل في تثبيت Dynamic Web TWAIN. يرجى تنزيل المثبت وتشغيله يدوياً.',
            LoadModuleFailed: 'فشل في تحميل وحدة Dynamic Web TWAIN. يرجى تحديث الصفحة وإعادة المحاولة.',
            DownloadFailed: 'فشل في تنزيل Dynamic Web TWAIN. يرجى التحقق من اتصالك بالإنترنت وإعادة المحاولة.'
        },
        
        // رسائل التثبيت
        install: {
            title: 'تثبيت Dynamic Web TWAIN',
            description: 'مطلوب تثبيت Dynamic Web TWAIN للوصول إلى الماسح الضوئي.',
            downloadNow: 'تنزيل الآن',
            installManually: 'تثبيت يدوي'
        }
    }
};

// إعدادات التحميل
Dynamsoft.WebTwainEnv.AutoLoad = false;

// إعدادات التنزيل
Dynamsoft.WebTwainEnv.Installers = {
    // مثبت Windows
    Windows: {
        URL: 'https://download.dynamsoft.com/webtwain/DWASSetup.exe',
        InstallSize: '54MB'
    },
    
    // مثبت macOS
    Mac: {
        URL: 'https://download.dynamsoft.com/webtwain/DWASSetup.pkg',
        InstallSize: '61MB'
    },
    
    // مثبت Linux
    Linux: {
        URL: 'https://download.dynamsoft.com/webtwain/DWASSetup_Linux.zip',
        InstallSize: '66MB'
    }
};

// دالة التهيئة
function initDynamicWebTWAIN() {
    if (Dynamsoft && Dynamsoft.WebTwain && Dynamsoft.WebTwain.Load) {
        Dynamsoft.WebTwain.Load();
    } else {
        console.error('لم يتم العثور على Dynamsoft.WebTwain.Load');
    }
}
