{"name": "labor-management-system", "version": "18.1.0", "description": "منصة استقدامي السحابية - نظام إدارة العمالة", "main": "index.js", "scripts": {"dev": "tailwindcss -i ./static/css/input.css -o ./static/css/tailwind.css --watch", "build": "tailwindcss -i ./static/css/input.css -o ./static/css/tailwind.css --minify", "build-prod": "NODE_ENV=production tailwindcss -i ./static/css/input.css -o ./static/css/tailwind.css --minify", "watch": "tailwindcss -i ./static/css/input.css -o ./static/css/tailwind.css --watch", "install-deps": "npm install", "setup": "npm install && npm run build"}, "keywords": ["django", "tailwindcss", "labor-management", "arabic", "rtl"], "author": "منتظر وسام", "license": "MIT", "devDependencies": {"tailwindcss": "^3.4.0", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "tailwindcss-rtl": "^0.9.0"}, "dependencies": {"alpinejs": "^3.13.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/muntader/labor-management-system.git"}, "bugs": {"url": "https://github.com/muntader/labor-management-system/issues"}, "homepage": "https://github.com/muntader/labor-management-system#readme"}