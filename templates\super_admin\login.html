<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول المسؤول الأعلى - منصة استقدامي</title>

    <!-- Security Headers -->
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SweetAlert2 -->
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }



        .security-pattern {
            position: relative;
            z-index: 1;
        }

        .login-card {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.5), transparent);
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 2px solid rgba(71, 85, 105, 0.3);
            color: rgba(71, 85, 105, 0.6);
            background: rgba(30, 41, 59, 0.8);
        }

        .step-number.active {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            border-color: #dc2626;
        }

        .step-number.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-color: #10b981;
        }

        .step-line {
            width: 60px;
            height: 2px;
            background: rgba(71, 85, 105, 0.3);
            margin: 0 10px;
        }

        .step-line.completed {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .input-field {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(71, 85, 105, 0.3);
            transition: all 0.3s ease;
        }

        .input-field:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
            background: rgba(30, 41, 59, 0.9);
        }

        .method-card {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(71, 85, 105, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .method-card:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(220, 38, 38, 0.2);
        }

        .method-card.selected {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .method-card.cursor-not-allowed {
            cursor: not-allowed;
            opacity: 0.6;
        }

        .method-card.cursor-not-allowed:hover {
            transform: none;
            box-shadow: none;
            border-color: rgba(71, 85, 105, 0.3);
        }

        .code-input {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(71, 85, 105, 0.3);
            transition: all 0.3s ease;
            font-size: 24px;
            text-align: center;
            font-weight: bold;
        }

        .code-input:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
            background: rgba(30, 41, 59, 0.9);
        }

        .action-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(220, 38, 38, 0.4);
        }

        .back-btn {
            background: rgba(75, 85, 99, 0.8);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(75, 85, 99, 1);
            transform: translateY(-2px);
        }

        .floating-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #dc2626;
            transition: all 0.3s ease;
        }

        .input-field:focus + .floating-icon {
            color: #ef4444;
            transform: translateY(-50%) scale(1.1);
        }







        .countdown {
            color: #fbbf24;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
        }
    </style>
</head>
<body class="security-pattern">

    <!-- Security Warning Banner -->
    <div class="bg-red-600 text-white text-center py-3 text-sm font-bold">
        <i class="fas fa-shield-alt ml-2"></i>
        منطقة آمنة - تسجيل دخول المسؤول الأعلى
        <i class="fas fa-lock ml-2"></i>
    </div>

    <div class="min-h-screen flex items-center justify-center p-6">
        <div class="login-card rounded-2xl p-8 w-full max-w-md">
            {% csrf_token %}

            <!-- Header -->
            <div class="text-center mb-8">
                {% load static %}
                <div class="relative mb-6">
                    <img src="{% static 'images/logo.webp' %}" alt="استقدامي" class="h-16 w-auto mx-auto">
                </div>

                <h1 class="text-2xl font-bold text-white mb-2">
                    <i class="fas fa-user-shield text-red-400 ml-2"></i>
                    المسؤول الأعلى
                </h1>
                <p class="text-gray-300 text-sm">تسجيل دخول آمن بثلاث خطوات</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step-number active" id="step-indicator-1">1</div>
                <div class="step-line" id="step-line-1"></div>
                <div class="step-number" id="step-indicator-2">2</div>
                <div class="step-line" id="step-line-2"></div>
                <div class="step-number" id="step-indicator-3">3</div>
            </div>

            <!-- Step 1: Credentials -->
            <div class="step active" id="step-1">
                <div class="space-y-6">
                    <h2 class="text-xl font-bold text-white text-center mb-6">
                        <i class="fas fa-user ml-2"></i>
                        بيانات تسجيل الدخول
                    </h2>

                    <div class="relative">
                        <input type="text"
                               id="username"
                               name="username"
                               required
                               class="input-field w-full px-4 py-3 pr-12 rounded-lg text-white placeholder-gray-400 focus:outline-none"
                               placeholder="اسم المستخدم">
                        <i class="floating-icon fas fa-user"></i>
                    </div>

                    <div class="relative">
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               class="input-field w-full px-4 py-3 pr-12 rounded-lg text-white placeholder-gray-400 focus:outline-none"
                               placeholder="كلمة المرور">
                        <i class="floating-icon fas fa-lock"></i>
                    </div>

                    <button type="button"
                            onclick="nextStep(1)"
                            class="action-btn w-full py-3 px-6 rounded-lg text-white font-bold text-lg">
                        <i class="fas fa-arrow-left ml-2"></i>
                        التالي
                    </button>
                </div>
            </div>

            <!-- Step 2: Authentication Method -->
            <div class="step" id="step-2">
                <div class="space-y-6">
                    <h2 class="text-xl font-bold text-white text-center mb-6">
                        <i class="fas fa-shield-alt ml-2"></i>
                        اختيار طريقة المصادقة
                    </h2>

                    <div class="grid grid-cols-1 gap-4">
                        <div class="method-card p-4 rounded-lg" onclick="selectMethod('email')" id="email-method">
                            <div class="text-center">
                                <i class="fas fa-envelope text-blue-400 text-2xl mb-2"></i>
                                <h3 class="text-white font-bold">البريد الإلكتروني</h3>
                                <p class="text-gray-400 text-sm"><EMAIL></p>
                            </div>
                        </div>

                        <div class="method-card p-4 rounded-lg cursor-not-allowed" onclick="selectMethod('app')" id="app-method" style="display: block;">
                            <div class="text-center opacity-50">
                                <i class="fas fa-mobile-alt text-gray-500 text-2xl mb-2"></i>
                                <h3 class="text-gray-500 font-bold">تطبيق المصادقة</h3>
                                <p class="text-gray-500 text-sm">جاري التحقق...</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-4 space-x-reverse">
                        <button type="button"
                                onclick="prevStep(2)"
                                class="back-btn flex-1 py-3 px-6 rounded-lg text-white font-bold">
                            <i class="fas fa-arrow-right ml-2"></i>
                            السابق
                        </button>
                        <button type="button"
                                onclick="nextStep(2)"
                                id="method-next-btn"
                                disabled
                                class="action-btn flex-1 py-3 px-6 rounded-lg text-white font-bold disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-arrow-left ml-2"></i>
                            التالي
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Verification Code -->
            <div class="step" id="step-3">
                <div class="space-y-6">
                    <h2 class="text-xl font-bold text-white text-center mb-6" id="step3-title">
                        <i class="fas fa-key ml-2"></i>
                        رمز التحقق
                    </h2>

                    <div id="step3-info" class="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-3 text-center">
                        <p class="text-blue-200 text-sm">
                            <i class="fas fa-info-circle ml-2"></i>
                            <span id="step3-message">تم إرسال رمز التحقق</span>
                        </p>
                    </div>

                    <div>
                        <input type="text"
                               id="verification_code"
                               name="verification_code"
                               maxlength="6"
                               required
                               class="code-input w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:outline-none"
                               placeholder="000000"
                               autocomplete="off">
                    </div>

                    <div id="countdown-container" class="text-center" style="display: none;">
                        <p class="text-gray-400 text-sm">
                            انتهاء صلاحية الرمز خلال:
                            <span class="countdown" id="countdown">01:00</span>
                        </p>
                    </div>

                    <div class="flex space-x-4 space-x-reverse">
                        <button type="button"
                                onclick="prevStep(3)"
                                class="back-btn flex-1 py-3 px-6 rounded-lg text-white font-bold">
                            <i class="fas fa-arrow-right ml-2"></i>
                            السابق
                        </button>
                        <button type="button"
                                onclick="login()"
                                class="action-btn flex-1 py-3 px-6 rounded-lg text-white font-bold">
                            <i class="fas fa-sign-in-alt ml-2"></i>
                            تسجيل دخول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Messages Area -->
            <div id="messages" class="mt-6"></div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-gray-400 text-xs">
                    <i class="fas fa-shield-alt ml-1"></i>
                    محمي بنظام أمان متعدد الطبقات
                </p>
                <p class="text-gray-500 text-xs mt-2">
                    © {{ "now"|date:"Y" }} منصة استقدامي السحابية
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Global variables
        let currentStep = 1;
        let selectedAuthMethod = null;
        let countdownTimer = null;

        // CSRF Token
        function getCSRFToken() {
            // Try to get from cookie first
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, 10) === 'csrftoken=') {
                        cookieValue = decodeURIComponent(cookie.substring(10));
                        break;
                    }
                }
            }

            // If not found in cookie, try to get from meta tag or hidden input
            if (!cookieValue) {
                const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfInput) {
                    cookieValue = csrfInput.value;
                }
            }

            return cookieValue;
        }

        // Show message
        function showMessage(message, type = 'error') {
            const messagesDiv = document.getElementById('messages');
            const colorClass = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            const iconClass = type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle';

            messagesDiv.innerHTML = `
                <div class="bg-${colorClass}-900 bg-opacity-50 border border-${colorClass}-600 text-${colorClass}-200 px-4 py-3 rounded-lg text-sm">
                    <i class="fas fa-${iconClass} ml-2"></i>
                    ${message}
                </div>
            `;

            // Auto-hide after 5 seconds
            setTimeout(() => {
                messagesDiv.innerHTML = '';
            }, 5000);
        }

        // Update step indicator
        function updateStepIndicator(step) {
            // Reset all steps
            for (let i = 1; i <= 3; i++) {
                const indicator = document.getElementById(`step-indicator-${i}`);
                const line = document.getElementById(`step-line-${i}`);

                indicator.classList.remove('active', 'completed');
                if (line) line.classList.remove('completed');

                if (i < step) {
                    indicator.classList.add('completed');
                    if (line) line.classList.add('completed');
                } else if (i === step) {
                    indicator.classList.add('active');
                }
            }
        }

        // Show step
        function showStep(step) {
            // Hide all steps
            document.querySelectorAll('.step').forEach(s => {
                s.classList.remove('active');
            });

            // Show current step
            document.getElementById(`step-${step}`).classList.add('active');

            // Update indicator
            updateStepIndicator(step);

            currentStep = step;
        }

        // Next step function
        function nextStep(step) {
            if (step === 1) {
                // Validate credentials
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    showMessage('يرجى إدخال اسم المستخدم وكلمة المرور');
                    return;
                }

                // Send AJAX request to validate credentials
                validateCredentials(username, password);

            } else if (step === 2) {
                // Validate method selection
                if (!selectedAuthMethod) {
                    showMessage('يرجى اختيار طريقة المصادقة');
                    return;
                }

                // Send AJAX request to send verification code
                sendVerificationCode(selectedAuthMethod);
            }
        }

        // Previous step function
        function prevStep(step) {
            if (step > 1) {
                showStep(step - 1);
            }
        }

        // Select authentication method
        function selectMethod(method) {
            const methodCard = document.getElementById(`${method}-method`);

            // التحقق من أن الطريقة غير معطلة
            if (methodCard.classList.contains('cursor-not-allowed')) {
                showMessage('هذه الطريقة غير متاحة. يرجى إعداد تطبيق المصادقة أولاً.', 'error');
                return;
            }

            // Remove selection from all cards
            document.querySelectorAll('.method-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            methodCard.classList.add('selected');

            selectedAuthMethod = method;

            // Enable next button
            document.getElementById('method-next-btn').disabled = false;
        }

        // Validate credentials (Step 1)
        function validateCredentials(username, password) {
            console.log('Starting credential validation...');
            console.log('CSRF Token:', getCSRFToken());

            // Show loading
            Swal.fire({
                title: 'جاري التحقق...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('{% url "super_admin:super_admin_login" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: `step=1&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                Swal.close();
                console.log('Response data:', data);

                if (data.success) {
                    showMessage(data.message, 'success');
                    showStep(2);

                    // Show/hide app method based on availability
                    const appMethod = document.getElementById('app-method');
                    console.log('🔍 Response data:', data);
                    console.log('🔍 App auth status:', data.has_app_auth);
                    console.log('🔍 App method element:', appMethod);

                    console.log('🔍 Current app method HTML before:', appMethod.innerHTML);
                    console.log('🔍 Current app method display before:', appMethod.style.display);

                    if (!data.has_app_auth) {
                        console.log('❌ App auth not available, showing disabled state');
                        appMethod.style.display = 'block'; // إظهار الكارت حتى لو معطل
                        // إضافة رسالة توضيحية
                        appMethod.innerHTML = `
                            <div class="text-center opacity-50">
                                <i class="fas fa-mobile-alt text-gray-500 text-2xl mb-2"></i>
                                <h3 class="text-gray-500 font-bold">تطبيق المصادقة</h3>
                                <p class="text-gray-500 text-sm">غير مُعد بعد</p>
                                <a href="/super_admin/setup-auth-app/" target="_blank" class="text-blue-400 text-xs hover:underline">
                                    <i class="fas fa-cog ml-1"></i>
                                    إعداد التطبيق
                                </a>
                            </div>
                        `;
                        appMethod.onclick = null; // إزالة وظيفة النقر
                        appMethod.classList.add('cursor-not-allowed');
                    } else {
                        console.log('✅ App auth available, showing enabled state');
                        appMethod.style.display = 'block';
                        // التأكد من أن المحتوى صحيح
                        appMethod.innerHTML = `
                            <div class="text-center">
                                <i class="fas fa-mobile-alt text-green-400 text-2xl mb-2"></i>
                                <h3 class="text-white font-bold">تطبيق المصادقة</h3>
                                <p class="text-gray-400 text-sm">Google Authenticator</p>
                            </div>
                        `;
                        appMethod.onclick = () => selectMethod('app');
                        appMethod.classList.remove('cursor-not-allowed');
                    }

                    console.log('🔍 Current app method HTML after:', appMethod.innerHTML);
                    console.log('🔍 Current app method display after:', appMethod.style.display);
                } else {
                    if (data.redirect) {
                        window.location.reload();
                    } else {
                        showMessage(data.message);
                    }
                }
            })
            .catch(error => {
                Swal.close();
                console.error('Fetch error:', error);
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
            });
        }

        // Send verification code (Step 2)
        function sendVerificationCode(method) {
            // Show loading
            Swal.fire({
                title: 'جاري الإرسال...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('{% url "super_admin:super_admin_login" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: `step=2&auth_method=${method}`
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();

                if (data.success) {
                    showMessage(data.message, 'success');
                    showStep(3);

                    // Update step 3 UI based on method
                    updateStep3UI(data.auth_method, data.email);

                    // Start countdown for email method
                    if (data.auth_method === 'email') {
                        startCountdown();
                    }

                    // Focus on code input
                    setTimeout(() => {
                        document.getElementById('verification_code').focus();
                    }, 500);
                } else {
                    if (data.redirect) {
                        window.location.reload();
                    } else {
                        showMessage(data.message);
                    }
                }
            })
            .catch(error => {
                Swal.close();
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
                console.error('Error:', error);
            });
        }

        // Update Step 3 UI
        function updateStep3UI(method, email) {
            const title = document.getElementById('step3-title');
            const info = document.getElementById('step3-info');
            const message = document.getElementById('step3-message');
            const countdownContainer = document.getElementById('countdown-container');

            if (method === 'email') {
                title.innerHTML = '<i class="fas fa-envelope ml-2"></i> رمز البريد الإلكتروني';
                info.className = 'bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-3 text-center';
                message.innerHTML = `<i class="fas fa-info-circle ml-2"></i> تم إرسال رمز التحقق إلى: ${email}`;
                countdownContainer.style.display = 'block';
            } else {
                title.innerHTML = '<i class="fas fa-mobile-alt ml-2"></i> رمز تطبيق المصادقة';
                info.className = 'bg-green-900 bg-opacity-30 border border-green-600 rounded-lg p-3 text-center';
                message.innerHTML = '<i class="fas fa-mobile-alt ml-2"></i> افتح تطبيق Google Authenticator وأدخل الرمز الحالي';
                countdownContainer.style.display = 'none';
            }
        }

        // Start countdown timer
        function startCountdown() {
            let timeLeft = 60; // 1 minute
            const countdownElement = document.getElementById('countdown');

            countdownTimer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    countdownElement.textContent = 'انتهت الصلاحية';
                    countdownElement.classList.add('text-red-400');
                }

                timeLeft--;
            }, 1000);
        }

        // Login function (Step 3)
        function login() {
            const code = document.getElementById('verification_code').value.trim();

            if (!code || code.length !== 6) {
                showMessage('يرجى إدخال رمز التحقق المكون من 6 أرقام');
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري تسجيل الدخول...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('{% url "super_admin:super_admin_login" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: `step=3&verification_code=${encodeURIComponent(code)}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('Login response:', data);

                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الدخول بنجاح!',
                        text: data.message,
                        confirmButtonColor: '#10b981',
                        timer: 2000,
                        timerProgressBar: true,
                        allowOutsideClick: false
                    }).then(() => {
                        // التأكد من وجود رابط التوجيه
                        const redirectUrl = data.redirect_url || '/super_admin/';
                        console.log('Redirecting to:', redirectUrl);
                        window.location.href = redirectUrl;
                    });
                } else {
                    Swal.close();
                    if (data.redirect) {
                        window.location.reload();
                    } else {
                        showMessage(data.message);
                    }
                }
            })
            .catch(error => {
                Swal.close();
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
                console.error('Error:', error);
            });
        }

        // Auto-submit when 6 digits entered
        document.addEventListener('DOMContentLoaded', function() {
            const codeInput = document.getElementById('verification_code');
            if (codeInput) {
                codeInput.addEventListener('input', function(e) {
                    if (e.target.value.length === 6) {
                        setTimeout(() => {
                            login();
                        }, 500);
                    }
                });
            }

            // Enter key handlers
            document.getElementById('username').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('password').focus();
                }
            });

            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    nextStep(1);
                }
            });
        });

        // Security monitoring
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent right-click
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير أمني',
                    text: 'العمليات محمية في هذه المنطقة',
                    confirmButtonColor: '#dc2626'
                });
            });

            // Prevent F12
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'وصول مرفوض',
                        text: 'أدوات المطور محظورة في هذه المنطقة',
                        confirmButtonColor: '#dc2626'
                    });
                }
            });
        });
    </script>
</body>
</html>
