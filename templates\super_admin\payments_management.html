{% extends 'layouts/super_admin.html' %}

{% block title %}إدارة المدفوعات - منصة استقدامي{% endblock %}

{% block content %}
<div class="space-y-6">

    <!-- رأس الصفحة -->
    <div class="admin-card rounded-2xl p-6 shadow-xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    <i class="fas fa-dollar-sign ml-2"></i>
                    إدارة المدفوعات
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">إدارة شاملة لجميع المدفوعات والمعاملات المالية</p>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <button class="admin-btn text-white px-4 py-2 rounded-lg hover:scale-105 transition-transform">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة دفعة جديدة
                </button>
                <button class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <i class="fas fa-download ml-2"></i>
                    تصدير التقرير
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات المدفوعات -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي المدفوعات</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ total_payments|default:"0" }} د.ع</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">مدفوعات مكتملة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ completed_payments|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">مدفوعات معلقة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ pending_payments|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">مدفوعات مرفوضة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ failed_payments|default:"0" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="admin-card rounded-xl p-6 shadow-lg">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-white mb-2">البحث</label>
                <div class="relative">
                    <input type="text" placeholder="ابحث عن دفعة..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 bg-white">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-white mb-2">الشركة</label>
                <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 bg-white">
                    <option value="">جميع الشركات</option>
                    {% for company in companies %}
                    <option value="{{ company.id }}">{{ company.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-white mb-2">الحالة</label>
                <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 bg-white">
                    <option value="">جميع الحالات</option>
                    <option value="completed">مكتملة</option>
                    <option value="pending">معلقة</option>
                    <option value="failed">مرفوضة</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-white mb-2">تاريخ الدفع</label>
                <input type="date" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 bg-white">
            </div>

            <div class="flex items-end">
                <button class="admin-btn text-white px-6 py-2 rounded-lg w-full">
                    <i class="fas fa-filter ml-2"></i>
                    تطبيق الفلتر
                </button>
            </div>
        </div>
    </div>

    <!-- جدول المدفوعات -->
    <div class="admin-card rounded-xl shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-medium">رقم الدفعة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الشركة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">المبلغ</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">طريقة الدفع</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">تاريخ الدفع</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الحالة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    {% for payment in payments %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4">
                            <span class="font-mono text-sm bg-gray-600 text-white px-2 py-1 rounded">#{{ payment.id|default:"001" }}</span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                                    {{ payment.company.name|first|default:"ش" }}
                                </div>
                                <div class="mr-3">
                                    <p class="font-medium text-white">{{ payment.company.name|default:"شركة تجريبية" }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="text-lg font-bold text-green-600">{{ payment.amount|default:"1,500,000" }} د.ع</span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                <i class="fas fa-credit-card ml-1"></i>
                                {{ payment.payment_method|default:"بطاقة ائتمان" }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <p class="text-sm text-gray-300">{{ payment.created_at|date:"d/m/Y"|default:"28/05/2025" }}</p>
                            <p class="text-xs text-gray-400">{{ payment.created_at|time:"H:i"|default:"14:30" }}</p>
                        </td>
                        <td class="px-6 py-4">
                            {% if payment.status == 'completed' or not payment.status %}
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">مكتملة</span>
                            {% elif payment.status == 'pending' %}
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">معلقة</span>
                            {% else %}
                                <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">مرفوضة</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-800 p-1 rounded" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-800 p-1 rounded" title="تحديث الحالة">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-purple-600 hover:text-purple-800 p-1 rounded" title="طباعة الإيصال">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800 p-1 rounded" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-money-bill-wave text-4xl mb-4"></i>
                                <p class="text-lg font-medium">لا توجد مدفوعات مسجلة</p>
                                <p class="text-sm">ابدأ بإضافة دفعة جديدة</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if payments.has_other_pages %}
        <div class="bg-gray-700 px-6 py-3 border-t border-gray-600">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-300">
                    عرض {{ payments.start_index }} إلى {{ payments.end_index }} من أصل {{ payments.paginator.count }} دفعة
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    {% if payments.has_previous %}
                        <a href="?page={{ payments.previous_page_number }}" class="px-3 py-1 bg-gray-600 text-white border border-gray-500 rounded text-sm hover:bg-gray-500">السابق</a>
                    {% endif %}

                    <span class="px-3 py-1 bg-green-600 text-white rounded text-sm">{{ payments.number }}</span>

                    {% if payments.has_next %}
                        <a href="?page={{ payments.next_page_number }}" class="px-3 py-1 bg-gray-600 text-white border border-gray-500 rounded text-sm hover:bg-gray-500">التالي</a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // البحث المباشر
    document.querySelector('input[placeholder="ابحث عن دفعة..."]').addEventListener('input', function(e) {
        // يمكن إضافة AJAX للبحث المباشر
        console.log('البحث عن:', e.target.value);
    });
</script>
{% endblock %}
