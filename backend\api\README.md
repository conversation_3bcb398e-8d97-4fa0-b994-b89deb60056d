# واجهة برمجة التطبيقات (API) لنظام إدارة العمالة

## نظرة عامة

هذه الوثيقة تشرح واجهة برمجة التطبيقات (API) الخاصة بنظام إدارة العمالة. تم تصميم هذه الواجهة لتوفير وصول سهل وآمن إلى بيانات النظام من خلال تطبيقات الويب والجوال.

## المميزات الرئيسية

- واجهة RESTful كاملة
- مصادقة باستخدام JWT (JSON Web Tokens)
- توثيق تفاعلي باستخدام Swagger/OpenAPI
- دعم كامل للغة العربية
- تحكم دقيق في الصلاحيات
- تصفية وترتيب وتقسيم البيانات
- تحميل وتنزيل الملفات
- إشعارات في الوقت الحقيقي

## المتطلبات التقنية

- Python 3.8+
- Django 4.0+
- Django REST Framework 3.13+
- PostgreSQL 13+
- Redis (للإشعارات في الوقت الحقيقي)
- Celery (للمهام الخلفية)

## هيكل API

تم تنظيم API في مجموعات منطقية تعكس هيكل النظام:

1. **المصادقة والمستخدمين** (`/api/auth/`)
2. **العمال** (`/api/workers/`)
3. **العقود** (`/api/contracts/`)
4. **الخدمات** (`/api/services/`)
5. **المستندات** (`/api/documents/`)
6. **الإجراءات** (`/api/procedures/`)
7. **التقارير** (`/api/reports/`)
8. **الإعدادات** (`/api/settings/`)

## نقاط النهاية الرئيسية

### المصادقة

- `POST /api/auth/login/` - تسجيل الدخول والحصول على رمز JWT
- `POST /api/auth/refresh/` - تحديث رمز JWT
- `POST /api/auth/logout/` - تسجيل الخروج وإبطال الرمز
- `GET /api/auth/user/` - الحصول على معلومات المستخدم الحالي
- `PUT /api/auth/user/` - تحديث معلومات المستخدم الحالي

### العمال

- `GET /api/workers/` - قائمة العمال
- `POST /api/workers/` - إنشاء عامل جديد
- `GET /api/workers/{id}/` - تفاصيل عامل محدد
- `PUT /api/workers/{id}/` - تحديث عامل محدد
- `DELETE /api/workers/{id}/` - حذف عامل محدد
- `GET /api/workers/{id}/documents/` - مستندات عامل محدد
- `GET /api/workers/{id}/procedures/` - إجراءات عامل محدد

### العقود

- `GET /api/contracts/` - قائمة العقود
- `POST /api/contracts/` - إنشاء عقد جديد
- `GET /api/contracts/{id}/` - تفاصيل عقد محدد
- `PUT /api/contracts/{id}/` - تحديث عقد محدد
- `DELETE /api/contracts/{id}/` - حذف عقد محدد
- `GET /api/contracts/{id}/workers/` - العمال المرتبطين بعقد محدد
- `GET /api/contracts/{id}/services/` - الخدمات المرتبطة بعقد محدد

### الخدمات

- `GET /api/services/` - قائمة الخدمات
- `POST /api/services/` - إنشاء خدمة جديدة
- `GET /api/services/{id}/` - تفاصيل خدمة محددة
- `PUT /api/services/{id}/` - تحديث خدمة محددة
- `DELETE /api/services/{id}/` - حذف خدمة محددة
- `GET /api/services/{id}/workers/` - العمال المرتبطين بخدمة محددة

### المستندات

- `GET /api/documents/` - قائمة المستندات
- `POST /api/documents/` - إنشاء مستند جديد
- `GET /api/documents/{id}/` - تفاصيل مستند محدد
- `PUT /api/documents/{id}/` - تحديث مستند محدد
- `DELETE /api/documents/{id}/` - حذف مستند محدد
- `GET /api/documents/{id}/download/` - تنزيل ملف مستند محدد

### التقارير

- `GET /api/reports/` - قائمة التقارير المتاحة
- `GET /api/reports/workers/` - تقرير العمال
- `GET /api/reports/contracts/` - تقرير العقود
- `GET /api/reports/services/` - تقرير الخدمات
- `GET /api/reports/financial/` - التقرير المالي
- `POST /api/reports/custom/` - إنشاء تقرير مخصص

## أمثلة على الاستخدام

### تسجيل الدخول والحصول على رمز JWT

```bash
curl -X POST "http://example.com/api/auth/login/" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "password", "serial_number": "XXXX-XXXX-XXXX-XXXX"}'
```

استجابة:

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### الحصول على قائمة العمال

```bash
curl -X GET "http://example.com/api/workers/" \
     -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

استجابة:

```json
{
  "count": 15,
  "next": "http://example.com/api/workers/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "full_name": "أحمد محمود",
      "nationality": "الهند",
      "passport_number": "P7890212",
      "passport_expiry_date": "2025-02-01",
      "residence_number": "2025-02-01",
      "residence_expiry_date": "2025-02-01",
      "status": "active",
      "job_title": "عامل نظافة",
      "hire_date": "2023-01-15"
    },
    // المزيد من العمال...
  ]
}
```

## التوثيق التفاعلي

يمكن الوصول إلى التوثيق التفاعلي الكامل للـ API من خلال:

- Swagger UI: `http://example.com/api/docs/`
- ReDoc: `http://example.com/api/redoc/`

## الأمان

- جميع طلبات API تتطلب مصادقة JWT باستثناء نقاط نهاية المصادقة
- يتم تشفير جميع الاتصالات باستخدام HTTPS
- يتم تطبيق التحكم في الوصول على مستوى الكائن (RBAC)
- يتم تسجيل جميع طلبات API للتدقيق والأمان

## معالجة الأخطاء

تستخدم API رموز حالة HTTP القياسية:

- `200 OK` - نجاح الطلب
- `201 Created` - تم إنشاء الكائن بنجاح
- `400 Bad Request` - طلب غير صالح
- `401 Unauthorized` - مصادقة مطلوبة
- `403 Forbidden` - ليس لديك صلاحية للوصول
- `404 Not Found` - الكائن غير موجود
- `500 Internal Server Error` - خطأ في الخادم

## التصفية والترتيب والتقسيم

يمكن تصفية وترتيب وتقسيم جميع قوائم الكائنات:

- تصفية: `?filter_field=value`
- ترتيب: `?ordering=field` أو `?ordering=-field` للترتيب التنازلي
- تقسيم: `?page=2&page_size=10`

## الإشعارات في الوقت الحقيقي

يمكن للعملاء الاشتراك في الإشعارات في الوقت الحقيقي باستخدام WebSockets:

- `ws://example.com/api/notifications/`

## المزيد من المعلومات

للحصول على مزيد من المعلومات حول استخدام API، يرجى الاتصال بفريق الدعم الفني.
