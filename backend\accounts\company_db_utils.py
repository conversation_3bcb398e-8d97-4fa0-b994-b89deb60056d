"""
Utility functions for company database operations.
This module provides helper functions to ensure all database operations
are performed on the correct company database in a multi-tenant environment.
"""

from django.shortcuts import get_object_or_404
from django.contrib import messages
from django.shortcuts import redirect
from .router import (
    get_company_database, get_company_manager, filter_company_db,
    count_company_objects, all_company_objects, create_in_company_db,
    save_to_company_db, get_company_object
)


def get_company_object_or_404(model_class, request=None, redirect_url=None, error_message=None, **kwargs):
    """
    Get object from company database or return 404/redirect.
    
    Args:
        model_class: The Django model class
        request: Django request object (for messages)
        redirect_url: URL to redirect to if object not found
        error_message: Custom error message
        **kwargs: Filter parameters
    
    Returns:
        Model instance or redirect response
    """
    try:
        return get_company_object(model_class, **kwargs)
    except model_class.DoesNotExist:
        if request and redirect_url:
            if error_message:
                messages.error(request, error_message)
            else:
                messages.error(request, f'{model_class._meta.verbose_name} غير موجود')
            return redirect(redirect_url)
        else:
            # Fallback to standard 404
            return get_object_or_404(get_company_manager(model_class), **kwargs)


def create_company_object(model_class, **kwargs):
    """
    Create object in company database.
    
    Args:
        model_class: The Django model class
        **kwargs: Object data
    
    Returns:
        Created model instance
    """
    return create_in_company_db(model_class, **kwargs)


def save_company_object(instance):
    """
    Save object to company database.
    
    Args:
        instance: Model instance to save
    
    Returns:
        Saved model instance
    """
    return save_to_company_db(instance)


def get_company_queryset(model_class, **filters):
    """
    Get queryset from company database with optional filters.
    
    Args:
        model_class: The Django model class
        **filters: Filter parameters
    
    Returns:
        QuerySet from company database
    """
    if filters:
        return filter_company_db(model_class, **filters)
    else:
        return all_company_objects(model_class)


def count_company_records(model_class, **filters):
    """
    Count records in company database.
    
    Args:
        model_class: The Django model class
        **filters: Filter parameters
    
    Returns:
        Integer count
    """
    return count_company_objects(model_class, **filters)


def get_company_manager_for_model(model_class):
    """
    Get manager for company database operations.
    
    Args:
        model_class: The Django model class
    
    Returns:
        Model manager for company database
    """
    return get_company_manager(model_class)


class CompanyDatabaseMixin:
    """
    Mixin for views that need company database operations.
    """
    
    def get_company_queryset(self, model_class, **filters):
        """Get queryset from company database."""
        return get_company_queryset(model_class, **filters)
    
    def get_company_object_or_404(self, model_class, **kwargs):
        """Get object from company database or 404."""
        return get_company_object_or_404(
            model_class, 
            request=getattr(self, 'request', None),
            **kwargs
        )
    
    def create_company_object(self, model_class, **kwargs):
        """Create object in company database."""
        return create_company_object(model_class, **kwargs)
    
    def save_company_object(self, instance):
        """Save object to company database."""
        return save_company_object(instance)
    
    def count_company_records(self, model_class, **filters):
        """Count records in company database."""
        return count_company_records(model_class, **filters)


def ensure_company_database_operations():
    """
    Decorator to ensure all database operations use company database.
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            # Set up company database context
            company_db = get_company_database()
            if not company_db:
                messages.error(request, 'لم يتم العثور على قاعدة بيانات الشركة')
                return redirect('company:login')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


# Convenience functions for common operations
def get_workers_for_company(**filters):
    """Get workers from company database."""
    from backend.workers.models import Worker
    return get_company_queryset(Worker, **filters)


def get_clients_for_company(**filters):
    """Get clients from company database."""
    from clients.models import Client
    return get_company_queryset(Client, **filters)


def get_services_for_company(**filters):
    """Get services from company database."""
    from services.models import Service
    return get_company_queryset(Service, **filters)


def get_documents_for_company(**filters):
    """Get documents from company database."""
    from backend.documents.models import Document
    return get_company_queryset(Document, **filters)


def get_procedures_for_company(**filters):
    """Get procedures from company database."""
    from backend.procedures.models import BloodTest
    return get_company_queryset(BloodTest, **filters)


# Statistics functions
def get_company_statistics():
    """
    Get comprehensive statistics for the current company.
    
    Returns:
        Dictionary with various statistics
    """
    from backend.workers.models import Worker
    from clients.models import Client
    from services.models import Service
    from backend.documents.models import Document
    
    stats = {
        'workers': {
            'total': count_company_records(Worker),
            'active': count_company_records(Worker, status='active'),
            'contract': count_company_records(Worker, worker_type='contract'),
            'monthly': count_company_records(Worker, worker_type='monthly'),
            'custom': count_company_records(Worker, worker_type='custom'),
        },
        'clients': {
            'total': count_company_records(Client),
            'active': count_company_records(Client, status='active'),
            'permanent': count_company_records(Client, client_type='permanent'),
            'monthly': count_company_records(Client, client_type='monthly'),
            'custom': count_company_records(Client, client_type='custom'),
        },
        'services': {
            'total': count_company_records(Service),
            'confirmed': count_company_records(Service, status='confirmed'),
            'pending': count_company_records(Service, status='pending'),
            'fixed': count_company_records(Service, is_fixed=True),
        },
        'documents': {
            'total': count_company_records(Document),
            'pending': count_company_records(Document, status='pending'),
            'completed': count_company_records(Document, status='completed'),
        }
    }
    
    return stats
