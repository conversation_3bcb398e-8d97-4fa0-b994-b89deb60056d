{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>{{ title }}</h5>
                    <div>
                        <a href="{% url 'workers:worker_documents' worker.id %}" class="btn btn-info btn-sm me-2">
                            <i class="fas fa-file-alt me-1"></i> المستندات
                        </a>
                        <a href="{% url 'workers:worker_update' worker.id %}" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-edit me-1"></i> تعديل
                        </a>
                        <a href="{% url 'workers:worker_delete' worker.id %}" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash me-1"></i> حذف
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-id-card me-2"></i>المعلومات الشخصية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-4">
                                        {% if worker.photo %}
                                            <img src="{{ worker.photo.url }}" alt="{{ worker }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                        {% else %}
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px;">
                                                <i class="fas fa-user fa-4x text-secondary"></i>
                                            </div>
                                        {% endif %}
                                        <h5>{{ worker.first_name }} {{ worker.last_name }}</h5>
                                        <p class="text-muted">
                                            {% if worker.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}

                                            {% if worker.status == 'active' %}
                                                <span class="badge bg-success">نشط</span>
                                            {% elif worker.status == 'inactive' %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% elif worker.status == 'on_leave' %}
                                                <span class="badge bg-warning">في إجازة</span>
                                            {% elif worker.status == 'terminated' %}
                                                <span class="badge bg-dark">منتهي الخدمة</span>
                                            {% endif %}
                                        </p>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tbody>
                                                <tr>
                                                    <th style="width: 40%;">الجنس</th>
                                                    <td>{{ worker.get_gender_display }}</td>
                                                </tr>
                                                <tr>
                                                    <th>الجنسية</th>
                                                    <td>{{ worker.get_nationality_display }}</td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ الميلاد</th>
                                                    <td>{{ worker.date_of_birth|date:"Y-m-d" }}</td>
                                                </tr>
                                                <tr>
                                                    <th>رقم الهاتف</th>
                                                    <td>{{ worker.phone_number|default:"-" }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-passport me-2"></i>معلومات جواز السفر والتأشيرة</h6>
                                </div>
                                <div class="card-body">
                                    <!-- صور الجواز والتأشيرة -->
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            {% if worker.passport_image %}
                                                <a href="{{ worker.passport_image.url }}" target="_blank" class="d-block text-center">
                                                    <img src="{{ worker.passport_image.url }}" alt="صورة جواز السفر" class="img-fluid rounded mb-2" style="max-height: 100px; object-fit: cover;">
                                                    <small class="d-block text-muted">صورة جواز السفر</small>
                                                </a>
                                            {% else %}
                                                <div class="text-center">
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mx-auto mb-2" style="height: 100px;">
                                                        <i class="fas fa-passport fa-2x text-secondary"></i>
                                                    </div>
                                                    <small class="d-block text-muted">لا توجد صورة للجواز</small>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-6">
                                            {% if worker.visa_image %}
                                                <a href="{{ worker.visa_image.url }}" target="_blank" class="d-block text-center">
                                                    <img src="{{ worker.visa_image.url }}" alt="صورة التأشيرة" class="img-fluid rounded mb-2" style="max-height: 100px; object-fit: cover;">
                                                    <small class="d-block text-muted">صورة التأشيرة</small>
                                                </a>
                                            {% else %}
                                                <div class="text-center">
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mx-auto mb-2" style="height: 100px;">
                                                        <i class="fas fa-id-card fa-2x text-secondary"></i>
                                                    </div>
                                                    <small class="d-block text-muted">لا توجد صورة للتأشيرة</small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tbody>
                                                <tr>
                                                    <th style="width: 40%;">رقم جواز السفر</th>
                                                    <td>{{ worker.passport_number }}</td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ الدخول</th>
                                                    <td>{{ worker.entry_date|date:"Y-m-d" }}</td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ انتهاء جواز السفر</th>
                                                    <td>
                                                        {{ worker.passport_expiry|date:"Y-m-d" }}
                                                        {% if worker.passport_expiry and worker.passport_expiry < today %}
                                                            <span class="badge bg-danger">منتهي</span>
                                                        {% elif worker.passport_expiry and worker.passport_expiry < expiry_warning %}
                                                            <span class="badge bg-warning">قريب من الانتهاء</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>رقم التأشيرة</th>
                                                    <td>{{ worker.visa_number }}</td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ انتهاء التأشيرة</th>
                                                    <td>
                                                        {{ worker.visa_expiry|date:"Y-m-d" }}
                                                        {% if worker.visa_expiry and worker.visa_expiry < today %}
                                                            <span class="badge bg-danger">منتهية</span>
                                                        {% elif worker.visa_expiry and worker.visa_expiry < expiry_warning %}
                                                            <span class="badge bg-warning">قريبة من الانتهاء</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tbody>
                                                <tr>
                                                    <th style="width: 40%;">تاريخ الانضمام</th>
                                                    <td>{{ worker.date_joined|date:"Y-m-d" }}</td>
                                                </tr>
                                                <tr>
                                                    <th>جهة طلب سمة الدخول</th>
                                                    <td>
                                                        {% if worker.recruitment_company %}
                                                            {{ worker.recruitment_company.name }} ({{ worker.recruitment_company.country }})
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>الراتب</th>
                                                    <td>{{ worker.salary }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    {% if worker.notes %}
                                        <p>{{ worker.notes|linebreaks }}</p>
                                    {% else %}
                                        <p class="text-muted">لا توجد ملاحظات.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'workers:worker_list' %}" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة العمال
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
