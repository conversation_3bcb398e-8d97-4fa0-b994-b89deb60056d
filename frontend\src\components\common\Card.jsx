import React from 'react';
import PropTypes from 'prop-types';
import { motion } from 'framer-motion';

/**
 * مكون بطاقة متقدم يدعم أنماط مختلفة وألوان متعددة
 */
const Card = ({
  children,
  title = null,
  subtitle = null,
  variant = 'default',
  color = null,
  icon = null,
  actions = null,
  footer = null,
  className = '',
  animate = false,
  onClick = null,
  ...rest
}) => {
  // تحديد الأنماط الأساسية للبطاقة
  const baseClasses = 'rounded-lg overflow-hidden transition-shadow duration-200';
  
  // تحديد أنماط اللون والمظهر
  const variantClasses = {
    default: 'bg-white shadow-card hover:shadow-card-hover',
    flat: 'bg-white border border-neutral-light',
    raised: 'bg-white shadow-lg',
    outlined: 'bg-white border border-neutral',
  };
  
  // تحديد أنماط الألوان
  const colorClasses = {
    primary: 'border-primary-light',
    secondary: 'border-secondary-light',
    success: 'border-success-light',
    danger: 'border-danger-light',
    warning: 'border-warning-light',
    info: 'border-info-light',
    'blue-345785': 'border-blue-345785',
    'blue-4b93da': 'border-blue-4b93da',
    'gray-294252': 'border-gray-294252',
    'gray-3773b5': 'border-gray-3773b5',
    'accent-42759b': 'border-accent-42759b',
    'accent-d34169': 'border-accent-d34169',
  };
  
  // تحديد أنماط رأس البطاقة
  const headerColorClasses = {
    primary: 'bg-primary-light text-primary-dark',
    secondary: 'bg-secondary-light text-secondary-dark',
    success: 'bg-success-light text-success-dark',
    danger: 'bg-danger-light text-white',
    warning: 'bg-warning-light text-warning-dark',
    info: 'bg-info-light text-info-dark',
    'blue-345785': 'bg-blue-345785 text-white',
    'blue-4b93da': 'bg-blue-4b93da text-white',
    'gray-294252': 'bg-gray-294252 text-white',
    'gray-3773b5': 'bg-gray-3773b5 text-white',
    'accent-42759b': 'bg-accent-42759b text-white',
    'accent-d34169': 'bg-accent-d34169 text-white',
  };
  
  // تجميع الأنماط
  const cardClasses = `${baseClasses} ${variantClasses[variant]} ${color ? colorClasses[color] : ''} ${className}`;
  const headerClasses = `px-6 py-4 ${color ? headerColorClasses[color] : 'bg-primary-lighter'}`;
  const bodyClasses = 'p-6';
  const footerClasses = 'px-6 py-4 border-t border-neutral-light bg-primary-lighter';
  
  // إنشاء محتوى البطاقة
  const cardContent = (
    <>
      {(title || subtitle || icon) && (
        <div className={headerClasses}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {icon && <div className="ml-3">{icon}</div>}
              <div>
                {title && <h3 className="font-bold">{title}</h3>}
                {subtitle && <p className="text-sm opacity-80">{subtitle}</p>}
              </div>
            </div>
            {actions && <div>{actions}</div>}
          </div>
        </div>
      )}
      
      <div className={bodyClasses}>
        {children}
      </div>
      
      {footer && (
        <div className={footerClasses}>
          {footer}
        </div>
      )}
    </>
  );
  
  // إذا كانت البطاقة متحركة
  if (animate) {
    return (
      <motion.div
        className={cardClasses}
        whileHover={{ y: -5, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.3 }}
        onClick={onClick}
        {...rest}
      >
        {cardContent}
      </motion.div>
    );
  }
  
  // بطاقة عادية
  return (
    <div
      className={cardClasses}
      onClick={onClick}
      {...rest}
    >
      {cardContent}
    </div>
  );
};

Card.propTypes = {
  children: PropTypes.node.isRequired,
  title: PropTypes.node,
  subtitle: PropTypes.node,
  variant: PropTypes.oneOf(['default', 'flat', 'raised', 'outlined']),
  color: PropTypes.string,
  icon: PropTypes.node,
  actions: PropTypes.node,
  footer: PropTypes.node,
  className: PropTypes.string,
  animate: PropTypes.bool,
  onClick: PropTypes.func,
};

export default Card;
