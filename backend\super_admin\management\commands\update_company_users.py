"""
أمر Django لتحديث المستخدمين في قواعد بيانات الشركات
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from backend.super_admin.models import Company
from backend.super_admin.database_connector import setup_company_db_connection, get_connection_name
from django.db import connections
from django.contrib.auth.hashers import make_password
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'تحديث المستخدمين في قواعد بيانات الشركات'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف الشركة (اختياري - إذا لم يتم تحديده سيتم تحديث جميع الشركات)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='إعادة إنشاء المستخدمين حتى لو كانوا موجودين'
        )

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        force = options.get('force', False)

        if company_id:
            try:
                company = Company.objects.get(id=company_id)
                companies = [company]
            except Company.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'الشركة بالمعرف {company_id} غير موجودة')
                )
                return
        else:
            companies = Company.objects.filter(status='active')

        self.stdout.write(f'سيتم تحديث المستخدمين في {len(companies)} شركة')

        for company in companies:
            self.stdout.write(f'\n--- تحديث المستخدمين للشركة: {company.name} ---')

            try:
                # إعداد الاتصال بقاعدة بيانات الشركة
                connection_success = setup_company_db_connection(company)
                if not connection_success:
                    self.stdout.write(
                        self.style.ERROR(f'فشل في الاتصال بقاعدة بيانات الشركة: {company.name}')
                    )
                    continue

                # الحصول على اتصال قاعدة البيانات
                connection_name = get_connection_name(company)
                company_connection = connections[connection_name]

                # إنشاء/تحديث المستخدمين
                self.create_company_users(company, company_connection, force)

                self.stdout.write(
                    self.style.SUCCESS(f'تم تحديث المستخدمين للشركة: {company.name}')
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'خطأ في تحديث المستخدمين للشركة {company.name}: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS('\nتم الانتهاء من تحديث المستخدمين')
        )

    def create_company_users(self, company, connection, force=False):
        """إنشاء/تحديث المستخدمين في قاعدة بيانات الشركة"""

        # قائمة المستخدمين الحقيقيين فقط (البيانات التي أنشأها المستخدم)
        users_to_create = [
            {
                'username': "MUNTADER_WISSAM",
                'password': "MUNTADDEERR13163@@13163",
                'first_name': "منتظر",
                'last_name': "وسام",
                'email': "<EMAIL>",
                'is_staff': True,
                'is_superuser': True,
                'is_active': True
            }
            # لا يتم إنشاء أي مستخدمين اختباريين أو إضافيين
            # فقط البيانات الحقيقية التي ينشئها المستخدم
        ]

        with connection.cursor() as cursor:
            for user_data in users_to_create:
                try:
                    # التحقق من وجود المستخدم
                    cursor.execute("SELECT id FROM auth_user WHERE username = %s", [user_data['username']])
                    user_exists = cursor.fetchone()

                    hashed_pwd = make_password(user_data['password'])

                    if not user_exists:
                        # إنشاء المستخدم الجديد
                        cursor.execute("""
                            INSERT INTO auth_user (
                                username, password, first_name, last_name, email,
                                is_staff, is_active, is_superuser, date_joined, last_login
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, [
                            user_data['username'],
                            hashed_pwd,
                            user_data['first_name'],
                            user_data['last_name'],
                            user_data['email'],
                            1 if user_data['is_staff'] else 0,
                            1 if user_data['is_active'] else 0,
                            1 if user_data['is_superuser'] else 0,
                            timezone.now().isoformat(),
                            timezone.now().isoformat()
                        ])

                        self.stdout.write(
                            self.style.SUCCESS(f'✅ تم إنشاء المستخدم: {user_data["username"]} | كلمة المرور: {user_data["password"]}')
                        )
                    elif force:
                        # تحديث كلمة المرور والبيانات للمستخدم الموجود
                        cursor.execute("""
                            UPDATE auth_user
                            SET password = %s, first_name = %s, last_name = %s, email = %s,
                                is_staff = %s, is_active = %s, is_superuser = %s
                            WHERE username = %s
                        """, [
                            hashed_pwd,
                            user_data['first_name'],
                            user_data['last_name'],
                            user_data['email'],
                            1 if user_data['is_staff'] else 0,
                            1 if user_data['is_active'] else 0,
                            1 if user_data['is_superuser'] else 0,
                            user_data['username']
                        ])

                        self.stdout.write(
                            self.style.WARNING(f'🔄 تم تحديث المستخدم: {user_data["username"]} | كلمة المرور: {user_data["password"]}')
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'⚠️ المستخدم موجود بالفعل: {user_data["username"]} (استخدم --force للتحديث)')
                        )

                except Exception as user_error:
                    self.stdout.write(
                        self.style.ERROR(f'❌ خطأ في إنشاء/تحديث المستخدم {user_data["username"]}: {str(user_error)}')
                    )

        # عرض ملخص المستخدمين
        self.show_users_summary(connection)

    def show_users_summary(self, connection):
        """عرض ملخص المستخدمين في قاعدة البيانات"""
        with connection.cursor() as cursor:
            cursor.execute("SELECT username, email, is_staff, is_superuser, is_active FROM auth_user ORDER BY id")
            users = cursor.fetchall()

            self.stdout.write('\n📋 ملخص المستخدمين:')
            self.stdout.write('-' * 80)
            for user in users:
                username, email, is_staff, is_superuser, is_active = user
                status = "نشط" if is_active else "غير نشط"
                role = "مسؤول أعلى" if is_superuser else ("موظف" if is_staff else "مستخدم عادي")
                self.stdout.write(f'👤 {username} | {email} | {role} | {status}')
