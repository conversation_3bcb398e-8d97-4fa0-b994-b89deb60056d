<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار طريقة المصادقة - المسؤول الأعلى</title>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- SweetAlert2 -->
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .security-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
        }
        
        .method-card {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(239, 68, 68, 0.2);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease;
        }
        
        .method-card:hover {
            border-color: #ef4444;
            transform: translateY(-5px);
            box-shadow: 0 35px 60px -12px rgba(239, 68, 68, 0.3);
        }
        
        .method-card.selected {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        
        .continue-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transition: all 0.3s ease;
        }
        
        .continue-btn:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(220, 38, 38, 0.4);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="security-pattern">

    <!-- Security Warning Banner -->
    <div class="bg-red-600 text-white text-center py-3 text-sm font-bold">
        <i class="fas fa-shield-alt ml-2"></i>
        الخطوة الثانية: اختيار طريقة المصادقة الثنائية
        <i class="fas fa-key ml-2"></i>
    </div>

    <div class="min-h-screen flex items-center justify-center p-6">
        <div class="method-card rounded-2xl p-8 w-full max-w-2xl">
            
            <!-- Header -->
            <div class="text-center mb-8">
                {% load static %}
                <div class="relative mb-6">
                    <div class="pulse-animation absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-shield-alt text-6xl text-red-400 opacity-20"></i>
                    </div>
                    <img src="{% static 'images/logo.webp' %}" alt="استقدامي" class="h-16 w-auto mx-auto relative z-10">
                </div>
                
                <h1 class="text-2xl font-bold text-white mb-2">
                    <i class="fas fa-key text-red-400 ml-2"></i>
                    اختيار طريقة المصادقة الثنائية
                </h1>
                <p class="text-gray-300 text-sm">اختر الطريقة المفضلة لديك للتحقق من هويتك</p>
            </div>

            <!-- Method Selection Form -->
            <form method="post" id="methodForm">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    
                    <!-- Email Method -->
                    <div class="method-option">
                        <input type="radio" name="auth_method" value="email" id="email_method" class="hidden" checked>
                        <label for="email_method" class="method-card block p-6 rounded-xl cursor-pointer">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-envelope text-white text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-bold text-white mb-2">البريد الإلكتروني</h3>
                                <p class="text-gray-400 text-sm mb-4">
                                    سيتم إرسال رمز التحقق إلى بريدك الإلكتروني
                                </p>
                                <div class="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-3">
                                    <p class="text-blue-200 text-xs">
                                        <i class="fas fa-info-circle ml-2"></i>
                                        <EMAIL>
                                    </p>
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Authenticator App Method -->
                    <div class="method-option">
                        <input type="radio" name="auth_method" value="app" id="app_method" class="hidden">
                        <label for="app_method" class="method-card block p-6 rounded-xl cursor-pointer">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-mobile-alt text-white text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-bold text-white mb-2">تطبيق المصادقة</h3>
                                <p class="text-gray-400 text-sm mb-4">
                                    استخدم Google Authenticator أو تطبيق مشابه
                                </p>
                                <div class="bg-green-900 bg-opacity-30 border border-green-600 rounded-lg p-3">
                                    <p class="text-green-200 text-xs">
                                        <i class="fas fa-mobile-alt ml-2"></i>
                                        رمز من 6 أرقام
                                    </p>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Continue Button -->
                <button type="submit" 
                        class="continue-btn w-full py-3 px-6 rounded-lg text-white font-bold text-lg">
                    <i class="fas fa-arrow-left ml-2"></i>
                    متابعة
                </button>

                <!-- Messages -->
                {% if messages %}
                    <div class="mt-6">
                        {% for message in messages %}
                            <div class="bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-900 bg-opacity-50 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-600 text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-200 px-4 py-3 rounded-lg text-sm">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} ml-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </form>

            <!-- Back to Login -->
            <div class="mt-8 text-center">
                <a href="{% url 'super_admin:super_admin_login' %}" 
                   class="text-gray-400 hover:text-white text-sm transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة لتسجيل الدخول
                </a>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-gray-400 text-xs">
                    <i class="fas fa-shield-alt ml-1"></i>
                    محمي بنظام أمان متعدد الطبقات
                </p>
                <p class="text-gray-500 text-xs mt-2">
                    © {{ "now"|date:"Y" }} منصة استقدامي السحابية
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Handle method selection
        document.addEventListener('DOMContentLoaded', function() {
            const methodOptions = document.querySelectorAll('.method-option');
            const radioButtons = document.querySelectorAll('input[name="auth_method"]');
            
            // Add click handlers for method cards
            methodOptions.forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                const card = option.querySelector('.method-card');
                
                option.addEventListener('click', function() {
                    // Remove selected class from all cards
                    document.querySelectorAll('.method-card').forEach(c => {
                        c.classList.remove('selected');
                    });
                    
                    // Add selected class to clicked card
                    card.classList.add('selected');
                    
                    // Check the radio button
                    radio.checked = true;
                });
            });
            
            // Set initial selection
            const checkedRadio = document.querySelector('input[name="auth_method"]:checked');
            if (checkedRadio) {
                const card = checkedRadio.closest('.method-option').querySelector('.method-card');
                card.classList.add('selected');
            }
        });

        // Security monitoring
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent right-click
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير أمني',
                    text: 'العمليات محمية في هذه المنطقة',
                    confirmButtonColor: '#dc2626'
                });
            });

            // Prevent F12
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'وصول مرفوض',
                        text: 'أدوات المطور محظورة في هذه المنطقة',
                        confirmButtonColor: '#dc2626'
                    });
                }
            });
        });

        // Form validation
        document.getElementById('methodForm').addEventListener('submit', function(e) {
            const selectedMethod = document.querySelector('input[name="auth_method"]:checked');

            if (!selectedMethod) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'يرجى الاختيار',
                    text: 'يرجى اختيار طريقة المصادقة المفضلة',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري المعالجة...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });
        });
    </script>
</body>
</html>
