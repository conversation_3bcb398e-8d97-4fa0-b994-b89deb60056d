from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.db import models
from backend.workers.models import Worker
from backend.contracts.models import Contract
from backend.services.models import BookingSchedule, ServiceType, Booking
from .models import FinancialReport, WorkerReport, ContractReport, ServiceReport

# Create your views here.

def report_list(request):
    """View function for the main reports page."""
    return render(request, 'reports/report_list.html')

@login_required
def financial_report(request):
    """View function for financial reports."""
    # Obtener datos de contratos para cálculos financieros
    contracts = Contract.objects.all()
    active_contracts = contracts.filter(status='active')

    # Calcular ingresos totales (valor de contratos activos)
    total_income = active_contracts.aggregate(models.Sum('total_value'))['total_value__sum'] or 0

    # Calcular gastos (salarios de trabajadores)
    workers = Worker.objects.filter(is_active=True)
    total_salaries = workers.aggregate(models.Sum('salary'))['salary__sum'] or 0

    # Calcular gastos de servicios
    schedules = BookingSchedule.objects.filter(status__in=['scheduled', 'in_progress'])
    service_costs = schedules.count() * 500  # Costo estimado por servicio

    # Calcular gastos totales
    total_expenses = total_salaries + service_costs

    # Calcular beneficio neto
    net_profit = total_income - total_expenses

    # Calcular ingresos por mes (para gráfico)
    today = timezone.now().date()
    six_months_ago = today - timezone.timedelta(days=180)

    # Crear diccionario para almacenar ingresos mensuales
    monthly_income = {}

    # Obtener contratos de los últimos 6 meses
    recent_contracts = contracts.filter(start_date__gte=six_months_ago)

    # Calcular ingresos por mes
    for contract in recent_contracts:
        month_key = contract.start_date.strftime('%Y-%m')
        if month_key in monthly_income:
            monthly_income[month_key] += contract.total_value
        else:
            monthly_income[month_key] = contract.total_value

    # Ordenar por mes
    monthly_income = dict(sorted(monthly_income.items()))

    # Formatear para mostrar en la plantilla
    months = []
    income_values = []

    for month, income in monthly_income.items():
        year, month_num = month.split('-')
        month_name = {
            '01': 'يناير',
            '02': 'فبراير',
            '03': 'مارس',
            '04': 'أبريل',
            '05': 'مايو',
            '06': 'يونيو',
            '07': 'يوليو',
            '08': 'أغسطس',
            '09': 'سبتمبر',
            '10': 'أكتوبر',
            '11': 'نوفمبر',
            '12': 'ديسمبر'
        }.get(month_num, month_num)

        months.append(f"{month_name} {year}")
        income_values.append(income)

    # Obtener informes financieros guardados
    financial_reports = FinancialReport.objects.all().order_by('-date_created')[:5]

    context = {
        'total_income': total_income,
        'total_expenses': total_expenses,
        'total_salaries': total_salaries,
        'service_costs': service_costs,
        'net_profit': net_profit,
        'active_contracts_count': active_contracts.count(),
        'active_workers_count': workers.count(),
        'months': months,
        'income_values': income_values,
        'financial_reports': financial_reports
    }

    return render(request, 'reports/financial_report.html', context)

@login_required
def worker_report(request):
    """View function for worker reports."""
    # Obtener todos los trabajadores
    workers = Worker.objects.all().order_by('first_name', 'last_name')

    # Calcular estadísticas
    total_workers = workers.count()
    active_workers = workers.filter(is_active=True).count()
    inactive_workers = total_workers - active_workers

    # Contar por nacionalidad
    nationality_counts = {}
    for nationality_code, nationality_name in Worker.NATIONALITY_CHOICES:
        count = workers.filter(nationality=nationality_code).count()
        if count > 0:
            nationality_counts[nationality_name] = count

    # Contar por género
    gender_counts = {
        'ذكر': workers.filter(gender='M').count(),
        'أنثى': workers.filter(gender='F').count()
    }

    # Calcular trabajadores con documentos próximos a expirar (en los próximos 30 días)
    today = timezone.now().date()
    thirty_days_later = today + timezone.timedelta(days=30)
    expiring_passports = workers.filter(passport_expiry__range=[today, thirty_days_later]).count()
    expiring_visas = workers.filter(visa_expiry__range=[today, thirty_days_later]).count()

    # Calcular salario promedio
    avg_salary = workers.aggregate(models.Avg('salary'))['salary__avg'] or 0

    # Obtener informes de trabajadores guardados
    worker_reports = WorkerReport.objects.all().order_by('-date_created')[:5]

    context = {
        'workers': workers,
        'total_workers': total_workers,
        'active_workers': active_workers,
        'inactive_workers': inactive_workers,
        'nationality_counts': nationality_counts,
        'gender_counts': gender_counts,
        'expiring_passports': expiring_passports,
        'expiring_visas': expiring_visas,
        'avg_salary': avg_salary,
        'worker_reports': worker_reports
    }

    return render(request, 'reports/worker_report.html', context)

@login_required
def contract_report(request):
    """View function for contract reports."""
    # Obtener todos los contratos
    contracts = Contract.objects.all().order_by('-start_date')

    # Calcular estadísticas
    total_contracts = contracts.count()
    active_contracts = contracts.filter(status='active').count()
    pending_contracts = contracts.filter(status='pending').count()
    completed_contracts = contracts.filter(status='completed').count()
    terminated_contracts = contracts.filter(status='terminated').count()

    # Calcular valor total de contratos activos
    total_value = contracts.filter(status='active').aggregate(models.Sum('total_value'))['total_value__sum'] or 0

    # Calcular contratos próximos a expirar (en los próximos 30 días)
    today = timezone.now().date()
    thirty_days_later = today + timezone.timedelta(days=30)
    expiring_contracts = contracts.filter(end_date__range=[today, thirty_days_later], status='active').count()

    # Contar contratos por cliente
    client_counts = {}
    for contract in contracts:
        client_name = contract.client.name
        if client_name in client_counts:
            client_counts[client_name] += 1
        else:
            client_counts[client_name] = 1

    # Ordenar por cantidad de contratos (descendente)
    client_counts = dict(sorted(client_counts.items(), key=lambda item: item[1], reverse=True)[:5])

    # Obtener informes de contratos guardados
    contract_reports = ContractReport.objects.all().order_by('-date_created')[:5]

    context = {
        'contracts': contracts,
        'total_contracts': total_contracts,
        'active_contracts': active_contracts,
        'pending_contracts': pending_contracts,
        'completed_contracts': completed_contracts,
        'terminated_contracts': terminated_contracts,
        'total_value': total_value,
        'expiring_contracts': expiring_contracts,
        'client_counts': client_counts,
        'contract_reports': contract_reports
    }

    return render(request, 'reports/contract_report.html', context)

@login_required
def service_report(request):
    """View function for service reports."""
    # Obtener todos los servicios
    schedules = BookingSchedule.objects.all().order_by('-date')

    # Calcular estadísticas
    total_services = schedules.count()
    scheduled_services = schedules.filter(status='scheduled').count()
    in_progress_services = schedules.filter(status='in_progress').count()
    completed_services = schedules.filter(status='completed').count()
    cancelled_services = schedules.filter(status='cancelled').count()

    # Calcular servicios programados para los próximos 7 días
    today = timezone.now().date()
    seven_days_later = today + timezone.timedelta(days=7)
    upcoming_services = schedules.filter(date__range=[today, seven_days_later], status='scheduled').count()

    # Contar servicios por tipo
    service_types = ServiceType.objects.all()
    service_type_counts = {}
    for service_type in service_types:
        count = Booking.objects.filter(service_type=service_type).count()
        if count > 0:
            service_type_counts[service_type.name] = count

    # Contar servicios por cliente
    client_counts = {}
    for schedule in schedules:
        client_name = schedule.booking.client.name
        if client_name in client_counts:
            client_counts[client_name] += 1
        else:
            client_counts[client_name] = 1

    # Ordenar por cantidad de servicios (descendente)
    client_counts = dict(sorted(client_counts.items(), key=lambda item: item[1], reverse=True)[:5])

    # Obtener informes de servicios guardados
    service_reports = ServiceReport.objects.all().order_by('-date_created')[:5]

    context = {
        'schedules': schedules,
        'total_services': total_services,
        'scheduled_services': scheduled_services,
        'in_progress_services': in_progress_services,
        'completed_services': completed_services,
        'cancelled_services': cancelled_services,
        'upcoming_services': upcoming_services,
        'service_type_counts': service_type_counts,
        'client_counts': client_counts,
        'service_reports': service_reports
    }

    return render(request, 'reports/service_report.html', context)

@login_required
def saved_report_list(request):
    """View function for saved reports."""
    return render(request, 'reports/saved_report_list.html')

@login_required
def create_report(request):
    """View function for creating a new report."""
    # Si el método es POST, procesar el formulario
    if request.method == 'POST':
        report_type = request.POST.get('report_type')

        # Redirigir a la página de reporte correspondiente
        if report_type == 'financial':
            return redirect('financial_report')
        elif report_type == 'workers':
            return redirect('worker_report')
        elif report_type == 'contracts':
            return redirect('contract_report')
        elif report_type == 'services':
            return redirect('service_report')

    # Si el método es GET, mostrar el formulario
    return render(request, 'reports/create_report.html')
