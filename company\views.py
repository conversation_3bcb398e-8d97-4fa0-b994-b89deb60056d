from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_protect, csrf_exempt
from django.db import connections
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import timedelta
from django.http import JsonResponse
import json
import logging
import os
from django.conf import settings

# استيراد نموذج Company من تطبيق backend.super_admin
from backend.super_admin.models import Company
from .models import CompanyUser, CompanyLoginAttempt

# إعداد السجل
logger = logging.getLogger(__name__)

@csrf_exempt
def check_serial(request):
    """وظيفة بسيطة للتحقق من الرقم التسلسلي"""
    try:
        # الحصول على الرقم التسلسلي من الطلب (GET أو POST)
        if request.method == 'GET':
            serial = request.GET.get('serial')
        else:
            try:
                data = json.loads(request.body)
                serial = data.get('serial')
            except:
                serial = request.POST.get('serial')

        # طباعة معلومات التصحيح
        print(f"التحقق من الرقم التسلسلي: {serial}")

        # التحقق من أن الرقم التسلسلي ليس فارغًا
        if not serial or serial.strip() == '':
            return JsonResponse({
                'success': False,
                'message': 'الرجاء إدخال الرقم التسلسلي'
            })

        # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        clean_serial = serial.replace('-', '').replace(' ', '')

        # التحقق من طول الرقم التسلسلي
        if len(clean_serial) != 16:
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
            })

        # التحقق من صحة الرقم التسلسلي (يجب أن يحتوي على أحرف وأرقام فقط)
        if not clean_serial.isalnum():
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي يجب أن يحتوي على أحرف وأرقام فقط'
            })

        # إعادة تنسيق الرقم التسلسلي (إضافة الشرطات)
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])

        # طباعة معلومات التصحيح
        print(f"البحث عن الشركة بالرقم التسلسلي: {clean_serial} أو {formatted_serial}")


        # البحث عن الشركة بناءً على الرقم التسلسلي
        try:
            company = Company.objects.filter(serial_number=clean_serial).first()
            if not company:
                company = Company.objects.filter(serial_number=formatted_serial).first()

            if company:
                # التحقق من حالة الشركة
                if company.status != 'active':
                    return JsonResponse({
                        'success': False,
                        'message': 'هذه الشركة غير نشطة حاليًا'
                    })

                print(f"تم العثور على الشركة: {company.name}, الحالة: {company.status}, قاعدة البيانات: {company.database_name}")
                return JsonResponse({
                    'success': True,
                    'name': company.name,
                    'serial': formatted_serial
                })
        except Exception as e:
            print(f"خطأ في البحث عن الشركة: {str(e)}")

        # لا نستخدم بيانات وهمية، نعتمد فقط على البيانات الحقيقية من قاعدة البيانات

        # إذا لم يتم العثور على الشركة
        return JsonResponse({
            'success': False,
            'message': 'لم يتم العثور على شركة بهذا الرقم التسلسلي'
        })
    except Exception as e:
        print(f"خطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء التحقق من الرقم التسلسلي'
        })

@csrf_exempt
def get_company_name(request):
    """وظيفة للتحقق من اسم الشركة بناءً على الرقم التسلسلي"""
    # طباعة معلومات الطلب للتصحيح
    print(f"طريقة الطلب: {request.method}")
    print(f"نوع المحتوى: {request.content_type}")

    if request.method == 'POST':
        try:
            # الحصول على الرقم التسلسلي من الطلب
            serial_number = None

            # محاولة الحصول على البيانات من JSON
            if 'application/json' in request.content_type:
                try:
                    data = json.loads(request.body)
                    serial_number = data.get('serial')
                    print(f"البيانات من JSON: {data}")
                except Exception as e:
                    print(f"خطأ في تحليل JSON: {str(e)}")

            # إذا لم نجد البيانات في JSON، نحاول الحصول عليها من POST
            if not serial_number:
                serial_number = request.POST.get('serial')
                print(f"البيانات من POST: {serial_number}")

            # إذا لم نجد البيانات في POST، نحاول الحصول عليها من GET
            if not serial_number:
                serial_number = request.GET.get('serial')
                print(f"البيانات من GET: {serial_number}")

            # إذا لم نجد البيانات في أي مكان، نرجع خطأ
            if not serial_number:
                print("لم يتم العثور على الرقم التسلسلي")
                return JsonResponse({
                    'success': False,
                    'message': 'الرجاء إدخال الرقم التسلسلي'
                })

            # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
            clean_serial = serial_number.replace('-', '').replace(' ', '')
            print(f"الرقم التسلسلي بعد التنظيف: {clean_serial}")

            # التحقق من طول الرقم التسلسلي
            if len(clean_serial) != 16:
                print(f"طول الرقم التسلسلي غير صحيح: {len(clean_serial)}")
                return JsonResponse({
                    'success': False,
                    'message': 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
                })

            # التحقق من صحة الرقم التسلسلي (يجب أن يحتوي على أحرف وأرقام فقط)
            if not clean_serial.isalnum():
                print("الرقم التسلسلي يحتوي على أحرف غير صالحة")
                return JsonResponse({
                    'success': False,
                    'message': 'الرقم التسلسلي يجب أن يحتوي على أحرف وأرقام فقط'
                })

            # إعادة تنسيق الرقم التسلسلي (إضافة الشرطات)
            formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])
            print(f"الرقم التسلسلي بعد التنسيق: {formatted_serial}")

            # البحث عن الشركة بناءً على الرقم التسلسلي
            try:
                # طباعة معلومات التصحيح
                print(f"البحث عن الشركة بالرقم التسلسلي: {clean_serial} أو {formatted_serial}")

                # البحث عن الشركة بالرقم التسلسلي المنسق أو غير المنسق
                company = Company.objects.filter(serial_number=clean_serial).first()
                if not company:
                    company = Company.objects.filter(serial_number=formatted_serial).first()

                # إذا تم العثور على الشركة، طباعة معلوماتها
                if company:
                    print(f"تم العثور على الشركة: {company.name}, الحالة: {company.status}, قاعدة البيانات: {company.database_name}")

                    # التحقق من حالة الشركة
                    if company.status != 'active':
                        return JsonResponse({
                            'success': False,
                            'message': 'هذه الشركة غير نشطة حاليًا'
                        })

                    return JsonResponse({
                        'success': True,
                        'name': company.name,
                        'serial': formatted_serial
                    })
            except Exception as e:
                print(f"خطأ في البحث عن الشركة: {str(e)}")

            # لا نستخدم بيانات وهمية، نعتمد فقط على البيانات الحقيقية من قاعدة البيانات

            # إذا لم يتم العثور على الشركة
            print("لم يتم العثور على الشركة")
            return JsonResponse({
                'success': False,
                'message': 'لم يتم العثور على شركة بهذا الرقم التسلسلي'
            })
        except Exception as e:
            print(f"حدث خطأ: {str(e)}")
            import traceback
            traceback.print_exc()
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء التحقق من الرقم التسلسلي: {str(e)}'
            })

    # إذا كانت الطريقة GET، نقوم بإرجاع صفحة بسيطة للاختبار
    return JsonResponse({
        'success': False,
        'message': 'استخدم طريقة POST للتحقق من الرقم التسلسلي'
    })

@csrf_protect
def company_login(request):
    """صفحة تسجيل دخول الشركات"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        serial_number = request.POST.get('serial_number')
        db_verified = request.POST.get('db_verified', 'false')
        ip_address = request.META.get('REMOTE_ADDR')

        # التحقق من أن قاعدة البيانات تم التحقق منها
        if db_verified != 'true':
            # استدعاء API للتحقق من قاعدة البيانات
            try:
                import requests
                import json

                # إرسال طلب للتحقق من قاعدة البيانات
                api_url = request.build_absolute_uri('/api/database/verify')
                response = requests.post(api_url, json={'serial_number': serial_number})
                logger.info(f"استجابة API للتحقق من قاعدة البيانات: {response.status_code}")
                logger.debug(f"محتوى الاستجابة: {response.text}")

                if response.status_code != 200:
                    error_message = 'تعذر الاتصال بقاعدة بيانات الشركة. تحقق من صحة الرقم التسلسلي أو من حالة الاشتراك.'
                    try:
                        error_data = response.json()
                        if 'message' in error_data:
                            error_message = error_data['message']
                    except:
                        pass

                    logger.error(f"فشل التحقق من قاعدة البيانات: {error_message}")
                    messages.error(request, error_message)
                    return render(request, 'company/login.html', {'now': timezone.now(), 'error_message': error_message})
            except Exception as e:
                error_message = f'تعذر الاتصال بخادم التحقق من قاعدة البيانات. يرجى المحاولة مرة أخرى لاحقًا.'
                logger.exception(f"استثناء أثناء التحقق من قاعدة البيانات: {str(e)}")
                messages.error(request, error_message)
                return render(request, 'company/login.html', {'now': timezone.now(), 'error_message': error_message})

        # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        clean_serial = serial_number.replace('-', '').replace(' ', '')

        # تنظيف الرقم التسلسلي وإعادة تنسيقه
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])

        # البحث عن الشركة بالرقم التسلسلي المنسق أو غير المنسق
        try:
            # طباعة معلومات التصحيح
            print(f"البحث عن الشركة بالرقم التسلسلي: {clean_serial} أو {formatted_serial}")

            # البحث عن الشركة بالرقم التسلسلي المنسق أو غير المنسق
            company = Company.objects.filter(serial_number=clean_serial).first()
            if not company:
                company = Company.objects.filter(serial_number=formatted_serial).first()

            # إذا تم العثور على الشركة، طباعة معلوماتها
            if company:
                print(f"تم العثور على الشركة: {company.name}, الحالة: {company.status}, قاعدة البيانات: {company.database_name}")
            else:
                print("لم يتم العثور على الشركة في قاعدة البيانات")

                # لا نستخدم بيانات وهمية، نعتمد فقط على البيانات الحقيقية من قاعدة البيانات
        except Exception as e:
            print(f"خطأ في البحث عن الشركة: {str(e)}")

            # لا نستخدم بيانات وهمية، نعتمد فقط على البيانات الحقيقية من قاعدة البيانات
            # تسجيل محاولة تسجيل دخول فاشلة
            CompanyLoginAttempt.objects.create(
                username=username,
                serial_number=serial_number,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=False
            )
            messages.error(request, 'حدث خطأ أثناء التحقق من الرقم التسلسلي')
            return render(request, 'company/login.html', {'now': timezone.now()})
        # التحقق من وجود الشركة
        if not company:
            # تسجيل محاولة تسجيل دخول فاشلة
            CompanyLoginAttempt.objects.create(
                username=username,
                serial_number=serial_number,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=False
            )
            messages.error(request, 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة')
            return render(request, 'company/login.html', {'now': timezone.now()})

        # التحقق من حالة الشركة
        if hasattr(company, 'status') and company.status != 'active':
            # تسجيل محاولة تسجيل دخول فاشلة
            CompanyLoginAttempt.objects.create(
                username=username,
                serial_number=serial_number,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=False
            )
            messages.error(request, 'الشركة غير نشطة')
            return render(request, 'company/login.html', {'now': timezone.now()})

        # التحقق من صحة قاعدة البيانات
        from backend.super_admin.database_connector import test_company_db_connection
        logger.info(f"التحقق من الاتصال بقاعدة بيانات الشركة: {company.name} ({company.database_name})")
        connection_test = test_company_db_connection(company)

        if not connection_test['success']:
            logger.error(f"فشل الاتصال بقاعدة البيانات: {connection_test['message']}")

            # محاولة إعادة تهيئة قاعدة البيانات
            try:
                logger.info(f"محاولة إعادة تهيئة قاعدة البيانات: {company.database_name}")
                from backend.super_admin.database_initializer import initialize_company_database
                init_result = initialize_company_database(company)
                logger.info(f"نتيجة إعادة تهيئة قاعدة البيانات: {init_result}")

                # إعادة اختبار الاتصال
                connection_test = test_company_db_connection(company)
                if connection_test['success']:
                    logger.info(f"تم إعادة تهيئة قاعدة البيانات بنجاح: {company.database_name}")
                else:
                    # تسجيل محاولة تسجيل دخول فاشلة
                    CompanyLoginAttempt.objects.create(
                        username=username,
                        serial_number=serial_number,
                        ip_address=ip_address,
                        user_agent=request.META.get('HTTP_USER_AGENT', ''),
                        is_successful=False
                    )
                    error_message = f'تعذر الاتصال بقاعدة بيانات الشركة. يرجى التواصل مع الدعم الفني.'
                    logger.error(f"فشل الاتصال بقاعدة البيانات بعد إعادة التهيئة: {connection_test['message']}")
                    messages.error(request, error_message)
                    return render(request, 'company/login.html', {'now': timezone.now(), 'error_message': error_message})
            except Exception as init_error:
                logger.error(f"خطأ في إعادة تهيئة قاعدة البيانات: {str(init_error)}")
                # تسجيل محاولة تسجيل دخول فاشلة
                CompanyLoginAttempt.objects.create(
                    username=username,
                    serial_number=serial_number,
                    ip_address=ip_address,
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    is_successful=False
                )
                error_message = f'حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى التواصل مع الدعم الفني.'
                messages.error(request, error_message)
                return render(request, 'company/login.html', {'now': timezone.now(), 'error_message': error_message})

        # التحقق من بيانات المستخدم
        logger.info(f"التحقق من بيانات المستخدم: {username}")

        # تحقق مباشر من قاعدة بيانات الشركة
        try:
            # إعداد اتصال بقاعدة بيانات الشركة
            db_name = f"company_{company.id}"

            # التحقق من وجود المستخدم في قاعدة بيانات الشركة
            from django.db import connections
            from django.contrib.auth.hashers import check_password

            # إنشاء اتصال مؤقت بقاعدة بيانات الشركة
            from backend.super_admin.database_connector import setup_company_db_connection
            setup_success = setup_company_db_connection(company)

            if not setup_success:
                logger.error(f"فشل في إعداد الاتصال بقاعدة بيانات الشركة: {company.name}")
                messages.error(request, 'فشل في الاتصال بقاعدة بيانات الشركة')
                return render(request, 'company/login.html', {'now': timezone.now()})

            # التحقق من وجود المستخدم وكلمة المرور
            user_found = False

            with connections[db_name].cursor() as cursor:
                cursor.execute("SELECT id, password FROM auth_user WHERE username = %s AND is_active = 1", [username])
                user_data = cursor.fetchone()

                if user_data:
                    # استخراج كلمة المرور المشفرة
                    hashed_password = user_data[1]

                    # التحقق من كلمة المرور
                    if check_password(password, hashed_password):
                        user_found = True
                        logger.info(f"تم التحقق من صحة كلمة المرور للمستخدم {username} في قاعدة بيانات الشركة")
                    else:
                        logger.warning(f"كلمة مرور غير صحيحة للمستخدم {username} في قاعدة بيانات الشركة")
                else:
                    logger.warning(f"المستخدم {username} غير موجود في قاعدة بيانات الشركة")

            # إذا لم يتم العثور على المستخدم في قاعدة بيانات الشركة، نحاول المصادقة العادية
            if not user_found:
                user = authenticate(username=username, password=password)
            else:
                # استخراج المستخدم من قاعدة البيانات الرئيسية
                from django.contrib.auth.models import User
                try:
                    user = User.objects.get(username=username)
                except User.DoesNotExist:
                    # إنشاء المستخدم في قاعدة البيانات الرئيسية إذا لم يكن موجودًا
                    from django.contrib.auth.hashers import make_password
                    user = User.objects.create(
                        username=username,
                        password=make_password(password),
                        is_staff=False,
                        is_superuser=False,
                        is_active=True
                    )
                    logger.info(f"تم إنشاء المستخدم {username} في قاعدة البيانات الرئيسية")
        except Exception as auth_error:
            logger.error(f"خطأ أثناء التحقق من المستخدم في قاعدة بيانات الشركة: {str(auth_error)}")
            # نعود إلى طريقة المصادقة العادية
            user = authenticate(username=username, password=password)

        if user is not None:
            # التحقق من أن المستخدم ينتمي إلى الشركة
            try:
                # البحث عن ارتباط المستخدم بالشركة في نموذج UserCompany
                from backend.accounts.models import UserCompany
                user_company = UserCompany.objects.filter(user=user, company=company).first()

                if not user_company:
                    # إذا لم يتم العثور على ارتباط في UserCompany، نحاول البحث في نموذج CompanyUser
                    try:
                        company_user = CompanyUser.objects.get(user=user, company=company, is_active=True)
                        # إذا وجدنا ارتباط في نموذج CompanyUser، نستخدمه
                        print(f"تم العثور على ارتباط المستخدم بالشركة في نموذج CompanyUser: {company_user.id}")
                    except CompanyUser.DoesNotExist:
                        # إذا لم يتم العثور على ارتباط في CompanyUser، نقوم بإنشاء واحد
                        try:
                            print(f"إنشاء ارتباط جديد بين المستخدم {user.username} والشركة {company.name} في نموذج CompanyUser")
                            company_user = CompanyUser.objects.create(
                                user=user,
                                company=company,
                                is_admin=False,
                                is_active=True
                            )
                            print(f"تم إنشاء ارتباط جديد بنجاح: {company_user.id}")
                        except Exception as e:
                            print(f"خطأ في إنشاء ارتباط CompanyUser: {str(e)}")
                            # تسجيل محاولة تسجيل دخول فاشلة
                            CompanyLoginAttempt.objects.create(
                                username=username,
                                serial_number=serial_number,
                                ip_address=ip_address,
                                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                                is_successful=False
                            )
                            messages.error(request, 'المستخدم غير مرتبط بهذه الشركة أو غير نشط')
                            return render(request, 'company/login.html', {'now': timezone.now()})
            except Exception as e:
                print(f"خطأ في التحقق من ارتباط المستخدم بالشركة: {str(e)}")
                # تسجيل محاولة تسجيل دخول فاشلة
                CompanyLoginAttempt.objects.create(
                    username=username,
                    serial_number=serial_number,
                    ip_address=ip_address,
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    is_successful=False
                )
                messages.error(request, 'حدث خطأ أثناء التحقق من ارتباط المستخدم بالشركة')
                return render(request, 'company/login.html', {'now': timezone.now()})

            # تبديل قاعدة البيانات إلى قاعدة بيانات الشركة
            request.session['company_id'] = company.id
            request.session['company_db'] = company.database_name

            # إضافة معلومات قاعدة البيانات إلى جلسة المستخدم للاستخدام في الميدلوير
            db_name = f"company_{company.id}"
            request.session['current_db_name'] = db_name
            request.session['current_company_id'] = company.id
            request.session['serial_number'] = company.serial_number

            # إضافة معلومات قاعدة البيانات من اختبار الاتصال
            request.session['db_type'] = connection_test['db_type']
            request.session['db_tables_count'] = connection_test['tables_count']

            # تسجيل معلومات قاعدة البيانات
            logger.info(f"تم تعيين معلومات قاعدة البيانات في الجلسة: {db_name}, {company.id}, {company.serial_number}, {connection_test['db_type']}")
            logger.info(f"عدد الجداول في قاعدة البيانات: {connection_test['tables_count']}")

            # التحقق من وجود جداول في قاعدة البيانات
            if connection_test['tables_count'] == 0:
                logger.warning(f"قاعدة البيانات {company.database_name} لا تحتوي على جداول")
                # إعادة تهيئة قاعدة البيانات
                try:
                    logger.info(f"محاولة إعادة تهيئة قاعدة البيانات: {company.database_name}")
                    from backend.super_admin.database_initializer import initialize_company_database
                    init_result = initialize_company_database(company)
                    logger.info(f"نتيجة إعادة تهيئة قاعدة البيانات: {init_result}")
                except Exception as init_error:
                    logger.error(f"خطأ في إعادة تهيئة قاعدة البيانات: {str(init_error)}")

            # تسجيل الدخول
            logger.info(f"تسجيل دخول المستخدم: {user.username} إلى الشركة: {company.name}")
            login(request, user)

            # تسجيل محاولة تسجيل دخول ناجحة
            CompanyLoginAttempt.objects.create(
                username=username,
                serial_number=serial_number,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=True
            )

            messages.success(request, f'تم تسجيل الدخول بنجاح إلى {company.name}')
            return redirect('company:dashboard')
        else:
            # تسجيل محاولة تسجيل دخول فاشلة
            CompanyLoginAttempt.objects.create(
                username=username,
                serial_number=serial_number,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_successful=False
            )
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')

    # إضافة متغير now إلى السياق
    return render(request, 'company/login.html', {'now': timezone.now()})

def company_logout(request):
    """تسجيل خروج الشركة"""
    # إزالة معلومات الشركة من الجلسة
    session_keys = [
        'company_id', 'company_db', 'current_db_name',
        'current_company_id', 'serial_number'
    ]

    for key in session_keys:
        if key in request.session:
            del request.session[key]

    # طباعة معلومات التصحيح
    print("تم إزالة معلومات الشركة من الجلسة")

    # تسجيل الخروج
    logout(request)

    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('company:login')

# لوحة تحكم الشركة
@login_required
def company_dashboard(request):
    """عرض لوحة تحكم الشركة"""
    # التحقق من أن المستخدم ينتمي إلى شركة
    company = None

    # التحقق من وجود ارتباط في نموذج CompanyUser
    if hasattr(request.user, 'company_profile'):
        company = request.user.company_profile.company
        print(f"تم العثور على ارتباط المستخدم بالشركة في نموذج CompanyUser: {company.name}")
    else:
        # التحقق من وجود ارتباط في نموذج UserCompany
        from backend.accounts.models import UserCompany
        user_company = UserCompany.objects.filter(user=request.user).first()

        if user_company:
            company = user_company.company
            print(f"تم العثور على ارتباط المستخدم بالشركة في نموذج UserCompany: {company.name}")
        else:
            # التحقق من وجود معلومات الشركة في الجلسة
            company_id = request.session.get('company_id')
            if company_id:
                try:
                    from backend.super_admin.models import Company
                    company = Company.objects.get(id=company_id)
                    print(f"تم العثور على الشركة من معلومات الجلسة: {company.name}")

                    # إنشاء ارتباط بين المستخدم والشركة في نموذج CompanyUser
                    try:
                        from .models import CompanyUser
                        company_user = CompanyUser.objects.create(
                            user=request.user,
                            company=company,
                            is_admin=False,
                            is_active=True
                        )
                        print(f"تم إنشاء ارتباط جديد بنجاح: {company_user.id}")
                        company = company_user.company
                    except Exception as e:
                        print(f"خطأ في إنشاء ارتباط CompanyUser: {str(e)}")
                except Exception as e:
                    print(f"خطأ في البحث عن الشركة: {str(e)}")

    if not company:
        messages.error(request, 'ليس لديك صلاحيات للوصول إلى لوحة تحكم الشركة')
        return redirect('company:login')

    # الحصول على بيانات الشركة
    # company = request.user.company_profile.company

    # الحصول على الإحصائيات من API
    from backend.dashboard.views import dashboard_statistics_api, activities_api
    stats_response = dashboard_statistics_api(request)
    stats = json.loads(stats_response.content)

    # الحصول على الأنشطة الأخيرة من API
    activities_response = activities_api(request)
    activities_data = json.loads(activities_response.content)

    # أخذ آخر 4 أنشطة فقط
    recent_activities = activities_data.get('activities', [])[:4]

    context = {
        'company': company,
        'stats': stats,
        'recent_activities': recent_activities,
        'current_date': timezone.now(),
    }

    return render(request, 'company/dashboard.html', context)

# صفحة العمال
@login_required
def workers(_):
    """عرض صفحة العمال"""
    return redirect('/workers/')

# صفحة العملاء
@login_required
def clients(_):
    """عرض صفحة العملاء"""
    return redirect('/clients/')

# صفحة العقود
@login_required
def contracts(request):
    """عرض صفحة العقود"""
    return render(request, 'company/contracts.html')

# صفحة الخدمات
@login_required
def services(request):
    """عرض صفحة الخدمات"""
    return render(request, 'company/services.html')

# صفحة التقارير
@login_required
def reports(request):
    """عرض صفحة التقارير"""
    return render(request, 'company/reports.html')

# صفحة الأنشطة
@login_required
def activities(request):
    """عرض صفحة الأنشطة الكاملة"""
    # التحقق من أن المستخدم ينتمي إلى شركة
    company = None

    # التحقق من وجود ارتباط في نموذج CompanyUser
    if hasattr(request.user, 'company_profile'):
        company = request.user.company_profile.company
    else:
        # التحقق من وجود ارتباط في نموذج UserCompany
        from backend.accounts.models import UserCompany
        user_company = UserCompany.objects.filter(user=request.user).first()

        if user_company:
            company = user_company.company
        else:
            # التحقق من وجود معلومات الشركة في الجلسة
            company_id = request.session.get('company_id')
            if company_id:
                try:
                    from backend.super_admin.models import Company
                    company = Company.objects.get(id=company_id)
                except Exception as e:
                    print(f"خطأ في البحث عن الشركة: {str(e)}")

    if not company:
        messages.error(request, 'ليس لديك صلاحيات للوصول إلى صفحة الأنشطة')
        return redirect('company:login')

    # الحصول على الأنشطة من API
    from backend.dashboard.views import activities_api
    activities_response = activities_api(request)
    activities_data = json.loads(activities_response.content)
    all_activities = activities_data.get('activities', [])

    context = {
        'company': company,
        'activities': all_activities,
        'current_date': timezone.now(),
    }

    return render(request, 'company/activities.html', context)

# صفحة الملف الشخصي
@login_required
def profile(request):
    """عرض صفحة الملف الشخصي"""
    return render(request, 'company/profile.html')

# صفحة الإعدادات
@login_required
def settings(request):
    """عرض صفحة الإعدادات"""
    return render(request, 'company/settings.html')
