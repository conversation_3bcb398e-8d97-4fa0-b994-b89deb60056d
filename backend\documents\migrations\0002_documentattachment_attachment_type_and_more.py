# Generated by Django 5.2 on 2025-05-15 20:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='documentattachment',
            name='attachment_type',
            field=models.CharField(choices=[('passport', 'جواز سفر'), ('iqama', 'إقامة'), ('contract', 'عقد عمل'), ('medical', 'تقرير طبي'), ('certificate', 'شهادة'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع المرفق'),
        ),
        migrations.AddField(
            model_name='documentattachment',
            name='expiry_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية'),
        ),
    ]
