from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator

class Worker(models.Model):
    GENDER_CHOICES = (
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    )

    NATIONALITY_CHOICES = (
        ('EG', 'مصر'),
        ('IN', 'الهند'),
        ('PK', 'باكستان'),
        ('BD', 'بنغلاديش'),
        ('PH', 'الفلبين'),
        ('NP', 'نيبال'),
        ('LK', 'سريلانكا'),
        ('ET', 'إثيوبيا'),
        ('KE', 'كينيا'),
        ('ID', 'إندونيسيا'),
    )

    STATUS_CHOICES = (
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('on_leave', 'في إجازة'),
        ('terminated', 'منتهي الخدمة'),
    )

    WORKER_TYPE_CHOICES = (
        ('contract', 'عقد دائم'),
        ('custom', 'خدمة مخصصة'),
        ('monthly', 'عقد شهري'),
    )

    # معلومات شخصية
    first_name = models.CharField(max_length=100, default='', verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=100, default='', verbose_name='الاسم الأخير')
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='M', verbose_name='الجنس')
    nationality = models.CharField(max_length=2, choices=NATIONALITY_CHOICES, default='EG', verbose_name='الجنسية')
    date_of_birth = models.DateField(default=timezone.now, verbose_name='تاريخ الميلاد')
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    job_title = models.CharField(max_length=100, blank=True, null=True, verbose_name='المهنة')

    # معلومات جواز السفر
    passport_number = models.CharField(max_length=20, unique=True, default='P0000001', verbose_name='رقم جواز السفر')
    passport_issue_date = models.DateField(null=True, blank=True, verbose_name='تاريخ إصدار جواز السفر')
    passport_expiry = models.DateField(default=timezone.now, verbose_name='تاريخ انتهاء جواز السفر')
    entry_date = models.DateField(default=timezone.now, verbose_name='تاريخ إصدار التأشيرة / الستيكر')
    visa_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم التأشيرة')
    visa_expiry = models.DateField(default=timezone.now, verbose_name='تاريخ انتهاء التأشيرة / الاستيكر')

    # الصور والمستندات
    photo = models.ImageField(upload_to='workers/photos/', blank=True, null=True, verbose_name='الصورة الشخصية')
    passport_image = models.ImageField(upload_to='workers/passports/', blank=True, null=True, verbose_name='صورة جواز السفر')
    visa_image = models.ImageField(upload_to='workers/visas/', blank=True, null=True, verbose_name='صورة التأشيرة/الستيكر')

    # معلومات الشركة المستقدمة
    recruitment_company = models.ForeignKey('RecruitmentCompany', on_delete=models.SET_NULL, null=True, blank=True,
                                          related_name='workers', verbose_name='الشركة المستقدمة')

    # معلومات إضافية
    date_joined = models.DateField(default=timezone.now, verbose_name='تاريخ الانضمام')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    worker_type = models.CharField(max_length=20, choices=WORKER_TYPE_CHOICES, default='contract', verbose_name='نوع العامل')

    # معلومات الراتب
    salary = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الراتب')
    daily_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الأجر اليومي')
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الأجر بالساعة')

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    class Meta:
        verbose_name = 'عامل'
        verbose_name_plural = 'العمال'


class RecruitmentCompany(models.Model):
    """Model for recruitment companies"""
    name = models.CharField(max_length=100, verbose_name='اسم الشركة')
    country = models.CharField(max_length=50, verbose_name='الدولة')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف',
                           validators=[RegexValidator(regex=r'^\+?\d{8,15}$',
                                                     message='الرجاء إدخال رقم هاتف صحيح')])
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    contact_person = models.CharField(max_length=100, blank=True, null=True, verbose_name='الشخص المسؤول')
    license_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الترخيص')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'شركة استقدام'
        verbose_name_plural = 'شركات الاستقدام'
        ordering = ['name']


class WorkerDocument(models.Model):
    """Model for worker documents"""
    DOCUMENT_TYPES = (
        ('passport', 'جواز سفر'),
        ('visa', 'تأشيرة'),
        ('id_card', 'بطاقة هوية'),
        ('contract', 'عقد عمل'),
        ('medical', 'شهادة طبية'),
        ('other', 'مستندات أخرى'),
    )

    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='documents', verbose_name='العامل')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name='نوع المستند')
    title = models.CharField(max_length=100, verbose_name='عنوان المستند')
    file = models.FileField(upload_to='workers/documents/', verbose_name='الملف')
    upload_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.worker}"

    def get_delete_url(self):
        """Return the URL to delete this document."""
        from django.urls import reverse
        return reverse('workers:delete_worker_document', args=[self.id])

    class Meta:
        verbose_name = 'مستند العامل'
        verbose_name_plural = 'مستندات العمال'
