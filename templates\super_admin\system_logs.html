{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}سجل النظام - لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/system_logs_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/backup_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/system_logs_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/backup_logs_specific_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/system_logs_card_style.css' %}">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-800">سجل النظام</h1>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'super_admin:dashboard' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-arrow-right ml-2"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    {% if messages %}
    <div class="mb-6">
        {% for message in messages %}
        <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700 border border-green-200{% elif message.tags == 'error' %}bg-red-100 text-red-700 border border-red-200{% else %}bg-blue-100 text-blue-700 border border-blue-200{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md mb-8">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-purple-400 to-purple-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-filter text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">تصفية السجلات</h3>
                <p class="text-gray-500 text-sm mt-1">تصفية سجلات النظام حسب المعايير المختلفة</p>
            </div>
        </div>

        <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="space-y-2">
                <label for="action" class="block text-sm font-medium text-gray-700">نوع الإجراء</label>
                <select name="action" id="action" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                    <option value="">جميع الإجراءات</option>
                    {% for action_value, action_name in action_choices %}
                    <option value="{{ action_value }}" {% if request.GET.action == action_value %}selected{% endif %}>{{ action_name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="space-y-2">
                <label for="user_id" class="block text-sm font-medium text-gray-700">المستخدم</label>
                <select name="user_id" id="user_id" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                    <option value="">جميع المستخدمين</option>
                    {% for user in users %}
                    <option value="{{ user.id }}" {% if request.GET.user_id == user.id|stringformat:"i" %}selected{% endif %}>{{ user.username }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="space-y-2">
                <label for="company_id" class="block text-sm font-medium text-gray-700">الشركة</label>
                <select name="company_id" id="company_id" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                    <option value="">جميع الشركات</option>
                    {% for company in companies %}
                    <option value="{{ company.id }}" {% if request.GET.company_id == company.id|stringformat:"i" %}selected{% endif %}>{{ company.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                    <i class="fas fa-search ml-2"></i> تصفية
                </button>
            </div>
        </form>
    </div>

    <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-history text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">سجلات النظام</h3>
                <p class="text-gray-500 text-sm mt-1">سجل جميع العمليات التي تمت على النظام</p>
            </div>
            <div class="mr-auto">
                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full shadow-sm">
                    {{ logs.count }} سجل
                </span>
            </div>
        </div>

        <!-- أزرار التصدير -->
        <div class="flex mb-4">
            <a href="#" class="export-button export-excel ml-2">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </a>
            <a href="#" class="export-button export-pdf">
                <i class="fas fa-file-pdf"></i>
                تصدير PDF
            </a>
            <div class="mr-auto">
                <h3 class="text-lg font-bold text-gray-800">آخر الأحداث</h3>
            </div>
        </div>

        <!-- عرض السجلات على شكل بطاقات -->
        <div class="system-logs-cards">
            {% if logs %}
                {% for log in logs %}
                <div class="system-log-card">
                    <div class="system-log-card-header">
                        <div class="system-log-card-date">
                            <i class="fas fa-calendar-alt"></i>
                            {{ log.created_at|date:"Y-m-d H:i:s" }}
                        </div>
                        <div class="system-log-card-user">
                            {% if log.user %}
                            <div class="system-log-card-user-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            {{ log.user.username }}
                            {% else %}
                            <span class="text-gray-500">-</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="system-log-card-content">
                        {% if log.action == 'login' %}
                        <span class="system-log-card-action action-login">تسجيل دخول</span>
                        {% elif log.action == 'logout' %}
                        <span class="system-log-card-action action-logout">تسجيل خروج</span>
                        {% elif log.action == 'create' %}
                        <span class="system-log-card-action action-create">إنشاء</span>
                        {% elif log.action == 'update' %}
                        <span class="system-log-card-action action-update">تحديث</span>
                        {% elif log.action == 'delete' %}
                        <span class="system-log-card-action action-delete">حذف</span>
                        {% elif log.action == 'backup' %}
                        <span class="system-log-card-action action-backup">نسخ احتياطي</span>
                        {% elif log.action == 'restore' %}
                        <span class="system-log-card-action action-restore">استعادة</span>
                        {% elif log.action == 'download' %}
                        <span class="system-log-card-action action-download">تنزيل</span>
                        {% elif log.action == 'upload' %}
                        <span class="system-log-card-action action-upload">رفع</span>
                        {% elif log.action == 'activate' %}
                        <span class="system-log-card-action action-activate">تفعيل</span>
                        {% elif log.action == 'deactivate' %}
                        <span class="system-log-card-action action-deactivate">تعطيل</span>
                        {% else %}
                        <span class="system-log-card-action action-other">{{ log.action }}</span>
                        {% endif %}

                        <div class="system-log-card-description">
                            {% if log.company %}
                            <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded-md ml-2 inline-block">
                                <i class="fas fa-building ml-1"></i>{{ log.company.name }}
                            </span>
                            {% endif %}
                            {{ log.description }}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="flex flex-col items-center bg-gray-50 p-8 rounded-lg">
                    <div class="bg-blue-100 text-blue-500 p-4 rounded-full mb-4 shadow-sm">
                        <i class="fas fa-history text-3xl"></i>
                    </div>
                    <p class="text-lg font-medium text-gray-700">لا توجد سجلات متاحة</p>
                    <p class="text-sm text-gray-500 mt-2 max-w-md">لم يتم العثور على أي سجلات تطابق معايير التصفية</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JavaScript لتحسين تجربة المستخدم -->
<script src="{% static 'js/system_logs.js' %}"></script>
<script src="{% static 'js/system_backup_logs.js' %}"></script>
<script src="{% static 'js/system_logs_table_fix.js' %}"></script>
<script src="{% static 'js/backup_logs_specific_fix.js' %}"></script>
<script src="{% static 'js/system_logs_card.js' %}"></script>
{% endblock %}
