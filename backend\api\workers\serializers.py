from rest_framework import serializers
from workers.models import Worker
from django.utils.translation import gettext_lazy as _


class WorkerSerializer(serializers.ModelSerializer):
    """مسلسل بيانات العامل الأساسية"""
    
    full_name = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    
    class Meta:
        model = Worker
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'nationality',
            'passport_number', 'passport_expiry_date', 'residence_number',
            'residence_expiry_date', 'is_active', 'status', 'job_title',
            'hire_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"
    
    def get_status(self, obj):
        return _('نشط') if obj.is_active else _('غير نشط')


class WorkerDetailSerializer(WorkerSerializer):
    """مسلسل بيانات العامل التفصيلية"""
    
    days_until_passport_expiry = serializers.SerializerMethodField()
    days_until_residence_expiry = serializers.SerializerMethodField()
    days_until_contract_end = serializers.SerializerMethodField()
    
    class Meta(WorkerSerializer.Meta):
        fields = WorkerSerializer.Meta.fields + [
            'gender', 'birth_date', 'age', 'marital_status', 'phone_number',
            'email', 'address', 'passport_issue_date', 'passport_issue_place',
            'days_until_passport_expiry', 'residence_issue_date',
            'residence_place', 'days_until_residence_expiry', 'monthly_salary',
            'currency', 'recruitment_company', 'recruitment_cost',
            'contract_end_date', 'days_until_contract_end', 'notes'
        ]
    
    def get_days_until_passport_expiry(self, obj):
        return obj.days_until_passport_expiry if hasattr(obj, 'days_until_passport_expiry') else None
    
    def get_days_until_residence_expiry(self, obj):
        return obj.days_until_residence_expiry if hasattr(obj, 'days_until_residence_expiry') else None
    
    def get_days_until_contract_end(self, obj):
        return obj.days_until_contract_end if hasattr(obj, 'days_until_contract_end') else None


class WorkerCreateSerializer(serializers.ModelSerializer):
    """مسلسل إنشاء عامل جديد"""
    
    class Meta:
        model = Worker
        fields = [
            'first_name', 'last_name', 'nationality', 'gender', 'birth_date',
            'marital_status', 'phone_number', 'email', 'address',
            'passport_number', 'passport_issue_date', 'passport_expiry_date',
            'passport_issue_place', 'residence_number', 'residence_issue_date',
            'residence_expiry_date', 'residence_place', 'job_title',
            'hire_date', 'monthly_salary', 'currency', 'recruitment_company',
            'recruitment_cost', 'contract_end_date', 'is_active', 'notes'
        ]
    
    def validate_passport_expiry_date(self, value):
        """التحقق من تاريخ انتهاء جواز السفر"""
        if value and value < self.context['request'].data.get('passport_issue_date'):
            raise serializers.ValidationError(_('تاريخ انتهاء الجواز يجب أن يكون بعد تاريخ الإصدار'))
        return value
    
    def validate_residence_expiry_date(self, value):
        """التحقق من تاريخ انتهاء الإقامة"""
        if value and value < self.context['request'].data.get('residence_issue_date'):
            raise serializers.ValidationError(_('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'))
        return value


class WorkerUpdateSerializer(serializers.ModelSerializer):
    """مسلسل تحديث بيانات العامل"""
    
    class Meta:
        model = Worker
        fields = [
            'first_name', 'last_name', 'nationality', 'gender', 'birth_date',
            'marital_status', 'phone_number', 'email', 'address',
            'passport_number', 'passport_issue_date', 'passport_expiry_date',
            'passport_issue_place', 'residence_number', 'residence_issue_date',
            'residence_expiry_date', 'residence_place', 'job_title',
            'hire_date', 'monthly_salary', 'currency', 'recruitment_company',
            'recruitment_cost', 'contract_end_date', 'is_active', 'notes'
        ]
    
    def validate_passport_expiry_date(self, value):
        """التحقق من تاريخ انتهاء جواز السفر"""
        if value and self.instance.passport_issue_date and value < self.instance.passport_issue_date:
            raise serializers.ValidationError(_('تاريخ انتهاء الجواز يجب أن يكون بعد تاريخ الإصدار'))
        return value
    
    def validate_residence_expiry_date(self, value):
        """التحقق من تاريخ انتهاء الإقامة"""
        if value and self.instance.residence_issue_date and value < self.instance.residence_issue_date:
            raise serializers.ValidationError(_('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'))
        return value
