from django.contrib import admin
from .models import Company, SuperAdminUser, SystemLog, DatabaseBackup, BackupSchedule

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'serial_number', 'database_name', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('name', 'serial_number', 'database_name')
    readonly_fields = ('serial_number', 'created_at', 'updated_at')
    fieldsets = (
        ('معلومات الشركة', {
            'fields': ('name', 'serial_number', 'status', 'expiry_date')
        }),
        ('معلومات قاعدة البيانات', {
            'fields': ('database_name', 'database_user', 'database_password', 'database_host')
        }),
        ('بيانات مدير الشركة', {
            'fields': ('admin_username', 'admin_password')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(DatabaseBackup)
class DatabaseBackupAdmin(admin.ModelAdmin):
    list_display = ('company', 'file_name', 'backup_type', 'file_size', 'created_at', 'created_by')
    list_filter = ('backup_type', 'created_at', 'company')
    search_fields = ('file_name', 'description', 'company__name')
    readonly_fields = ('file_size', 'created_at', 'created_by')
    fieldsets = (
        ('معلومات النسخة الاحتياطية', {
            'fields': ('company', 'file_name', 'file_path', 'backup_type', 'is_encrypted', 'description')
        }),
        ('معلومات النظام', {
            'fields': ('file_size', 'created_at', 'created_by')
        }),
    )

@admin.register(BackupSchedule)
class BackupScheduleAdmin(admin.ModelAdmin):
    list_display = ('company', 'is_active', 'frequency', 'next_backup', 'last_backup')
    list_filter = ('is_active', 'frequency', 'created_at')
    search_fields = ('company__name',)
    readonly_fields = ('last_backup', 'next_backup', 'created_at', 'updated_at', 'created_by', 'updated_by')
    fieldsets = (
        ('معلومات الجدولة', {
            'fields': ('company', 'is_active', 'frequency', 'time')
        }),
        ('إعدادات متقدمة', {
            'fields': ('day_of_week', 'day_of_month', 'retention_period')
        }),
        ('معلومات النظام', {
            'fields': ('last_backup', 'next_backup', 'created_at', 'updated_at', 'created_by', 'updated_by')
        }),
    )

@admin.register(SuperAdminUser)
class SuperAdminUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'two_factor_enabled', 'last_login_ip')
    list_filter = ('two_factor_enabled',)
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('last_login_ip', 'token_expiry')

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'company', 'created_at', 'ip_address')
    list_filter = ('action', 'created_at')
    search_fields = ('user__username', 'description', 'company__name')
    readonly_fields = ('user', 'action', 'company', 'description', 'ip_address', 'created_at')
