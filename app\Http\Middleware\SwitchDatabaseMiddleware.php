<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Company;
use App\Services\DatabaseConnectionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SwitchDatabaseMiddleware
{
    /**
     * خدمة الاتصال بقواعد البيانات
     *
     * @var DatabaseConnectionService
     */
    protected $databaseService;

    /**
     * إنشاء مثيل جديد من الوسيط
     *
     * @param DatabaseConnectionService $databaseService
     * @return void
     */
    public function __construct(DatabaseConnectionService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * معالجة الطلب الوارد
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // التحقق من وجود معلومات الشركة في الجلسة
        $companyId = $request->session()->get('company_id');
        $serialNumber = $request->session()->get('serial_number');
        $companyDb = $request->session()->get('company_db');

        // للتوافق مع الكود القديم
        $currentCompanyId = $request->session()->get('current_company_id');
        if (!$companyId && $currentCompanyId) {
            $companyId = $currentCompanyId;
            $request->session()->put('company_id', $companyId);
        }

        if (!$companyId || !$serialNumber) {
            // إذا كان المستخدم مسجل الدخول ولكن لا توجد معلومات شركة، نقوم بتسجيل الخروج
            if (Auth::check() && !$request->routeIs('company.login', 'company.logout')) {
                Auth::logout();
                $request->session()->flush();

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.'
                    ], 401);
                }

                return redirect()->route('company.login')
                    ->with('error', 'انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.');
            }

            // إذا كان المستخدم غير مسجل الدخول، نسمح بالمرور (ربما يحاول الوصول إلى صفحة تسجيل الدخول)
            return $next($request);
        }

        // البحث عن الشركة
        $company = Company::find($companyId);

        if (!$company) {
            // إذا لم يتم العثور على الشركة، نقوم بتسجيل الخروج
            Auth::logout();
            $request->session()->flush();

            // تسجيل الخطأ
            Log::error('الشركة غير موجودة', [
                'company_id' => $companyId,
                'user_id' => Auth::id()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            return redirect()->route('company.login')
                ->with('error', 'الشركة غير موجودة. يرجى تسجيل الدخول مرة أخرى.');
        }

        // التحقق من حالة الشركة
        if ($company->status !== 'active') {
            // إذا كانت الشركة غير نشطة، نقوم بتسجيل الخروج
            Auth::logout();
            $request->session()->flush();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير نشطة'
                ], 403);
            }

            return redirect()->route('company.login')
                ->with('error', 'الشركة غير نشطة. يرجى التواصل مع المسؤول.');
        }

        // تبديل الاتصال إلى قاعدة بيانات الشركة
        $switched = $this->databaseService->switchToCompanyDatabase($company);

        if (!$switched) {
            // تسجيل الخطأ
            Log::error('فشل في تبديل الاتصال إلى قاعدة بيانات الشركة', [
                'company_id' => $company->id,
                'database_name' => $company->database_name,
                'user_id' => Auth::id()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال بقاعدة بيانات الشركة'
                ], 500);
            }

            return redirect()->route('company.database_error')
                ->with('error', 'فشل في الاتصال بقاعدة بيانات الشركة');
        }

        // تسجيل نجاح التبديل
        Log::info('تم تبديل الاتصال إلى قاعدة بيانات الشركة', [
            'company_id' => $company->id,
            'database_name' => $company->database_name,
            'user_id' => Auth::id()
        ]);

        // إضافة معلومات الشركة إلى الطلب
        $request->attributes->add(['company' => $company]);

        // متابعة معالجة الطلب
        $response = $next($request);

        return $response;
    }
}
