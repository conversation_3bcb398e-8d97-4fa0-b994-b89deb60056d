{% extends 'layouts/super_admin_unified.html' %}

{% block title %}إدارة الشركات - منصة استقدامي{% endblock %}

{% block content %}
<div class="space-y-6">

    <!-- رأس الصفحة -->
    <div class="admin-card rounded-2xl p-6 shadow-xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-purple-600 via-violet-600 to-blue-600 bg-clip-text text-transparent">
                    <i class="fas fa-building ml-2"></i>
                    إدارة الشركات
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">إدارة شاملة لجميع الشركات المسجلة في النظام</p>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <button class="admin-btn text-white px-4 py-2 rounded-lg hover:scale-105 transition-transform">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة شركة جديدة
                </button>
                <button class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <i class="fas fa-download ml-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات الشركات -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                    <i class="fas fa-building"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الشركات</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ companies.count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">نشطة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ active_count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">معلقة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ suspended_count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl p-4 shadow-lg">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">غير نشطة</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ inactive_count|default:"0" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="admin-card rounded-xl p-6 shadow-lg">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <div class="relative">
                    <input type="text" placeholder="ابحث عن شركة..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشطة</option>
                    <option value="suspended">معلقة</option>
                    <option value="inactive">غير نشطة</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الإنشاء</label>
                <input type="date" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
            </div>

            <div class="flex items-end">
                <button class="admin-btn text-white px-6 py-2 rounded-lg w-full">
                    <i class="fas fa-filter ml-2"></i>
                    تطبيق الفلتر
                </button>
            </div>
        </div>
    </div>

    <!-- جدول الشركات -->
    <div class="admin-card rounded-xl shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-purple-500 to-violet-500 text-white">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-medium">الشركة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الرقم التسلسلي</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">العملة المفضلة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">المدير</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">تاريخ الإنشاء</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الحالة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    {% for company in companies %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                    {{ company.name|first }}
                                </div>
                                <div class="mr-3">
                                    <p class="font-medium text-gray-900 dark:text-white">{{ company.name }}</p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ company.email }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="font-mono text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">{{ company.serial_number }}</span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if company.preferred_currency == 'IQD' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
                                    {% if company.preferred_currency == 'IQD' %}
                                        <i class="fas fa-coins ml-1"></i>
                                        دينار عراقي
                                    {% elif company.preferred_currency == 'USD' %}
                                        <i class="fas fa-dollar-sign ml-1"></i>
                                        دولار أمريكي
                                    {% else %}
                                        {{ company.preferred_currency }}
                                    {% endif %}
                                </span>
                                {% if company.use_custom_exchange_rate %}
                                    <span class="mr-2 text-xs text-orange-600 dark:text-orange-400">
                                        <i class="fas fa-edit"></i>
                                        مخصص
                                    </span>
                                {% endif %}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                مثال: {{ company.get_currency_example }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <p class="font-medium text-gray-900 dark:text-white">{{ company.manager_name|default:"غير محدد" }}</p>
                        </td>
                        <td class="px-6 py-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ company.created_at|date:"d/m/Y" }}</p>
                        </td>
                        <td class="px-6 py-4">
                            {% if company.is_active %}
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">نشطة</span>
                            {% else %}
                                <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">غير نشطة</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-800 p-1 rounded" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-800 p-1 rounded" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>

                                <button class="text-purple-600 hover:text-purple-800 p-1 rounded" title="الإعدادات">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800 p-1 rounded" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-building text-4xl mb-4"></i>
                                <p class="text-lg font-medium">لا توجد شركات مسجلة</p>
                                <p class="text-sm">ابدأ بإضافة شركة جديدة</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if companies.has_other_pages %}
        <div class="bg-gray-50 dark:bg-gray-800 px-6 py-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    عرض {{ companies.start_index }} إلى {{ companies.end_index }} من أصل {{ companies.paginator.count }} شركة
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    {% if companies.has_previous %}
                        <a href="?page={{ companies.previous_page_number }}" class="px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-600">السابق</a>
                    {% endif %}

                    <span class="px-3 py-1 bg-purple-600 text-white rounded text-sm">{{ companies.number }}</span>

                    {% if companies.has_next %}
                        <a href="?page={{ companies.next_page_number }}" class="px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-600">التالي</a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // البحث المباشر
    document.querySelector('input[placeholder="ابحث عن شركة..."]').addEventListener('input', function(e) {
        // يمكن إضافة AJAX للبحث المباشر
        console.log('البحث عن:', e.target.value);
    });
</script>
{% endblock %}
