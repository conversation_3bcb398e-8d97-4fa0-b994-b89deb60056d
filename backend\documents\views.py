from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q
from .models import Document, DocumentAttachment
from .forms import DocumentForm, DocumentAttachmentForm, DocumentFilterForm
from backend.dashboard.models import Activity

@login_required
def documents_dashboard(request):
    """لوحة تحكم المستندات"""
    # إحصائيات المستندات
    total_documents = Document.objects.count()
    incoming_documents = Document.objects.filter(document_type='incoming').count()
    outgoing_documents = Document.objects.filter(document_type='outgoing').count()
    pending_documents = Document.objects.filter(status='pending').count()

    # المستندات الحديثة
    recent_documents = Document.objects.order_by('-created_at')[:5]

    context = {
        'page_title': 'لوحة تحكم المستندات',
        'total_documents': total_documents,
        'incoming_documents': incoming_documents,
        'outgoing_documents': outgoing_documents,
        'pending_documents': pending_documents,
        'recent_documents': recent_documents,
    }

    return render(request, 'documents/dashboard.html', context)

@login_required
def document_list(request):
    """View function for the document list page."""
    documents = Document.objects.all()
    return render(request, 'documents/document_list.html', {
        'page_title': 'قائمة المستندات',
        'documents': documents
    })

@login_required
def incoming_outgoing(request):
    """View function for the incoming and outgoing documents page."""
    # Get statistics
    incoming_count = Document.objects.filter(document_type='incoming').count()
    outgoing_count = Document.objects.filter(document_type='outgoing').count()
    pending_count = Document.objects.filter(status='pending').count()

    # Initialize filter form
    filter_form = DocumentFilterForm(request.GET or None)

    # Apply filters if form is valid
    documents = Document.objects.all()
    if filter_form.is_valid():
        filters = {}
        if filter_form.cleaned_data['document_number']:
            filters['document_number__icontains'] = filter_form.cleaned_data['document_number']
        if filter_form.cleaned_data['document_type']:
            filters['document_type'] = filter_form.cleaned_data['document_type']
        if filter_form.cleaned_data['entity']:
            filters['entity__icontains'] = filter_form.cleaned_data['entity']
        if filter_form.cleaned_data['status']:
            filters['status'] = filter_form.cleaned_data['status']

        # Date range filter
        if filter_form.cleaned_data['date_from']:
            filters['document_date__gte'] = filter_form.cleaned_data['date_from']
        if filter_form.cleaned_data['date_to']:
            filters['document_date__lte'] = filter_form.cleaned_data['date_to']

        documents = documents.filter(**filters)

    # Process form submission for new document
    if request.method == 'POST':
        form = DocumentForm(request.POST)
        if form.is_valid():
            document = form.save(commit=False)
            document.created_by = request.user
            document.save()

            # Log activity
            Activity.objects.create(
                description=f'تم إضافة معاملة جديدة: {document.document_number}',
                activity_type='document_added',
                date_created=timezone.now()
            )

            messages.success(request, 'تم إضافة المعاملة بنجاح')
            return redirect('documents:incoming_outgoing')
    else:
        form = DocumentForm()

    return render(request, 'documents/incoming_outgoing.html', {
        'page_title': 'سجل الصادر والوارد',
        'documents': documents,
        'form': form,
        'filter_form': filter_form,
        'incoming_count': incoming_count,
        'outgoing_count': outgoing_count,
        'pending_count': pending_count
    })

@login_required
def document_detail(request, document_id):
    """View function for viewing a document's details."""
    document = get_object_or_404(Document, id=document_id)
    attachments = document.attachments.all()

    return render(request, 'documents/document_detail.html', {
        'page_title': f'تفاصيل المعاملة: {document.document_number}',
        'document': document,
        'attachments': attachments
    })

@login_required
def document_update(request, document_id):
    """View function for updating a document."""
    document = get_object_or_404(Document, id=document_id)

    if request.method == 'POST':
        form = DocumentForm(request.POST, instance=document)
        if form.is_valid():
            form.save()

            # Log activity
            Activity.objects.create(
                description=f'تم تحديث معاملة: {document.document_number}',
                activity_type='document_updated',
                date_created=timezone.now()
            )

            messages.success(request, 'تم تحديث المعاملة بنجاح')
            return redirect('documents:document_detail', document_id=document.id)
    else:
        form = DocumentForm(instance=document)

    return render(request, 'documents/document_form.html', {
        'page_title': f'تعديل معاملة: {document.document_number}',
        'form': form,
        'document': document
    })

@login_required
def document_delete(request, document_id):
    """View function for deleting a document."""
    document = get_object_or_404(Document, id=document_id)

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('documents:document_list')

    # حذف المستند
    document_number = document.document_number
    document.delete()

    # Log activity
    Activity.objects.create(
        description=f'تم حذف معاملة: {document_number}',
        activity_type='document_deleted',
        date_created=timezone.now()
    )

    messages.success(request, 'تم حذف المعاملة بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('documents:incoming_outgoing')

@login_required
def document_upload(request):
    """View function for uploading a document for a worker."""
    worker_id = request.GET.get('worker')

    # إذا لم يتم تحديد عامل، قم بإعادة توجيه المستخدم إلى صفحة قائمة العمال
    if not worker_id:
        messages.error(request, 'يجب تحديد العامل لرفع المستندات')
        return redirect('workers:worker_list')

    # تحقق من وجود العامل
    try:
        from backend.workers.models import Worker
        worker = Worker.objects.get(id=worker_id)
    except Worker.DoesNotExist:
        messages.error(request, 'العامل غير موجود')
        return redirect('workers:worker_list')

    # الحصول على المستندات المرتبطة بالعامل
    worker_documents = Document.objects.filter(
        entity__icontains=f'Worker: {worker.first_name} {worker.last_name}'
    )

    # تحضير متغيرات التاريخ للتحقق من انتهاء الصلاحية
    today = timezone.now().date()
    # تحذير قبل 30 يوم من انتهاء الصلاحية
    expiry_warning = today + timezone.timedelta(days=30)

    if request.method == 'POST':
        form = DocumentAttachmentForm(request.POST, request.FILES)
        if form.is_valid():
            # إنشاء مستند جديد للعامل
            document = Document.objects.create(
                document_number=f'WORKER-DOC-{worker.id}-{timezone.now().strftime("%Y%m%d%H%M%S")}',
                document_type='worker_document',
                document_date=timezone.now().date(),
                entity=f'Worker: {worker.first_name} {worker.last_name}',
                subject=form.cleaned_data.get('description') or f'مستند {form.cleaned_data.get("attachment_type")} للعامل {worker.first_name} {worker.last_name}',
                status='active',
                created_by=request.user
            )

            # إنشاء مرفق للمستند
            attachment = form.save(commit=False)
            attachment.document = document
            attachment.uploaded_by = request.user
            attachment.save()

            # تسجيل النشاط
            Activity.objects.create(
                description=f'تم رفع مستند للعامل: {worker.first_name} {worker.last_name}',
                activity_type='worker_document_uploaded',
                date_created=timezone.now()
            )

            messages.success(request, 'تم رفع المستند بنجاح')
            return redirect(f'{request.path}?worker={worker.id}')
    else:
        form = DocumentAttachmentForm()

    return render(request, 'documents/document_upload.html', {
        'page_title': f'رفع مستند للعامل: {worker.first_name} {worker.last_name}',
        'form': form,
        'worker': worker,
        'today': today,
        'expiry_warning': expiry_warning,
        'worker_documents': worker_documents
    })

@login_required
def add_document_attachment(request, document_id):
    """View function for adding an attachment to a document."""
    document = get_object_or_404(Document, id=document_id)

    if request.method == 'POST':
        form = DocumentAttachmentForm(request.POST, request.FILES)
        if form.is_valid():
            attachment = form.save(commit=False)
            attachment.document = document
            attachment.uploaded_by = request.user
            attachment.save()

            # Log activity
            Activity.objects.create(
                description=f'تم إضافة مرفق للمعاملة: {document.document_number}',
                activity_type='document_attachment_added',
                date_created=timezone.now()
            )

            messages.success(request, 'تم إضافة المرفق بنجاح')
            return redirect('documents:document_detail', document_id=document.id)
    else:
        form = DocumentAttachmentForm()

    return render(request, 'documents/add_document_attachment.html', {
        'page_title': f'إضافة مرفق للمعاملة: {document.document_number}',
        'form': form,
        'document': document
    })

@login_required
def delete_document_attachment(request, document_id, attachment_id):
    """View function for deleting a document attachment."""
    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('documents:document_list')

    try:
        document = get_object_or_404(Document, id=document_id)
        attachment = get_object_or_404(DocumentAttachment, id=attachment_id, document=document)
    except:
        messages.error(request, 'لم يتم العثور على المستند أو المرفق')
        # العودة إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('documents:document_list')

    # تحقق مما إذا كان المستند مرتبط بعامل
    is_worker_document = document.document_type == 'worker_document' and document.entity.startswith('Worker:')

    # استخراج معرف العامل من اسم الكيان إذا كان مستند عامل
    worker_id = None
    if is_worker_document:
        try:
            from backend.workers.models import Worker
            # البحث عن العامل باستخدام الاسم في الكيان
            worker_name = document.entity.replace('Worker:', '').strip()
            worker = Worker.objects.filter(
                first_name__in=[part.strip() for part in worker_name.split()],
                last_name__in=[part.strip() for part in worker_name.split()]
            ).first()
            if worker:
                worker_id = worker.id
        except:
            pass

    # محاولة الحصول على معرف العامل من الـ GET parameters إذا لم يتم العثور عليه
    if not worker_id:
        worker_id = request.GET.get('worker_id')

    # حفظ الصفحة السابقة (HTTP_REFERER) للعودة إليها بعد الحذف
    referer = request.META.get('HTTP_REFERER')

    # حذف المرفق
    attachment.delete()

    # Log activity
    Activity.objects.create(
        description=f'تم حذف مرفق من المعاملة: {document.document_number}',
        activity_type='document_attachment_deleted',
        date_created=timezone.now()
    )

    messages.success(request, 'تم حذف المرفق بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    if referer:
        return redirect(referer)

    # إذا لم تكن الصفحة السابقة متوفرة، استخدم التوجيه الافتراضي
    if is_worker_document and worker_id:
        return redirect('workers:worker_detail', worker_id=worker_id)
    else:
        # في حالة عدم وجود معرف العامل، العودة إلى صفحة المستندات
        return redirect('documents:document_list')
