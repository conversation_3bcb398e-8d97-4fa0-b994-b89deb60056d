import React, { createContext, useState, useEffect } from 'react';

// إنشاء سياق السمة
export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  // حالة السمة (فاتح/داكن)
  const [darkMode, setDarkMode] = useState(false);
  
  // حالة الشريط الجانبي (مفتوح/مغلق)
  const [sidebarOpen, setSidebarOpen] = useState(true);
  
  // حجم الخط
  const [fontSize, setFontSize] = useState('medium'); // small, medium, large
  
  // تحميل تفضيلات المستخدم من التخزين المحلي عند بدء التشغيل
  useEffect(() => {
    const storedDarkMode = localStorage.getItem('darkMode') === 'true';
    const storedSidebarOpen = localStorage.getItem('sidebarOpen') !== 'false';
    const storedFontSize = localStorage.getItem('fontSize') || 'medium';
    
    setDarkMode(storedDarkMode);
    setSidebarOpen(storedSidebarOpen);
    setFontSize(storedFontSize);
    
    // تطبيق السمة الداكنة على عنصر html إذا كانت مفعلة
    if (storedDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // تطبيق حجم الخط
    document.documentElement.setAttribute('data-font-size', storedFontSize);
  }, []);
  
  // تبديل وضع السمة الداكنة
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };
  
  // تبديل حالة الشريط الجانبي
  const toggleSidebar = () => {
    const newSidebarOpen = !sidebarOpen;
    setSidebarOpen(newSidebarOpen);
    localStorage.setItem('sidebarOpen', newSidebarOpen.toString());
  };
  
  // تعيين حجم الخط
  const changeFontSize = (size) => {
    setFontSize(size);
    localStorage.setItem('fontSize', size);
    document.documentElement.setAttribute('data-font-size', size);
  };
  
  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    darkMode,
    toggleDarkMode,
    sidebarOpen,
    toggleSidebar,
    fontSize,
    changeFontSize
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
