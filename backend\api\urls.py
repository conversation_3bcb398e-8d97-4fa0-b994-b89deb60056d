from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework import permissions
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from .views import api_root, verify_database

# إنشاء توثيق Swagger/OpenAPI
schema_view = get_schema_view(
    openapi.Info(
        title=_("نظام إدارة العمالة API"),
        default_version='v1',
        description=_("واجهة برمجة التطبيقات لنظام إدارة العمالة"),
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.IsAuthenticated,),
)

# إنشاء الموجه الرئيسي
router = DefaultRouter()

# تم نقل دالة الصفحة الرئيسية للـ API إلى ملف views.py

# تعريف مسارات API
urlpatterns = [
    # الصفحة الرئيسية للـ API
    path('', api_root, name='api-root'),

    # مسارات API الرئيسية
    path('', include(router.urls)),

    # التحقق من قاعدة البيانات
    path('database/verify', verify_database, name='verify-database'),

    # توثيق API
    path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

# إضافة مسارات إضافية في وضع التطوير
if settings.DEBUG:
    urlpatterns += [
        # مسارات للاختبار والتطوير
        path('api-auth/', include('rest_framework.urls')),
    ]
