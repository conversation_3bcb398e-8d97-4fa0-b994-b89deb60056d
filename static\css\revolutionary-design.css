/* نظام التصميم الثوري - مستوى إبداعي متقدم */

/* متغيرات CSS ثورية */
:root {
    /* ألوان ديناميكية متطورة */
    --color-quantum-blue: #0066ff;
    --color-quantum-purple: #6600ff;
    --color-quantum-pink: #ff0066;
    --color-quantum-cyan: #00ffff;
    --color-quantum-green: #00ff66;
    --color-quantum-orange: #ff6600;
    --color-quantum-red: #ff0033;
    --color-quantum-yellow: #ffff00;
    
    /* تدرجات هولوجرافية */
    --gradient-holographic: linear-gradient(45deg, #ff0066, #6600ff, #0066ff, #00ffff, #00ff66, #ffff00, #ff6600);
    --gradient-aurora-borealis: linear-gradient(135deg, #00ff88 0%, #0099ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%);
    --gradient-cosmic: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460, #533483);
    --gradient-neon: linear-gradient(90deg, #ff006e, #8338ec, #3a86ff, #06ffa5);
    --gradient-plasma: linear-gradient(135deg, #ff006e 0%, #fb5607 25%, #ffbe0b 50%, #8338ec 75%, #3a86ff 100%);
    --gradient-matrix: linear-gradient(180deg, #00ff41 0%, #00d4aa 50%, #0099ff 100%);
    
    /* ظلال متعددة الأبعاد */
    --shadow-quantum: 0 0 20px rgba(102, 0, 255, 0.5), 0 0 40px rgba(255, 0, 102, 0.3), 0 0 60px rgba(0, 255, 255, 0.2);
    --shadow-holographic: 0 8px 32px rgba(255, 0, 102, 0.3), 0 16px 64px rgba(102, 0, 255, 0.2), 0 24px 96px rgba(0, 255, 255, 0.1);
    --shadow-neon-glow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 40px currentColor, 0 0 80px currentColor;
    --shadow-depth: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24), 0 3px 6px rgba(0,0,0,0.16), 0 10px 20px rgba(0,0,0,0.19), 0 15px 30px rgba(0,0,0,0.25);
    
    /* انتقالات متقدمة */
    --transition-quantum: all 0.8s cubic-bezier(0.23, 1, 0.320, 1);
    --transition-elastic: all 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-morphing: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    
    /* متغيرات الحركة المتقدمة */
    --animation-speed-slow: 3s;
    --animation-speed-normal: 1.5s;
    --animation-speed-fast: 0.8s;
    --animation-speed-lightning: 0.3s;
}

/* إعدادات عامة ثورية */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    overflow-x: hidden;
}

body {
    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    direction: rtl;
    background: var(--gradient-cosmic);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
}

/* خلفية متحركة ثلاثية الأبعاد */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 0, 102, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(102, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 255, 0.1) 0%, transparent 50%);
    animation: cosmic-drift var(--animation-speed-slow) ease-in-out infinite alternate;
    pointer-events: none;
    z-index: -1;
}

/* نظام البطاقات الثوري */
.card-revolutionary {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    box-shadow: var(--shadow-holographic);
    transition: var(--transition-quantum);
    position: relative;
    overflow: hidden;
    will-change: transform, box-shadow;
}

.card-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-holographic);
    opacity: 0;
    transition: var(--transition-quantum);
}

.card-revolutionary::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: var(--transition-quantum);
    animation: rotate 4s linear infinite;
}

.card-revolutionary:hover {
    transform: translateY(-20px) scale(1.05) rotateX(5deg);
    box-shadow: var(--shadow-quantum);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-revolutionary:hover::before {
    opacity: 1;
}

.card-revolutionary:hover::after {
    opacity: 1;
}

/* نظام الأزرار الثوري */
.btn-revolutionary {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 16px;
    cursor: pointer;
    transition: var(--transition-quantum);
    overflow: hidden;
    text-decoration: none;
    user-select: none;
    will-change: transform;
    z-index: 1;
}

.btn-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-holographic);
    background-size: 400% 400%;
    opacity: 0;
    transition: var(--transition-quantum);
    animation: gradient-shift 3s ease infinite;
    z-index: -1;
}

.btn-revolutionary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    transition: var(--transition-elastic);
    transform: translate(-50%, -50%);
    border-radius: 50%;
    z-index: -1;
}

.btn-revolutionary:hover {
    transform: translateY(-8px) scale(1.1);
    box-shadow: var(--shadow-neon-glow);
}

.btn-revolutionary:hover::before {
    opacity: 1;
}

.btn-revolutionary:active::after {
    width: 300px;
    height: 300px;
}

/* أنواع الأزرار */
.btn-quantum {
    background: var(--gradient-neon);
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.btn-holographic {
    background: var(--gradient-holographic);
    color: white;
    animation: holographic-shift 2s ease-in-out infinite;
}

.btn-plasma {
    background: var(--gradient-plasma);
    color: white;
    box-shadow: var(--shadow-quantum);
}

.btn-matrix {
    background: var(--gradient-matrix);
    color: #000;
    font-weight: 700;
}

/* تأثيرات الحركة الثورية */
@keyframes cosmic-drift {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    100% { transform: translateX(20px) translateY(-20px) rotate(1deg); }
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes holographic-shift {
    0%, 100% { filter: hue-rotate(0deg); }
    25% { filter: hue-rotate(90deg); }
    50% { filter: hue-rotate(180deg); }
    75% { filter: hue-rotate(270deg); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes quantum-pulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 0, 102, 0.7);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(255, 0, 102, 0);
    }
}

@keyframes morphing-float {
    0%, 100% { 
        transform: translateY(0px) rotateX(0deg) rotateY(0deg);
        border-radius: 24px;
    }
    25% { 
        transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
        border-radius: 32px 24px 32px 24px;
    }
    50% { 
        transform: translateY(-20px) rotateX(0deg) rotateY(5deg);
        border-radius: 40px;
    }
    75% { 
        transform: translateY(-10px) rotateX(-5deg) rotateY(-2deg);
        border-radius: 24px 32px 24px 32px;
    }
}

@keyframes neon-glow {
    0%, 100% { 
        text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    }
    50% { 
        text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor, 0 0 40px currentColor;
    }
}

@keyframes data-stream {
    0% { transform: translateY(100vh) scaleY(0); }
    100% { transform: translateY(-100vh) scaleY(1); }
}

@keyframes hologram-flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
    75% { opacity: 0.9; }
}

/* فئات الحركة الثورية */
.animate-quantum-pulse { animation: quantum-pulse 2s infinite; }
.animate-morphing-float { animation: morphing-float 4s ease-in-out infinite; }
.animate-neon-glow { animation: neon-glow 2s ease-in-out infinite; }
.animate-hologram-flicker { animation: hologram-flicker 3s ease-in-out infinite; }
.animate-cosmic-drift { animation: cosmic-drift 6s ease-in-out infinite alternate; }

/* تأثيرات التحويم الثورية */
.hover-quantum {
    transition: var(--transition-quantum);
}

.hover-quantum:hover {
    transform: translateY(-15px) scale(1.05) rotateX(10deg);
    box-shadow: var(--shadow-quantum);
    filter: brightness(1.2) saturate(1.3);
}

.hover-holographic {
    transition: var(--transition-morphing);
}

.hover-holographic:hover {
    background: var(--gradient-holographic);
    background-size: 400% 400%;
    animation: gradient-shift 1s ease infinite;
    transform: translateZ(20px);
}

.hover-morphing {
    transition: var(--transition-elastic);
}

.hover-morphing:hover {
    border-radius: 50% 20% 50% 20%;
    transform: rotate(5deg) scale(1.1);
}

/* نظام النصوص الثوري */
.text-quantum {
    background: var(--gradient-neon);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    animation: gradient-shift 3s ease infinite;
}

.text-holographic {
    background: var(--gradient-holographic);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    animation: holographic-shift 2s ease-in-out infinite;
}

.text-neon {
    color: var(--color-quantum-cyan);
    animation: neon-glow 2s ease-in-out infinite;
    font-weight: 700;
}

.text-glitch {
    position: relative;
    color: white;
    font-weight: 700;
}

.text-glitch::before,
.text-glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.text-glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--color-quantum-pink);
    z-index: -1;
}

.text-glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--color-quantum-cyan);
    z-index: -2;
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(2px, -2px); }
    40% { transform: translate(2px, 2px); }
    60% { transform: translate(-2px, -2px); }
    80% { transform: translate(-2px, 2px); }
}

/* تأثيرات الخلفية الثورية */
.background-quantum {
    background: var(--gradient-cosmic);
    position: relative;
}

.background-quantum::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 25% 25%, rgba(255, 0, 102, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(102, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
    animation: cosmic-drift var(--animation-speed-slow) ease-in-out infinite alternate;
    pointer-events: none;
}

/* تحسينات الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسينات الوضع الليلي */
.dark .card-revolutionary {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.05);
}

.dark .btn-revolutionary {
    box-shadow: var(--shadow-quantum);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .card-revolutionary:hover {
        transform: translateY(-10px) scale(1.02);
    }
    
    .btn-revolutionary:hover {
        transform: translateY(-4px) scale(1.05);
    }
    
    .hover-quantum:hover {
        transform: translateY(-8px) scale(1.02);
    }
}
