"""
أمر Django لإنشاء نسخ احتياطية تلقائية للشركات
يمكن تشغيله يدوياً أو جدولته باستخدام cron
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from backend.super_admin.models import Company, SystemSettings
from backend.super_admin.backup_utils import (
    backup_company_database,
    create_comprehensive_backup_for_all_companies,
    clean_old_backups
)
import logging

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'إنشاء نسخ احتياطية شاملة للشركات'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف شركة محددة (اختياري)',
        )
        parser.add_argument(
            '--all-companies',
            action='store_true',
            help='إنشاء نسخة احتياطية لجميع الشركات النشطة',
        )
        parser.add_argument(
            '--clean-old',
            action='store_true',
            help='حذف النسخ الاحتياطية القديمة',
        )
        parser.add_argument(
            '--retention-days',
            type=int,
            default=30,
            help='عدد الأيام للاحتفاظ بالنسخ الاحتياطية (افتراضي: 30)',
        )
        parser.add_argument(
            '--backup-type',
            type=str,
            default='automatic',
            choices=['manual', 'automatic', 'daily', 'weekly', 'monthly'],
            help='نوع النسخة الاحتياطية',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                f'🚀 بدء عملية النسخ الاحتياطي - {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )
        )

        try:
            # تنظيف النسخ الاحتياطية القديمة أولاً
            if options['clean_old']:
                self.stdout.write('🧹 تنظيف النسخ الاحتياطية القديمة...')
                cleaned_count = clean_old_backups(
                    company_id=options.get('company_id'),
                    retention_days=options['retention_days']
                )
                self.stdout.write(
                    self.style.SUCCESS(f'✅ تم حذف {cleaned_count} نسخة احتياطية قديمة')
                )

            # إنشاء نسخ احتياطية جديدة
            if options['all_companies']:
                # نسخ احتياطية لجميع الشركات
                self.stdout.write('📦 إنشاء نسخ احتياطية لجميع الشركات...')
                success_count, failed_count, messages = create_comprehensive_backup_for_all_companies()
                
                for message in messages:
                    if message.startswith('✅'):
                        self.stdout.write(self.style.SUCCESS(message))
                    else:
                        self.stdout.write(self.style.ERROR(message))
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'📊 النتائج: نجح {success_count}, فشل {failed_count}'
                    )
                )

            elif options.get('company_id'):
                # نسخة احتياطية لشركة محددة
                company_id = options['company_id']
                self.stdout.write(f'📦 إنشاء نسخة احتياطية للشركة {company_id}...')
                
                try:
                    company = Company.objects.get(id=company_id)
                    backup, error = backup_company_database(
                        company_id=company_id,
                        backup_type=options['backup_type'],
                        description=f'نسخة احتياطية {options["backup_type"]} - {timezone.now().strftime("%Y-%m-%d %H:%M")}'
                    )
                    
                    if backup:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✅ تم إنشاء نسخة احتياطية للشركة {company.name}'
                            )
                        )
                        self.stdout.write(f'📁 الملف: {backup.file_name}')
                        self.stdout.write(f'📏 الحجم: {backup.file_size / (1024*1024):.2f} MB')
                    else:
                        self.stdout.write(
                            self.style.ERROR(f'❌ فشل في إنشاء النسخة الاحتياطية: {error}')
                        )
                        
                except Company.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'❌ الشركة {company_id} غير موجودة')
                    )

            else:
                # عرض المساعدة إذا لم يتم تحديد خيارات
                self.stdout.write(
                    self.style.WARNING(
                        '⚠️  يرجى تحديد --company-id أو --all-companies'
                    )
                )
                return

            # عرض إحصائيات النسخ الاحتياطية
            self.show_backup_statistics()

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في عملية النسخ الاحتياطي: {str(e)}')
            )
            logger.error(f'Backup command error: {str(e)}')

        self.stdout.write(
            self.style.SUCCESS(
                f'🏁 انتهت عملية النسخ الاحتياطي - {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )
        )

    def show_backup_statistics(self):
        """عرض إحصائيات النسخ الاحتياطية"""
        try:
            from backend.super_admin.models import DatabaseBackup
            
            total_backups = DatabaseBackup.objects.count()
            total_size = sum(backup.file_size for backup in DatabaseBackup.objects.all())
            
            # إحصائيات حسب النوع
            manual_count = DatabaseBackup.objects.filter(backup_type='manual').count()
            automatic_count = DatabaseBackup.objects.filter(backup_type='automatic').count()
            daily_count = DatabaseBackup.objects.filter(backup_type='daily').count()
            weekly_count = DatabaseBackup.objects.filter(backup_type='weekly').count()
            monthly_count = DatabaseBackup.objects.filter(backup_type='monthly').count()

            self.stdout.write('\n📊 إحصائيات النسخ الاحتياطية:')
            self.stdout.write(f'   📦 إجمالي النسخ: {total_backups}')
            self.stdout.write(f'   💾 الحجم الإجمالي: {total_size / (1024*1024*1024):.2f} GB')
            self.stdout.write(f'   🔧 يدوية: {manual_count}')
            self.stdout.write(f'   🤖 تلقائية: {automatic_count}')
            self.stdout.write(f'   📅 يومية: {daily_count}')
            self.stdout.write(f'   📆 أسبوعية: {weekly_count}')
            self.stdout.write(f'   🗓️  شهرية: {monthly_count}')

            # أحدث نسخة احتياطية
            latest_backup = DatabaseBackup.objects.order_by('-created_at').first()
            if latest_backup:
                self.stdout.write(f'   🕐 أحدث نسخة: {latest_backup.created_at.strftime("%Y-%m-%d %H:%M")}')
                self.stdout.write(f'   🏢 الشركة: {latest_backup.company.name}')

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  لا يمكن عرض الإحصائيات: {str(e)}')
            )
