from django.core.management.base import BaseCommand
from backend.super_admin.models import Company


class Command(BaseCommand):
    help = 'تحديث بيانات مدير الشركة للشركات الموجودة'

    def handle(self, *args, **options):
        """تحديث بيانات مدير الشركة للشركات التي لا تحتوي على بيانات مدير"""
        
        # البحث عن الشركات التي لا تحتوي على بيانات مدير
        companies_without_admin = Company.objects.filter(
            admin_username__isnull=True
        ) | Company.objects.filter(
            admin_username__exact=''
        )
        
        updated_count = 0
        
        for company in companies_without_admin:
            # إنشاء بيانات مدير جديدة
            company.admin_username = company.generate_admin_username()
            company.admin_password = company.generate_admin_password()
            company.save()
            
            updated_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'تم تحديث بيانات مدير الشركة: {company.name}'
                    f'\nاسم المستخدم: {company.admin_username}'
                    f'\nكلمة المرور: {company.admin_password}'
                    f'\n{"="*50}'
                )
            )
        
        if updated_count == 0:
            self.stdout.write(
                self.style.WARNING('جميع الشركات تحتوي على بيانات مدير بالفعل')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'تم تحديث بيانات {updated_count} شركة بنجاح'
                )
            )
