<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <!-- Security Headers -->
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">

    <title>{% block title %}المسؤول الأعلى - منصة استقدامي{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% load static %}{% static 'images/favicon.ico' %}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Styles -->
    <style>
        * {
            font-family: 'Tajawal', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(90deg, #1e293b 0%, #334155 100%);
            border-bottom: 3px solid #dc2626;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .security-badge {
            background: linear-gradient(45deg, #dc2626, #ef4444);
            animation: pulse 2s infinite;
        }

        .admin-btn {
            background: linear-gradient(45deg, #1e40af, #3b82f6);
            transition: all 0.3s ease;
        }

        .admin-btn:hover {
            background: linear-gradient(45deg, #1d4ed8, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .danger-zone {
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            border: 2px solid #dc2626;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .protected-area {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
        }

        .admin-card {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.3);
        }

        .admin-card:hover {
            border-color: rgba(239, 68, 68, 0.5);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        /* إصلاح مشكلة الألوان في حقول الإدخال */
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="password"],
        input[type="number"],
        input[type="url"],
        input[type="search"],
        input[type="date"],
        input[type="datetime-local"],
        input[type="time"],
        input[type="month"],
        input[type="week"],
        textarea,
        select {
            color: #1f2937 !important;
            background-color: #ffffff !important;
        }

        input::placeholder,
        textarea::placeholder {
            color: #6b7280 !important;
            opacity: 1 !important;
        }

        input:focus,
        textarea:focus,
        select:focus {
            color: #1f2937 !important;
            background-color: #ffffff !important;
        }

        select option {
            color: #1f2937 !important;
            background-color: #ffffff !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body class="bg-slate-900 text-white min-h-screen">
    <!-- Security Warning Banner -->
    <div class="bg-red-600 text-white text-center py-2 text-sm font-medium">
        <i class="fas fa-shield-alt ml-2"></i>
        منطقة محمية - للمسؤولين المعتمدين فقط - جميع الأنشطة مسجلة ومراقبة
        <i class="fas fa-eye ml-2"></i>
    </div>

    <!-- Admin Header -->
    <header class="admin-header sticky top-0 z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo & Title -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <img src="{% load static %}{% static 'images/logo.webp' %}" alt="استقدامي" class="h-12 w-auto">
                    <div>
                        <h1 class="text-2xl font-bold text-white">منصة استقدامي</h1>
                        <p class="text-red-400 text-sm font-medium">
                            <i class="fas fa-crown ml-1"></i>
                            المسؤول الأعلى
                        </p>
                    </div>
                </div>

                <!-- Security Badge -->
                <div class="security-badge px-4 py-2 rounded-full text-white text-sm font-bold">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    نظام أمان متقدم
                </div>

                <!-- Admin Actions -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- System Status -->
                    <div class="text-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-1 animate-pulse"></div>
                        <span class="text-xs text-gray-300">النظام نشط</span>
                    </div>

                    <!-- User Info -->
                    <div class="text-right">
                        <p class="text-white font-medium">{{ user.username|default:'MUNTADER_WISSAM' }}</p>
                        <p class="text-gray-300 text-xs">مسؤول النظام</p>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Toolbar -->
    <nav class="bg-slate-800 border-b border-slate-700 shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-3">
                <!-- Navigation Menu -->
                <div class="flex items-center space-x-3 space-x-reverse">
                    <a href="{% url 'super_admin:dashboard' %}"
                       class="flex items-center px-3 py-2 rounded-lg text-white hover:bg-slate-700 transition-colors text-sm font-medium {% if request.resolver_match.url_name == 'dashboard' %}bg-slate-700{% endif %}">
                        <i class="fas fa-building ml-2 text-blue-400"></i>
                        <span class="hidden lg:inline">الشركات</span>
                        <span class="lg:hidden">شركات</span>
                    </a>

                    <a href="{% url 'super_admin:database_management' %}"
                       class="flex items-center px-3 py-2 rounded-lg text-white hover:bg-slate-700 transition-colors text-sm font-medium {% if request.resolver_match.url_name == 'database_management' %}bg-slate-700{% endif %}">
                        <i class="fas fa-database ml-2 text-green-400"></i>
                        <span class="hidden lg:inline">قواعد البيانات</span>
                        <span class="lg:hidden">البيانات</span>
                    </a>

                    <a href="{% url 'super_admin:system_logs' %}"
                       class="flex items-center px-3 py-2 rounded-lg text-white hover:bg-slate-700 transition-colors text-sm font-medium {% if request.resolver_match.url_name == 'system_logs' %}bg-slate-700{% endif %}">
                        <i class="fas fa-list-alt ml-2 text-purple-400"></i>
                        <span class="hidden lg:inline">سجل الأحداث</span>
                        <span class="lg:hidden">السجل</span>
                    </a>

                    <!-- Dropdown for Management -->
                    <div class="relative group">
                        <button class="flex items-center px-3 py-2 rounded-lg text-white hover:bg-slate-700 transition-colors text-sm font-medium">
                            <i class="fas fa-cogs ml-2 text-orange-400"></i>
                            <span class="hidden lg:inline">الإدارة</span>
                            <span class="lg:hidden">إدارة</span>
                            <i class="fas fa-chevron-down mr-2 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-1 w-48 bg-slate-800 rounded-lg shadow-lg border border-slate-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <a href="{% url 'super_admin:maintenance_mode' %}" class="block px-4 py-2 text-white hover:bg-slate-700 rounded-t-lg text-sm">
                                <i class="fas fa-tools ml-2 text-yellow-400"></i>
                                وضع الصيانة
                            </a>
                            <a href="{% url 'super_admin:subscription_plans' %}" class="block px-4 py-2 text-white hover:bg-slate-700 text-sm">
                                <i class="fas fa-credit-card ml-2 text-blue-400"></i>
                                خطط الاشتراك
                            </a>
                            <a href="{% url 'super_admin:company_subscriptions' %}" class="block px-4 py-2 text-white hover:bg-slate-700 text-sm">
                                <i class="fas fa-building ml-2 text-green-400"></i>
                                اشتراكات الشركات
                            </a>
                            <a href="{% url 'super_admin:payments_management' %}" class="block px-4 py-2 text-white hover:bg-slate-700 text-sm">
                                <i class="fas fa-dollar-sign ml-2 text-emerald-400"></i>
                                إدارة المدفوعات
                            </a>
                            <a href="{% url 'super_admin:currency_management' %}" class="block px-4 py-2 text-white hover:bg-slate-700 rounded-b-lg text-sm">
                                <i class="fas fa-coins ml-2 text-yellow-400"></i>
                                إدارة العملات
                            </a>
                        </div>
                    </div>

                    <a href="{% url 'super_admin:system_settings' %}"
                       class="flex items-center px-3 py-2 rounded-lg text-white hover:bg-slate-700 transition-colors text-sm font-medium {% if request.resolver_match.url_name == 'system_settings' %}bg-slate-700{% endif %}">
                        <i class="fas fa-cog ml-2 text-gray-400"></i>
                        <span class="hidden lg:inline">الإعدادات</span>
                        <span class="lg:hidden">إعدادات</span>
                    </a>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="showCreateCompanyModal()"
                            class="admin-btn px-3 py-2 rounded-lg text-white font-medium text-sm">
                        <i class="fas fa-plus ml-2"></i>
                        <span class="hidden md:inline">إضافة شركة</span>
                        <span class="md:hidden">إضافة</span>
                    </button>

                    <a href="{% url 'super_admin:super_admin_logout' %}"
                       onclick="return confirm('هل تريد تسجيل الخروج الآمن من النظام؟')"
                       class="bg-red-600 hover:bg-red-700 px-3 py-2 rounded-lg text-white font-medium transition-colors text-sm">
                        <i class="fas fa-sign-out-alt ml-2"></i>
                        <span class="hidden md:inline">خروج آمن</span>
                        <span class="md:hidden">خروج</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Security Notice -->
        <div class="protected-area rounded-xl p-4 mb-6">
            <div class="flex items-center justify-center text-center">
                <div>
                    <i class="fas fa-lock text-3xl text-red-400 mb-2"></i>
                    <h3 class="text-lg font-bold text-white mb-1">منطقة إدارية محمية</h3>
                    <p class="text-gray-300 text-sm">
                        أنت تتعامل مع نظام إدارة حساس - تأكد من صحة جميع العمليات قبل التنفيذ
                    </p>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        {% block content %}
        <div class="text-center py-20">
            <i class="fas fa-database text-6xl text-blue-400 mb-4"></i>
            <h2 class="text-3xl font-bold text-white mb-2">لوحة تحكم المسؤول الأعلى</h2>
            <p class="text-gray-300">مرحباً بك في نظام إدارة منصة استقدامي</p>
        </div>
        {% endblock %}
    </main>

    <!-- Admin Footer -->
    {% include 'components/footer-admin.html' %}

    <!-- Scripts -->
    <script>
        // Security monitoring
        document.addEventListener('DOMContentLoaded', function() {
            // تم تسجيل الوصول بأمان

            // Prevent right-click in production
            {% if not debug %}
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                alert('العمليات محمية في هذه المنطقة');
            });
            {% endif %}

            // Auto-logout warning
            let warningShown = false;
            setTimeout(function() {
                if (!warningShown) {
                    warningShown = true;
                    if (confirm('تحذير أمني: هل تريد الاستمرار في الجلسة؟\n\nسيتم تسجيل الخروج التلقائي خلال 5 دقائق للأمان.')) {
                        // Reset timer
                        warningShown = false;
                    }
                }
            }, 1800000); // 30 minutes
        });

        // Prevent console access in production
        {% if not debug %}
        (function() {
            let devtools = {open: false, orientation: null};
            setInterval(function() {
                if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
                    if (!devtools.open) {
                        devtools.open = true;
                        alert('تم اكتشاف محاولة وصول غير مصرح بها!');
                        window.location.href = '{% url "super_admin:super_admin_logout" %}';
                    }
                }
            }, 500);
        })();
        {% endif %}
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
