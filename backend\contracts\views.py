from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods

from .models import Contract, ContractDocument, ContractPayment, ContractRenewal
from backend.workers.models import Worker
from backend.clients.models import Client


@login_required
def contract_list(request):
    """عرض قائمة العقود"""
    
    # البحث والتصفية
    search_query = request.GET.get('search', '')
    contract_type = request.GET.get('contract_type', '')
    status = request.GET.get('status', '')
    
    contracts = Contract.objects.select_related('worker', 'client').all()
    
    if search_query:
        contracts = contracts.filter(
            Q(contract_number__icontains=search_query) |
            Q(worker__name__icontains=search_query) |
            Q(client__name__icontains=search_query)
        )
    
    if contract_type:
        contracts = contracts.filter(contract_type=contract_type)
        
    if status:
        contracts = contracts.filter(status=status)
    
    # ترتيب النتائج
    contracts = contracts.order_by('-created_at')
    
    # التصفح
    paginator = Paginator(contracts, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # إحصائيات
    stats = {
        'total': Contract.objects.count(),
        'active': Contract.objects.filter(status='active').count(),
        'draft': Contract.objects.filter(status='draft').count(),
        'completed': Contract.objects.filter(status='completed').count(),
        'monthly': Contract.objects.filter(contract_type='monthly').count(),
        'permanent': Contract.objects.filter(contract_type='permanent').count(),
    }
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'contract_type': contract_type,
        'status': status,
        'stats': stats,
        'title': 'إدارة العقود',
        'contract_types': Contract.CONTRACT_TYPE_CHOICES,
        'statuses': Contract.STATUS_CHOICES,
    }
    
    return render(request, 'contracts/contract_list.html', context)


@login_required
def contract_detail(request, contract_id):
    """عرض تفاصيل العقد"""
    
    contract = get_object_or_404(Contract, id=contract_id)
    
    # الحصول على المستندات والمدفوعات
    documents = ContractDocument.objects.filter(contract=contract).order_by('-uploaded_at')
    payments = ContractPayment.objects.filter(contract=contract).order_by('-payment_date')
    renewals = ContractRenewal.objects.filter(original_contract=contract).order_by('-created_at')
    
    context = {
        'contract': contract,
        'documents': documents,
        'payments': payments,
        'renewals': renewals,
        'title': f'تفاصيل العقد - {contract.contract_number}'
    }
    
    return render(request, 'contracts/contract_detail.html', context)


@login_required
def contract_create(request):
    """إضافة عقد جديد"""
    
    if request.method == 'POST':
        # معالجة البيانات المرسلة
        try:
            # إنشاء رقم عقد تلقائي
            last_contract = Contract.objects.order_by('-id').first()
            if last_contract:
                contract_number = f"CON-{int(last_contract.contract_number.split('-')[1]) + 1:06d}"
            else:
                contract_number = "CON-000001"
            
            contract = Contract.objects.create(
                contract_number=contract_number,
                contract_type=request.POST.get('contract_type'),
                worker_id=request.POST.get('worker'),
                client_id=request.POST.get('client'),
                start_date=request.POST.get('start_date'),
                end_date=request.POST.get('end_date') or None,
                salary=request.POST.get('salary'),
                currency=request.POST.get('currency', 'SAR'),
                working_hours_per_day=request.POST.get('working_hours_per_day', 8),
                working_days_per_week=request.POST.get('working_days_per_week', 6),
                terms_and_conditions=request.POST.get('terms_and_conditions', ''),
                notes=request.POST.get('notes', ''),
            )
            
            messages.success(request, f'تم إنشاء العقد {contract.contract_number} بنجاح')
            return redirect('contracts:contract_detail', contract_id=contract.id)
            
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء العقد: {str(e)}')
    
    # الحصول على العمال والعملاء المتاحين
    workers = Worker.objects.filter(status='available')
    clients = Client.objects.filter(status='active')
    
    context = {
        'workers': workers,
        'clients': clients,
        'contract_types': Contract.CONTRACT_TYPE_CHOICES,
        'title': 'إضافة عقد جديد'
    }
    
    return render(request, 'contracts/contract_form.html', context)


@login_required
def contract_edit(request, contract_id):
    """تعديل العقد"""
    
    contract = get_object_or_404(Contract, id=contract_id)
    
    if request.method == 'POST':
        try:
            contract.contract_type = request.POST.get('contract_type')
            contract.status = request.POST.get('status')
            contract.start_date = request.POST.get('start_date')
            contract.end_date = request.POST.get('end_date') or None
            contract.salary = request.POST.get('salary')
            contract.currency = request.POST.get('currency', 'SAR')
            contract.working_hours_per_day = request.POST.get('working_hours_per_day', 8)
            contract.working_days_per_week = request.POST.get('working_days_per_week', 6)
            contract.terms_and_conditions = request.POST.get('terms_and_conditions', '')
            contract.notes = request.POST.get('notes', '')
            contract.save()
            
            messages.success(request, f'تم تحديث العقد {contract.contract_number} بنجاح')
            return redirect('contracts:contract_detail', contract_id=contract.id)
            
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث العقد: {str(e)}')
    
    # الحصول على العمال والعملاء المتاحين
    workers = Worker.objects.all()
    clients = Client.objects.all()
    
    context = {
        'contract': contract,
        'workers': workers,
        'clients': clients,
        'contract_types': Contract.CONTRACT_TYPE_CHOICES,
        'statuses': Contract.STATUS_CHOICES,
        'title': f'تعديل العقد - {contract.contract_number}',
        'action': 'edit'
    }
    
    return render(request, 'contracts/contract_form.html', context)


@login_required
@require_http_methods(["POST"])
def contract_delete(request, contract_id):
    """حذف العقد"""
    
    contract = get_object_or_404(Contract, id=contract_id)
    
    try:
        contract_number = contract.contract_number
        contract.delete()
        messages.success(request, f'تم حذف العقد {contract_number} بنجاح')
        return JsonResponse({'success': True, 'message': 'تم الحذف بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الحذف: {str(e)}'})


@login_required
def contract_dashboard(request):
    """لوحة تحكم العقود"""
    
    # إحصائيات العقود
    total_contracts = Contract.objects.count()
    active_contracts = Contract.objects.filter(status='active').count()
    expired_contracts = Contract.objects.filter(status='expired').count()
    
    # العقود الحديثة
    recent_contracts = Contract.objects.select_related('worker', 'client').order_by('-created_at')[:5]
    
    # العقود المنتهية قريباً
    from datetime import date, timedelta
    upcoming_expiry = Contract.objects.filter(
        end_date__lte=date.today() + timedelta(days=30),
        status='active'
    ).order_by('end_date')[:5]
    
    context = {
        'total_contracts': total_contracts,
        'active_contracts': active_contracts,
        'expired_contracts': expired_contracts,
        'recent_contracts': recent_contracts,
        'upcoming_expiry': upcoming_expiry,
        'title': 'لوحة تحكم العقود'
    }
    
    return render(request, 'contracts/dashboard.html', context)


@login_required
def contract_api_search(request):
    """API للبحث السريع في العقود"""
    
    query = request.GET.get('q', '')
    if len(query) < 2:
        return JsonResponse({'results': []})
    
    contracts = Contract.objects.filter(
        Q(contract_number__icontains=query) |
        Q(worker__name__icontains=query) |
        Q(client__name__icontains=query)
    ).select_related('worker', 'client')[:10]
    
    results = []
    for contract in contracts:
        results.append({
            'id': contract.id,
            'contract_number': contract.contract_number,
            'worker_name': contract.worker.name,
            'client_name': contract.client.name,
            'status': contract.get_status_display(),
            'contract_type': contract.get_contract_type_display()
        })
    
    return JsonResponse({'results': results})
