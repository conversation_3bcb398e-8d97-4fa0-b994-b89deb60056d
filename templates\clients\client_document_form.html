{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}إضافة مستند | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إضافة مستند</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                إضافة مستند جديد للعميل {{ client.name }}
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'clients:client_detail' client.id %}"
               class="bg-gray-500 text-white dark:bg-gray-600 dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-gray-600 hover:shadow-md dark:hover:bg-gray-700 transition-all duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                <span>العودة للعميل</span>
            </a>
        </div>
    </div>

    <!-- Document Form -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700 max-w-2xl mx-auto">
        <div class="border-b border-gray-200 dark:border-slate-700 p-4 bg-gray-50 dark:bg-slate-800">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">نموذج إضافة مستند</h3>
        </div>
        <div class="p-6">
            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <!-- نوع المستند -->
                <div>
                    <label for="id_document_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع المستند <span class="text-red-500">*</span></label>
                    {{ form.document_type }}
                    {% if form.document_type.errors %}
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.document_type.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- الملف -->
                <div>
                    <label for="id_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الملف <span class="text-red-500">*</span></label>
                    <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-6 mb-2">
                        <div class="text-center">
                            <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-3xl mb-3"></i>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">اسحب الملف هنا أو انقر للتصفح</p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 mb-3">PNG, JPG, PDF حتى 10MB</p>
                            {{ form.file }}
                        </div>
                    </div>
                    {% if form.file.errors %}
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.file.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- الوصف -->
                <div>
                    <label for="id_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وصف المستند</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.description.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">وصف اختياري للمستند</p>
                </div>
                
                <!-- تاريخ الانتهاء -->
                <div>
                    <label for="id_expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ انتهاء الصلاحية</label>
                    {{ form.expiry_date }}
                    {% if form.expiry_date.errors %}
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.expiry_date.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">اختياري: أدخل تاريخ انتهاء صلاحية المستند إذا كان ذلك مناسبًا</p>
                </div>
                
                <!-- أزرار الإرسال -->
                <div class="flex justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200 dark:border-slate-700">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors duration-300 flex items-center">
                        <i class="fas fa-save ml-2"></i>
                        حفظ المستند
                    </button>
                    <a href="{% url 'clients:client_detail' client.id %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors duration-300 flex items-center">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين واجهة اختيار الملف
        const fileInput = document.getElementById('id_file');
        const fileContainer = fileInput.closest('div');
        
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                // إضافة اسم الملف المختار
                const fileNameElement = document.createElement('p');
                fileNameElement.className = 'mt-2 text-sm text-blue-600 dark:text-blue-400';
                fileNameElement.innerHTML = '<i class="fas fa-file ml-1"></i> ' + fileName;
                
                // إزالة أي اسم ملف سابق
                const existingFileName = fileContainer.querySelector('.text-blue-600');
                if (existingFileName) {
                    existingFileName.remove();
                }
                
                fileContainer.appendChild(fileNameElement);
            }
        });
    });
</script>
{% endblock %}
