# Generated by Django 5.2 on 2025-05-28 18:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0011_superadminuser_account_locked_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الخطة')),
                ('plan_type', models.CharField(choices=[('basic', 'أساسي'), ('standard', 'قياسي'), ('premium', 'متميز'), ('enterprise', 'مؤسسي')], max_length=20, verbose_name='نوع الخطة')),
                ('description', models.TextField(verbose_name='وصف الخطة')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('billing_cycle', models.CharField(choices=[('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('semi_annual', 'نصف سنوي'), ('annual', 'سنوي')], max_length=20, verbose_name='دورة الفوترة')),
                ('max_users', models.IntegerField(verbose_name='الحد الأقصى للمستخدمين')),
                ('max_workers', models.IntegerField(verbose_name='الحد الأقصى للعمال')),
                ('max_clients', models.IntegerField(verbose_name='الحد الأقصى للعملاء')),
                ('storage_limit_gb', models.IntegerField(verbose_name='حد التخزين (جيجابايت)')),
                ('trial_days', models.IntegerField(default=14, verbose_name='أيام الفترة التجريبية')),
                ('features', models.JSONField(default=dict, verbose_name='الميزات المتاحة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'خطة اشتراك',
                'verbose_name_plural': 'خطط الاشتراك',
                'ordering': ['price'],
            },
        ),
        migrations.CreateModel(
            name='CompanySubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('trial', 'فترة تجريبية'), ('active', 'نشط'), ('expired', 'منتهي الصلاحية'), ('suspended', 'معلق'), ('cancelled', 'ملغي')], default='trial', max_length=20, verbose_name='حالة الاشتراك')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ الانتهاء')),
                ('trial_end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الفترة التجريبية')),
                ('auto_renewal', models.BooleanField(default=True, verbose_name='التجديد التلقائي')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('remaining_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ المتبقي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subscription', to='super_admin.company', verbose_name='الشركة')),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='super_admin.subscriptionplan', verbose_name='خطة الاشتراك')),
            ],
            options={
                'verbose_name': 'اشتراك شركة',
                'verbose_name_plural': 'اشتراكات الشركات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceMode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='وضع الصيانة نشط')),
                ('title', models.CharField(default='النظام تحت الصيانة', max_length=200, verbose_name='عنوان الصيانة')),
                ('message', models.TextField(default='نعتذر، النظام تحت الصيانة حالياً. سيعود للعمل قريباً.', verbose_name='رسالة الصيانة')),
                ('estimated_completion', models.DateTimeField(blank=True, null=True, verbose_name='الوقت المتوقع للانتهاء')),
                ('allowed_ips', models.TextField(blank=True, null=True, verbose_name='عناوين IP المسموح لها بالوصول')),
                ('bypass_for_superusers', models.BooleanField(default=True, verbose_name='السماح للمسؤولين بالوصول')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('activated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التفعيل بواسطة')),
            ],
            options={
                'verbose_name': 'وضع الصيانة',
                'verbose_name_plural': 'وضع الصيانة',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('bank_transfer', 'تحويل بنكي'), ('credit_card', 'بطاقة ائتمان'), ('cash', 'نقدي'), ('check', 'شيك'), ('online', 'دفع إلكتروني')], max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('completed', 'مكتمل'), ('failed', 'فاشل'), ('cancelled', 'ملغي'), ('refunded', 'مسترد')], default='pending', max_length=20, verbose_name='حالة الدفع')),
                ('transaction_id', models.CharField(max_length=100, unique=True, verbose_name='رقم المعاملة')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='الرقم المرجعي')),
                ('payment_date', models.DateTimeField(verbose_name='تاريخ الدفع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('receipt_file', models.FileField(blank=True, null=True, upload_to='receipts/', verbose_name='إيصال الدفع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم المعالجة بواسطة')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='super_admin.companysubscription', verbose_name='الاشتراك')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-payment_date'],
            },
        ),
    ]
