/*
 * Labor Management System - Main Stylesheet
 * Based on the provided Visual Style Guide
 */

:root {
  /* Color Palette - Updated */
  --primary-blue: #143D8D;
  --secondary-blue: #407BFF;
  --white: #FFFFFF;
  --light-bg: #F5F7FA;
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --border-gray: #D1D5DB;
  --input-bg: #F9FAFB;
  --alert-bg: #E5F1FF;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(31, 41, 55, 0.05);
  --shadow-md: 0 4px 8px rgba(31, 41, 55, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(31, 41, 55, 0.1), 0 4px 6px -2px rgba(31, 41, 55, 0.05);
}

/* Base Styles */
body {
  font-family: 'Tajawal', sans-serif;
  color: var(--text-primary);
  background-color: var(--light-bg);
  line-height: 1.5;
  direction: rtl;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Tajawal', sans-serif;
  color: var(--primary-blue);
  margin-bottom: var(--spacing-md);
  font-weight: 700;
}

h1 { font-size: 24px; }
h2 { font-size: 22px; }
h3 { font-size: 20px; }
h4 { font-size: 18px; }
h5 { font-size: 16px; }
h6 { font-size: 14px; }

a {
  color: var(--secondary-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-blue);
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-align: center;
  font-size: 14px;
}

.btn-primary {
  background-color: var(--secondary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-secondary {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: #0c2a66;
  color: var(--white);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-blue);
  color: var(--primary-blue);
}

.btn-outline:hover {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
}

.btn-icon i {
  margin-left: var(--spacing-sm);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.form-control {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-gray);
  background-color: var(--input-bg);
  color: var(--text-primary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--secondary-blue);
  outline: none;
  box-shadow: 0 0 8px rgba(64, 123, 255, 0.3);
}

.form-select {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-gray);
  background-color: var(--input-bg);
  color: var(--text-primary);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236B7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-position: left var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 1.25rem;
  padding-left: 2.5rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  font-size: 14px;
}

.form-select:focus {
  border-color: var(--secondary-blue);
  outline: none;
  box-shadow: 0 0 8px rgba(64, 123, 255, 0.3);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(31, 41, 55, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid var(--light-bg);
  background-color: var(--white);
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 20px;
  border-top: 1px solid var(--light-bg);
  background-color: var(--white);
}

/* Tables */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 10px 15px;
  text-align: right;
  font-size: 14px;
}

.table thead th {
  background-color: var(--light-bg);
  color: var(--primary-blue);
  font-weight: 600;
  border-bottom: 2px solid var(--border-gray);
}

.table tbody tr {
  border-bottom: 1px solid var(--light-bg);
}

.table tbody tr:nth-child(even) {
  background-color: rgba(245, 247, 250, 0.5);
}

.table tbody tr:hover {
  background-color: rgba(64, 123, 255, 0.05);
}

/* Sidebar */
.sidebar {
  background-color: var(--primary-blue);
  color: var(--white);
  min-height: 100vh;
  width: 250px;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-item {
  margin-bottom: 2px;
}

.sidebar-link {
  display: block;
  padding: 10px 15px;
  color: var(--white);
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.sidebar-link:hover, .sidebar-link.active {
  background-color: var(--secondary-blue);
  color: var(--white);
}

.sidebar-header {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 0.6);
  padding: 0 15px;
}

/* Alerts */
.alert {
  background-color: var(--alert-bg);
  color: var(--primary-blue);
  border-radius: 8px;
  padding: 10px 15px;
  margin-bottom: 15px;
  font-size: 14px;
}

.alert-danger {
  background-color: #FEE2E2;
  color: #DC2626;
}

.alert-success {
  background-color: #D1FAE5;
  color: #059669;
}

.alert-warning {
  background-color: #FEF3C7;
  color: #D97706;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .hidden-sm {
    display: none;
  }
}
