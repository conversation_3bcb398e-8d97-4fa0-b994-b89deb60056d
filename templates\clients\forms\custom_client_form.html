<!-- نموذج العميل المخصص -->
<div class="space-y-6">
    <!-- معلومات العميل الأساسية -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-orange-500 pr-3">المعلومات الأساسية</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- الاسم -->
            <div>
                <label for="id_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم <span class="text-red-500">*</span></label>
                {{ form.name }}
                {% if form.name.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- رقم البطاقة الوطنية -->
            <div>
                <label for="id_national_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم البطاقة الوطنية</label>
                {{ form.national_id }}
                {% if form.national_id.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.national_id.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- الحالة -->
            <div>
                <label for="id_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.status.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- معلومات الاتصال -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-orange-500 pr-3">معلومات الاتصال</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- رقم الهاتف 1 -->
            <div>
                <label for="id_phone_1" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف 1 <span class="text-red-500">*</span></label>
                {{ form.phone_1 }}
                {% if form.phone_1.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.phone_1.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- رقم الهاتف 2 -->
            <div>
                <label for="id_phone_2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف 2</label>
                {{ form.phone_2 }}
                {% if form.phone_2.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.phone_2.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- العنوان والموقع -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-orange-500 pr-3">العنوان والموقع</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- العنوان النصي -->
            <div>
                <label for="id_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العنوان <span class="text-red-500">*</span></label>
                {{ form.address }}
                {% if form.address.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.address.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- رابط الموقع على خرائط Google -->
            <div>
                <label for="id_map_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رابط الموقع على خرائط Google</label>
                {{ form.map_url }}
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">انسخ رابط الموقع من خرائط Google والصقه هنا</p>
            </div>
        </div>

        <!-- حقول الإحداثيات المخفية -->
        <div class="hidden">
            {{ form.location_lat }}
            {{ form.location_lng }}
        </div>

        <!-- عرض معلومات الموقع والخريطة -->
        <div id="location-info-container" class="mt-4 hidden">
            <div class="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-700 p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-800 dark:text-white text-lg" id="location-name">اسم المنطقة</h4>
                    <div class="flex space-x-2 space-x-reverse">
                        <button type="button" id="refresh-location" onclick="event.preventDefault(); event.stopPropagation(); return false;" class="px-3 py-1 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800 transition-colors duration-300 text-sm flex items-center">
                            <i class="fas fa-location-arrow ml-1"></i>
                            تحديث موقعي
                        </button>
                        <a href="#" id="open-in-google-maps" target="_blank" class="px-3 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300 text-sm flex items-center">
                            <i class="fas fa-map-marked-alt ml-1"></i>
                            فتح في خرائط Google
                        </a>
                        <a href="#" id="open-in-waze" target="_blank" class="px-3 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300 text-sm flex items-center">
                            <i class="fab fa-waze ml-1"></i>
                            فتح في Waze
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">العنوان</div>
                        <div class="font-medium text-gray-800 dark:text-white" id="location-address">-</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">المسافة التقريبية</div>
                        <div class="font-medium text-gray-800 dark:text-white" id="location-distance">-</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">وقت الوصول التقريبي</div>
                        <div class="font-medium text-gray-800 dark:text-white" id="location-duration">-</div>
                    </div>
                </div>

                <div id="map" class="w-full h-80 rounded-lg border border-gray-300 dark:border-slate-600"></div>
            </div>
        </div>

        <!-- تعليمات استخدام خرائط Google -->
        <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 class="font-medium text-blue-700 dark:text-blue-300 mb-2">كيفية الحصول على رابط الموقع من خرائط Google:</h4>
            <ol class="list-decimal list-inside text-sm text-gray-700 dark:text-gray-300 space-y-2">
                <li>افتح <a href="https://maps.google.com" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">خرائط Google</a> في نافذة جديدة</li>
                <li>ابحث عن الموقع المطلوب أو حدده على الخريطة</li>
                <li>انقر على الموقع بزر الفأرة الأيمن واختر "ما هذا المكان؟" أو انقر مع الاستمرار على الهاتف</li>
                <li>انقر على الإحداثيات التي تظهر في أسفل الشاشة</li>
                <li>انسخ الرابط من شريط العنوان والصقه في الحقل أعلاه</li>
            </ol>
        </div>
    </div>

    <!-- ملاحظات إضافية -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-orange-500 pr-3">ملاحظات إضافية</h3>

        <div>
            <label for="id_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
            {{ form.notes }}
            {% if form.notes.errors %}
            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.notes.errors.0 }}</p>
            {% endif %}
        </div>
    </div>

    <!-- المستندات المطلوبة -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-orange-500 pr-3">المستندات المطلوبة</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- البطاقة الوطنية (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">البطاقة الوطنية (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_national_id_front" class="hidden" id="document_national_id_front">
                        <button type="button" onclick="document.getElementById('document_national_id_front').click()" class="mt-2 px-4 py-2 bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_national_id_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- البطاقة الوطنية (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">البطاقة الوطنية (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_national_id_back" class="hidden" id="document_national_id_back">
                        <button type="button" onclick="document.getElementById('document_national_id_back').click()" class="mt-2 px-4 py-2 bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_national_id_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- بطاقة السكن (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">بطاقة السكن (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_residence_card_front" class="hidden" id="document_residence_card_front">
                        <button type="button" onclick="document.getElementById('document_residence_card_front').click()" class="mt-2 px-4 py-2 bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_residence_card_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- بطاقة السكن (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">بطاقة السكن (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_residence_card_back" class="hidden" id="document_residence_card_back">
                        <button type="button" onclick="document.getElementById('document_residence_card_back').click()" class="mt-2 px-4 py-2 bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_residence_card_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>
        </div>
    </div>
</div>
