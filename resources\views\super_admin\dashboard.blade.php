@extends('layouts.master_admin')

@section('title', 'لوحة التحكم | المسؤول الأعلى')

@section('page_title', 'لوحة التحكم')
@section('page_subtitle', 'نظرة عامة على النظام')

@section('extra_css')
<style>
    .stat-card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 transition-all duration-300;
    }
    
    .stat-card:hover {
        @apply shadow-lg transform -translate-y-1;
    }
    
    .stat-icon {
        @apply w-12 h-12 rounded-full flex items-center justify-center text-white text-xl;
    }
    
    .stat-value {
        @apply text-2xl font-bold mt-2;
    }
    
    .stat-label {
        @apply text-gray-500 dark:text-gray-400 text-sm;
    }
    
    .activity-item {
        @apply flex items-start p-3 border-b border-gray-200 dark:border-gray-700;
    }
    
    .activity-icon {
        @apply w-8 h-8 rounded-full flex items-center justify-center text-white ml-3;
    }
</style>
@endsection

@section('content')
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- إحصائية الشركات -->
    <div class="stat-card">
        <div class="flex justify-between items-start">
            <div>
                <div class="stat-value text-admin-primary dark:text-admin-accent">{{ $stats['companies_count'] ?? 0 }}</div>
                <div class="stat-label">الشركات</div>
            </div>
            <div class="stat-icon bg-admin-primary">
                <i class="fas fa-building"></i>
            </div>
        </div>
        <div class="mt-4 text-xs">
            <span class="text-green-500"><i class="fas fa-arrow-up"></i> {{ $stats['active_companies'] ?? 0 }}</span> نشطة
            <span class="text-red-500 mr-2"><i class="fas fa-arrow-down"></i> {{ $stats['inactive_companies'] ?? 0 }}</span> غير نشطة
        </div>
    </div>
    
    <!-- إحصائية قواعد البيانات -->
    <div class="stat-card">
        <div class="flex justify-between items-start">
            <div>
                <div class="stat-value text-admin-secondary dark:text-admin-accent">{{ $stats['databases_count'] ?? 0 }}</div>
                <div class="stat-label">قواعد البيانات</div>
            </div>
            <div class="stat-icon bg-admin-secondary">
                <i class="fas fa-database"></i>
            </div>
        </div>
        <div class="mt-4 text-xs">
            <span class="text-blue-500"><i class="fas fa-server"></i> {{ $stats['total_db_size'] ?? '0 MB' }}</span> الحجم الإجمالي
        </div>
    </div>
    
    <!-- إحصائية النسخ الاحتياطي -->
    <div class="stat-card">
        <div class="flex justify-between items-start">
            <div>
                <div class="stat-value text-admin-accent dark:text-admin-accent">{{ $stats['backups_count'] ?? 0 }}</div>
                <div class="stat-label">النسخ الاحتياطية</div>
            </div>
            <div class="stat-icon bg-admin-accent">
                <i class="fas fa-save"></i>
            </div>
        </div>
        <div class="mt-4 text-xs">
            <span class="text-gray-500"><i class="fas fa-clock"></i> {{ $stats['last_backup'] ?? 'لا يوجد' }}</span> آخر نسخة
        </div>
    </div>
    
    <!-- إحصائية الأحداث -->
    <div class="stat-card">
        <div class="flex justify-between items-start">
            <div>
                <div class="stat-value text-gray-700 dark:text-gray-300">{{ $stats['logs_count'] ?? 0 }}</div>
                <div class="stat-label">سجل الأحداث</div>
            </div>
            <div class="stat-icon bg-gray-700 dark:bg-gray-600">
                <i class="fas fa-history"></i>
            </div>
        </div>
        <div class="mt-4 text-xs">
            <span class="text-yellow-500"><i class="fas fa-exclamation-triangle"></i> {{ $stats['warnings_count'] ?? 0 }}</span> تحذيرات
            <span class="text-red-500 mr-2"><i class="fas fa-times-circle"></i> {{ $stats['errors_count'] ?? 0 }}</span> أخطاء
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- آخر الشركات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="bg-admin-primary dark:bg-admin-dark text-white p-4 flex justify-between items-center">
            <h3 class="font-bold">آخر الشركات</h3>
            <a href="{{ route('super_admin.companies') }}" class="text-xs text-admin-accent hover:underline">عرض الكل</a>
        </div>
        <div class="p-4">
            @if(isset($latest_companies) && count($latest_companies) > 0)
                <div class="space-y-3">
                    @foreach($latest_companies as $company)
                    <div class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="w-10 h-10 rounded-full bg-admin-light flex items-center justify-center ml-3">
                            <i class="fas fa-building text-admin-primary"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">{{ $company->name }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $company->created_at->format('Y-m-d') }}</div>
                        </div>
                        <div>
                            <span class="px-2 py-1 text-xs rounded-full {{ $company->status == 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $company->status == 'active' ? 'نشطة' : 'غير نشطة' }}
                            </span>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-info-circle text-2xl mb-2"></i>
                    <p>لا توجد شركات حتى الآن</p>
                </div>
            @endif
        </div>
    </div>
    
    <!-- آخر النسخ الاحتياطية -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="bg-admin-secondary dark:bg-admin-dark text-white p-4 flex justify-between items-center">
            <h3 class="font-bold">آخر النسخ الاحتياطية</h3>
            <a href="{{ route('super_admin.backup') }}" class="text-xs text-admin-accent hover:underline">عرض الكل</a>
        </div>
        <div class="p-4">
            @if(isset($latest_backups) && count($latest_backups) > 0)
                <div class="space-y-3">
                    @foreach($latest_backups as $backup)
                    <div class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="w-10 h-10 rounded-full bg-admin-light flex items-center justify-center ml-3">
                            <i class="fas fa-save text-admin-secondary"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">{{ $backup->company ? $backup->company->name : 'النظام' }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $backup->created_at->format('Y-m-d H:i') }}</div>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">{{ $backup->size_formatted }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-info-circle text-2xl mb-2"></i>
                    <p>لا توجد نسخ احتياطية حتى الآن</p>
                </div>
            @endif
        </div>
    </div>
    
    <!-- آخر الأحداث -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="bg-admin-accent dark:bg-admin-dark text-white p-4 flex justify-between items-center">
            <h3 class="font-bold">آخر الأحداث</h3>
            <a href="{{ route('super_admin.logs') }}" class="text-xs text-admin-light hover:underline">عرض الكل</a>
        </div>
        <div class="p-4">
            @if(isset($latest_logs) && count($latest_logs) > 0)
                <div class="space-y-3">
                    @foreach($latest_logs as $log)
                    <div class="activity-item">
                        <div class="activity-icon 
                            {{ $log->action == 'login' ? 'bg-green-500' : 
                               $log->action == 'logout' ? 'bg-blue-500' : 
                               $log->action == 'create' ? 'bg-purple-500' : 
                               $log->action == 'update' ? 'bg-yellow-500' : 
                               $log->action == 'delete' ? 'bg-red-500' : 'bg-gray-500' }}">
                            <i class="fas 
                                {{ $log->action == 'login' ? 'fa-sign-in-alt' : 
                                   $log->action == 'logout' ? 'fa-sign-out-alt' : 
                                   $log->action == 'create' ? 'fa-plus' : 
                                   $log->action == 'update' ? 'fa-edit' : 
                                   $log->action == 'delete' ? 'fa-trash' : 'fa-info-circle' }}"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">{{ $log->user ? $log->user->username : 'النظام' }}</div>
                            <div class="text-sm">{{ $log->description }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $log->created_at->format('Y-m-d H:i') }}</div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-info-circle text-2xl mb-2"></i>
                    <p>لا توجد أحداث حتى الآن</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('extra_js')
<script>
    // يمكن إضافة أي سكربت خاص بالصفحة هنا
    document.addEventListener('DOMContentLoaded', function() {
        // مثال: عرض رسالة ترحيب
        showToast('مرحباً بك في لوحة تحكم المسؤول الأعلى', 'success');
    });
</script>
@endsection
