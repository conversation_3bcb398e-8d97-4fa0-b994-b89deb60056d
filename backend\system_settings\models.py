from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User, Group, Permission

class SystemSetting(models.Model):
    """Model for storing system-wide settings"""
    key = models.CharField(max_length=100, unique=True, verbose_name='المفتاح')
    value = models.TextField(verbose_name='القيمة')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    category = models.CharField(max_length=50, default='general', verbose_name='الفئة')
    is_active = models.BooleanField(default=True, verbose_name='مفعل')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.key}: {self.value}"

    class Meta:
        verbose_name = 'إعداد النظام'
        verbose_name_plural = 'إعدادات النظام'
        ordering = ['category', 'key']

class UserProfile(models.Model):
    """Extended user profile information"""
    ROLE_CHOICES = (
        ('admin', 'مدير النظام'),
        ('manager', 'مدير'),
        ('supervisor', 'مشرف'),
        ('staff', 'موظف'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name='المستخدم')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='staff', verbose_name='الدور')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True, verbose_name='الصورة الشخصية')
    last_login_ip = models.GenericIPAddressField(blank=True, null=True, verbose_name='IP آخر تسجيل دخول')
    is_active = models.BooleanField(default=True, verbose_name='مفعل')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"

    class Meta:
        verbose_name = 'ملف المستخدم'
        verbose_name_plural = 'ملفات المستخدمين'

class UserPermission(models.Model):
    """Custom user permissions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_permissions', verbose_name='المستخدم')
    module = models.CharField(max_length=50, verbose_name='الوحدة')
    can_view = models.BooleanField(default=False, verbose_name='يمكن العرض')
    can_add = models.BooleanField(default=False, verbose_name='يمكن الإضافة')
    can_edit = models.BooleanField(default=False, verbose_name='يمكن التعديل')
    can_delete = models.BooleanField(default=False, verbose_name='يمكن الحذف')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.user.username} - {self.module}"

    class Meta:
        verbose_name = 'صلاحية المستخدم'
        verbose_name_plural = 'صلاحيات المستخدمين'
        unique_together = ('user', 'module')

class WorkerSetting(models.Model):
    """Settings for worker management"""
    setting_key = models.CharField(max_length=100, unique=True, verbose_name='مفتاح الإعداد')
    setting_value = models.TextField(verbose_name='قيمة الإعداد')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    is_active = models.BooleanField(default=True, verbose_name='مفعل')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.setting_key}: {self.setting_value}"

    class Meta:
        verbose_name = 'إعداد العمال'
        verbose_name_plural = 'إعدادات العمال'

class NotificationSetting(models.Model):
    """Settings for system notifications"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notification_settings', verbose_name='المستخدم')
    notification_type = models.CharField(max_length=50, verbose_name='نوع الإشعار')
    email_enabled = models.BooleanField(default=True, verbose_name='تفعيل البريد الإلكتروني')
    sms_enabled = models.BooleanField(default=False, verbose_name='تفعيل الرسائل القصيرة')
    in_app_enabled = models.BooleanField(default=True, verbose_name='تفعيل إشعارات التطبيق')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.user.username} - {self.notification_type}"

    class Meta:
        verbose_name = 'إعداد الإشعارات'
        verbose_name_plural = 'إعدادات الإشعارات'
        unique_together = ('user', 'notification_type')

class Currency(models.Model):
    """Currency model for the system"""
    code = models.CharField(max_length=3, unique=True, verbose_name='رمز العملة')
    name = models.CharField(max_length=50, verbose_name='اسم العملة')
    symbol = models.CharField(max_length=5, verbose_name='رمز العملة')
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=4, default=1.0, verbose_name='سعر الصرف')
    is_default = models.BooleanField(default=False, verbose_name='العملة الافتراضية')
    is_active = models.BooleanField(default=True, verbose_name='مفعلة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # If this currency is set as default, unset all others
        if self.is_default:
            Currency.objects.filter(is_default=True).update(is_default=False)
        # If no default currency exists, set this one as default
        elif not Currency.objects.filter(is_default=True).exists():
            self.is_default = True
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = 'عملة'
        verbose_name_plural = 'العملات'
        ordering = ['-is_default', 'code']

class AuditLog(models.Model):
    """System audit log"""
    ACTION_CHOICES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('view', 'عرض'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
        ('other', 'أخرى'),
    )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='audit_logs', verbose_name='المستخدم')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name='الإجراء')
    module = models.CharField(max_length=50, verbose_name='الوحدة')
    record_id = models.IntegerField(null=True, blank=True, verbose_name='رقم السجل')
    description = models.TextField(verbose_name='الوصف')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='عنوان IP')
    user_agent = models.TextField(null=True, blank=True, verbose_name='متصفح المستخدم')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.get_action_display()} - {self.module} - {self.created_at}"

    class Meta:
        verbose_name = 'سجل التدقيق'
        verbose_name_plural = 'سجلات التدقيق'
        ordering = ['-created_at']


class SystemAction(models.Model):
    """System actions that can be undone"""
    ACTION_TYPES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
    )

    STATUS_CHOICES = (
        ('pending', 'قيد الانتظار'),
        ('completed', 'مكتمل'),
        ('undone', 'تم التراجع'),
    )

    MODULE_CHOICES = (
        ('user_management', 'إدارة المستخدمين'),
        ('workers', 'العمال'),
        ('contracts', 'العقود'),
        ('currencies', 'العملات'),
        ('worker_settings', 'إعدادات العمال'),
        ('system_settings', 'إعدادات النظام'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='system_actions', verbose_name='المستخدم')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name='نوع الإجراء')
    module = models.CharField(max_length=50, verbose_name='الوحدة')
    record_id = models.IntegerField(blank=True, null=True, verbose_name='معرف السجل')
    description = models.TextField(verbose_name='الوصف')
    previous_data = models.JSONField(blank=True, null=True, verbose_name='البيانات السابقة')
    current_data = models.JSONField(blank=True, null=True, verbose_name='البيانات الحالية')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='completed', verbose_name='الحالة')
    can_undo = models.BooleanField(default=True, verbose_name='يمكن التراجع')
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    undone_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التراجع')
    undone_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name='undone_actions', verbose_name='تم التراجع بواسطة')

    def __str__(self):
        return f"{self.user.username} - {self.get_action_type_display()} - {self.created_at}"

    def get_module_display(self):
        """Return the display value for the module"""
        for code, name in self.MODULE_CHOICES:
            if code == self.module:
                return name
        return self.module

    class Meta:
        verbose_name = 'إجراء النظام'
        verbose_name_plural = 'إجراءات النظام'
        ordering = ['-created_at']
