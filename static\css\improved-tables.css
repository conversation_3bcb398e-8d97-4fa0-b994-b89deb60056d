/**
 * تحسينات لعرض الجداول في النظام
 * يحل مشكلة ارتفاع الخلايا وعرض النصوص
 */

/* تنسيقات عامة للجداول */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color, #333);
    border-collapse: separate;
    border-spacing: 0;
}

/* تحسين ارتفاع الخلايا وعرض النصوص */
.table th,
.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    line-height: 1.5;
    min-height: 3rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 300px; /* حد أقصى لعرض الخلية */
}

/* تحسين عرض النص في الخلايا */
.table td {
    white-space: normal; /* السماح بالتفاف النص */
    overflow: visible; /* عرض النص الكامل */
    text-overflow: clip; /* عدم قطع النص */
}

/* تنسيق رأس الجدول */
.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.02);
    position: sticky;
    top: 0;
    z-index: 10;
}

/* تنسيق الصفوف عند التحويم */
.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* تنسيق الجداول المخططة */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تنسيق الجداول المحاطة */
.table-bordered {
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* تنسيق الجداول المستجيبة */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-height: 600px; /* ارتفاع أقصى مع إمكانية التمرير */
    overflow-y: auto;
}

/* تثبيت رأس الجدول عند التمرير */
.table-responsive .table thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
}

/* تنسيق خلايا الإجراءات */
.table td.actions {
    white-space: nowrap;
    width: 1%;
    text-align: center;
}

/* تنسيق أزرار الإجراءات */
.table .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin: 0 2px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    border: none;
    color: #495057;
}

.table .action-btn:hover {
    transform: translateY(-2px);
}

.table .action-btn-view {
    color: #17a2b8;
}

.table .action-btn-edit {
    color: #ffc107;
}

.table .action-btn-delete {
    color: #dc3545;
}

/* تنسيق الحالات */
.table .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.table .status-active {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.table .status-inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.table .status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.table .status-completed {
    background-color: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.table .status-cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }
    
    .table .action-btn {
        width: 28px;
        height: 28px;
    }
}
