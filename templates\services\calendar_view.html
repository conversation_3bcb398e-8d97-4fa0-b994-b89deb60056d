{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}{{ title }} | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">{{ title }}</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                عرض جداول الخدمات في التقويم
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'service_list' %}"
               class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                <span>العودة للخدمات</span>
            </a>
            <a href="{% url 'worker_cards_view' %}"
               class="bg-amber-600 text-white dark:bg-amber-700 dark:text-white px-5 py-2 rounded-lg hover:bg-amber-700 hover:shadow-md dark:hover:bg-amber-800 transition-all duration-300 flex items-center">
                <i class="fas fa-id-card ml-2 text-sm"></i>
                <span>بطاقات العمال</span>
            </a>
            <a href="{% url 'booking_grid_view' %}"
               class="bg-green-600 text-white dark:bg-green-700 dark:text-white px-5 py-2 rounded-lg hover:bg-green-700 hover:shadow-md dark:hover:bg-green-800 transition-all duration-300 flex items-center">
                <i class="fas fa-th-large ml-2 text-sm"></i>
                <span>شبكة الحجوزات</span>
            </a>
            <a href="{% url 'booking_create' %}"
               class="bg-primary text-white dark:bg-dark-accent dark:text-white px-5 py-2 rounded-lg hover:bg-primary-dark hover:shadow-md dark:hover:bg-blue-700 transition-all duration-300 flex items-center">
                <i class="fas fa-plus ml-2 text-sm"></i>
                <span>إضافة حجز جديد</span>
            </a>
        </div>
    </div>

    <!-- Calendar Navigation -->
    <div class="bg-white dark:bg-[#1E293B] rounded-xl shadow-md p-4 mb-6 border border-gray-100 dark:border-slate-700">
        <div class="flex justify-between items-center">
            <div class="flex items-center gap-2">
                <button id="prev-month" class="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-white transition-all duration-200">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button id="today-btn" class="px-3 py-1 rounded-xl bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-200 text-sm font-semibold transition-all duration-200">
                    اليوم
                </button>
            </div>

            <div class="text-center">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">
                    <span id="month-name" class="ml-1"></span>
                    <span id="year">{{ year }}</span>
                </h3>
            </div>

            <button id="next-month" class="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-white transition-all duration-200">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
    </div>

    <!-- Calendar -->
    <div class="bg-white dark:bg-[#1E293B] rounded-xl shadow-md p-6 border border-gray-100 dark:border-slate-700">
        <!-- Calendar Grid -->
        <div class="grid grid-cols-7 gap-3">
            <!-- Day Headers -->
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الأحد</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الإثنين</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الثلاثاء</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الأربعاء</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الخميس</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">الجمعة</div>
            <div class="text-center font-bold text-gray-800 dark:text-white p-2 bg-gray-100 dark:bg-slate-700 rounded-xl">السبت</div>

            <!-- Calendar Days (will be populated by JavaScript) -->
            <div id="calendar-days" class="col-span-7 grid grid-cols-7 gap-3">
                <!-- Days will be populated here -->
            </div>
        </div>
    </div>

    <!-- Schedule Details Modal -->
    <div id="schedule-modal" class="fixed inset-0 bg-black bg-opacity-60 hidden flex items-center justify-center z-50">
        <div class="bg-white dark:bg-[#1E293B] rounded-xl shadow-xl p-6 max-w-md w-full mx-4 transform transition-all duration-300" onclick="event.stopPropagation();">
            <div class="flex justify-between items-center mb-4 border-b border-gray-100 dark:border-slate-700 pb-3">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white" id="modal-title">تفاصيل الجدول</h3>
                <button id="close-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center justify-center transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-content" class="space-y-4 max-h-[60vh] overflow-y-auto pr-1 custom-scrollbar">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="mt-6 flex justify-end border-t border-gray-100 dark:border-slate-700 pt-3">
                <button id="view-schedule" class="bg-blue-600 text-white px-5 py-2 rounded-xl hover:bg-blue-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-2">
                    <i class="fas fa-eye"></i>
                    <span>عرض التفاصيل</span>
                </button>
            </div>
        </div>
    </div>

    <style>
        /* Custom scrollbar for modal content */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* Dark mode scrollbar */
        .dark .custom-scrollbar::-webkit-scrollbar-track {
            background: #2d3748;
        }

        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #4a5568;
        }

        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #718096;
        }
    </style>
</div>
{% endblock %}

{% block inner_extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from server
        const calendarData = {{ calendar_data|safe }};

        // Current month and year
        let currentYear = {{ year }};
        let currentMonth = {{ month }};

        // Month names in Arabic
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        // Service type colors
        const serviceTypeColors = {
            'regular': {bg: 'bg-blue-50', text: 'text-blue-700', dark_bg: 'dark:bg-blue-900/30', dark_text: 'dark:text-blue-300'},
            'full_day': {bg: 'bg-purple-50', text: 'text-purple-700', dark_bg: 'dark:bg-purple-900/30', dark_text: 'dark:text-purple-300'},
            'custom': {bg: 'bg-orange-50', text: 'text-orange-700', dark_bg: 'dark:bg-orange-900/30', dark_text: 'dark:text-orange-300'},
            'weekly': {bg: 'bg-green-50', text: 'text-green-700', dark_bg: 'dark:bg-green-900/30', dark_text: 'dark:text-green-300'},
            'monthly': {bg: 'bg-pink-50', text: 'text-pink-700', dark_bg: 'dark:bg-pink-900/30', dark_text: 'dark:text-pink-300'},
            'default': {bg: 'bg-gray-50', text: 'text-gray-700', dark_bg: 'dark:bg-gray-900/30', dark_text: 'dark:text-gray-300'}
        };

        // Initialize calendar
        renderCalendar(currentYear, currentMonth);

        // Event listeners for navigation
        document.getElementById('prev-month').addEventListener('click', function() {
            currentMonth--;
            if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }
            renderCalendar(currentYear, currentMonth);
        });

        document.getElementById('next-month').addEventListener('click', function() {
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
            renderCalendar(currentYear, currentMonth);
        });

        // Today button
        document.getElementById('today-btn').addEventListener('click', function() {
            const today = new Date();
            currentMonth = today.getMonth() + 1;
            currentYear = today.getFullYear();
            renderCalendar(currentYear, currentMonth);
        });

        // Close modal when clicking the close button
        document.getElementById('close-modal').addEventListener('click', function() {
            closeModal();
        });

        // Close modal when clicking outside the modal content
        document.getElementById('schedule-modal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeModal();
            }
        });

        // Function to close the modal
        function closeModal() {
            const modal = document.getElementById('schedule-modal');
            modal.classList.add('hidden');
            modal.style.display = 'none';
        }

        // Function to open the modal
        function openModal() {
            const modal = document.getElementById('schedule-modal');
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
        }

        // Render calendar
        function renderCalendar(year, month) {
            // Update month/year display
            document.getElementById('month-name').textContent = monthNames[month - 1];
            document.getElementById('year').textContent = year;

            // Get first day of month and number of days
            const firstDay = new Date(year, month - 1, 1);
            const lastDay = new Date(year, month, 0);
            const daysInMonth = lastDay.getDate();

            // Get day of week for first day (0 = Sunday, 6 = Saturday)
            const firstDayOfWeek = firstDay.getDay();

            // Get current date for highlighting today
            const today = new Date();
            const isCurrentMonth = today.getMonth() + 1 === month && today.getFullYear() === year;
            const currentDate = today.getDate();

            // Clear calendar
            const calendarDays = document.getElementById('calendar-days');
            calendarDays.innerHTML = '';

            // Add empty cells for days before first day of month
            for (let i = 0; i < firstDayOfWeek; i++) {
                const emptyCell = document.createElement('div');
                emptyCell.className = 'h-28 bg-gray-50 dark:bg-slate-800/30 rounded-xl border border-gray-100 dark:border-slate-700';
                calendarDays.appendChild(emptyCell);
            }

            // Add days of month
            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                const dayCell = document.createElement('div');

                // Check if this is today
                const isToday = isCurrentMonth && day === currentDate;

                // Set day cell classes with conditional today highlighting
                dayCell.className = `h-28 p-2 rounded-xl border ${isToday ? 'bg-indigo-50 border-indigo-300 dark:bg-indigo-900/30 dark:border-indigo-700' : 'bg-white dark:bg-slate-800/50 border-gray-100 dark:border-slate-700'} hover:shadow-md transition-all duration-200 relative overflow-hidden`;

                // Add day number
                const dayNumber = document.createElement('div');
                dayNumber.className = `text-sm font-bold ${isToday ? 'text-indigo-700 dark:text-indigo-300' : 'text-gray-600 dark:text-gray-300'} text-right`;
                dayNumber.textContent = day;
                dayCell.appendChild(dayNumber);

                // Add schedules count for this day
                if (calendarData[dateStr] && calendarData[dateStr].length > 0) {
                    const totalEvents = calendarData[dateStr].length;

                    // Create a booking count indicator
                    const bookingsCount = document.createElement('div');
                    bookingsCount.className = 'mt-1 text-xs text-center text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition';
                    bookingsCount.textContent = `${totalEvents} ${totalEvents === 1 ? 'حجز' : 'حجوزات'}`;

                    // Click to show all events for this day
                    bookingsCount.addEventListener('click', function() {
                        showAllSchedulesForDay(calendarData[dateStr], dateStr);
                    });

                    dayCell.appendChild(bookingsCount);
                }

                calendarDays.appendChild(dayCell);
            }
        }

        // Show schedule details in modal
        function showScheduleDetails(schedule, dateStr) {
            const modal = document.getElementById('schedule-modal');
            const modalContent = document.getElementById('modal-content');
            const viewScheduleBtn = document.getElementById('view-schedule');

            // Get color scheme based on service type
            const colorScheme = serviceTypeColors[schedule.service_type_key] || serviceTypeColors.default;

            // Set modal title with service type indicator
            document.getElementById('modal-title').innerHTML = `
                <div class="flex items-center">
                    <span class="w-3 h-3 rounded-full ${colorScheme.bg} ml-2"></span>
                    <span>${schedule.client_name}</span>
                </div>
            `;

            // Format date in Arabic
            const formattedDate = formatDateArabic(dateStr);

            // Set modal content
            modalContent.innerHTML = `
                <div class="bg-gray-50 dark:bg-slate-800 rounded-lg p-4 mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <p class="text-sm font-semibold text-gray-500 dark:text-gray-400">التاريخ</p>
                        <p class="font-medium text-gray-800 dark:text-white">${formattedDate}</p>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <p class="text-sm font-semibold text-gray-500 dark:text-gray-400">الوقت</p>
                        <p class="font-medium text-gray-800 dark:text-white">${schedule.start_time} - ${schedule.end_time}</p>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="text-sm font-semibold text-gray-500 dark:text-gray-400">نوع الخدمة</p>
                        <p class="font-medium">
                            <span class="px-2 py-1 text-xs rounded-full ${colorScheme.bg} ${colorScheme.text} ${colorScheme.dark_bg} ${colorScheme.dark_text}">
                                ${schedule.service_type}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <p class="text-sm font-semibold text-gray-500 dark:text-gray-400">الحالة</p>
                        <p class="font-medium">
                            <span class="px-2 py-1 text-xs rounded-full
                                ${schedule.status === 'scheduled' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                                ${schedule.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : ''}
                                ${schedule.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                                ${schedule.status === 'cancelled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : ''}">
                                ${getStatusDisplay(schedule.status)}
                            </span>
                        </p>
                    </div>

                    <div class="mt-4">
                        <p class="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">العمال</p>
                        <div class="bg-white dark:bg-slate-700 rounded-lg p-2 border border-gray-100 dark:border-slate-600">
                            ${schedule.workers && schedule.workers.length > 0 ?
                                schedule.workers.map(worker => `
                                    <div class="text-sm font-medium text-gray-800 dark:text-white py-1 border-b border-gray-100 dark:border-slate-600 last:border-0 flex items-center">
                                        <i class="fas fa-user-tie ml-2 text-xs text-gray-500 dark:text-gray-400"></i>${worker}
                                    </div>
                                `).join('') :
                                '<div class="text-sm text-gray-500 dark:text-gray-400 text-center py-1">لا يوجد عمال معينين</div>'
                            }
                        </div>
                    </div>
                </div>
            `;

            // Set view button link
            viewScheduleBtn.onclick = function() {
                // Redirect to schedule detail page
                window.location.href = `/services/schedules/${schedule.id}/`;
            };

            // Show modal
            openModal();
        }

        // Show all schedules for a specific day
        function showAllSchedulesForDay(schedules, dateStr) {
            const modal = document.getElementById('schedule-modal');
            const modalContent = document.getElementById('modal-content');
            const viewScheduleBtn = document.getElementById('view-schedule');

            // Format date in Arabic
            const formattedDate = formatDateArabic(dateStr);

            // Set modal title
            document.getElementById('modal-title').textContent = `حجوزات يوم ${formattedDate}`;

            // Create table header for schedules
            let schedulesHTML = `
                <div class="overflow-hidden rounded-lg border border-gray-200 dark:border-slate-700">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                        <thead class="bg-gray-50 dark:bg-slate-800">
                            <tr>
                                <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">العميل</th>
                                <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">العامل</th>
                                <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">الوقت</th>
                                <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">نوع الخدمة</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-slate-700 bg-white dark:bg-slate-800">
            `;

            // Add each schedule as a row
            schedules.forEach(schedule => {
                // Get color scheme based on service type
                const colorScheme = serviceTypeColors[schedule.service_type_key] || serviceTypeColors.default;

                // Get workers list or placeholder
                const workersList = schedule.workers && schedule.workers.length > 0
                    ? schedule.workers.join(', ')
                    : 'لم يتم تعيين عمال';

                // Add row with appropriate background color based on service type
                schedulesHTML += `
                    <tr class="${colorScheme.bg} ${colorScheme.dark_bg} hover:bg-opacity-70 dark:hover:bg-opacity-70 cursor-pointer transition-all duration-200"
                        onclick="window.location.href='/services/schedules/${schedule.id}/'">
                        <td class="px-3 py-2 text-sm font-medium ${colorScheme.text} ${colorScheme.dark_text}">${schedule.client_name}</td>
                        <td class="px-3 py-2 text-sm ${colorScheme.text} ${colorScheme.dark_text}">${workersList}</td>
                        <td class="px-3 py-2 text-sm ${colorScheme.text} ${colorScheme.dark_text}">${schedule.start_time} - ${schedule.end_time}</td>
                        <td class="px-3 py-2 text-sm ${colorScheme.text} ${colorScheme.dark_text}">${schedule.service_type}</td>
                    </tr>
                `;
            });

            // Close the table
            schedulesHTML += `
                        </tbody>
                    </table>
                </div>
            `;

            // Set modal content
            modalContent.innerHTML = schedulesHTML;

            // Hide view button since each schedule is clickable
            viewScheduleBtn.style.display = 'none';

            // Show modal
            openModal();
        }

        // Format date in Arabic
        function formatDateArabic(dateStr) {
            const date = new Date(dateStr);
            const day = date.getDate();
            const month = monthNames[date.getMonth()];
            const year = date.getFullYear();

            return `${day} ${month} ${year}`;
        }

        // Helper function to get status display text
        function getStatusDisplay(status) {
            switch (status) {
                case 'scheduled': return 'مجدول';
                case 'in_progress': return 'قيد التنفيذ';
                case 'completed': return 'مكتمل';
                case 'cancelled': return 'ملغي';
                default: return status;
            }
        }
    });
</script>
{% endblock %}
