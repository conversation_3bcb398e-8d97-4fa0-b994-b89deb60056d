from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
import uuid

from .models import (
    Worker, Contract, DailyService, Daily24HService, CustomService, WorkerOperation,
    BloodTestRequest, BloodTestRequestWorker, SponsorshipTransfer, WorkerTransfer
)

# دالة لتوليد أرقام مرجعية فريدة
def generate_unique_number(prefix):
    """توليد رقم مرجعي فريد بالبادئة المحددة"""
    timestamp = timezone.now().strftime('%Y%m%d%H%M')
    unique_id = uuid.uuid4().hex[:6].upper()
    return f"{prefix}-{timestamp}-{unique_id}"

# إشارة لتوليد أرقام العقود تلقائيًا
@receiver(pre_save, sender=Contract)
def generate_contract_number(sender, instance, **kwargs):
    """توليد رقم عقد تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.contract_number:
        instance.contract_number = generate_unique_number('CNT')

# إشارة لتوليد أرقام الخدمة اليومية تلقائيًا
@receiver(pre_save, sender=DailyService)
def generate_daily_service_number(sender, instance, **kwargs):
    """توليد رقم خدمة يومية تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.service_number:
        instance.service_number = generate_unique_number('DS')

# إشارة لتوليد أرقام خدمة 24 ساعة تلقائيًا
@receiver(pre_save, sender=Daily24HService)
def generate_24h_service_number(sender, instance, **kwargs):
    """توليد رقم خدمة 24 ساعة تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.service_number:
        instance.service_number = generate_unique_number('24H')

# إشارة لتوليد أرقام الخدمة المخصصة تلقائيًا
@receiver(pre_save, sender=CustomService)
def generate_custom_service_number(sender, instance, **kwargs):
    """توليد رقم خدمة مخصصة تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.service_number:
        instance.service_number = generate_unique_number('CS')

# إشارة لتوليد أرقام العمليات تلقائيًا
@receiver(pre_save, sender=WorkerOperation)
def generate_operation_number(sender, instance, **kwargs):
    """توليد رقم عملية تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.operation_number:
        instance.operation_number = generate_unique_number('OP')

# إشارة لتوليد أرقام طلب فحص الدم تلقائيًا
@receiver(pre_save, sender=BloodTestRequest)
def generate_blood_test_request_number(sender, instance, **kwargs):
    """توليد رقم طلب فحص دم تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.request_number:
        instance.request_number = generate_unique_number('BT')

# إشارة لتوليد أرقام نقل الكفالة تلقائيًا
@receiver(pre_save, sender=SponsorshipTransfer)
def generate_sponsorship_transfer_number(sender, instance, **kwargs):
    """توليد رقم نقل كفالة تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.transfer_number:
        instance.transfer_number = generate_unique_number('ST')

# إشارة لتوليد أرقام نقل العامل تلقائيًا
@receiver(pre_save, sender=WorkerTransfer)
def generate_worker_transfer_number(sender, instance, **kwargs):
    """توليد رقم نقل عامل تلقائيًا إذا لم يتم توفير واحد"""
    if not instance.transfer_number:
        instance.transfer_number = generate_unique_number('WT')

# إشارة لتحديث حالة العامل عند إنشاء نقل
@receiver(post_save, sender=WorkerTransfer)
def update_worker_branch(sender, instance, created, **kwargs):
    """تحديث فرع العامل عند اكتمال النقل"""
    if instance.status == 'completed':
        worker = instance.worker
        worker.branch = instance.to_branch
        worker.save(update_fields=['branch'])

# إشارة لتحديث حالة العامل عند اكتمال العملية
@receiver(post_save, sender=WorkerOperation)
def update_worker_status_on_operation(sender, instance, created, **kwargs):
    """تحديث حالة العامل عند اكتمال العملية"""
    if instance.status == 'completed' and instance.worker.status == 'pending':
        worker = instance.worker
        worker.status = 'active'
        worker.save(update_fields=['status'])
