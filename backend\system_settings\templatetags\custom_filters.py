from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using a key.
    Usage: {{ dictionary|get_item:key }}
    """
    if dictionary is None:
        return None
    return dictionary.get(key)

@register.filter
def get_attr(obj, attr):
    """
    Get an attribute from an object.
    Usage: {{ object|get_attr:attribute_name }}
    """
    if obj is None:
        return None
    return getattr(obj, attr, None)
