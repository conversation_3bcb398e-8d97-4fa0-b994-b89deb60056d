from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.utils import timezone

def company_login(request):
    """صفحة تسجيل دخول الشركات"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        serial_number = request.POST.get('serial_number')
        
        # التحقق من بيانات تسجيل الدخول
        user = authenticate(username=username, password=password)
        
        if user is not None:
            login(request, user)
            messages.success(request, 'تم تسجيل الدخول بنجاح')
            return redirect('company:dashboard')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render(request, 'layouts/login.html', {'now': timezone.now()})

def company_logout(request):
    """تسجيل خروج الشركة"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('company:login')

@login_required
def company_dashboard(request):
    """لوحة تحكم الشركة"""
    context = {
        'current_date': timezone.now(),
    }
    return render(request, 'company/dashboard.html', context)
