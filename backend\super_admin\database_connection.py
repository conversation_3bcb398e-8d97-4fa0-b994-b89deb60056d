"""
وحدة للتعامل مع الاتصال بقواعد بيانات الشركات
"""

from django.db import connections
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging
import mysql.connector
from contextlib import contextmanager

logger = logging.getLogger(__name__)

def setup_company_db_connection(company):
    """
    إعداد اتصال قاعدة بيانات الشركة في إعدادات Django
    
    Args:
        company: كائن الشركة الذي يحتوي على معلومات الاتصال بقاعدة البيانات
    
    Returns:
        bool: True إذا تم إعداد الاتصال بنجاح، False خلاف ذلك
    """
    try:
        # تحديد اسم الاتصال بناءً على الرقم التسلسلي للشركة
        connection_name = f"company_{company.serial_number.replace('-', '_')}"
        
        # تحديث إعدادات الاتصال بقاعدة بيانات الشركة
        settings.DATABASES[connection_name] = {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': company.database_name if hasattr(company, 'database_name') else company.db_name,
            'USER': company.database_user if hasattr(company, 'database_user') else company.db_user,
            'PASSWORD': company.database_password if hasattr(company, 'database_password') else company.db_password,
            'HOST': company.database_host if hasattr(company, 'database_host') else company.db_host,
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
                'use_unicode': True,
            },
        }
        
        # إعادة تهيئة الاتصال إذا كان موجودًا
        if connection_name in connections:
            connections[connection_name].close()
            del connections[connection_name]
        
        # اختبار الاتصال
        connections[connection_name].ensure_connection()
        
        logger.info(f"تم إعداد الاتصال بقاعدة بيانات الشركة {company.name} بنجاح")
        return True
    
    except Exception as e:
        logger.error(f"فشل في إعداد الاتصال بقاعدة بيانات الشركة {company.name}: {str(e)}")
        return False

@contextmanager
def company_db_connection(company):
    """
    سياق للاتصال بقاعدة بيانات الشركة
    
    Args:
        company: كائن الشركة الذي يحتوي على معلومات الاتصال بقاعدة البيانات
    
    Yields:
        اسم الاتصال بقاعدة البيانات
    """
    connection_name = f"company_{company.serial_number.replace('-', '_')}"
    
    # إعداد الاتصال
    success = setup_company_db_connection(company)
    
    if not success:
        raise ImproperlyConfigured(f"فشل في إعداد الاتصال بقاعدة بيانات الشركة {company.name}")
    
    try:
        yield connection_name
    finally:
        # إغلاق الاتصال
        if connection_name in connections:
            connections[connection_name].close()

def execute_query_on_company_db(company, query, params=None):
    """
    تنفيذ استعلام على قاعدة بيانات الشركة
    
    Args:
        company: كائن الشركة
        query: استعلام SQL
        params: معلمات الاستعلام (اختياري)
    
    Returns:
        نتيجة الاستعلام
    """
    with company_db_connection(company) as connection_name:
        with connections[connection_name].cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # إذا كان الاستعلام SELECT، قم بإرجاع النتائج
            if query.strip().upper().startswith('SELECT'):
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # إذا كان الاستعلام INSERT/UPDATE/DELETE، قم بإرجاع عدد الصفوف المتأثرة
            return cursor.rowcount

def test_company_db_connection(company):
    """
    اختبار الاتصال بقاعدة بيانات الشركة
    
    Args:
        company: كائن الشركة
    
    Returns:
        dict: نتيجة الاختبار
    """
    try:
        # محاولة الاتصال بقاعدة البيانات مباشرة باستخدام mysql.connector
        db_name = company.database_name if hasattr(company, 'database_name') else company.db_name
        db_user = company.database_user if hasattr(company, 'database_user') else company.db_user
        db_password = company.database_password if hasattr(company, 'database_password') else company.db_password
        db_host = company.database_host if hasattr(company, 'database_host') else company.db_host
        
        connection = mysql.connector.connect(
            host=db_host,
            user=db_user,
            password=db_password,
            database=db_name
        )
        
        cursor = connection.cursor()
        
        # التحقق من وجود الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        
        return {
            'success': True,
            'message': 'تم الاتصال بقاعدة البيانات بنجاح',
            'tables_count': len(tables),
            'tables': [table[0] for table in tables]
        }
    
    except Exception as e:
        logger.error(f"فشل في اختبار الاتصال بقاعدة بيانات الشركة {company.name}: {str(e)}")
        return {
            'success': False,
            'message': f'فشل في الاتصال بقاعدة البيانات: {str(e)}',
            'error': str(e)
        }
