@extends('layouts.master_admin')

@section('title', 'إعدادات النظام | المسؤول الأعلى')

@section('page_title', 'إعدادات النظام')
@section('page_subtitle', 'تخصيص وتكوين إعدادات النظام')

@section('extra_css')
<style>
    .settings-card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6;
    }
    
    .settings-header {
        @apply p-4 border-b border-gray-200 dark:border-gray-700 bg-admin-primary dark:bg-admin-dark text-white;
    }
    
    .settings-body {
        @apply p-6;
    }
    
    .form-group {
        @apply mb-4;
    }
    
    .form-label {
        @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
    }
    
    .form-control {
        @apply w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2;
    }
    
    .form-check {
        @apply flex items-center;
    }
    
    .form-check-input {
        @apply ml-2 h-4 w-4 text-admin-primary dark:text-admin-accent rounded border-gray-300 dark:border-gray-600;
    }
    
    .form-check-label {
        @apply text-sm text-gray-700 dark:text-gray-300;
    }
    
    .color-preview {
        @apply w-8 h-8 rounded-full inline-block ml-2 border border-gray-300 dark:border-gray-600;
    }
</style>
@endsection

@section('content')
<form action="{{ route('super_admin.settings.update') }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    <!-- General Settings -->
    <div class="settings-card">
        <div class="settings-header">
            <h3 class="text-lg font-bold">الإعدادات العامة</h3>
        </div>
        <div class="settings-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="app_name" class="form-label">اسم التطبيق</label>
                    <input type="text" id="app_name" name="app_name" class="form-control" value="{{ $settings->app_name ?? 'منصة استقدامي السحابية' }}">
                </div>
                
                <div class="form-group">
                    <label for="app_version" class="form-label">إصدار التطبيق</label>
                    <input type="text" id="app_version" name="app_version" class="form-control" value="{{ $settings->app_version ?? '1.0.0' }}">
                </div>
                
                <div class="form-group">
                    <label for="default_language" class="form-label">اللغة الافتراضية</label>
                    <select id="default_language" name="default_language" class="form-control">
                        <option value="ar" {{ ($settings->default_language ?? 'ar') == 'ar' ? 'selected' : '' }}>العربية</option>
                        <option value="en" {{ ($settings->default_language ?? 'ar') == 'en' ? 'selected' : '' }}>الإنجليزية</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                    <select id="timezone" name="timezone" class="form-control">
                        <option value="Asia/Riyadh" {{ ($settings->timezone ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (GMT+3)</option>
                        <option value="Asia/Dubai" {{ ($settings->timezone ?? 'Asia/Riyadh') == 'Asia/Dubai' ? 'selected' : '' }}>دبي (GMT+4)</option>
                        <option value="Asia/Baghdad" {{ ($settings->timezone ?? 'Asia/Riyadh') == 'Asia/Baghdad' ? 'selected' : '' }}>بغداد (GMT+3)</option>
                        <option value="Africa/Cairo" {{ ($settings->timezone ?? 'Asia/Riyadh') == 'Africa/Cairo' ? 'selected' : '' }}>القاهرة (GMT+2)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="logo" class="form-label">شعار النظام</label>
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden ml-4">
                            @if(isset($settings->logo) && $settings->logo)
                                <img src="{{ asset('storage/' . $settings->logo) }}" alt="شعار النظام" class="w-full h-full object-contain">
                            @else
                                <i class="fas fa-image text-gray-400 text-2xl"></i>
                            @endif
                        </div>
                        <input type="file" id="logo" name="logo" class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="favicon" class="form-label">أيقونة الموقع</label>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden ml-4">
                            @if(isset($settings->favicon) && $settings->favicon)
                                <img src="{{ asset('storage/' . $settings->favicon) }}" alt="أيقونة الموقع" class="w-full h-full object-contain">
                            @else
                                <i class="fas fa-image text-gray-400 text-sm"></i>
                            @endif
                        </div>
                        <input type="file" id="favicon" name="favicon" class="form-control">
                    </div>
                </div>
            </div>
            
            <div class="form-group mt-4">
                <label class="form-label">وضع الصيانة</label>
                <div class="form-check">
                    <input type="checkbox" id="maintenance_mode" name="maintenance_mode" class="form-check-input" {{ ($settings->maintenance_mode ?? false) ? 'checked' : '' }}>
                    <label for="maintenance_mode" class="form-check-label">تفعيل وضع الصيانة</label>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">عند تفعيل وضع الصيانة، سيتم منع جميع المستخدمين من الوصول إلى النظام باستثناء المسؤول الأعلى.</p>
            </div>
        </div>
    </div>
    
    <!-- Security Settings -->
    <div class="settings-card">
        <div class="settings-header">
            <h3 class="text-lg font-bold">إعدادات الأمان</h3>
        </div>
        <div class="settings-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="session_timeout" class="form-label">مدة صلاحية الجلسة (بالدقائق)</label>
                    <input type="number" id="session_timeout" name="session_timeout" class="form-control" value="{{ $settings->session_timeout ?? 60 }}" min="1" max="1440">
                </div>
                
                <div class="form-group">
                    <label for="password_expiry_days" class="form-label">مدة صلاحية كلمة المرور (بالأيام)</label>
                    <input type="number" id="password_expiry_days" name="password_expiry_days" class="form-control" value="{{ $settings->password_expiry_days ?? 90 }}" min="0" max="365">
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">0 = لا تنتهي صلاحية كلمة المرور</p>
                </div>
                
                <div class="form-group">
                    <label for="max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                    <input type="number" id="max_login_attempts" name="max_login_attempts" class="form-control" value="{{ $settings->max_login_attempts ?? 5 }}" min="1" max="10">
                </div>
                
                <div class="form-group">
                    <label for="lockout_time" class="form-label">مدة القفل بعد تجاوز محاولات تسجيل الدخول (بالدقائق)</label>
                    <input type="number" id="lockout_time" name="lockout_time" class="form-control" value="{{ $settings->lockout_time ?? 30 }}" min="1" max="1440">
                </div>
            </div>
            
            <div class="form-group mt-4">
                <label class="form-label">المصادقة الثنائية</label>
                <div class="form-check">
                    <input type="checkbox" id="two_factor_for_admins" name="two_factor_for_admins" class="form-check-input" {{ ($settings->two_factor_for_admins ?? false) ? 'checked' : '' }}>
                    <label for="two_factor_for_admins" class="form-check-label">تفعيل المصادقة الثنائية للمسؤولين</label>
                </div>
            </div>
            
            <div class="form-group mt-2">
                <label for="two_factor_type" class="form-label">نوع المصادقة الثنائية</label>
                <select id="two_factor_type" name="two_factor_type" class="form-control">
                    <option value="email" {{ ($settings->two_factor_type ?? 'email') == 'email' ? 'selected' : '' }}>البريد الإلكتروني</option>
                    <option value="sms" {{ ($settings->two_factor_type ?? 'email') == 'sms' ? 'selected' : '' }}>الرسائل النصية</option>
                    <option value="authenticator" {{ ($settings->two_factor_type ?? 'email') == 'authenticator' ? 'selected' : '' }}>تطبيق المصادقة</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Theme Settings -->
    <div class="settings-card">
        <div class="settings-header">
            <h3 class="text-lg font-bold">إعدادات المظهر</h3>
        </div>
        <div class="settings-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="primary_color" class="form-label">اللون الرئيسي</label>
                    <div class="flex items-center">
                        <div class="color-preview" style="background-color: {{ $settings->primary_color ?? '#174785' }}"></div>
                        <input type="color" id="primary_color" name="primary_color" class="form-control" value="{{ $settings->primary_color ?? '#174785' }}">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="secondary_color" class="form-label">اللون الثانوي</label>
                    <div class="flex items-center">
                        <div class="color-preview" style="background-color: {{ $settings->secondary_color ?? '#4B8F8C' }}"></div>
                        <input type="color" id="secondary_color" name="secondary_color" class="form-control" value="{{ $settings->secondary_color ?? '#4B8F8C' }}">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="accent_color" class="form-label">لون التمييز</label>
                    <div class="flex items-center">
                        <div class="color-preview" style="background-color: {{ $settings->accent_color ?? '#42d3d8' }}"></div>
                        <input type="color" id="accent_color" name="accent_color" class="form-control" value="{{ $settings->accent_color ?? '#42d3d8' }}">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="font_family" class="form-label">الخط المستخدم</label>
                    <select id="font_family" name="font_family" class="form-control">
                        <option value="Tajawal" {{ ($settings->font_family ?? 'Tajawal') == 'Tajawal' ? 'selected' : '' }}>Tajawal</option>
                        <option value="Cairo" {{ ($settings->font_family ?? 'Tajawal') == 'Cairo' ? 'selected' : '' }}>Cairo</option>
                        <option value="Almarai" {{ ($settings->font_family ?? 'Tajawal') == 'Almarai' ? 'selected' : '' }}>Almarai</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group mt-4">
                <label class="form-label">الوضع الليلي</label>
                <div class="form-check">
                    <input type="checkbox" id="dark_mode_default" name="dark_mode_default" class="form-check-input" {{ ($settings->dark_mode_default ?? false) ? 'checked' : '' }}>
                    <label for="dark_mode_default" class="form-check-label">تفعيل الوضع الليلي افتراضيًا</label>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Email Settings -->
    <div class="settings-card">
        <div class="settings-header">
            <h3 class="text-lg font-bold">إعدادات البريد الإلكتروني</h3>
        </div>
        <div class="settings-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="mail_from_address" class="form-label">عنوان البريد الإلكتروني المرسل</label>
                    <input type="email" id="mail_from_address" name="mail_from_address" class="form-control" value="{{ $settings->mail_from_address ?? '<EMAIL>' }}">
                </div>
                
                <div class="form-group">
                    <label for="mail_from_name" class="form-label">اسم المرسل</label>
                    <input type="text" id="mail_from_name" name="mail_from_name" class="form-control" value="{{ $settings->mail_from_name ?? 'منصة استقدامي السحابية' }}">
                </div>
                
                <div class="form-group">
                    <label for="mail_host" class="form-label">خادم SMTP</label>
                    <input type="text" id="mail_host" name="mail_host" class="form-control" value="{{ $settings->mail_host ?? 'smtp.gmail.com' }}">
                </div>
                
                <div class="form-group">
                    <label for="mail_port" class="form-label">منفذ SMTP</label>
                    <input type="number" id="mail_port" name="mail_port" class="form-control" value="{{ $settings->mail_port ?? 587 }}">
                </div>
                
                <div class="form-group">
                    <label for="mail_username" class="form-label">اسم مستخدم SMTP</label>
                    <input type="text" id="mail_username" name="mail_username" class="form-control" value="{{ $settings->mail_username ?? '' }}">
                </div>
                
                <div class="form-group">
                    <label for="mail_password" class="form-label">كلمة مرور SMTP</label>
                    <input type="password" id="mail_password" name="mail_password" class="form-control" value="{{ $settings->mail_password ?? '' }}">
                </div>
            </div>
            
            <div class="form-group mt-4">
                <label class="form-label">تشفير SMTP</label>
                <select id="mail_encryption" name="mail_encryption" class="form-control">
                    <option value="tls" {{ ($settings->mail_encryption ?? 'tls') == 'tls' ? 'selected' : '' }}>TLS</option>
                    <option value="ssl" {{ ($settings->mail_encryption ?? 'tls') == 'ssl' ? 'selected' : '' }}>SSL</option>
                    <option value="none" {{ ($settings->mail_encryption ?? 'tls') == 'none' ? 'selected' : '' }}>بدون تشفير</option>
                </select>
            </div>
            
            <div class="mt-4">
                <button type="button" id="test-email-btn" class="bg-admin-secondary hover:bg-admin-accent text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-paper-plane ml-2"></i>
                    اختبار إعدادات البريد الإلكتروني
                </button>
            </div>
        </div>
    </div>
    
    <!-- Submit Button -->
    <div class="flex justify-end">
        <button type="submit" class="bg-admin-primary hover:bg-admin-dark text-white px-6 py-3 rounded-lg flex items-center">
            <i class="fas fa-save ml-2"></i>
            حفظ الإعدادات
        </button>
    </div>
</form>

<!-- Test Email Modal -->
<div id="test-email-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden" x-data="{ show: false }" x-show="show" x-cloak>
    <div class="absolute inset-0 bg-black bg-opacity-50" @click="show = false"></div>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md relative">
        <h3 class="text-lg font-bold mb-4 text-gray-900 dark:text-white">اختبار إعدادات البريد الإلكتروني</h3>
        
        <div class="form-group">
            <label for="test_email" class="form-label">عنوان البريد الإلكتروني للاختبار</label>
            <input type="email" id="test_email" class="form-control" placeholder="أدخل عنوان البريد الإلكتروني">
        </div>
        
        <div class="flex justify-end mt-6 space-x-3 rtl:space-x-reverse">
            <button @click="show = false" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">إلغاء</button>
            <button id="send-test-email" class="px-4 py-2 bg-admin-primary text-white rounded-lg hover:bg-admin-dark transition-colors">إرسال</button>
        </div>
    </div>
</div>
@endsection

@section('extra_js')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Color preview update
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(input => {
            input.addEventListener('input', function() {
                const preview = this.previousElementSibling;
                preview.style.backgroundColor = this.value;
            });
        });
        
        // Test email button
        const testEmailBtn = document.getElementById('test-email-btn');
        const testEmailModal = document.getElementById('test-email-modal');
        
        testEmailBtn.addEventListener('click', function() {
            testEmailModal.__x.$data.show = true;
        });
        
        // Send test email
        const sendTestEmailBtn = document.getElementById('send-test-email');
        
        sendTestEmailBtn.addEventListener('click', function() {
            const testEmail = document.getElementById('test_email').value;
            
            if (!testEmail) {
                showToast('يرجى إدخال عنوان بريد إلكتروني صالح', 'error');
                return;
            }
            
            // Get email settings from form
            const mailFromAddress = document.getElementById('mail_from_address').value;
            const mailFromName = document.getElementById('mail_from_name').value;
            const mailHost = document.getElementById('mail_host').value;
            const mailPort = document.getElementById('mail_port').value;
            const mailUsername = document.getElementById('mail_username').value;
            const mailPassword = document.getElementById('mail_password').value;
            const mailEncryption = document.getElementById('mail_encryption').value;
            
            // Show loading state
            sendTestEmailBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جاري الإرسال...';
            sendTestEmailBtn.disabled = true;
            
            // Send test email request
            fetch('/super_admin/settings/test-email', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    test_email: testEmail,
                    mail_from_address: mailFromAddress,
                    mail_from_name: mailFromName,
                    mail_host: mailHost,
                    mail_port: mailPort,
                    mail_username: mailUsername,
                    mail_password: mailPassword,
                    mail_encryption: mailEncryption
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم إرسال بريد الاختبار بنجاح', 'success');
                    testEmailModal.__x.$data.show = false;
                } else {
                    showToast(data.message || 'فشل في إرسال بريد الاختبار', 'error');
                }
            })
            .catch(error => {
                showToast('حدث خطأ أثناء إرسال بريد الاختبار', 'error');
                console.error(error);
            })
            .finally(() => {
                sendTestEmailBtn.innerHTML = 'إرسال';
                sendTestEmailBtn.disabled = false;
            });
        });
    });
</script>
@endsection
