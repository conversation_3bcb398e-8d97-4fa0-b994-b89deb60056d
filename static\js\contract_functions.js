/**
 * Funciones para la gestión de contratos
 * Este archivo contiene todas las funciones relacionadas con la vista previa, impresión y manipulación de contratos
 */

// Función para calcular la fecha de finalización basada en el tipo de contrato
function calculateEndDate(startDate, contractType) {
    if (!startDate) return '';

    const date = new Date(startDate);

    switch (contractType) {
        case 'two_years':
            date.setFullYear(date.getFullYear() + 2);
            break;
        case 'one_year':
            date.setFullYear(date.getFullYear() + 1);
            break;
        case 'monthly':
            date.setMonth(date.getMonth() + 1);
            break;
        case 'custom':
            date.setMonth(date.getMonth() + 3); // Por defecto 3 meses para contratos personalizados
            break;
        default:
            date.setFullYear(date.getFullYear() + 2); // Por defecto 2 años
    }

    return date.toISOString().split('T')[0]; // Formato YYYY-MM-DD
}

// Función para actualizar la fecha de finalización en el formulario
function updateEndDate() {
    const startDateInput = document.getElementById('id_start_date');
    const contractTypeSelect = document.getElementById('id_contract_type');
    const calculatedEndDateInput = document.getElementById('calculated_end_date');

    if (startDateInput && contractTypeSelect && calculatedEndDateInput) {
        const startDate = startDateInput.value;
        const contractType = contractTypeSelect.value;

        if (startDate && contractType) {
            const endDate = calculateEndDate(startDate, contractType);
            calculatedEndDateInput.value = endDate;
        }
    }
}

// Función para vista previa del contrato
function previewContract(contractId) {
    if (!contractId) {
        // Si estamos en el formulario de creación, recopilamos los datos del formulario
        const formData = collectFormData();
        // Aquí podríamos hacer una llamada AJAX para generar una vista previa
        // Por ahora, abrimos una ventana con los datos básicos
        openPreviewWindow(formData);
    } else {
        // Si tenemos un ID de contrato, abrimos la URL de vista previa
        const url = `/contracts/${contractId}/preview/`;
        const previewWindow = window.open(url, '_blank', 'width=800,height=600');

        if (previewWindow) {
            previewWindow.focus();
        } else {
            alert('Se ha bloqueado la ventana emergente. Por favor, permita ventanas emergentes para este sitio.');
        }
    }
}

// Función para imprimir el contrato
function printContract(contractId) {
    if (!contractId) {
        // Si estamos en el formulario de creación, primero mostramos la vista previa
        const formData = collectFormData();
        const previewWindow = openPreviewWindow(formData);

        if (previewWindow) {
            // Esperamos a que se cargue la página y luego imprimimos
            previewWindow.onload = function() {
                setTimeout(function() {
                    previewWindow.print();
                }, 1000);
            };
        }
    } else {
        // Si tenemos un ID de contrato, abrimos la URL de vista previa con parámetro de impresión
        const url = `/contracts/${contractId}/preview/?print=true`;
        const printWindow = window.open(url, '_blank');

        if (printWindow) {
            // Esperamos a que se cargue la página y luego imprimimos
            printWindow.onload = function() {
                setTimeout(function() {
                    printWindow.print();
                }, 1000);
            };
        } else {
            alert('Se ha bloqueado la ventana emergente. Por favor, permita ventanas emergentes para este sitio.');
        }
    }
}

// Función para recopilar datos del formulario
function collectFormData() {
    const formData = {
        contract_number: document.getElementById('id_contract_number')?.value || '',
        client_name: document.getElementById('id_client')?.options[document.getElementById('id_client')?.selectedIndex]?.text || '',
        client_id: document.getElementById('id_client')?.value || '',
        start_date: document.getElementById('id_start_date')?.value || '',
        end_date: document.getElementById('calculated_end_date')?.value || '',
        total_value: document.getElementById('id_total_value')?.value || '',
        status: document.getElementById('id_status')?.options[document.getElementById('id_status')?.selectedIndex]?.text || '',
        status_value: document.getElementById('id_status')?.value || '',
        contract_type: document.getElementById('id_contract_type')?.value || '',
        description: document.getElementById('id_description')?.value || '',
        terms: document.getElementById('id_terms')?.value || '',
        notes: document.getElementById('id_notes')?.value || '',
        worker_name: document.getElementById('id_worker')?.options[document.getElementById('id_worker')?.selectedIndex]?.text || '',
        worker_id: document.getElementById('id_worker')?.value || '',
        template_id: document.getElementById('id_template')?.value || '',
        template_name: document.getElementById('id_template')?.options[document.getElementById('id_template')?.selectedIndex]?.text || ''
    };

    return formData;
}

// Función para abrir ventana de vista previa con datos del formulario
function openPreviewWindow(formData) {
    // Verificar si hay un template seleccionado
    if (formData.template_id) {
        // Si hay un template seleccionado, intentamos usar la API para obtener la vista previa
        return openTemplatePreview(formData);
    } else {
        // Si no hay template, usamos la vista previa predeterminada
        return openDefaultPreview(formData);
    }
}

// Función para abrir vista previa usando el template seleccionado
function openTemplatePreview(formData) {
    // Crear una ventana para mostrar la vista previa
    const previewWindow = window.open('', '_blank', 'width=800,height=600');

    if (!previewWindow) {
        alert('Se ha bloqueado la ventana emergente. Por favor, permita ventanas emergentes para este sitio.');
        return null;
    }

    // Mostrar un mensaje de carga
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معاينة العقد</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    background-color: white;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                }
                .loading {
                    text-align: center;
                }
                .loading-spinner {
                    border: 6px solid #f3f3f3;
                    border-top: 6px solid #3498db;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    animation: spin 2s linear infinite;
                    margin: 20px auto;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        </head>
        <body>
            <div class="loading">
                <div class="loading-spinner"></div>
                <h2>جاري تحميل معاينة العقد...</h2>
                <p>يرجى الانتظار قليلاً</p>
            </div>
        </body>
        </html>
    `);

    // Preparar los datos para enviar al servidor
    const requestData = {
        template_id: formData.template_id,
        contract_data: {
            contract_number: formData.contract_number,
            client_name: formData.client_name,
            client_id: formData.client_id,
            start_date: formData.start_date,
            end_date: formData.end_date,
            total_value: formData.total_value,
            status: formData.status,
            status_value: formData.status_value,
            contract_type: formData.contract_type,
            worker_name: formData.worker_name,
            worker_id: formData.worker_id,
            description: formData.description,
            terms: formData.terms,
            notes: formData.notes,
            date_created: new Date().toISOString().split('T')[0]
        }
    };

    // Simular la carga del template (en una implementación real, esto sería una llamada AJAX)
    setTimeout(() => {
        // Generar HTML para la vista previa basada en el template
        const templateHtml = generateTemplateHtml(formData);
        previewWindow.document.open();
        previewWindow.document.write(templateHtml);
        previewWindow.document.close();
    }, 1000);

    return previewWindow;
}

// Función para generar HTML basado en el template
function generateTemplateHtml(formData) {
    // En una implementación real, esto vendría del servidor basado en el template seleccionado
    // Por ahora, generamos un HTML mejorado que simula un template

    return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معاينة العقد - ${formData.template_name}</title>
        <style>
            @page {
                size: A4;
                margin: 0;
            }
            body {
                font-family: 'Arial', sans-serif;
                background-color: white;
                margin: 0;
                padding: 0;
                direction: rtl;
            }

            .page {
                width: 210mm;
                height: 297mm;
                padding: 20mm;
                margin: 0 auto;
                background-color: white;
                box-sizing: border-box;
                position: relative;
                page-break-after: always;
            }

            .contract-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #3498db;
                padding-bottom: 20px;
            }

            .contract-title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #2c3e50;
            }

            .contract-subtitle {
                font-size: 18px;
                color: #7f8c8d;
            }

            .contract-section {
                margin-bottom: 25px;
            }

            .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #2c3e50;
                border-bottom: 1px solid #eee;
                padding-bottom: 5px;
            }

            .section-content {
                font-size: 16px;
                line-height: 1.6;
            }

            .contract-footer {
                margin-top: 50px;
                border-top: 1px solid #eee;
                padding-top: 20px;
            }

            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
            }

            .signature-box {
                border-top: 1px solid #000;
                width: 200px;
                text-align: center;
                padding-top: 10px;
            }

            .contract-info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .info-item {
                margin-bottom: 10px;
            }

            .info-label {
                font-weight: bold;
                color: #555;
            }

            .info-value {
                margin-top: 5px;
            }

            .worker-info {
                background-color: #f9f9f9;
                padding: 15px;
                border-radius: 5px;
                margin-top: 20px;
            }

            .contract-logo {
                text-align: center;
                margin-bottom: 20px;
            }

            .contract-logo img {
                max-width: 150px;
                height: auto;
            }

            .watermark {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 100px;
                color: rgba(200, 200, 200, 0.1);
                z-index: -1;
                white-space: nowrap;
            }

            .no-print {
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 1000;
            }

            .print-btn {
                padding: 10px 20px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                display: flex;
                align-items: center;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }

            .print-btn:hover {
                background-color: #2980b9;
            }

            @media print {
                .page {
                    margin: 0;
                    box-shadow: none;
                }

                .no-print {
                    display: none !important;
                }

                body {
                    margin: 0;
                    padding: 0;
                }
            }
        </style>
    </head>
    <body>
        <div class="no-print">
            <button onclick="window.print()" class="print-btn">
                <span style="margin-left: 5px;">&#x1F5A8;</span> طباعة العقد
            </button>
        </div>

        <div class="page">
            <div class="watermark">نسخة معاينة</div>

            <div class="contract-logo">
                <!-- Logo placeholder -->
                <div style="font-size: 40px; color: #3498db;">
                    <i class="fas fa-building"></i>
                </div>
                <div style="font-weight: bold; margin-top: 5px;">اسم الشركة</div>
            </div>

            <div class="contract-header">
                <h1 class="contract-title">عقد عمل</h1>
                <h2 class="contract-subtitle">رقم العقد: ${formData.contract_number}</h2>
            </div>

            <div class="contract-section">
                <h3 class="section-title">معلومات العقد الأساسية</h3>
                <div class="section-content">
                    <div class="contract-info-grid">
                        <div class="info-item">
                            <div class="info-label">رقم العقد</div>
                            <div class="info-value">${formData.contract_number}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">العميل</div>
                            <div class="info-value">${formData.client_name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ البداية</div>
                            <div class="info-value">${formData.start_date}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ الانتهاء</div>
                            <div class="info-value">${formData.end_date}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">القيمة الإجمالية</div>
                            <div class="info-value">${formData.total_value} ريال</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">الحالة</div>
                            <div class="info-value">${formData.status}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">نوع العقد</div>
                            <div class="info-value">
                                ${getContractTypeName(formData.contract_type)}
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">${new Date().toISOString().split('T')[0]}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="contract-section">
                <h3 class="section-title">معلومات العامل</h3>
                <div class="section-content">
                    <div class="worker-info">
                        <p><strong>اسم العامل:</strong> ${formData.worker_name}</p>
                    </div>
                </div>
            </div>

            ${formData.description ? `
            <div class="contract-section">
                <h3 class="section-title">وصف العقد</h3>
                <div class="section-content">
                    <p>${formData.description.replace(/\n/g, '<br>')}</p>
                </div>
            </div>
            ` : ''}

            ${formData.terms ? `
            <div class="contract-section">
                <h3 class="section-title">شروط وأحكام العقد</h3>
                <div class="section-content">
                    <p>${formData.terms.replace(/\n/g, '<br>')}</p>
                </div>
            </div>
            ` : ''}

            <div class="contract-footer">
                <p>تم إنشاء هذا العقد بتاريخ: ${new Date().toISOString().split('T')[0]}</p>
                <p>قالب العقد المستخدم: ${formData.template_name}</p>

                <div class="signature-section">
                    <div class="signature-box">
                        <p>توقيع الطرف الأول</p>
                        <p>الشركة</p>
                    </div>

                    <div class="signature-box">
                        <p>توقيع الطرف الثاني</p>
                        <p>${formData.client_name}</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Si se abrió con parámetro de impresión, imprimir automáticamente
            if (window.opener && window.location.href.indexOf('print=true') > -1) {
                setTimeout(function() {
                    window.print();
                }, 1000);
            }
        </script>
    </body>
    </html>
    `;
}

// Función para obtener el nombre del tipo de contrato
function getContractTypeName(contractType) {
    switch(contractType) {
        case 'two_years':
            return 'عقد سنتين';
        case 'one_year':
            return 'عقد سنة';
        case 'monthly':
            return 'عقد شهري';
        case 'custom':
            return 'عقد مخصص';
        default:
            return contractType;
    }
}

// Función para abrir vista previa predeterminada
function openDefaultPreview(formData) {
    // Crear HTML para la vista previa
    let previewHtml = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معاينة العقد</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                background-color: white;
                margin: 0;
                padding: 20px;
                direction: rtl;
            }

            .contract-preview {
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
                max-width: 800px;
                margin: 0 auto;
            }

            .contract-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #3498db;
                padding-bottom: 20px;
            }

            .contract-title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #2c3e50;
            }

            .contract-subtitle {
                font-size: 18px;
                color: #7f8c8d;
            }

            .contract-section {
                margin-bottom: 25px;
            }

            .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #2c3e50;
                border-bottom: 1px solid #eee;
                padding-bottom: 5px;
            }

            .section-content {
                font-size: 16px;
                line-height: 1.6;
            }

            .contract-footer {
                margin-top: 50px;
                border-top: 1px solid #eee;
                padding-top: 20px;
            }

            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
            }

            .signature-box {
                border-top: 1px solid #000;
                width: 200px;
                text-align: center;
                padding-top: 10px;
            }

            @media print {
                body {
                    background-color: white;
                }

                .contract-preview {
                    box-shadow: none;
                    padding: 0;
                }

                .no-print {
                    display: none !important;
                }
            }
        </style>
    </head>
    <body>
        <div class="no-print" style="text-align: center; margin-bottom: 20px;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                <span style="margin-left: 5px;">&#x1F5A8;</span> طباعة العقد
            </button>
        </div>

        <div class="contract-preview">
            <div class="contract-header">
                <h1 class="contract-title">عقد عمل</h1>
                <h2 class="contract-subtitle">رقم العقد: ${formData.contract_number}</h2>
            </div>

            <div class="contract-section">
                <h3 class="section-title">معلومات العقد الأساسية</h3>
                <div class="section-content">
                    <div style="display: flex; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 250px;">
                            <p><strong>رقم العقد:</strong> ${formData.contract_number}</p>
                            <p><strong>العميل:</strong> ${formData.client_name}</p>
                            <p><strong>تاريخ البداية:</strong> ${formData.start_date}</p>
                        </div>
                        <div style="flex: 1; min-width: 250px;">
                            <p><strong>تاريخ الانتهاء:</strong> ${formData.end_date}</p>
                            <p><strong>القيمة الإجمالية:</strong> ${formData.total_value} ريال</p>
                            <p><strong>الحالة:</strong> ${formData.status}</p>
                        </div>
                    </div>
                </div>
            </div>

            ${formData.worker_name ? `
            <div class="contract-section">
                <h3 class="section-title">معلومات العامل</h3>
                <div class="section-content">
                    <p><strong>اسم العامل:</strong> ${formData.worker_name}</p>
                </div>
            </div>
            ` : ''}

            ${formData.description ? `
            <div class="contract-section">
                <h3 class="section-title">وصف العقد</h3>
                <div class="section-content">
                    <p>${formData.description.replace(/\n/g, '<br>')}</p>
                </div>
            </div>
            ` : ''}

            ${formData.terms ? `
            <div class="contract-section">
                <h3 class="section-title">شروط وأحكام العقد</h3>
                <div class="section-content">
                    <p>${formData.terms.replace(/\n/g, '<br>')}</p>
                </div>
            </div>
            ` : ''}

            ${formData.notes ? `
            <div class="contract-section">
                <h3 class="section-title">ملاحظات</h3>
                <div class="section-content">
                    <p>${formData.notes.replace(/\n/g, '<br>')}</p>
                </div>
            </div>
            ` : ''}

            <div class="contract-footer">
                <p>تم إنشاء هذا العقد بتاريخ: ${new Date().toISOString().split('T')[0]}</p>

                <div class="signature-section">
                    <div class="signature-box">
                        <p>توقيع الطرف الأول</p>
                        <p>الشركة</p>
                    </div>

                    <div class="signature-box">
                        <p>توقيع الطرف الثاني</p>
                        <p>${formData.client_name}</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;

    // Abrir ventana y escribir HTML
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    if (previewWindow) {
        previewWindow.document.write(previewHtml);
        previewWindow.document.close();
        return previewWindow;
    } else {
        alert('Se ha bloqueado la ventana emergente. Por favor, permita ventanas emergentes para este sitio.');
        return null;
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Actualizar fecha de finalización cuando cambie la fecha de inicio o el tipo de contrato
    const startDateInput = document.getElementById('id_start_date');
    const contractTypeSelect = document.getElementById('id_contract_type');

    if (startDateInput) {
        startDateInput.addEventListener('change', updateEndDate);
    }

    if (contractTypeSelect) {
        contractTypeSelect.addEventListener('change', updateEndDate);
    }

    // Inicializar fecha de finalización
    updateEndDate();

    // Botón de vista previa
    const previewBtn = document.getElementById('preview-contract-btn');
    if (previewBtn) {
        previewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Verificar si estamos en la página de detalles o en el formulario
            const contractId = this.getAttribute('data-contract-id');
            previewContract(contractId);
        });
    }

    // Botón de impresión
    const printBtn = document.getElementById('print-contract-btn');
    if (printBtn) {
        printBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Verificar si estamos en la página de detalles o en el formulario
            const contractId = this.getAttribute('data-contract-id');
            printContract(contractId);
        });
    }

    // Inicializar botones de vista previa e impresión en la lista de contratos
    const previewLinks = document.querySelectorAll('.preview-contract-link');
    previewLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const contractId = this.getAttribute('data-contract-id');
            previewContract(contractId);
        });
    });

    const printLinks = document.querySelectorAll('.print-contract-link');
    printLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const contractId = this.getAttribute('data-contract-id');
            printContract(contractId);
        });
    });

    // Si estamos en la página de vista previa y hay un parámetro print=true, imprimir automáticamente
    if (window.location.href.indexOf('print=true') > -1) {
        setTimeout(function() {
            window.print();
        }, 1000);
    }
});
