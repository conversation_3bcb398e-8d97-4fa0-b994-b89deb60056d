# Generated by Django 5.2 on 2025-04-08 14:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('contact_person', models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VisaType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='نوع الفيزا')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'نوع الفيزا',
                'verbose_name_plural': 'أنواع الفيزا',
            },
        ),
        migrations.CreateModel(
            name='Worker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=100, verbose_name='الاسم الأخير')),
                ('nationality', models.CharField(max_length=100, verbose_name='الجنسية')),
                ('gender', models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('passport_number', models.CharField(max_length=50, unique=True, verbose_name='رقم جواز السفر')),
                ('passport_expiry', models.DateField(verbose_name='تاريخ انتهاء جواز السفر')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('arrival_date', models.DateField(verbose_name='تاريخ الوصول')),
                ('visa_expiry', models.DateField(verbose_name='تاريخ انتهاء الفيزا')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='worker_photos/', verbose_name='الصورة الشخصية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='workers.company', verbose_name='الشركة المستقدمة')),
                ('visa_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='workers.visatype', verbose_name='نوع الفيزا')),
            ],
            options={
                'verbose_name': 'عامل',
                'verbose_name_plural': 'العمال',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.CreateModel(
            name='WorkerDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(max_length=100, verbose_name='نوع المستند')),
                ('document_file', models.FileField(upload_to='worker_documents/', verbose_name='الملف')),
                ('upload_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'مستند العامل',
                'verbose_name_plural': 'مستندات العمال',
                'ordering': ['-upload_date'],
            },
        ),
    ]
