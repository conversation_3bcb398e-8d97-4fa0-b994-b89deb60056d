{% extends 'layouts/dashboard.html' %}
{% load static %}

{% block title %}{{ title }} | منصة استقدامي السحابية{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">{{ title }}</h2>
            <p class="text-sm text-gray-500 mt-1">
                {% if worker %}
                    تعديل بيانات العامل: {{ worker.first_name }} {{ worker.last_name }}
                {% else %}
                    إضافة عامل جديد إلى النظام
                {% endif %}
            </p>
        </div>
        <a href="{% url 'workers:dashboard' %}"
           class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
            <i class="fas fa-arrow-right ml-2 text-sm"></i>
            <span>العودة</span>
        </a>
    </div>

    <!-- Worker Type Selection -->
    <div class="bg-white dark:bg-[#1E293B] p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">اختر نوع العامل</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4" x-data="{ workerType: '{{ worker.worker_type|default:worker_type }}' }">
            <!-- عامل عقد دائم -->
            <label class="relative cursor-pointer">
                <input type="radio" name="worker_type_selector" value="contract" class="sr-only peer"
                       onclick="selectWorkerType('contract'); updateSubmitButton('contract');"
                       :checked="workerType === 'contract'">
                <div class="flex flex-col items-center p-4 rounded-lg border-2 transition-all duration-300 h-full
                            peer-checked:border-[#2563EB] peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20
                            border-gray-200 dark:border-gray-700 hover:border-[#2563EB] dark:hover:border-[#3b82f6]">
                    <div class="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mb-3">
                        <i class="fas fa-file-contract text-[#2563EB] text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-gray-800 dark:text-white mb-1">عامل عقد دائم</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
                        عامل مرتبط بعقد طويل الأمد مع الشركة
                    </p>
                </div>
                <div class="absolute top-2 left-2 w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600
                            peer-checked:border-[#2563EB] peer-checked:bg-[#2563EB] peer-checked:flex peer-checked:items-center peer-checked:justify-center hidden">
                    <i class="fas fa-check text-white text-xs"></i>
                </div>
            </label>

            <!-- عامل خدمة مخصصة -->
            <label class="relative cursor-pointer">
                <input type="radio" name="worker_type_selector" value="custom" class="sr-only peer"
                       onclick="selectWorkerType('custom'); updateSubmitButton('custom');"
                       :checked="workerType === 'custom'">
                <div class="flex flex-col items-center p-4 rounded-lg border-2 transition-all duration-300 h-full
                            peer-checked:border-[#F97316] peer-checked:bg-orange-50 dark:peer-checked:bg-orange-900/20
                            border-gray-200 dark:border-gray-700 hover:border-[#F97316] dark:hover:border-[#f97316]">
                    <div class="w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center mb-3">
                        <i class="fas fa-tools text-[#F97316] text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-gray-800 dark:text-white mb-1">عامل خدمة مخصصة</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
                        عامل يقدم خدمات مخصصة حسب طلب العميل
                    </p>
                </div>
                <div class="absolute top-2 left-2 w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600
                            peer-checked:border-[#F97316] peer-checked:bg-[#F97316] peer-checked:flex peer-checked:items-center peer-checked:justify-center hidden">
                    <i class="fas fa-check text-white text-xs"></i>
                </div>
            </label>

            <!-- عامل عقد شهري -->
            <label class="relative cursor-pointer">
                <input type="radio" name="worker_type_selector" value="monthly" class="sr-only peer"
                       onclick="selectWorkerType('monthly'); updateSubmitButton('monthly');"
                       :checked="workerType === 'monthly'">
                <div class="flex flex-col items-center p-4 rounded-lg border-2 transition-all duration-300 h-full
                            peer-checked:border-[#A855F7] peer-checked:bg-purple-50 dark:peer-checked:bg-purple-900/20
                            border-gray-200 dark:border-gray-700 hover:border-[#A855F7] dark:hover:border-[#c084fc]">
                    <div class="w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mb-3">
                        <i class="fas fa-calendar-alt text-[#A855F7] text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-gray-800 dark:text-white mb-1">عامل عقد شهري</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
                        عامل مرتبط بعقد شهري متكرر مع العميل
                    </p>
                </div>
                <div class="absolute top-2 left-2 w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600
                            peer-checked:border-[#A855F7] peer-checked:bg-[#A855F7] peer-checked:flex peer-checked:items-center peer-checked:justify-center hidden">
                    <i class="fas fa-check text-white text-xs"></i>
                </div>
            </label>
        </div>
    </div>

    <!-- Form -->
    <form method="POST" enctype="multipart/form-data" class="space-y-6 bg-white dark:bg-[#1E293B] p-6 rounded-lg shadow-md" x-data="{ workerType: '{{ worker.worker_type|default:worker_type }}' }">
        {% csrf_token %}

        <!-- Personal Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 dark:text-white mb-4 border-r-4 border-blue-600 pr-3">البيانات الشخصية</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="first_name" class="block mb-1 text-gray-700 dark:text-gray-300">الاسم الأول</label>
                    <input type="text" id="first_name" name="first_name" value="{{ worker.first_name|default:'' }}"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="last_name" class="block mb-1 text-gray-700 dark:text-gray-300">الاسم الأخير</label>
                    <input type="text" id="last_name" name="last_name" value="{{ worker.last_name|default:'' }}"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="gender" class="block mb-1 text-gray-700 dark:text-gray-300">الجنس</label>
                    <select id="gender" name="gender" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                        <option value="M" {% if worker.gender == 'M' %}selected{% endif %}>ذكر</option>
                        <option value="F" {% if worker.gender == 'F' %}selected{% endif %}>أنثى</option>
                    </select>
                </div>

                <div>
                    <label for="nationality" class="block mb-1 text-gray-700 dark:text-gray-300">الجنسية</label>
                    <select id="nationality" name="nationality" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                        <option value="">اختر الجنسية</option>
                        {% for code, name in nationality_choices %}
                            <option value="{{ code }}" {% if worker.nationality == code %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="date_of_birth" class="block mb-1 text-gray-700 dark:text-gray-300">تاريخ الميلاد</label>
                    <input type="date" id="date_of_birth" name="date_of_birth" value="{{ worker.date_of_birth|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="recruitment_company" class="block mb-1 text-gray-700 dark:text-gray-300">جهة طلب سمة الدخول</label>
                    <select id="recruitment_company" name="recruitment_company" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        <option value="">اختر جهة طلب سمة الدخول</option>
                        {% for company in recruitment_companies %}
                            <option value="{{ company.id }}" {% if worker.recruitment_company_id == company.id %}selected{% endif %}>{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="job_title" class="block mb-1 text-gray-700 dark:text-gray-300">المهنة</label>
                    <input type="text" id="job_title" name="job_title" value="{{ worker.job_title|default:'' }}"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500"
                           placeholder="مثال: سائق، عامل، ممرضة">
                </div>

                <div>
                    <label for="phone_number" class="block mb-1 text-gray-700 dark:text-gray-300">رقم الهاتف</label>
                    <input type="tel" id="phone_number" name="phone_number" value="{{ worker.phone_number|default:'' }}"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500"
                           placeholder="مثال: +966501234567">
                </div>

                <!-- تم الاحتفاظ بالحقل المخفي لنوع العامل لضمان عمل النموذج بشكل صحيح -->
                <input type="hidden" id="worker_type" name="worker_type" value="{{ worker.worker_type|default:worker_type }}" onchange="console.log('تغيير قيمة الحقل المخفي:', this.value);">
            </div>
        </div>

        <!-- Passport Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">بيانات جواز السفر</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="passport_number" class="block mb-1 text-gray-700">رقم جواز السفر</label>
                    <input type="text" id="passport_number" name="passport_number" value="{{ worker.passport_number|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="passport_issue_date" class="block mb-1 text-gray-700">تاريخ إصدار جواز السفر</label>
                    <input type="date" id="passport_issue_date" name="passport_issue_date" value="{{ worker.passport_issue_date|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="passport_expiry" class="block mb-1 text-gray-700">تاريخ انتهاء جواز السفر</label>
                    <input type="date" id="passport_expiry" name="passport_expiry" value="{{ worker.passport_expiry|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="visa_number" class="block mb-1 text-gray-700">رقم التأشيرة</label>
                    <input type="text" id="visa_number" name="visa_number" value="{{ worker.visa_number|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="entry_date" class="block mb-1 text-gray-700">تاريخ إصدار التأشيرة / الستيكر</label>
                    <input type="date" id="entry_date" name="entry_date" value="{{ worker.entry_date|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="visa_expiry" class="block mb-1 text-gray-700">تاريخ انتهاء التأشيرة / الستيكر</label>
                    <input type="date" id="visa_expiry" name="visa_expiry" value="{{ worker.visa_expiry|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- تم إزالة معلومات العقد الخاصة بكل نوع من أنواع العمال -->

        <!-- Additional Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 dark:text-white mb-4 border-r-4 border-blue-600 pr-3">معلومات إضافية</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="status" class="block mb-1 text-gray-700 dark:text-gray-300">الحالة</label>
                    <select id="status" name="status" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        {% for code, name in status_choices %}
                            <option value="{{ code }}" {% if worker.status == code %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="salary" class="block mb-1 text-gray-700 dark:text-gray-300">الراتب</label>
                    <input type="number" id="salary" name="salary" value="{{ worker.salary|default:'' }}" step="0.01"
                           class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div class="md:col-span-2">
                    <label for="notes" class="block mb-1 text-gray-700 dark:text-gray-300">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3"
                              class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">{{ worker.notes|default:'' }}</textarea>
                </div>
            </div>
        </div>

        <!-- Documents Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">الصور والمستندات</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Document Type 1: الصورة الشخصية -->
                <div class="p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                    <h4 class="text-md font-semibold text-gray-700 dark:text-white mb-3">الصورة الشخصية</h4>

                    <div class="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 text-center">
                        <div class="mb-3">
                            <svg class="mx-auto h-10 w-10 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">اسحب الملف هنا أو انقر للتصفح</p>
                        <div class="flex justify-center">
                            <label for="photo" class="cursor-pointer px-4 py-2 bg-amber-100 text-amber-700 rounded-md hover:bg-amber-200 transition-colors">
                                اختر ملف
                                <input type="file" id="photo" name="photo" accept="image/*" class="hidden">
                            </label>
                        </div>
                        {% if worker.photo %}
                        <div class="mt-4 flex items-center justify-center">
                            <img src="{{ worker.photo.url }}" alt="صورة العامل" class="h-20 w-20 object-cover rounded-md border border-gray-300">
                        </div>
                        {% endif %}
                    </div>

                    <div class="mt-3">
                        <label for="photo_description" class="block mb-1 text-gray-700 dark:text-gray-300">وصف المستند (اختياري)</label>
                        <input type="text" id="photo_description" name="photo_description" placeholder="وصف المستند (اختياري)"
                               class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                    </div>
                </div>

                <!-- Document Type 2: صورة جواز السفر -->
                <div class="p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                    <h4 class="text-md font-semibold text-gray-700 dark:text-white mb-3">صورة جواز السفر</h4>

                    <div class="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 text-center">
                        <div class="mb-3">
                            <svg class="mx-auto h-10 w-10 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">اسحب الملف هنا أو انقر للتصفح</p>
                        <div class="flex justify-center">
                            <label for="passport_image" class="cursor-pointer px-4 py-2 bg-amber-100 text-amber-700 rounded-md hover:bg-amber-200 transition-colors">
                                اختر ملف
                                <input type="file" id="passport_image" name="passport_image" accept="image/*" class="hidden">
                            </label>
                        </div>
                        {% if worker.passport_image %}
                        <div class="mt-4 flex items-center justify-center">
                            <img src="{{ worker.passport_image.url }}" alt="صورة جواز السفر" class="h-20 w-20 object-cover rounded-md border border-gray-300">
                        </div>
                        {% endif %}
                    </div>

                    <div class="mt-3">
                        <label for="passport_image_description" class="block mb-1 text-gray-700 dark:text-gray-300">وصف المستند (اختياري)</label>
                        <input type="text" id="passport_image_description" name="passport_image_description" placeholder="وصف المستند (اختياري)"
                               class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                    </div>
                </div>

                <!-- Document Type 3: صورة التأشيرة/الستيكر -->
                <div class="p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                    <h4 class="text-md font-semibold text-gray-700 dark:text-white mb-3">صورة التأشيرة/الستيكر</h4>

                    <div class="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 text-center">
                        <div class="mb-3">
                            <svg class="mx-auto h-10 w-10 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">اسحب الملف هنا أو انقر للتصفح</p>
                        <div class="flex justify-center">
                            <label for="visa_image" class="cursor-pointer px-4 py-2 bg-amber-100 text-amber-700 rounded-md hover:bg-amber-200 transition-colors">
                                اختر ملف
                                <input type="file" id="visa_image" name="visa_image" accept="image/*" class="hidden">
                            </label>
                        </div>
                        {% if worker.visa_image %}
                        <div class="mt-4 flex items-center justify-center">
                            <img src="{{ worker.visa_image.url }}" alt="صورة التأشيرة" class="h-20 w-20 object-cover rounded-md border border-gray-300">
                        </div>
                        {% endif %}
                    </div>

                    <div class="mt-3">
                        <label for="visa_image_description" class="block mb-1 text-gray-700 dark:text-gray-300">وصف المستند (اختياري)</label>
                        <input type="text" id="visa_image_description" name="visa_image_description" placeholder="وصف المستند (اختياري)"
                               class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
            <button type="submit" id="submitButton"
                    class="px-6 py-2 rounded-md shadow-md transition-all duration-300 flex items-center">
                {% if worker %}
                    <i class="fas fa-save ml-2"></i> حفظ التعديلات
                {% else %}
                    <i class="fas fa-plus ml-2"></i>
                    <span id="submitButtonText">إضافة عامل</span>
                {% endif %}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // دالة لتهيئة مناطق سحب وإفلات الملفات
    function initializeFileDropZones() {
        const dropZones = document.querySelectorAll('.border-dashed');

        dropZones.forEach(zone => {
            const fileInput = zone.querySelector('input[type="file"]');
            if (!fileInput) return;

            // إضافة مستمعي أحداث السحب والإفلات
            zone.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('bg-gray-100', 'dark:bg-gray-700');
            });

            zone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('bg-gray-100', 'dark:bg-gray-700');
            });

            zone.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('bg-gray-100', 'dark:bg-gray-700');

                if (e.dataTransfer.files.length) {
                    fileInput.files = e.dataTransfer.files;

                    // إظهار معاينة الصورة
                    if (fileInput.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            // البحث عن عنصر الصورة الموجود أو إنشاء عنصر جديد
                            let previewContainer = zone.querySelector('.preview-container');

                            if (!previewContainer) {
                                previewContainer = document.createElement('div');
                                previewContainer.className = 'mt-4 flex items-center justify-center preview-container';
                                zone.appendChild(previewContainer);
                            }

                            let imgElement = previewContainer.querySelector('img');

                            if (!imgElement) {
                                imgElement = document.createElement('img');
                                imgElement.className = 'h-24 w-24 object-cover rounded-md border border-gray-300';
                                previewContainer.appendChild(imgElement);
                            }

                            imgElement.src = e.target.result;
                            imgElement.alt = 'معاينة الملف';
                        };
                        reader.readAsDataURL(fileInput.files[0]);
                    }
                }
            });

            // إضافة مستمع لتغيير الملف عند اختياره من زر "اختر ملف"
            fileInput.addEventListener('change', function() {
                if (this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // البحث عن عنصر الصورة الموجود أو إنشاء عنصر جديد
                        let previewContainer = zone.querySelector('.preview-container');

                        if (!previewContainer) {
                            previewContainer = document.createElement('div');
                            previewContainer.className = 'mt-4 flex items-center justify-center preview-container';
                            zone.appendChild(previewContainer);
                        }

                        let imgElement = previewContainer.querySelector('img');

                        if (!imgElement) {
                            imgElement = document.createElement('img');
                            imgElement.className = 'h-24 w-24 object-cover rounded-md border border-gray-300';
                            previewContainer.appendChild(imgElement);
                        }

                        imgElement.src = e.target.result;
                        imgElement.alt = 'معاينة الملف';
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });
        });
    }

    // دالة عامة لاختيار نوع العامل - يتم استدعاؤها من البطاقات العلوية
    function selectWorkerType(type) {
        console.log('تم اختيار نوع العامل من البطاقة:', type);

        // تحديث الحقل المخفي مباشرة
        const workerTypeInput = document.getElementById('worker_type');
        if (workerTypeInput) {
            workerTypeInput.value = type;
            console.log('تم تحديث الحقل المخفي مباشرة:', workerTypeInput.value);

            // إطلاق حدث تغيير لتحديث Alpine.js
            const event = new Event('change');
            workerTypeInput.dispatchEvent(event);
        }

        // تحديث Alpine.js
        if (typeof Alpine !== 'undefined') {
            document.querySelectorAll('[x-data*="workerType"]').forEach(el => {
                if (el.__x) {
                    el.__x.updateData('workerType', type);
                }
            });
        }

        // تحديث عنوان الصفحة
        updatePageTitle(type);

        // تحديث حالة البطاقات
        updateCardSelection(type);

        // تحديث زر الإضافة
        updateSubmitButton(type);
    }

    // تحديث عنوان الصفحة حسب نوع العامل
    function updatePageTitle(workerType) {
        const titleElement = document.querySelector('h2');

        if (titleElement) {
            if (workerType === 'contract') {
                titleElement.textContent = 'إضافة عامل عقد دائم';
            } else if (workerType === 'custom') {
                titleElement.textContent = 'إضافة عامل خدمة مخصصة';
            } else if (workerType === 'monthly') {
                titleElement.textContent = 'إضافة عامل عقد شهري';
            }
        }
    }

    // تحديث حالة البطاقات
    function updateCardSelection(workerType) {
        const workerTypeRadios = document.querySelectorAll('input[name="worker_type_selector"]');
        workerTypeRadios.forEach(radio => {
            radio.checked = (radio.value === workerType);
        });
    }

    // تحديث زر الإضافة
    function updateSubmitButton(workerType) {
        console.log('تحديث زر الإضافة لنوع العامل:', workerType);

        // الحصول على عناصر الزر
        const submitButton = document.getElementById('submitButton');
        const submitButtonText = document.getElementById('submitButtonText');
        const buttonIcon = submitButton ? submitButton.querySelector('i.fas') : null;

        if (!submitButton) {
            console.error('لم يتم العثور على زر الإضافة!');
            return;
        }

        // تعريف الألوان حسب نوع العامل
        let bgColor, borderColor, textColor, hoverBgColor;
        let buttonText = '';

        switch (workerType) {
            case 'contract':
                bgColor = '#f0f7ff';      // خلفية زرقاء فاتحة
                borderColor = '#2563EB';  // حدود زرقاء
                textColor = '#2563EB';    // نص أزرق
                hoverBgColor = '#2563EB'; // خلفية زرقاء عند التحويم
                buttonText = 'إضافة عامل عقد دائم';
                break;

            case 'custom':
                bgColor = '#fff7ed';      // خلفية برتقالية فاتحة
                borderColor = '#F97316';  // حدود برتقالية
                textColor = '#F97316';    // نص برتقالي
                hoverBgColor = '#F97316'; // خلفية برتقالية عند التحويم
                buttonText = 'إضافة عامل خدمة مخصصة';
                break;

            case 'monthly':
                bgColor = '#faf5ff';      // خلفية بنفسجية فاتحة
                borderColor = '#A855F7';  // حدود بنفسجية
                textColor = '#A855F7';    // نص بنفسجي
                hoverBgColor = '#A855F7'; // خلفية بنفسجية عند التحويم
                buttonText = 'إضافة عامل عقد شهري';
                break;

            default:
                console.error('نوع عامل غير معروف:', workerType);
                return;
        }

        // تطبيق الأنماط على الزر
        submitButton.style.backgroundColor = bgColor;
        submitButton.style.borderColor = borderColor;
        submitButton.style.color = textColor;
        submitButton.style.borderWidth = '2px';
        submitButton.style.borderStyle = 'solid';

        // تطبيق الأنماط على الأيقونة
        if (buttonIcon) {
            buttonIcon.style.color = textColor;
        }

        // تحديث نص الزر
        if (submitButtonText) {
            submitButtonText.textContent = buttonText;
        }

        // إضافة تأثيرات التحويم
        submitButton.onmouseover = function() {
            this.style.backgroundColor = hoverBgColor;
            this.style.color = 'white';
            if (buttonIcon) buttonIcon.style.color = 'white';
        };

        submitButton.onmouseout = function() {
            this.style.backgroundColor = bgColor;
            this.style.color = textColor;
            if (buttonIcon) buttonIcon.style.color = textColor;
        };

        console.log('تم تطبيق الأنماط على زر الإضافة:', {
            bgColor: bgColor,
            borderColor: borderColor,
            textColor: textColor,
            buttonText: buttonText
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل الصفحة');

        // تهيئة مناطق سحب وإفلات الملفات
        initializeFileDropZones();

        // الحصول على عناصر النموذج
        const workerTypeInput = document.getElementById('worker_type');
        const submitButton = document.getElementById('submitButton');

        if (!submitButton) {
            console.error('لم يتم العثور على زر الإضافة!');
            return;
        }

        // تحديث الحالة الأولية
        const initialWorkerType = workerTypeInput ? workerTypeInput.value : 'contract';
        console.log('نوع العامل الأولي:', initialWorkerType);

        // تحديث جميع العناصر المرتبطة بنوع العامل
        selectWorkerType(initialWorkerType);

        // تطبيق التحديث الأولي على الزر
        updateSubmitButton(initialWorkerType);

        // تحديث زر الإضافة مرة أخرى بعد تأخير قصير للتأكد من تحميل الصفحة بالكامل
        setTimeout(function() {
            // تحديد البطاقة المختارة حاليًا
            const selectedRadio = document.querySelector('input[name="worker_type_selector"]:checked');
            const currentType = selectedRadio ? selectedRadio.value : initialWorkerType;

            // تطبيق التحديث على الزر
            updateSubmitButton(currentType);
            console.log('تم تحديث زر الإضافة بعد تأخير:', currentType);

            // تطبيق الأنماط مباشرة على الزر للتأكد
            applyDirectStyles(submitButton, currentType);
        }, 300);

        // إضافة مستمع للتغييرات في الحقل المخفي
        workerTypeInput.addEventListener('change', function() {
            console.log('تم تغيير قيمة الحقل المخفي إلى:', this.value);
            // تحديث زر الإضافة عند تغيير نوع العامل
            updateSubmitButton(this.value);
            // تطبيق الأنماط مباشرة على الزر للتأكد
            applyDirectStyles(submitButton, this.value);
        });

        // التحقق من اختيار نوع العامل قبل إرسال النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('قيمة نوع العامل عند إرسال النموذج:', workerTypeInput.value);

            // التحقق من اختيار نوع العامل
            if (!workerTypeInput.value || workerTypeInput.value === '') {
                e.preventDefault(); // منع إرسال النموذج

                // عرض رسالة خطأ
                const errorMessage = document.createElement('div');
                errorMessage.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4';
                errorMessage.innerHTML = `
                    <strong class="font-bold">تنبيه!</strong>
                    <span class="block sm:inline"> يرجى اختيار نوع العامل قبل الحفظ.</span>
                `;

                // إضافة الرسالة إلى أعلى النموذج
                const form = document.querySelector('form');
                form.insertBefore(errorMessage, form.firstChild);

                // تمرير إلى أعلى الصفحة
                window.scrollTo({ top: 0, behavior: 'smooth' });

                // إزالة الرسالة بعد 5 ثوانٍ
                setTimeout(() => {
                    errorMessage.remove();
                }, 5000);

                return false;
            }
        });

        // إضافة مستمعي الأحداث للبطاقات
        document.querySelectorAll('input[name="worker_type_selector"]').forEach(radio => {
            // إضافة مستمع للتغيير
            radio.addEventListener('change', function() {
                console.log('تم تغيير البطاقة إلى:', this.value);
                // تحديث زر الإضافة عند تغيير البطاقة
                updateSubmitButton(this.value);
                // تطبيق الأنماط مباشرة على الزر للتأكد
                applyDirectStyles(submitButton, this.value);
            });

            // إضافة مستمع للنقر
            radio.parentElement.addEventListener('click', function() {
                const radioValue = radio.value;
                console.log('تم النقر على البطاقة:', radioValue);
                // تحديث زر الإضافة عند النقر على البطاقة
                updateSubmitButton(radioValue);
                // تطبيق الأنماط مباشرة على الزر للتأكد
                applyDirectStyles(submitButton, radioValue);
            });

            // تحقق مما إذا كانت البطاقة مختارة بالفعل
            if (radio.checked) {
                console.log('البطاقة المختارة بالفعل:', radio.value);
                updateSubmitButton(radio.value);
                // تطبيق الأنماط مباشرة على الزر للتأكد
                applyDirectStyles(submitButton, radio.value);
            }
        });
    });

    // دالة مساعدة لتطبيق الأنماط مباشرة على الزر
    function applyDirectStyles(button, workerType) {
        if (!button) return;

        const buttonIcon = button.querySelector('i.fas');
        const submitButtonText = document.getElementById('submitButtonText');

        // تحديد الألوان حسب نوع العامل
        let bgColor, borderColor, textColor;
        let buttonText = '';

        switch (workerType) {
            case 'contract':
                bgColor = '#f0f7ff';
                borderColor = '#2563EB';
                textColor = '#2563EB';
                buttonText = 'إضافة عامل عقد دائم';
                break;
            case 'custom':
                bgColor = '#fff7ed';
                borderColor = '#F97316';
                textColor = '#F97316';
                buttonText = 'إضافة عامل خدمة مخصصة';
                break;
            case 'monthly':
                bgColor = '#faf5ff';
                borderColor = '#A855F7';
                textColor = '#A855F7';
                buttonText = 'إضافة عامل عقد شهري';
                break;
            default:
                return;
        }

        // تطبيق الأنماط مباشرة
        button.setAttribute('style', `
            background-color: ${bgColor} !important;
            border: 2px solid ${borderColor} !important;
            color: ${textColor} !important;
        `);

        if (buttonIcon) {
            buttonIcon.setAttribute('style', `color: ${textColor} !important;`);
        }

        if (submitButtonText) {
            submitButtonText.textContent = buttonText;
        }

        console.log('تم تطبيق الأنماط مباشرة على الزر:', workerType);
    }
</script>
{% endblock %}
