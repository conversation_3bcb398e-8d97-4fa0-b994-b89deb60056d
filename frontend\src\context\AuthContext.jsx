import React, { createContext, useState, useCallback } from 'react';
import { toast } from 'react-toastify';
import authService from '../services/auth';

// إنشاء سياق المصادقة
export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // التحقق من حالة المصادقة
  const checkAuth = useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);
        return;
      }
      
      const userData = await authService.getCurrentUser();
      setUser(userData);
      setIsAuthenticated(true);
      setError(null);
    } catch (err) {
      console.error('Error checking authentication:', err);
      setIsAuthenticated(false);
      setUser(null);
      setError('فشل التحقق من حالة المصادقة');
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    } finally {
      setLoading(false);
    }
  }, []);

  // تسجيل الدخول
  const login = async (username, password, serialNumber) => {
    try {
      setLoading(true);
      const data = await authService.login(username, password, serialNumber);
      
      localStorage.setItem('token', data.access);
      localStorage.setItem('refreshToken', data.refresh);
      localStorage.setItem('serialNumber', serialNumber);
      
      setUser(data.user);
      setIsAuthenticated(true);
      setError(null);
      
      toast.success('تم تسجيل الدخول بنجاح');
      return true;
    } catch (err) {
      console.error('Login error:', err);
      setError(err.response?.data?.detail || 'فشل تسجيل الدخول');
      toast.error(err.response?.data?.detail || 'فشل تسجيل الدخول');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      setLoading(true);
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        await authService.logout(refreshToken);
      }
      
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('serialNumber');
      
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (err) {
      console.error('Logout error:', err);
      // تسجيل الخروج محليًا حتى في حالة فشل الطلب
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('serialNumber');
      
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  // تحديث رمز الوصول
  const refreshToken = async () => {
    try {
      const refresh = localStorage.getItem('refreshToken');
      
      if (!refresh) {
        throw new Error('No refresh token available');
      }
      
      const data = await authService.refreshToken(refresh);
      localStorage.setItem('token', data.access);
      
      return data.access;
    } catch (err) {
      console.error('Token refresh error:', err);
      logout();
      throw err;
    }
  };

  // تغيير كلمة المرور
  const changePassword = async (oldPassword, newPassword) => {
    try {
      setLoading(true);
      await authService.changePassword(oldPassword, newPassword);
      toast.success('تم تغيير كلمة المرور بنجاح');
      return true;
    } catch (err) {
      console.error('Change password error:', err);
      setError(err.response?.data?.detail || 'فشل تغيير كلمة المرور');
      toast.error(err.response?.data?.detail || 'فشل تغيير كلمة المرور');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    user,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    checkAuth,
    refreshToken,
    changePassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
