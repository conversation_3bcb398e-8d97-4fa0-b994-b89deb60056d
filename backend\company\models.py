from django.db import models
from django.contrib.auth.models import User
from backend.super_admin.models import Company


class CompanyUser(models.Model):
    """نموذج مستخدمي الشركة مع نظام الأدوار والصلاحيات"""

    # خيارات الأدوار
    ROLE_CHOICES = [
        ('admin', 'مدير الشركة'),
        ('hr_manager', 'مدير الموارد البشرية'),
        ('finance_manager', 'مدير المالية'),
        ('operations_manager', 'مدير العمليات'),
        ('employee', 'موظف'),
        ('viewer', 'مشاهد فقط'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="الشركة")
    role = models.CharField(max_length=50, choices=ROLE_CHOICES, default='employee', verbose_name="الدور")

    # الصلاحيات التفصيلية
    can_manage_users = models.BooleanField(default=False, verbose_name="إدارة المستخدمين")
    can_manage_workers = models.BooleanField(default=True, verbose_name="إدارة العمال")
    can_manage_clients = models.BooleanField(default=False, verbose_name="إدارة العملاء")
    can_manage_contracts = models.BooleanField(default=False, verbose_name="إدارة العقود")
    can_manage_finance = models.BooleanField(default=False, verbose_name="إدارة المالية")
    can_view_reports = models.BooleanField(default=True, verbose_name="عرض التقارير")
    can_export_data = models.BooleanField(default=False, verbose_name="تصدير البيانات")
    can_manage_settings = models.BooleanField(default=False, verbose_name="إدارة الإعدادات")

    # إخفاء الأقسام (صلاحيات عكسية)
    hide_workers_section = models.BooleanField(default=False, verbose_name="إخفاء قسم العمال")
    hide_clients_section = models.BooleanField(default=False, verbose_name="إخفاء قسم العملاء")
    hide_contracts_section = models.BooleanField(default=False, verbose_name="إخفاء قسم العقود")
    hide_procedures_section = models.BooleanField(default=False, verbose_name="إخفاء قسم الإجراءات")
    hide_services_section = models.BooleanField(default=False, verbose_name="إخفاء قسم الخدمات")
    hide_finance_section = models.BooleanField(default=False, verbose_name="إخفاء قسم المالية")
    hide_reports_section = models.BooleanField(default=False, verbose_name="إخفاء قسم التقارير")
    hide_tools_section = models.BooleanField(default=False, verbose_name="إخفاء قسم الأدوات المساعدة")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='created_company_users', verbose_name="أنشأ بواسطة")
    last_login = models.DateTimeField(null=True, blank=True, verbose_name="آخر تسجيل دخول")

    class Meta:
        verbose_name = "مستخدم الشركة"
        verbose_name_plural = "مستخدمو الشركات"
        db_table = 'company_companyuser'
        unique_together = ['user', 'company']

    def __str__(self):
        return f"{self.user.username} - {self.company.name} ({self.get_role_display()})"

    def save(self, *args, **kwargs):
        """تعيين الصلاحيات تلقائياً حسب الدور"""
        if self.role == 'admin':
            # مدير الشركة له جميع الصلاحيات
            self.can_manage_users = True
            self.can_manage_workers = True
            self.can_manage_clients = True
            self.can_manage_contracts = True
            self.can_manage_finance = True
            self.can_view_reports = True
            self.can_export_data = True
            self.can_manage_settings = True
        elif self.role == 'hr_manager':
            # مدير الموارد البشرية
            self.can_manage_users = True
            self.can_manage_workers = True
            self.can_manage_clients = False
            self.can_manage_contracts = True
            self.can_manage_finance = False
            self.can_view_reports = True
            self.can_export_data = True
            self.can_manage_settings = False
        elif self.role == 'finance_manager':
            # مدير المالية
            self.can_manage_users = False
            self.can_manage_workers = False
            self.can_manage_clients = True
            self.can_manage_contracts = True
            self.can_manage_finance = True
            self.can_view_reports = True
            self.can_export_data = True
            self.can_manage_settings = False
        elif self.role == 'operations_manager':
            # مدير العمليات
            self.can_manage_users = False
            self.can_manage_workers = True
            self.can_manage_clients = True
            self.can_manage_contracts = False
            self.can_manage_finance = False
            self.can_view_reports = True
            self.can_export_data = False
            self.can_manage_settings = False
        elif self.role == 'employee':
            # موظف عادي
            self.can_manage_users = False
            self.can_manage_workers = True
            self.can_manage_clients = False
            self.can_manage_contracts = False
            self.can_manage_finance = False
            self.can_view_reports = True
            self.can_export_data = False
            self.can_manage_settings = False
        elif self.role == 'viewer':
            # مشاهد فقط
            self.can_manage_users = False
            self.can_manage_workers = False
            self.can_manage_clients = False
            self.can_manage_contracts = False
            self.can_manage_finance = False
            self.can_view_reports = True
            self.can_export_data = False
            self.can_manage_settings = False

        super().save(*args, **kwargs)

    def has_permission(self, permission):
        """التحقق من وجود صلاحية معينة"""
        permission_map = {
            'manage_users': self.can_manage_users,
            'manage_workers': self.can_manage_workers,
            'manage_clients': self.can_manage_clients,
            'manage_contracts': self.can_manage_contracts,
            'manage_finance': self.can_manage_finance,
            'view_reports': self.can_view_reports,
            'export_data': self.can_export_data,
            'manage_settings': self.can_manage_settings,
        }
        return permission_map.get(permission, False)

    def can_view_section(self, section):
        """التحقق من إمكانية عرض قسم معين"""
        section_map = {
            'workers': not self.hide_workers_section,
            'clients': not self.hide_clients_section,
            'contracts': not self.hide_contracts_section,
            'procedures': not self.hide_procedures_section,
            'services': not self.hide_services_section,
            'finance': not self.hide_finance_section,
            'reports': not self.hide_reports_section,
            'tools': not self.hide_tools_section,
        }
        return section_map.get(section, True)

    def get_permissions_list(self):
        """الحصول على قائمة الصلاحيات"""
        permissions = []
        if self.can_manage_users: permissions.append('إدارة المستخدمين')
        if self.can_manage_workers: permissions.append('إدارة العمال')
        if self.can_manage_clients: permissions.append('إدارة العملاء')
        if self.can_manage_contracts: permissions.append('إدارة العقود')
        if self.can_manage_finance: permissions.append('إدارة المالية')
        if self.can_view_reports: permissions.append('عرض التقارير')
        if self.can_export_data: permissions.append('تصدير البيانات')
        if self.can_manage_settings: permissions.append('إدارة الإعدادات')
        return permissions

    def get_hidden_sections_list(self):
        """الحصول على قائمة الأقسام المخفية"""
        hidden_sections = []
        if self.hide_workers_section: hidden_sections.append('العمال')
        if self.hide_clients_section: hidden_sections.append('العملاء')
        if self.hide_contracts_section: hidden_sections.append('العقود')
        if self.hide_procedures_section: hidden_sections.append('الإجراءات')
        if self.hide_services_section: hidden_sections.append('الخدمات')
        if self.hide_finance_section: hidden_sections.append('المالية')
        if self.hide_reports_section: hidden_sections.append('التقارير')
        if self.hide_tools_section: hidden_sections.append('الأدوات المساعدة')
        return hidden_sections

    def get_visible_sections_list(self):
        """الحصول على قائمة الأقسام المرئية"""
        all_sections = ['العمال', 'العملاء', 'العقود', 'الإجراءات', 'الخدمات', 'المالية', 'التقارير', 'الأدوات المساعدة']
        hidden_sections = self.get_hidden_sections_list()
        return [section for section in all_sections if section not in hidden_sections]
