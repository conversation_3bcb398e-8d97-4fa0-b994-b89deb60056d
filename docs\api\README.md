# وثائق واجهة برمجة التطبيقات (API)

## نظرة عامة

توفر واجهة برمجة التطبيقات (API) في نظام إدارة العمالة وصولاً برمجياً إلى جميع وظائف النظام، بما في ذلك:
- إدارة العمال
- إدارة العقود
- إدارة الخدمات
- إدارة المستندات
- إدارة التقارير

## المصادقة

تستخدم واجهة برمجة التطبيقات مصادقة JWT (JSON Web Token) للتحقق من هوية المستخدمين.

### الحصول على رمز الوصول

```
POST /api/token/
```

**المعلمات المطلوبة:**
- `username`: اسم المستخدم
- `password`: كلمة المرور
- `company_code`: رمز الشركة

**الاستجابة:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### تحديث رمز الوصول

```
POST /api/token/refresh/
```

**المعلمات المطلوبة:**
- `refresh`: رمز التحديث

**الاستجابة:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## نقاط النهاية الرئيسية

### العمال

#### الحصول على قائمة العمال

```
GET /api/workers/
```

#### الحصول على تفاصيل عامل

```
GET /api/workers/{id}/
```

#### إنشاء عامل جديد

```
POST /api/workers/
```

#### تحديث بيانات عامل

```
PUT /api/workers/{id}/
```

#### حذف عامل

```
DELETE /api/workers/{id}/
```

### العقود

#### الحصول على قائمة العقود

```
GET /api/contracts/
```

#### الحصول على تفاصيل عقد

```
GET /api/contracts/{id}/
```

#### إنشاء عقد جديد

```
POST /api/contracts/
```

#### تحديث بيانات عقد

```
PUT /api/contracts/{id}/
```

#### حذف عقد

```
DELETE /api/contracts/{id}/
```

### الخدمات

#### الحصول على قائمة الخدمات

```
GET /api/services/
```

#### الحصول على تفاصيل خدمة

```
GET /api/services/{id}/
```

#### إنشاء خدمة جديدة

```
POST /api/services/
```

#### تحديث بيانات خدمة

```
PUT /api/services/{id}/
```

#### حذف خدمة

```
DELETE /api/services/{id}/
```

## أمثلة على الاستخدام

### مثال على إنشاء عامل جديد

**الطلب:**
```
POST /api/workers/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
  "name": "أحمد محمد",
  "nationality": "مصري",
  "passport_number": "A12345678",
  "job_title": "فني تنظيف",
  "salary": 1500,
  "contract_start_date": "2023-01-01",
  "contract_end_date": "2024-01-01"
}
```

**الاستجابة:**
```json
{
  "id": 123,
  "name": "أحمد محمد",
  "nationality": "مصري",
  "passport_number": "A12345678",
  "job_title": "فني تنظيف",
  "salary": 1500,
  "contract_start_date": "2023-01-01",
  "contract_end_date": "2024-01-01",
  "created_at": "2023-05-10T12:34:56Z",
  "updated_at": "2023-05-10T12:34:56Z"
}
```

## التعامل مع الأخطاء

تستخدم واجهة برمجة التطبيقات رموز حالة HTTP القياسية للإشارة إلى نجاح أو فشل الطلب:

- `200 OK`: تم تنفيذ الطلب بنجاح
- `201 Created`: تم إنشاء الكائن بنجاح
- `400 Bad Request`: طلب غير صالح
- `401 Unauthorized`: مصادقة غير صالحة
- `403 Forbidden`: ليس لديك صلاحية للوصول إلى هذا المورد
- `404 Not Found`: المورد غير موجود
- `500 Internal Server Error`: خطأ في الخادم

## المزيد من المعلومات

للمزيد من المعلومات حول واجهة برمجة التطبيقات، يمكنك زيارة صفحة التوثيق التفاعلية:

```
GET /api/docs/
```

أو صفحة Swagger UI:

```
GET /api/swagger/
```
