from django.contrib import admin
from .models import (
    BloodTest, SponsorshipTransfer, Operation,
    WorkPermit, ResidenceID, BranchTransfer,
    ProcedureAttachment
)

class ProcedureAttachmentInline(admin.TabularInline):
    model = ProcedureAttachment
    extra = 1
    fields = ('file', 'description', 'uploaded_by', 'uploaded_at')
    readonly_fields = ('uploaded_by', 'uploaded_at')

@admin.register(BloodTest)
class BloodTestAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'reservation_date', 'result', 'created_at')
    list_filter = ('status', 'result', 'reservation_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'reference_number')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(SponsorshipTransfer)
class SponsorshipTransferAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'contract_number', 'contract_date', 'created_at')
    list_filter = ('status', 'contract_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'contract_number', 'client_name')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(Operation)
class OperationAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'contract_number', 'contract_date', 'created_at')
    list_filter = ('status', 'contract_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'contract_number', 'client_name')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(WorkPermit)
class WorkPermitAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'permit_number', 'issue_date', 'expiry_date', 'created_at')
    list_filter = ('status', 'issue_date', 'expiry_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'permit_number')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(ResidenceID)
class ResidenceIDAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'id_number', 'issue_date', 'expiry_date', 'created_at')
    list_filter = ('status', 'issue_date', 'expiry_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'id_number')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(BranchTransfer)
class BranchTransferAdmin(admin.ModelAdmin):
    list_display = ('worker', 'status', 'destination_branch', 'transfer_date', 'created_at')
    list_filter = ('status', 'destination_branch', 'transfer_date', 'created_at')
    search_fields = ('worker__first_name', 'worker__last_name', 'worker__passport_number', 'reason')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProcedureAttachmentInline]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(ProcedureAttachment)
class ProcedureAttachmentAdmin(admin.ModelAdmin):
    list_display = ('get_procedure_type', 'get_worker', 'description', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('description', 'blood_test__worker__first_name', 'blood_test__worker__last_name')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def get_procedure_type(self, obj):
        if obj.blood_test:
            return 'فحص الدم'
        elif obj.sponsorship_transfer:
            return 'تحويل الكفالة'
        elif obj.operation:
            return 'التشغيل'
        elif obj.work_permit:
            return 'إجازة العمل'
        elif obj.residence_id:
            return 'هوية الإقامة'
        elif obj.branch_transfer:
            return 'النقل إلى فرع آخر'
        return 'غير محدد'
    get_procedure_type.short_description = 'نوع الإجراء'

    def get_worker(self, obj):
        if obj.blood_test:
            return f"{obj.blood_test.worker.first_name} {obj.blood_test.worker.last_name}"
        elif obj.sponsorship_transfer:
            return f"{obj.sponsorship_transfer.worker.first_name} {obj.sponsorship_transfer.worker.last_name}"
        elif obj.operation:
            return f"{obj.operation.worker.first_name} {obj.operation.worker.last_name}"
        elif obj.work_permit:
            return f"{obj.work_permit.worker.first_name} {obj.work_permit.worker.last_name}"
        elif obj.residence_id:
            return f"{obj.residence_id.worker.first_name} {obj.residence_id.worker.last_name}"
        elif obj.branch_transfer:
            return f"{obj.branch_transfer.worker.first_name} {obj.branch_transfer.worker.last_name}"
        return 'غير محدد'
    get_worker.short_description = 'العامل'

    def save_model(self, request, obj, form, change):
        if not change:
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)