from django.urls import path
from .views import super_admin, dashboard, worker, client, contract, service, procedure, branch, settings, reports

app_name = 'labor_management'

urlpatterns = [
    # Super Admin URLs
    path('superadmin/login/', super_admin.super_admin_login, name='super_admin_login'),
    path('superadmin/resend-2fa/', super_admin.resend_2fa_setup, name='super_admin_resend_2fa'),
    path('superadmin/dashboard/', super_admin.super_admin_dashboard, name='super_admin_dashboard'),
    path('superadmin/companies/', super_admin.company_list, name='super_admin_company_list'),
    path('superadmin/companies/add/', super_admin.company_create, name='super_admin_company_add'),
    path('superadmin/companies/<int:company_id>/', super_admin.company_detail, name='super_admin_company_detail'),
    path('superadmin/companies/<int:company_id>/edit/', super_admin.company_edit, name='super_admin_company_edit'),
    path('superadmin/companies/<int:company_id>/delete/', super_admin.company_delete, name='super_admin_company_delete'),
    path('superadmin/companies/<int:company_id>/test-connection/', super_admin.company_test_connection, name='super_admin_company_test_connection'),

    # Dashboard URLs
    path('', dashboard.index, name='dashboard'),

    # Worker URLs
    path('workers/', worker.worker_list, name='worker_list'),
    path('workers/add/', worker.worker_create, name='worker_add'),
    path('workers/<int:worker_id>/', worker.worker_detail, name='worker_detail'),
    path('workers/<int:worker_id>/edit/', worker.worker_edit, name='worker_edit'),
    path('workers/<int:worker_id>/delete/', worker.worker_delete, name='worker_delete'),

    # Worker Transfer URLs
    path('worker-transfers/', worker.worker_transfer_list, name='worker_transfer_list'),
    path('worker-transfers/add/', worker.worker_transfer_create, name='worker_transfer_add'),
    path('worker-transfers/<int:transfer_id>/', worker.worker_transfer_detail, name='worker_transfer_detail'),
    path('worker-transfers/<int:transfer_id>/edit/', worker.worker_transfer_edit, name='worker_transfer_edit'),
    path('worker-transfers/<int:transfer_id>/delete/', worker.worker_transfer_delete, name='worker_transfer_delete'),

    # Client URLs
    path('clients/', client.client_list, name='client_list'),
    path('clients/add/', client.client_create, name='client_add'),
    path('clients/<int:client_id>/', client.client_detail, name='client_detail'),
    path('clients/<int:client_id>/edit/', client.client_edit, name='client_edit'),
    path('clients/<int:client_id>/delete/', client.client_delete, name='client_delete'),

    # Contract URLs
    path('contracts/', contract.contract_list, name='contract_list'),
    path('contracts/add/', contract.contract_create, name='contract_add'),
    path('contracts/<int:contract_id>/', contract.contract_detail, name='contract_detail'),
    path('contracts/<int:contract_id>/edit/', contract.contract_edit, name='contract_edit'),
    path('contracts/<int:contract_id>/delete/', contract.contract_delete, name='contract_delete'),
    path('contracts/<int:contract_id>/print/', contract.contract_print, name='contract_print'),

    # Daily Service URLs
    path('daily-services/', service.daily_service_list, name='daily_service_list'),
    path('daily-services/add/', service.daily_service_create, name='daily_service_add'),
    path('daily-services/<int:service_id>/', service.daily_service_detail, name='daily_service_detail'),
    path('daily-services/<int:service_id>/edit/', service.daily_service_edit, name='daily_service_edit'),
    path('daily-services/<int:service_id>/delete/', service.daily_service_delete, name='daily_service_delete'),
    path('daily-services/calendar/', service.daily_service_calendar, name='daily_service_calendar'),
    path('daily-services/summary/', service.daily_service_summary, name='daily_service_summary'),

    # 24-Hour Service URLs
    path('24h-services/', service.daily_24h_service_list, name='daily_24h_service_list'),
    path('24h-services/add/', service.daily_24h_service_create, name='daily_24h_service_add'),
    path('24h-services/<int:service_id>/', service.daily_24h_service_detail, name='daily_24h_service_detail'),
    path('24h-services/<int:service_id>/edit/', service.daily_24h_service_edit, name='daily_24h_service_edit'),
    path('24h-services/<int:service_id>/delete/', service.daily_24h_service_delete, name='daily_24h_service_delete'),
    path('24h-services/calendar/', service.daily_24h_service_calendar, name='daily_24h_service_calendar'),
    path('24h-services/summary/', service.daily_24h_service_summary, name='daily_24h_service_summary'),

    # Custom Service URLs
    path('custom-services/', service.custom_service_list, name='custom_service_list'),
    path('custom-services/add/', service.custom_service_create, name='custom_service_add'),
    path('custom-services/<int:service_id>/', service.custom_service_detail, name='custom_service_detail'),
    path('custom-services/<int:service_id>/edit/', service.custom_service_edit, name='custom_service_edit'),
    path('custom-services/<int:service_id>/delete/', service.custom_service_delete, name='custom_service_delete'),
    path('custom-services/calendar/', service.custom_service_calendar, name='custom_service_calendar'),

    # Service Utility APIs
    path('api/available-workers/', service.available_workers, name='api_available_workers'),
    path('api/service-statistics/', service.service_statistics, name='api_service_statistics'),

    # Blood Test URLs
    path('blood-tests/', procedure.blood_test_list, name='blood_test_list'),
    path('blood-tests/add/', procedure.blood_test_create, name='blood_test_add'),
    path('blood-tests/<int:test_id>/', procedure.blood_test_detail, name='blood_test_detail'),
    path('blood-tests/<int:test_id>/edit/', procedure.blood_test_edit, name='blood_test_edit'),
    path('blood-tests/<int:test_id>/delete/', procedure.blood_test_delete, name='blood_test_delete'),
    path('blood-tests/<int:test_id>/results/', procedure.blood_test_results, name='blood_test_results'),

    # Sponsorship Transfer URLs
    path('sponsorship-transfers/', procedure.sponsorship_transfer_list, name='sponsorship_transfer_list'),
    path('sponsorship-transfers/add/', procedure.sponsorship_transfer_create, name='sponsorship_transfer_add'),
    path('sponsorship-transfers/<int:transfer_id>/', procedure.sponsorship_transfer_detail, name='sponsorship_transfer_detail'),
    path('sponsorship-transfers/<int:transfer_id>/edit/', procedure.sponsorship_transfer_edit, name='sponsorship_transfer_edit'),
    path('sponsorship-transfers/<int:transfer_id>/delete/', procedure.sponsorship_transfer_delete, name='sponsorship_transfer_delete'),
    path('sponsorship-transfers/<int:transfer_id>/print/', procedure.sponsorship_transfer_print, name='sponsorship_transfer_print'),

    # Worker Operation URLs
    path('worker-operations/', procedure.worker_operation_list, name='worker_operation_list'),
    path('worker-operations/add/', procedure.worker_operation_create, name='worker_operation_add'),
    path('worker-operations/<int:operation_id>/', procedure.worker_operation_detail, name='worker_operation_detail'),
    path('worker-operations/<int:operation_id>/edit/', procedure.worker_operation_edit, name='worker_operation_edit'),
    path('worker-operations/<int:operation_id>/delete/', procedure.worker_operation_delete, name='worker_operation_delete'),
    path('worker-operations/<int:operation_id>/documents/', procedure.worker_operation_documents, name='worker_operation_documents'),

    # Branch URLs
    path('branches/', branch.branch_list, name='branch_list'),
    path('branches/add/', branch.branch_create, name='branch_add'),
    path('branches/<int:branch_id>/', branch.branch_detail, name='branch_detail'),
    path('branches/<int:branch_id>/edit/', branch.branch_edit, name='branch_edit'),
    path('branches/<int:branch_id>/delete/', branch.branch_delete, name='branch_delete'),

    # Settings URLs
    path('settings/', settings.settings_general, name='settings_general'),
    path('settings/pricing/', settings.settings_pricing, name='settings_pricing'),
    path('settings/overtime/', settings.settings_overtime, name='settings_overtime'),

    # Report URLs
    path('reports/daily/', reports.report_daily, name='report_daily'),
    path('reports/monthly/', reports.report_monthly, name='report_monthly'),
    path('reports/contracts/', reports.report_contracts, name='report_contracts'),
    path('reports/financial/', reports.report_financial, name='report_financial'),

    # تم حذف جميع مسارات تسجيل الدخول القديمة والاعتماد فقط على المسارات الجديدة
    # في backend\super_admin\urls.py و company\urls.py
]
