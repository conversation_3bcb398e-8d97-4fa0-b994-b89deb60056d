from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class Document(models.Model):
    """Model for incoming and outgoing documents."""

    DOCUMENT_TYPE_CHOICES = (
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
    )

    STATUS_CHOICES = (
        ('completed', 'مكتملة'),
        ('pending', 'قيد الإجراء'),
        ('rejected', 'مرفوضة'),
    )

    document_number = models.CharField(max_length=50, verbose_name='رقم المعاملة')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPE_CHOICES, verbose_name='نوع المعاملة')
    entity = models.CharField(max_length=100, verbose_name='الجهة')
    subject = models.CharField(max_length=200, verbose_name='الموضوع')
    details = models.TextField(blank=True, null=True, verbose_name='تفاصيل المعاملة')
    document_date = models.DateField(verbose_name='تاريخ المعاملة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='حالة المعاملة')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_documents', verbose_name='تم الإنشاء بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'معاملة'
        verbose_name_plural = 'معاملات'
        ordering = ['-document_date']

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.document_number} - {self.subject}"


class DocumentAttachment(models.Model):
    """Model for document attachments."""

    ATTACHMENT_TYPE_CHOICES = (
        ('passport', 'جواز سفر'),
        ('visa_sticker', 'ستيكر التأشيرة'),
        ('passport_with_visa', 'جواز سفر مع الستيكر'),
        ('iqama', 'إقامة'),
        ('contract', 'عقد عمل'),
        ('medical', 'تقرير طبي'),
        ('certificate', 'شهادة'),
        ('other', 'أخرى'),
    )

    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='attachments', verbose_name='المعاملة')
    file = models.FileField(upload_to='documents/attachments/%Y/%m/', verbose_name='الملف')
    attachment_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPE_CHOICES, default='other', verbose_name='نوع المرفق')
    description = models.CharField(max_length=200, blank=True, null=True, verbose_name='وصف الملف')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='تم الرفع بواسطة')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')

    class Meta:
        verbose_name = 'مرفق معاملة'
        verbose_name_plural = 'مرفقات المعاملات'
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"مرفق للمعاملة {self.document.document_number}"

    @property
    def filename(self):
        return self.file.name.split('/')[-1]

    def get_delete_url(self):
        """Return the URL to delete this attachment."""
        from django.urls import reverse
        return reverse('documents:delete_document_attachment', args=[self.document.id, self.id])
