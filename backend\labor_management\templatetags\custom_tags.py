from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter
def getattribute(obj, attr):
    """
    استخراج قيمة من كائن باستخدام اسم الخاصية
    مثال: {{ object|getattribute:"attribute_name" }}
    """
    if hasattr(obj, attr):
        return getattr(obj, attr)
    
    # محاولة الوصول إلى القيمة كعنصر في قاموس
    try:
        return obj[attr]
    except (KeyError, TypeError):
        return ""

@register.filter
def getitem(dictionary, key):
    """
    استخراج قيمة من قاموس باستخدام المفتاح
    مثال: {{ dictionary|getitem:"key" }}
    """
    if dictionary is None:
        return ""
    
    try:
        return dictionary.get(key, "")
    except (AttributeError, TypeError):
        return ""

@register.filter
@stringfilter
def replace_id(url, id):
    """
    استبدال {id} في URL بقيمة معينة
    مثال: {{ url|replace_id:object.id }}
    """
    return url.replace('{id}', str(id))

@register.filter
def add_class(field, css_class):
    """
    إضافة فئة CSS إلى حقل نموذج
    مثال: {{ form.field|add_class:"form-control" }}
    """
    return field.as_widget(attrs={"class": css_class})

@register.filter
def add_placeholder(field, placeholder):
    """
    إضافة نص توضيحي إلى حقل نموذج
    مثال: {{ form.field|add_placeholder:"أدخل القيمة" }}
    """
    return field.as_widget(attrs={"placeholder": placeholder})

@register.filter
def add_attr(field, attr):
    """
    إضافة سمة إلى حقل نموذج
    مثال: {{ form.field|add_attr:"data-toggle:modal" }}
    """
    attrs = {}
    parts = attr.split(':')
    if len(parts) == 2:
        attrs[parts[0]] = parts[1]
    return field.as_widget(attrs=attrs)

@register.simple_tag
def query_transform(request, **kwargs):
    """
    تحويل معلمات الاستعلام في URL
    مثال: {% query_transform request page=1 sort="name" %}
    """
    updated = request.GET.copy()
    for key, value in kwargs.items():
        if value is not None:
            updated[key] = value
        else:
            updated.pop(key, None)
    return updated.urlencode()

@register.simple_tag
def pagination_range(current_page, total_pages, window=5):
    """
    إنشاء نطاق لترقيم الصفحات
    مثال: {% pagination_range page.number page.paginator.num_pages %}
    """
    if total_pages <= window:
        return range(1, total_pages + 1)
    
    half_window = window // 2
    
    if current_page <= half_window:
        return range(1, window + 1)
    elif current_page >= total_pages - half_window:
        return range(total_pages - window + 1, total_pages + 1)
    else:
        return range(current_page - half_window, current_page + half_window + 1)

@register.simple_tag
def get_verbose_name(instance, field_name):
    """
    الحصول على الاسم الوصفي لحقل في نموذج
    مثال: {% get_verbose_name object "field_name" %}
    """
    return instance._meta.get_field(field_name).verbose_name

@register.filter
def get_item_display(obj, field_name):
    """
    الحصول على القيمة المعروضة لحقل اختيار
    مثال: {{ object|get_item_display:"status" }}
    """
    display_method = f"get_{field_name}_display"
    if hasattr(obj, display_method):
        return getattr(obj, display_method)()
    return getattr(obj, field_name, "")
