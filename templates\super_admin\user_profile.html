{% extends 'layouts/super_admin.html' %}

{% block title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-user-circle ml-2"></i>
                    الملف الشخصي
                </h1>
                <p class="text-blue-100">
                    إدارة بيانات الحساب وإعدادات الأمان
                </p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-2xl text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-id-card ml-2"></i>
                البيانات الشخصية
            </h2>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <i class="fas fa-shield-alt ml-1"></i>
                مسؤول أعلى
            </span>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                        الاسم الأول
                    </label>
                    <input type="text" 
                           id="first_name" 
                           name="first_name" 
                           value="{{ user.first_name|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Last Name -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم العائلة
                    </label>
                    <input type="text" 
                           id="last_name" 
                           name="last_name" 
                           value="{{ user.last_name|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                        <span class="text-red-500">*</span>
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="{{ user.email|default:'' }}"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-xs text-gray-500 mt-1">
                        <i class="fas fa-info-circle ml-1"></i>
                        يُستخدم لإرسال رموز المصادقة الثنائية
                    </p>
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                    </label>
                    <input type="tel" 
                           id="phone" 
                           name="phone" 
                           value="{{ profile.phone|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <!-- Username (Read-only) -->
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                    اسم المستخدم
                </label>
                <input type="text" 
                       id="username" 
                       value="{{ user.username }}"
                       readonly
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed">
                <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-lock ml-1"></i>
                    لا يمكن تغيير اسم المستخدم لأسباب أمنية
                </p>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>

    <!-- Security Settings -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-shield-alt ml-2"></i>
            إعدادات الأمان
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Two-Factor Authentication Status -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-medium text-gray-900">
                        <i class="fas fa-mobile-alt ml-2"></i>
                        المصادقة الثنائية
                    </h3>
                    {% if profile.two_factor_enabled %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle ml-1"></i>
                            مُفعلة
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-exclamation-triangle ml-1"></i>
                            غير مُفعلة
                        </span>
                    {% endif %}
                </div>
                <p class="text-sm text-gray-600 mb-3">
                    {% if profile.two_factor_enabled %}
                        المصادقة الثنائية مُفعلة لحماية إضافية
                    {% else %}
                        يُنصح بتفعيل المصادقة الثنائية لحماية أفضل
                    {% endif %}
                </p>
                <a href="{% url 'super_admin:reset_2fa_settings' %}" 
                   class="inline-flex items-center px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                    <i class="fas fa-cog ml-1"></i>
                    إدارة المصادقة الثنائية
                </a>
            </div>

            <!-- Password Security -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-medium text-gray-900">
                        <i class="fas fa-key ml-2"></i>
                        كلمة المرور
                    </h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-shield-alt ml-1"></i>
                        محمية
                    </span>
                </div>
                <p class="text-sm text-gray-600 mb-3">
                    آخر تحديث: {{ user.last_login|date:"Y/m/d" }}
                </p>
                <a href="{% url 'super_admin:change_password' %}" 
                   class="inline-flex items-center px-3 py-1.5 text-sm bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors">
                    <i class="fas fa-edit ml-1"></i>
                    تغيير كلمة المرور
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-tools ml-2"></i>
            إجراءات سريعة
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Email Settings -->
            <a href="{% url 'super_admin:email_settings' %}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-3">
                    <i class="fas fa-envelope text-blue-600"></i>
                </div>
                <div>
                    <h3 class="font-medium text-gray-900">إعدادات البريد</h3>
                    <p class="text-sm text-gray-500">تكوين البريد الإلكتروني</p>
                </div>
            </a>

            <!-- IP Restrictions -->
            <a href="{% url 'super_admin:ip_restrictions' %}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center ml-3">
                    <i class="fas fa-ban text-red-600"></i>
                </div>
                <div>
                    <h3 class="font-medium text-gray-900">تقييد IP</h3>
                    <p class="text-sm text-gray-500">إدارة عناوين IP المسموحة</p>
                </div>
            </a>

            <!-- System Logs -->
            <a href="{% url 'super_admin:system_logs' %}" 
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center ml-3">
                    <i class="fas fa-list-alt text-green-600"></i>
                </div>
                <div>
                    <h3 class="font-medium text-gray-900">سجل النشاط</h3>
                    <p class="text-sm text-gray-500">عرض سجل العمليات</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    {% if logs %}
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-history ml-2"></i>
            النشاط الأخير
        </h2>

        <div class="space-y-3">
            {% for log in logs %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                        <i class="fas fa-{{ log.action|default:'info' }} text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ log.description }}</p>
                        <p class="text-xs text-gray-500">{{ log.ip_address }}</p>
                    </div>
                </div>
                <span class="text-xs text-gray-500">
                    {{ log.created_at|date:"Y/m/d H:i" }}
                </span>
            </div>
            {% endfor %}
        </div>

        <div class="mt-4 text-center">
            <a href="{% url 'super_admin:system_logs' %}" 
               class="text-blue-600 hover:text-blue-800 text-sm">
                عرض جميع السجلات
                <i class="fas fa-arrow-left mr-1"></i>
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
