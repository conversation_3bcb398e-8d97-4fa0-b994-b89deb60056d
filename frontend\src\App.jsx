import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Context
import { useAuth } from './hooks/useAuth';

// Layouts
import Layout from './components/layout/Layout';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import WorkerList from './pages/Workers/WorkerList';
import WorkerDetail from './pages/Workers/WorkerDetail';
import WorkerForm from './pages/Workers/WorkerForm';
import ContractList from './pages/Contracts/ContractList';
import ContractDetail from './pages/Contracts/ContractDetail';
import ContractForm from './pages/Contracts/ContractForm';
import ServiceList from './pages/Services/ServiceList';
import ServiceDetail from './pages/Services/ServiceDetail';
import ServiceForm from './pages/Services/ServiceForm';
import DocumentList from './pages/Documents/DocumentList';
import DocumentDetail from './pages/Documents/DocumentDetail';
import DocumentForm from './pages/Documents/DocumentForm';
import ReportList from './pages/Reports/ReportList';
import Settings from './pages/Settings/Settings';
import UIComponents from './pages/UIComponents';
import NotFound from './pages/NotFound';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  const { checkAuth } = useAuth();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<Login />} />

        {/* Protected Routes */}
        <Route path="/" element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />

          {/* Workers Routes */}
          <Route path="workers">
            <Route index element={<WorkerList />} />
            <Route path=":id" element={<WorkerDetail />} />
            <Route path="create" element={<WorkerForm />} />
            <Route path="edit/:id" element={<WorkerForm />} />
          </Route>

          {/* Contracts Routes */}
          <Route path="contracts">
            <Route index element={<ContractList />} />
            <Route path=":id" element={<ContractDetail />} />
            <Route path="create" element={<ContractForm />} />
            <Route path="edit/:id" element={<ContractForm />} />
          </Route>

          {/* Services Routes */}
          <Route path="services">
            <Route index element={<ServiceList />} />
            <Route path=":id" element={<ServiceDetail />} />
            <Route path="create" element={<ServiceForm />} />
            <Route path="edit/:id" element={<ServiceForm />} />
          </Route>

          {/* Documents Routes */}
          <Route path="documents">
            <Route index element={<DocumentList />} />
            <Route path=":id" element={<DocumentDetail />} />
            <Route path="create" element={<DocumentForm />} />
            <Route path="edit/:id" element={<DocumentForm />} />
          </Route>

          {/* Reports Routes */}
          <Route path="reports" element={<ReportList />} />

          {/* Settings Routes */}
          <Route path="settings" element={<Settings />} />

          {/* UI Components Route */}
          <Route path="ui-components" element={<UIComponents />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>

      {/* Toast Container for Notifications */}
      <ToastContainer
        position="bottom-left"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  );
}

export default App;
