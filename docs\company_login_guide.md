# دليل تسجيل الدخول للشركات الجديدة

## 📋 نظرة عامة

عند إنشاء شركة جديدة في النظام، يتم تلقائياً إنشاء مستخدم مسؤول للشركة باستخدام **بيانات قاعدة البيانات** المحددة أثناء إنشاء الشركة.

## 🔧 كيفية عمل النظام

### عند إنشاء شركة جديدة:

1. **إنشاء الشركة** في قاعدة البيانات المركزية
2. **توليد رقم تسلسلي فريد** (16 حرف بتنسيق XXXX-XXXX-XXXX-XXXX)
3. **إنشاء قاعدة بيانات منفصلة** للشركة
4. **تطبيق جميع الهجرات** على قاعدة بيانات الشركة
5. **إنشاء مستخدم مسؤول الشركة** باستخدام:
   - **اسم المستخدم:** من حقل `database_user`
   - **كلمة المرور:** من حقل `database_password`

## 🚀 كيفية تسجيل الدخول

### للوصول إلى الشركة الجديدة:

1. **اذهب إلى صفحة تسجيل دخول الشركة:** `/company/login/`

2. **أدخل البيانات التالية:**
   - **الرقم التسلسلي:** الرقم المولد للشركة
   - **اسم المستخدم:** نفس قيمة `database_user` المحددة عند إنشاء الشركة
   - **كلمة المرور:** نفس قيمة `database_password` المحددة عند إنشاء الشركة

## 📝 مثال عملي

إذا تم إنشاء شركة بالبيانات التالية:
- **اسم الشركة:** شركة النور للاستقدام
- **الرقم التسلسلي:** `ABCD-EFGH-IJKL-MNOP`
- **اسم مستخدم قاعدة البيانات:** `noor_admin`
- **كلمة مرور قاعدة البيانات:** `noor123456`

### بيانات تسجيل الدخول:
- **الرقم التسلسلي:** `ABCD-EFGH-IJKL-MNOP`
- **اسم المستخدم:** `noor_admin`
- **كلمة المرور:** `noor123456`

## ⚠️ ملاحظات هامة

1. **أمان البيانات:** تأكد من استخدام كلمات مرور قوية لقاعدة البيانات
2. **تغيير كلمة المرور:** يمكن تغيير كلمة المرور لاحقاً من داخل النظام
3. **النسخ الاحتياطي:** احتفظ بنسخة من بيانات تسجيل الدخول في مكان آمن
4. **الصلاحيات:** المستخدم المنشأ يحصل على صلاحيات مدير كاملة للشركة

## 🔒 الأمان

- يتم تشفير كلمات المرور باستخدام خوارزميات Django الآمنة
- كل شركة لها قاعدة بيانات منفصلة ومعزولة
- الرقم التسلسلي يعمل كمفتاح للوصول إلى قاعدة البيانات الصحيحة

## 🛠️ استكشاف الأخطاء

### إذا لم تتمكن من تسجيل الدخول:

1. **تحقق من الرقم التسلسلي:** تأكد من إدخاله بالتنسيق الصحيح
2. **تحقق من اسم المستخدم:** يجب أن يطابق `database_user` بالضبط
3. **تحقق من كلمة المرور:** يجب أن تطابق `database_password` بالضبط
4. **تحقق من حالة الشركة:** يجب أن تكون الشركة في حالة "نشطة"

### للحصول على المساعدة:

- راجع سجلات النظام في لوحة المسؤول الأعلى
- تحقق من حالة قاعدة بيانات الشركة
- تواصل مع مسؤول النظام إذا استمرت المشكلة

## 📞 الدعم الفني

للحصول على المساعدة الفنية، يرجى التواصل مع فريق الدعم مع توفير:
- اسم الشركة
- الرقم التسلسلي
- وصف المشكلة
- لقطة شاشة من رسالة الخطأ (إن وجدت)
