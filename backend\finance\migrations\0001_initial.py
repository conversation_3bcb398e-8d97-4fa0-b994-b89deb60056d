# Generated by Django 5.2 on 2025-05-26 17:51

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الميزانية')),
                ('category', models.CharField(choices=[('salary', 'راتب'), ('service_fee', 'رسوم خدمة'), ('commission', 'عمولة'), ('office_expense', 'مصروفات مكتبية'), ('travel', 'سفر'), ('maintenance', 'صيانة'), ('other', 'أخرى')], max_length=20, verbose_name='الفئة')),
                ('planned_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ المخطط')),
                ('actual_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المبلغ الفعلي')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'ميزانية',
                'verbose_name_plural': 'الميزانيات',
            },
        ),
        migrations.CreateModel(
            name='FinancialAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('account_type', models.CharField(choices=[('bank', 'حساب بنكي'), ('cash', 'نقدي'), ('credit', 'ائتماني'), ('investment', 'استثماري')], max_length=20, verbose_name='نوع الحساب')),
                ('account_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الرصيد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'حساب مالي',
                'verbose_name_plural': 'الحسابات المالية',
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة')),
                ('client_name', models.CharField(max_length=200, verbose_name='اسم العميل')),
                ('client_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد العميل')),
                ('client_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف العميل')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('issue_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الإصدار')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(max_length=20, unique=True, verbose_name='رقم المعاملة')),
                ('transaction_type', models.CharField(choices=[('income', 'دخل'), ('expense', 'مصروف'), ('transfer', 'تحويل')], max_length=20, verbose_name='نوع المعاملة')),
                ('category', models.CharField(choices=[('salary', 'راتب'), ('service_fee', 'رسوم خدمة'), ('commission', 'عمولة'), ('office_expense', 'مصروفات مكتبية'), ('travel', 'سفر'), ('maintenance', 'صيانة'), ('other', 'أخرى')], max_length=20, verbose_name='الفئة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المرجع')),
                ('transaction_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ المعاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='finance.financialaccount', verbose_name='الحساب')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة مالية',
                'verbose_name_plural': 'المعاملات المالية',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
    ]
