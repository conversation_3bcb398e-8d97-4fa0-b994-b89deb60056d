from rest_framework import viewsets, filters, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils.translation import gettext_lazy as _

from workers.models import Worker
from documents.models import Document
from procedures.models import BloodTest, SponsorshipTransfer, Operation, WorkPermit, ResidenceID, BranchTransfer

from .serializers import WorkerSerializer, WorkerDetailSerializer, WorkerCreateSerializer, WorkerUpdateSerializer
from api.documents.serializers import DocumentSerializer
from api.procedures.serializers import (
    BloodTestSerializer, SponsorshipTransferSerializer, OperationSerializer,
    WorkPermitSerializer, ResidenceIDSerializer, BranchTransferSerializer
)


class WorkerViewSet(viewsets.ModelViewSet):
    """
    واجهة برمجة التطبيقات للعمال.
    توفر عمليات CRUD للعمال.
    """
    queryset = Worker.objects.all()
    serializer_class = WorkerSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['nationality', 'is_active', 'job_title', 'recruitment_company']
    search_fields = ['first_name', 'last_name', 'passport_number', 'residence_number']
    ordering_fields = ['first_name', 'last_name', 'nationality', 'passport_expiry_date', 'residence_expiry_date', 'hire_date']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return WorkerDetailSerializer
        elif self.action == 'create':
            return WorkerCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return WorkerUpdateSerializer
        return WorkerSerializer
    
    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """
        الحصول على مستندات العامل.
        """
        worker = self.get_object()
        documents = Document.objects.filter(related_to_worker=worker)
        serializer = DocumentSerializer(documents, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def procedures(self, request, pk=None):
        """
        الحصول على إجراءات العامل.
        """
        worker = self.get_object()
        
        # الحصول على جميع أنواع الإجراءات
        blood_tests = BloodTest.objects.filter(worker=worker)
        sponsorship_transfers = SponsorshipTransfer.objects.filter(worker=worker)
        operations = Operation.objects.filter(worker=worker)
        work_permits = WorkPermit.objects.filter(worker=worker)
        residence_ids = ResidenceID.objects.filter(worker=worker)
        branch_transfers = BranchTransfer.objects.filter(worker=worker)
        
        # تسلسل البيانات
        blood_tests_serializer = BloodTestSerializer(blood_tests, many=True)
        sponsorship_transfers_serializer = SponsorshipTransferSerializer(sponsorship_transfers, many=True)
        operations_serializer = OperationSerializer(operations, many=True)
        work_permits_serializer = WorkPermitSerializer(work_permits, many=True)
        residence_ids_serializer = ResidenceIDSerializer(residence_ids, many=True)
        branch_transfers_serializer = BranchTransferSerializer(branch_transfers, many=True)
        
        # تجميع البيانات
        data = {
            'blood_tests': blood_tests_serializer.data,
            'sponsorship_transfers': sponsorship_transfers_serializer.data,
            'operations': operations_serializer.data,
            'work_permits': work_permits_serializer.data,
            'residence_ids': residence_ids_serializer.data,
            'branch_transfers': branch_transfers_serializer.data,
        }
        
        return Response(data)
    
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """
        تبديل حالة العامل (نشط/غير نشط).
        """
        worker = self.get_object()
        worker.is_active = not worker.is_active
        worker.save()
        
        status_text = _('نشط') if worker.is_active else _('غير نشط')
        return Response({'status': status_text, 'is_active': worker.is_active})
    
    @action(detail=False, methods=['get'])
    def expiring_documents(self, request):
        """
        الحصول على العمال الذين تنتهي وثائقهم قريبًا.
        """
        days = int(request.query_params.get('days', 30))
        
        # الحصول على العمال الذين تنتهي جوازات سفرهم أو إقاماتهم قريبًا
        workers_with_expiring_documents = Worker.objects.filter_expiring_documents(days=days)
        
        serializer = WorkerSerializer(workers_with_expiring_documents, many=True)
        return Response(serializer.data)
