# وثائق الواجهة الأمامية

## نظرة عامة

تم بناء الواجهة الأمامية لنظام إدارة العمالة باستخدام React و Tailwind CSS، وتوفر:
- واجهة مستخدم حديثة ومتجاوبة
- دعم كامل للغة العربية واتجاه RTL
- تجاوب مع جميع أحجام الشاشات
- أداء عالي وسرعة تحميل

## هيكل المشروع

```
frontend/
├── public/             # الملفات العامة
│   ├── index.html      # ملف HTML الرئيسي
│   ├── favicon.ico     # أيقونة الموقع
│   └── manifest.json   # ملف التكوين للمتصفح
├── src/                # كود المصدر
│   ├── assets/         # الأصول (الصور، الخطوط، إلخ)
│   ├── components/     # مكونات React القابلة لإعادة الاستخدام
│   ├── contexts/       # سياقات React
│   ├── hooks/          # خطافات React المخصصة
│   ├── layouts/        # تخطيطات الصفحات
│   ├── pages/          # صفحات التطبيق
│   ├── services/       # خدمات API
│   ├── utils/          # وظائف مساعدة
│   ├── App.js          # مكون التطبيق الرئيسي
│   └── index.js        # نقطة الدخول
├── package.json        # تبعيات المشروع
└── tailwind.config.js  # تكوين Tailwind CSS
```

## المكونات الرئيسية

### 1. مكونات الواجهة

#### DataTable

مكون لعرض البيانات في جدول مع دعم الفرز والتصفية والصفحات.

```jsx
import DataTable from '../components/DataTable';

<DataTable
  data={workers}
  columns={columns}
  pagination={true}
  searchable={true}
  sortable={true}
/>
```

#### FormCard

مكون لعرض النماذج في بطاقة مع عنوان ووصف.

```jsx
import FormCard from '../components/FormCard';

<FormCard
  title="إضافة عامل جديد"
  description="أدخل بيانات العامل الجديد"
>
  <form>
    {/* حقول النموذج */}
  </form>
</FormCard>
```

#### AlertMessage

مكون لعرض رسائل التنبيه للمستخدم.

```jsx
import AlertMessage from '../components/AlertMessage';

<AlertMessage
  type="success"
  message="تم إضافة العامل بنجاح"
  dismissible={true}
/>
```

### 2. خطافات مخصصة

#### useAuth

خطاف للتعامل مع المصادقة وإدارة الجلسة.

```jsx
import { useAuth } from '../hooks/useAuth';

function LoginPage() {
  const { login, isAuthenticated, user } = useAuth();
  
  // استخدام الخطاف
}
```

#### useApi

خطاف للتعامل مع طلبات API.

```jsx
import { useApi } from '../hooks/useApi';

function WorkersList() {
  const { data, loading, error, fetchData } = useApi('/api/workers/');
  
  // استخدام الخطاف
}
```

## الصفحات الرئيسية

### 1. لوحة التحكم

صفحة لوحة التحكم الرئيسية التي تعرض ملخصًا للبيانات والإحصائيات.

### 2. العمال

صفحات إدارة العمال، بما في ذلك قائمة العمال وإضافة عامل جديد وتفاصيل العامل.

### 3. العقود

صفحات إدارة العقود، بما في ذلك قائمة العقود وإضافة عقد جديد وتفاصيل العقد.

### 4. الخدمات

صفحات إدارة الخدمات، بما في ذلك قائمة الخدمات وإضافة خدمة جديدة وتفاصيل الخدمة.

### 5. المستندات

صفحات إدارة المستندات، بما في ذلك قائمة المستندات وإضافة مستند جديد وتفاصيل المستند.

### 6. التقارير

صفحات عرض وإنشاء التقارير المختلفة.

## التصميم الموحد

تم تطبيق تصميم موحد عبر جميع أجزاء النظام، يتضمن:
- نظام ألوان موحد
- خطوط موحدة (Cairo و Tajawal)
- مكونات واجهة مستخدم متسقة
- تجربة مستخدم سلسة

للمزيد من المعلومات حول التصميم الموحد، راجع [دليل التصميم الموحد](UNIFIED-DESIGN-IMPLEMENTATION.md).

## التثبيت والتشغيل

### متطلبات النظام

- Node.js 14+
- npm 6+ أو yarn 1.22+

### تثبيت التبعيات

```bash
# باستخدام npm
npm install

# باستخدام yarn
yarn install
```

### تشغيل خادم التطوير

```bash
# باستخدام npm
npm start

# باستخدام yarn
yarn start
```

### بناء الإصدار النهائي

```bash
# باستخدام npm
npm run build

# باستخدام yarn
yarn build
```

## المزيد من المعلومات

للمزيد من المعلومات حول الواجهة الأمامية، يمكنك الاطلاع على:
- [دليل التصميم الموحد](UNIFIED-DESIGN-IMPLEMENTATION.md)
- [وثائق محرر القوالب](template-editor.md)
