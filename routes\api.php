<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\DatabaseController;
use App\Http\Controllers\Api\SerialNumberController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public API routes
Route::post('/database/verify', [DatabaseController::class, 'testConnection']);
Route::post('/verify-database', [DatabaseController::class, 'verifyDatabase']);
Route::post('/accounts/api/check-serial-number', [SerialNumberController::class, 'check']);

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // User info
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Database info
    Route::get('/database/info', [DatabaseController::class, 'getDatabaseInfo']);
    Route::get('/database/tables', [DatabaseController::class, 'getTables']);
});
