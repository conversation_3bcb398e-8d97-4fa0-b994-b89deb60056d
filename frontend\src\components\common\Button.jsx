import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

/**
 * مكون زر متقدم يدعم أنماط مختلفة وألوان متعددة
 */
const Button = ({
  children,
  type = 'button',
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  loading = false,
  icon = null,
  iconPosition = 'start',
  href = null,
  to = null,
  onClick = () => {},
  ...rest
}) => {
  // تحديد الأنماط الأساسية للزر
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed';
  
  // تحديد أنماط الحجم
  const sizeClasses = {
    sm: 'px-3 py-1 text-sm rounded-md',
    md: 'px-4 py-2 rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-lg',
    icon: 'p-2 rounded-full',
  };
  
  // تحديد أنماط اللون والمظهر
  const variantClasses = {
    // الأنماط الأساسية
    primary: 'bg-primary text-white hover:bg-primary-dark focus:ring-primary',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary',
    success: 'bg-success text-white hover:bg-success-dark focus:ring-success',
    danger: 'bg-danger text-white hover:bg-danger-dark focus:ring-danger',
    warning: 'bg-warning text-white hover:bg-warning-dark focus:ring-warning',
    info: 'bg-info text-white hover:bg-info-dark focus:ring-info',
    neutral: 'bg-neutral text-neutral-dark hover:bg-neutral-dark hover:text-white focus:ring-neutral',
    
    // أنماط الحدود
    'outline-primary': 'bg-transparent border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary',
    'outline-secondary': 'bg-transparent border border-secondary text-secondary hover:bg-secondary hover:text-white focus:ring-secondary',
    'outline-success': 'bg-transparent border border-success text-success hover:bg-success hover:text-white focus:ring-success',
    'outline-danger': 'bg-transparent border border-danger text-danger hover:bg-danger hover:text-white focus:ring-danger',
    'outline-warning': 'bg-transparent border border-warning text-warning hover:bg-warning hover:text-white focus:ring-warning',
    'outline-info': 'bg-transparent border border-info text-info hover:bg-info hover:text-white focus:ring-info',
    'outline-neutral': 'bg-transparent border border-neutral text-neutral-dark hover:bg-neutral hover:text-white focus:ring-neutral',
    
    // أنماط الألوان الإضافية
    'blue-345785': 'bg-blue-345785 text-white hover:bg-blue-345785/90 focus:ring-blue-345785',
    'blue-4b93da': 'bg-blue-4b93da text-white hover:bg-blue-4b93da/90 focus:ring-blue-4b93da',
    'gray-294252': 'bg-gray-294252 text-white hover:bg-gray-294252/90 focus:ring-gray-294252',
    'gray-3773b5': 'bg-gray-3773b5 text-white hover:bg-gray-3773b5/90 focus:ring-gray-3773b5',
    'accent-42759b': 'bg-accent-42759b text-white hover:bg-accent-42759b/90 focus:ring-accent-42759b',
    'accent-d34169': 'bg-accent-d34169 text-white hover:bg-accent-d34169/90 focus:ring-accent-d34169',
    
    // أنماط الحدود للألوان الإضافية
    'outline-blue-345785': 'bg-transparent border border-blue-345785 text-blue-345785 hover:bg-blue-345785 hover:text-white focus:ring-blue-345785',
    'outline-blue-4b93da': 'bg-transparent border border-blue-4b93da text-blue-4b93da hover:bg-blue-4b93da hover:text-white focus:ring-blue-4b93da',
    'outline-gray-294252': 'bg-transparent border border-gray-294252 text-gray-294252 hover:bg-gray-294252 hover:text-white focus:ring-gray-294252',
    'outline-accent-42759b': 'bg-transparent border border-accent-42759b text-accent-42759b hover:bg-accent-42759b hover:text-white focus:ring-accent-42759b',
  };
  
  // تجميع الأنماط
  const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;
  
  // إنشاء محتوى الزر مع الأيقونة
  const buttonContent = (
    <>
      {loading ? (
        <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin ml-2"></div>
      ) : icon && iconPosition === 'start' ? (
        <span className="ml-2">{icon}</span>
      ) : null}
      
      <span>{children}</span>
      
      {icon && iconPosition === 'end' && !loading ? (
        <span className="mr-2">{icon}</span>
      ) : null}
    </>
  );
  
  // إذا كان هناك رابط خارجي
  if (href) {
    return (
      <a
        href={href}
        className={classes}
        disabled={disabled || loading}
        {...rest}
      >
        {buttonContent}
      </a>
    );
  }
  
  // إذا كان هناك رابط داخلي
  if (to) {
    return (
      <Link
        to={to}
        className={classes}
        {...rest}
      >
        {buttonContent}
      </Link>
    );
  }
  
  // زر عادي
  return (
    <button
      type={type}
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      {...rest}
    >
      {buttonContent}
    </button>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  variant: PropTypes.string,
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'icon']),
  className: PropTypes.string,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  icon: PropTypes.node,
  iconPosition: PropTypes.oneOf(['start', 'end']),
  href: PropTypes.string,
  to: PropTypes.string,
  onClick: PropTypes.func,
};

export default Button;
