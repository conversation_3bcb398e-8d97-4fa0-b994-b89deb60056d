{% extends 'base_tailwind.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- زر العودة إلى تفاصيل العامل -->
    <div class="mb-4">
        <a href="{% url 'workers:worker_detail' worker.id %}" class="bg-white dark:bg-[#1E293B] border-2 border-blue-500 dark:border-blue-600 text-blue-500 dark:text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600 transition-all duration-300 flex items-center shadow-sm hover:shadow-md inline-flex">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة إلى تفاصيل العامل</span>
        </a>
    </div>

    <!-- عنوان الصفحة -->
    <div class="mb-6">
        <div class="bg-gray-800 dark:bg-gray-900 rounded-lg shadow-md p-6" style="background-color: #1e3a8a !important;">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-xl font-bold text-white mb-1">مستندات العامل</h3>
                    <p class="text-blue-100">{{ worker.first_name }} {{ worker.last_name }}</p>
                </div>
                <div>
                    <a href="{% url 'workers:add_worker_document' worker.id %}" class="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors duration-300 flex items-center shadow-md">
                        <i class="fas fa-plus ml-2"></i>
                        <span class="font-bold">إضافة مستند جديد</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المستندات -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-100 dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h5 class="font-bold text-gray-700 dark:text-gray-200 flex items-center">
                <i class="fas fa-file-alt ml-2"></i>قائمة المستندات
            </h5>
        </div>
        <div class="p-6">
            {% if documents %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">#</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">نوع المستند</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العنوان</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الرفع</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الانتهاء</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الملف</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-[#1E293B] divide-y divide-gray-200 dark:divide-gray-700">
                            {% for document in documents %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ forloop.counter }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ document.get_document_type_display }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ document.title }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ document.upload_date|date:"Y-m-d" }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        {% if document.expiry_date %}
                                            {{ document.expiry_date|date:"Y-m-d" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{{ document.file.url }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-3">
                                            <i class="fas fa-eye ml-1"></i> عرض
                                        </a>
                                        <a href="{{ document.file.url }}" download class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                            <i class="fas fa-download ml-1"></i> تنزيل
                                        </a>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <form method="POST" action="{% url 'workers:delete_worker_document' document.id %}">
                                            {% csrf_token %}
                                            <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300">
                                                <i class="fas fa-trash ml-1"></i> حذف
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="bg-blue-50 dark:bg-blue-900/20 border-r-4 border-blue-500 dark:border-blue-600 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-500 dark:text-blue-400"></i>
                        </div>
                        <div class="mr-3">
                            <p class="text-blue-700 dark:text-blue-300">لا توجد مستندات لهذا العامل. يمكنك إضافة مستندات جديدة باستخدام زر "إضافة مستند جديد".</p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
