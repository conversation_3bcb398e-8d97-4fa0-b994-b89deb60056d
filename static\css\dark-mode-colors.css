
/**
 * ملف ال<PERSON><PERSON><PERSON><PERSON> المخصص للوضع الليلي
 * يحتوي على تعريفات الألوان الموحدة للوضع الليلي
 */

:root {
    /* ألوان البطاقات في الوضع الليلي - تدرجات Slate */
    --dark-card-bg: #1E293B; /* slate-800 */
    --dark-card-bg-darker: #0F172A; /* slate-900 */
    --dark-card-text-primary: #F1F5F9; /* slate-100 */
    --dark-card-text-secondary: #94A3B8; /* slate-400 */
    --dark-card-text-tertiary: #CBD5E1; /* slate-300 */
    --dark-card-border: #334155; /* slate-700 */

    /* ألوان الأزرار في الوضع الليلي */
    --dark-button-primary: #334155; /* slate-700 */
    --dark-button-secondary: #475569; /* slate-600 */
    --dark-button-disabled: #475569; /* slate-600 */
    --dark-button-text: #F1F5F9; /* slate-100 */
    --dark-button-text-disabled: #94A3B8; /* slate-400 */

    /* ألوان الأيقونات في الوضع الليلي - ألوان مميزة لكل نوع */
    --icon-workers: #EC4899; /* pink-500 */
    --icon-clients: #F59E0B; /* amber-500 */
    --icon-active-contracts: #8B5CF6; /* violet-500 */
    --icon-expired-contracts: #EF4444; /* red-500 */
    --icon-custom-contracts: #F97316; /* orange-500 */
    --icon-daily-services: #3B82F6; /* blue-500 */
    --icon-monthly-services: #A855F7; /* purple-500 */
    --icon-services: #06B6D4; /* cyan-500 */
    --icon-users: #14B8A6; /* teal-500 */

    /* ألوان الأيقونات في الوضع الليلي - تدرجات مختلفة */
    --icon-blue: #93C5FD; /* blue-300 */
    --icon-green: #86EFAC; /* green-300 */
    --icon-bright-green: #4ADE80; /* green-400 */
    --icon-red: #FCA5A5; /* red-300 */
    --icon-light-blue: #7DD3FC; /* sky-300 */
    --icon-purple: #D8B4FE; /* purple-300 */
    --icon-indigo: #A5B4FC; /* indigo-300 */
    --icon-amber: #FCD34D; /* amber-300 */
    --icon-yellow: #FDE68A; /* amber-200 */
    --icon-orange: #FDBA74; /* orange-300 */
    --icon-teal: #5EEAD4; /* teal-300 */
    --icon-pink: #F9A8D4; /* pink-300 */

    /* ألوان التنبيهات */
    --icon-yellow-alert: #FCD34D; /* amber-300 */
    --icon-orange-alert: #FDBA74; /* orange-300 */
    --icon-exclamation: #F59E0B; /* amber-500 */
    --icon-id-card: #F97316; /* orange-500 */

    /* ظلال الوضع الليلي */
    --dark-shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --dark-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/**
 * ألوان البطاقات والنصوص في الوضع الليلي
 */
.dark .bg-white {
    background-color: var(--dark-card-bg) !important;
}

.dark .bg-gray-50,
.dark .bg-gray-100 {
    background-color: var(--dark-card-bg-darker) !important;
}

/* تطبيق ألوان النصوص المقترحة */
.dark .text-gray-800,
.dark .text-gray-900,
.dark .font-bold,
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
    color: var(--dark-card-text-primary) !important;
}

.dark .text-gray-600,
.dark .text-gray-700 {
    color: var(--dark-card-text-secondary) !important;
}

.dark .text-gray-500,
.dark .text-sm {
    color: var(--dark-card-text-tertiary) !important;
}

/* تطبيق ألوان الأزرار */
.dark .bg-\[\#143D8D\] {
    background-color: var(--dark-button-primary) !important;
}

.dark .hover\:bg-\[\#143D8D\]:hover {
    background-color: var(--dark-button-primary) !important;
}

.dark .text-\[\#143D8D\] {
    color: var(--dark-card-text-primary) !important;
}

.dark .bg-white.text-sm {
    background-color: var(--dark-button-secondary) !important;
    color: var(--dark-card-text-primary) !important;
}

/**
 * ظلال البطاقات في الوضع الليلي
 */
.dark .shadow-md,
.dark .shadow {
    box-shadow: var(--dark-shadow-sm) !important;
}

.dark .hover\:shadow-lg:hover,
.dark .hover\:shadow-md:hover {
    box-shadow: var(--dark-shadow-lg) !important;
}

/**
 * إلغاء أي تأثيرات شفافية على الأيقونات في الوضع الليلي
 */
.dark [class*="text-"] i,
.dark [class*="text-"] svg,
.dark .card-icon,
.dark .dashboard-card .card-icon,
.dark .stats-card .card-icon {
    opacity: 1 !important;
    filter: none !important;
}

/* تطبيق ألوان خاصة للأيقونات حسب النوع */
/* أيقونات العمال */
.dark .fa-hard-hat,
.dark .fa-users,
.dark .fa-user-plus {
    color: var(--icon-workers) !important;
}

/* أيقونات العملاء */
.dark .fa-user-tie {
    color: var(--icon-clients) !important;
}

/* أيقونات العقود النشطة */
.dark .fa-file-contract {
    color: var(--icon-active-contracts) !important;
}

/* أيقونات العقود المنتهية */
.dark .text-\[\#FF0000\] .fa-file-contract {
    color: var(--icon-expired-contracts) !important;
}

/* أيقونات الخدمات */
.dark .fa-tools,
.dark .fa-concierge-bell {
    color: var(--icon-services) !important;
}

/* أيقونات الستيكر والجواز */
.dark .fa-passport {
    color: var(--icon-blue) !important;
}

.dark .fa-id-card,
.dark .fa-id-badge {
    color: var(--icon-green) !important;
}

.dark .fa-plus.text-gray-400,
.dark .text-xs.text-gray-400 {
    color: var(--dark-card-text-secondary) !important;
}

/* أيقونات العقود المخصصة */
.dark .fa-file-signature {
    color: var(--icon-custom-contracts) !important;
}

/* أيقونات الخدمات اليومية */
.dark .fa-calendar-day {
    color: var(--icon-daily-services) !important;
}

/* أيقونات الخدمات الشهرية */
.dark .fa-calendar-alt {
    color: var(--icon-monthly-services) !important;
}

/* أيقونات المستخدمين */
.dark .fa-users-cog {
    color: var(--icon-users) !important;
}

/* أيقونات التنبيهات */
.dark .fa-exclamation-triangle {
    color: var(--icon-exclamation) !important;
}

.dark .fa-id-card {
    color: var(--icon-id-card) !important;
}

/* أيقونات أخرى */
.dark .fa-broom,
.dark .fa-hand-sparkles {
    color: var(--icon-teal) !important;
}

/**
 * ألوان الأيقونات والحدود في الوضع الليلي
 */

/* تطبيق ألوان الأيقونات في البطاقات الإحصائية */
.dark .text-\[\#2196F3\],
.dark [class*="text-blue"] {
    color: var(--icon-blue) !important;
}

.dark .text-\[\#4CAF50\],
.dark [class*="text-green-600"] {
    color: var(--icon-green) !important;
}

.dark .text-\[\#00C853\],
.dark [class*="text-green"] {
    color: var(--icon-bright-green) !important;
}

.dark .text-\[\#FF0000\],
.dark [class*="text-red"] {
    color: var(--icon-red) !important;
}

.dark .text-\[\#03A9F4\] {
    color: var(--icon-light-blue) !important;
}

.dark .text-\[\#9C27B0\],
.dark [class*="text-purple"] {
    color: var(--icon-purple) !important;
}

.dark .text-\[\#3F51B5\],
.dark [class*="text-indigo"] {
    color: var(--icon-indigo) !important;
}

.dark .text-\[\#FFC107\],
.dark [class*="text-yellow"] {
    color: var(--icon-amber) !important;
}

/* تطبيق ألوان الحدود */
.dark .border-\[\#2196F3\] {
    border-color: var(--icon-blue) !important;
}

.dark .border-\[\#4CAF50\] {
    border-color: var(--icon-green) !important;
}

.dark .border-\[\#00C853\] {
    border-color: var(--icon-bright-green) !important;
}

.dark .border-\[\#FF0000\] {
    border-color: var(--icon-red) !important;
}

.dark .border-\[\#03A9F4\] {
    border-color: var(--icon-light-blue) !important;
}

.dark .border-\[\#9C27B0\] {
    border-color: var(--icon-purple) !important;
}

.dark .border-\[\#3F51B5\] {
    border-color: var(--icon-indigo) !important;
}

.dark .border-\[\#FFC107\] {
    border-color: var(--icon-amber) !important;
}

/* ألوان التنبيهات */
.dark .text-yellow-500 {
    color: var(--icon-yellow-alert) !important;
}

.dark .text-orange-500 {
    color: var(--icon-orange-alert) !important;
}

/* بطاقات التنبيهات */
.dark .bg-yellow-50 {
    background-color: rgba(245, 158, 11, 0.1) !important;
}

.dark .border-yellow-400 {
    border-color: var(--icon-yellow-alert) !important;
}

.dark .text-yellow-800 {
    color: var(--icon-yellow-alert) !important;
}

.dark .bg-orange-50 {
    background-color: rgba(249, 115, 22, 0.1) !important;
}

.dark .border-orange-400 {
    border-color: var(--icon-orange-alert) !important;
}

.dark .text-orange-800 {
    color: var(--icon-orange-alert) !important;
}

/* بطاقات العقود والمستندات */
.dark .bg-purple-50 {
    background-color: rgba(139, 92, 246, 0.1) !important; /* violet-500 with opacity */
}

.dark .text-purple-800 {
    color: var(--icon-purple) !important;
}

.dark .text-purple-300 {
    color: var(--icon-purple) !important;
}

.dark .text-purple-400 {
    color: var(--icon-purple) !important;
}

.dark .text-yellow-300 {
    color: var(--icon-yellow) !important;
}

.dark .text-yellow-400 {
    color: var(--icon-yellow) !important;
}

/* تطبيق ألوان خلفيات حاويات الأيقونات */
.dark .bg-blue-100 {
    background-color: rgba(147, 197, 253, 0.2) !important; /* blue-300 with opacity */
}

.dark .bg-green-100 {
    background-color: rgba(134, 239, 172, 0.2) !important; /* green-300 with opacity */
}

.dark .bg-purple-100 {
    background-color: rgba(216, 180, 254, 0.2) !important; /* purple-300 with opacity */
}

.dark .bg-yellow-100 {
    background-color: rgba(252, 211, 77, 0.2) !important; /* amber-300 with opacity */
}

.dark .bg-indigo-100 {
    background-color: rgba(165, 180, 252, 0.2) !important; /* indigo-300 with opacity */
}

/* تحسين مظهر البطاقات في الوضع الليلي */
.dark .border-gray-100 {
    border-color: var(--dark-card-border) !important;
}
