from django.urls import path
from . import views
from . import views_database
from . import views_backup
from . import api

app_name = 'super_admin'

urlpatterns = [
    # صفحات المصادقة
    path('login/', views.super_admin_login, name='login'),
    path('logout/', views.super_admin_logout, name='super_admin_logout'),
    path('2fa/', views.super_admin_2fa, name='super_admin_2fa'),
    path('setup-2fa/', views.setup_2fa, name='setup_2fa'),
    path('setup-authenticator/', views.setup_authenticator, name='setup_authenticator'),
    path('resend-2fa-code/', views.resend_2fa_code, name='resend_2fa_code'),

    # لوحة التحكم والإحصائيات
    path('', views.super_admin_dashboard, name='dashboard'),

    # إدارة الشركات
    path('companies/create/', views.create_company, name='create_company'),
    path('companies/<int:company_id>/', views.company_detail, name='company_detail'),
    path('companies/<int:company_id>/toggle-status/', views.toggle_company_status, name='toggle_company_status'),
    path('companies/<int:company_id>/delete/', views.delete_company, name='delete_company'),

    # إدارة قواعد البيانات
    path('database/', views_database.database_management, name='database_management'),
    path('database/schema/<int:company_id>/', views_database.view_database_schema, name='view_database_schema'),

    # إعدادات النظام
    path('settings/', views.system_settings, name='system_settings'),
    path('settings/new/', views.system_settings_new, name='system_settings_new'),

    # النسخ الاحتياطي
    path('backup/', views.system_backup, name='system_backup'),
    path('backup/create/', views.create_backup, name='create_backup'),
    path('backup/restore/', views_backup.restore_backup, name='restore_backup'),
    path('backup/restore/<str:filename>/', views_backup.restore_backup_view, name='restore_backup_view'),
    path('backup/details/<str:filename>/', views_backup.backup_details, name='backup_details'),
    path('backup/download/<str:filename>/', views_backup.download_system_backup, name='download_system_backup'),
    path('backup/delete/<str:filename>/', views_backup.delete_system_backup, name='delete_system_backup'),
    path('backup/clean-old/', views_backup.clean_old_system_backups, name='clean_old_system_backups'),
    path('backup/schedule/', views_backup.system_backup_schedule, name='system_backup_schedule'),
    path('companies/<int:company_id>/backup/download/<str:filename>/', views_backup.download_backup, name='download_backup'),
    path('companies/<int:company_id>/backup/upload/', views_backup.upload_backup, name='upload_backup'),
    path('companies/<int:company_id>/backup/schedule/', views.manage_backup_schedule, name='manage_backup_schedule'),

    # سجل النظام
    path('logs/', views.system_logs, name='system_logs'),

    # الأرقام التسلسلية المحظورة
    path('blocked-serials/', views.blocked_serials, name='blocked_serials'),
    path('blocked-serials/add/', views.add_blocked_serial, name='add_blocked_serial'),
    path('blocked-serials/<int:serial_id>/edit/', views.edit_blocked_serial, name='edit_blocked_serial'),
    path('blocked-serials/<int:serial_id>/delete/', views.delete_blocked_serial, name='delete_blocked_serial'),

    # الملف الشخصي
    path('profile/', views.user_profile, name='user_profile'),
    path('profile/change-password/', views.change_password, name='change_password'),
    path('profile/ip-restrictions/', views.ip_restrictions, name='ip_restrictions'),
    path('profile/email-settings/', views.email_settings, name='email_settings'),

    # واجهة برمجة التطبيقات (API)
    path('api/companies/', api.get_companies, name='api_get_companies'),
    path('api/companies/<int:company_id>/', api.get_company_details, name='api_get_company_details'),
    path('api/companies/<int:company_id>/tables/', api.get_company_tables_api, name='api_get_company_tables'),
    path('api/companies/<int:company_id>/tables/<str:table_name>/structure/', api.get_table_structure_api, name='api_get_table_structure'),
    path('api/companies/<int:company_id>/tables/<str:table_name>/data/', api.get_table_data_api, name='api_get_table_data'),
    path('api/companies/<int:company_id>/execute-query/', api.execute_query_api, name='api_execute_query'),
]
