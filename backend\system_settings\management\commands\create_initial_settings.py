from django.core.management.base import BaseCommand
from system_settings.models import SystemSetting, WorkerSetting
import os
import json
from django.conf import settings

class Command(BaseCommand):
    help = 'Creates initial system settings'

    def handle(self, *args, **kwargs):
        # استيراد إعدادات النظام من ملف التكوين
        system_settings_file = os.path.join(settings.BASE_DIR, 'config', 'system_settings.json')
        try:
            with open(system_settings_file, 'r', encoding='utf-8') as f:
                system_settings = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # استخدام القيم الافتراضية إذا لم يتم العثور على ملف التكوين أو كان غير صالح
            self.stdout.write(self.style.WARNING(f"لم يتم العثور على ملف تكوين إعدادات النظام أو كان غير صالح: {system_settings_file}"))
            self.stdout.write(self.style.WARNING("تم استخدام القيم الافتراضية بدلاً من ذلك"))

            # System settings
            system_settings = [
                # General settings
                {
                    'key': 'site_name',
                    'value': 'نظام إدارة العمالة',
                    'description': 'اسم الموقع الذي يظهر في العنوان والترويسة',
                    'category': 'general'
                },
                {
                    'key': 'site_description',
                    'value': 'نظام متكامل لإدارة العمالة',
                    'description': 'وصف الموقع المختصر',
                    'category': 'general'
                },
                {
                    'key': 'currency',
                    'value': 'ر.س',
                    'description': 'العملة المستخدمة في النظام',
                    'category': 'general'
                },
                {
                    'key': 'date_format',
                    'value': 'Y-m-d',
                    'description': 'صيغة التاريخ المستخدمة في النظام',
                    'category': 'general'
                },

                # Company settings
                {
                    'key': 'company_name',
                    'value': '',
                    'description': 'اسم الشركة',
                    'category': 'company'
                },
                {
                    'key': 'company_address',
                    'value': '',
                    'description': 'عنوان الشركة',
                    'category': 'company'
                },
                {
                    'key': 'company_phone',
                    'value': '',
                    'description': 'رقم هاتف الشركة',
                    'category': 'company'
                },
                {
                    'key': 'company_email',
                    'value': '',
                    'description': 'البريد الإلكتروني للشركة',
                    'category': 'company'
                },

                # Security settings
                {
                    'key': 'password_expiry_days',
                    'value': '90',
                    'description': 'عدد أيام صلاحية كلمة المرور',
                    'category': 'security'
                },
                {
                    'key': 'session_timeout_minutes',
                    'value': '30',
                    'description': 'مدة انتهاء الجلسة بالدقائق',
                    'category': 'security'
                },
            ]

        # استيراد إعدادات العمال من ملف التكوين
        worker_settings_file = os.path.join(settings.BASE_DIR, 'config', 'worker_settings.json')
        try:
            with open(worker_settings_file, 'r', encoding='utf-8') as f:
                worker_settings = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # استخدام القيم الافتراضية إذا لم يتم العثور على ملف التكوين أو كان غير صالح
            self.stdout.write(self.style.WARNING(f"لم يتم العثور على ملف تكوين إعدادات العمال أو كان غير صالح: {worker_settings_file}"))
            self.stdout.write(self.style.WARNING("تم استخدام القيم الافتراضية بدلاً من ذلك"))

            # Worker settings
            worker_settings = [
                {
                    'setting_key': 'passport_expiry_warning_days',
                    'setting_value': '60',
                    'description': 'عدد أيام التنبيه قبل انتهاء جواز السفر'
                },
                {
                    'setting_key': 'visa_expiry_warning_days',
                    'setting_value': '30',
                    'description': 'عدد أيام التنبيه قبل انتهاء التأشيرة'
                }
            ]

        # Create system settings
        for setting in system_settings:
            SystemSetting.objects.get_or_create(
                key=setting['key'],
                defaults={
                    'value': setting['value'],
                    'description': setting['description'],
                    'category': setting['category']
                }
            )

        # Create worker settings
        for setting in worker_settings:
            WorkerSetting.objects.get_or_create(
                setting_key=setting['setting_key'],
                defaults={
                    'setting_value': setting['setting_value'],
                    'description': setting['description']
                }
            )

        self.stdout.write(self.style.SUCCESS('Successfully created initial settings'))
