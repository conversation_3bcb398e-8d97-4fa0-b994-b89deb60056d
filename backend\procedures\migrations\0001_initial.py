# Generated by Django 5.2 on 2025-04-15 21:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('workers', '0008_remove_visa_number'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BloodTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('reservation_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الحجز')),
                ('reservation_time', models.TimeField(blank=True, null=True, verbose_name='موعد الحجز')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحالة المرجعي')),
                ('result', models.CharField(blank=True, choices=[('', 'اختر النتيجة'), ('passed', 'مجتاز'), ('failed', 'غير مجتاز')], max_length=20, null=True, verbose_name='نتيجة الفحص')),
                ('result_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النتيجة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'فحص الدم',
                'verbose_name_plural': 'فحوصات الدم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BranchTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('transfer_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النقل')),
                ('destination_branch', models.CharField(blank=True, choices=[('', 'اختر الفرع'), ('baghdad', 'فرع بغداد'), ('basra', 'فرع البصرة'), ('erbil', 'فرع أربيل')], max_length=50, null=True, verbose_name='الفرع المرسل إليه')),
                ('reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='سبب النقل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'النقل إلى فرع آخر',
                'verbose_name_plural': 'عمليات النقل إلى فروع أخرى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Operation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('contract_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم العقد')),
                ('contract_date', models.DateField(blank=True, null=True, verbose_name='تاريخ العقد')),
                ('client_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'التشغيل',
                'verbose_name_plural': 'عمليات التشغيل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResidenceID',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('id_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم هوية الإقامة')),
                ('issue_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'هوية الإقامة',
                'verbose_name_plural': 'هويات الإقامة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SponsorshipTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('contract_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم العقد')),
                ('contract_date', models.DateField(blank=True, null=True, verbose_name='تاريخ العقد')),
                ('client_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'تحويل الكفالة',
                'verbose_name_plural': 'تحويلات الكفالة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkPermit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('pending', 'قيد الإجراء'), ('completed', 'مكتمل'), ('rejected', 'مرفوض')], default='not_started', max_length=20, verbose_name='الحالة')),
                ('outgoing_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الصادر')),
                ('outgoing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permit_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم إجازة العمل')),
                ('issue_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s_procedures', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_procedures', to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'إجازة العمل',
                'verbose_name_plural': 'إجازات العمل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProcedureAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='procedures/attachments/%Y/%m/', verbose_name='الملف')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='وصف الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('blood_test', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.bloodtest', verbose_name='فحص الدم')),
                ('branch_transfer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.branchtransfer', verbose_name='النقل إلى فرع آخر')),
                ('operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.operation', verbose_name='التشغيل')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
                ('residence_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.residenceid', verbose_name='هوية الإقامة')),
                ('sponsorship_transfer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.sponsorshiptransfer', verbose_name='تحويل الكفالة')),
                ('work_permit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='procedures.workpermit', verbose_name='إجازة العمل')),
            ],
            options={
                'verbose_name': 'مرفق إجراء',
                'verbose_name_plural': 'مرفقات الإجراءات',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
