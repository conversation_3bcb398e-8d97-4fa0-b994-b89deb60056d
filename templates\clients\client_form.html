{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}{{ title }} | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">{{ title }}</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                {% if is_edit %}
                تعديل بيانات العميل
                {% else %}
                إضافة عميل جديد
                {% endif %}
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'clients:client_list' %}"
               class="bg-gray-500 text-white dark:bg-gray-600 dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-gray-600 hover:shadow-md dark:hover:bg-gray-700 transition-all duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                <span>العودة للقائمة</span>
            </a>
        </div>
    </div>

    <!-- Form Container -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
        <!-- Client Type Tabs -->
        <div class="bg-gray-50 dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700">
            <div class="flex">
                <a href="{% url 'clients:client_create' %}?type=permanent"
                   class="px-6 py-3 text-sm font-medium {% if client_type == 'permanent' %}text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400{% else %}text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300{% endif %}">
                    <i class="fas fa-user-tie ml-2 {% if client_type == 'permanent' %}text-blue-600 dark:text-blue-400{% endif %}"></i>
                    عميل عقد دائم
                </a>
                <a href="{% url 'clients:client_create' %}?type=monthly"
                   class="px-6 py-3 text-sm font-medium {% if client_type == 'monthly' %}text-purple-600 border-b-2 border-purple-600 dark:text-purple-400 dark:border-purple-400{% else %}text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300{% endif %}">
                    <i class="fas fa-user-tie ml-2 {% if client_type == 'monthly' %}text-purple-600 dark:text-purple-400{% endif %}"></i>
                    عميل عقد شهري
                </a>
                <a href="{% url 'clients:client_create' %}?type=custom"
                   class="px-6 py-3 text-sm font-medium {% if client_type == 'custom' %}text-orange-600 border-b-2 border-orange-600 dark:text-orange-400 dark:border-orange-400{% else %}text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300{% endif %}">
                    <i class="fas fa-user-tie ml-2 {% if client_type == 'custom' %}text-orange-600 dark:text-orange-400{% endif %}"></i>
                    عميل خدمة مخصصة
                </a>
            </div>
        </div>

        <!-- Form -->
        <form method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            {% csrf_token %}
            <input type="hidden" name="client_type" value="{{ client_type }}">

            <!-- Client Form based on type -->
            {% if client_type == 'permanent' %}
                {% include 'clients/forms/permanent_client_form.html' %}
            {% elif client_type == 'monthly' %}
                {% include 'clients/forms/monthly_client_form.html' %}
            {% elif client_type == 'custom' %}
                {% include 'clients/forms/custom_client_form.html' %}
            {% endif %}

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200 dark:border-slate-700">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors duration-300 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    {% if is_edit %}تحديث{% else %}حفظ{% endif %}
                </button>
                <a href="{% url 'clients:client_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors duration-300 flex items-center">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block inner_extra_js %}
<!-- Include Leaflet for map -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة الخريطة إذا كان نوع العميل هو خدمة مخصصة
        if ('{{ client_type }}' === 'custom') {
            initMapHandling();
        }

        // تهيئة معالجة الخريطة
        function initMapHandling() {
            const mapUrlInput = document.getElementById('id_map_url');
            const latField = document.getElementById('id_location_lat');
            const lngField = document.getElementById('id_location_lng');
            const locationInfoContainer = document.getElementById('location-info-container');
            const locationName = document.getElementById('location-name');
            const locationAddress = document.getElementById('location-address');
            const locationDistance = document.getElementById('location-distance');
            const locationDuration = document.getElementById('location-duration');
            const openInGoogleMaps = document.getElementById('open-in-google-maps');
            const openInWaze = document.getElementById('open-in-waze');
            const refreshLocationButton = document.getElementById('refresh-location');

            let map, marker;

            // إضافة معالج حدث لزر تحديث الموقع
            if (refreshLocationButton) {
                refreshLocationButton.addEventListener('click', function(e) {
                    // منع السلوك الافتراضي للزر
                    e.preventDefault();
                    // منع انتشار الحدث لمنع تقديم النموذج
                    e.stopPropagation();

                    // تحديث الموقع
                    updateUserLocation().then(location => {
                        console.log("تم تحديث الموقع:", location);

                        // إعادة حساب المسافة والوقت إذا كانت هناك وجهة محددة
                        if (window.lastDestination) {
                            fetchOSRMRouteInfo(
                                location.lat,
                                location.lng,
                                window.lastDestination.lat,
                                window.lastDestination.lng
                            );
                        }
                    }).catch(error => {
                        console.error("فشل تحديث الموقع:", error);
                    });

                    // منع تقديم النموذج بشكل صريح
                    return false;
                });
            }

            // إذا كان هناك رابط موجود بالفعل، قم بتحليله وعرض الخريطة
            if (mapUrlInput.value) {
                processMapUrl(mapUrlInput.value);
            }

            // معالجة تغيير رابط الخريطة
            mapUrlInput.addEventListener('input', function() {
                const url = this.value.trim();
                if (url) {
                    processMapUrl(url);
                } else {
                    // إخفاء معلومات الموقع إذا كان الرابط فارغًا
                    locationInfoContainer.classList.add('hidden');
                }
            });

            // معالجة رابط الخريطة
            function processMapUrl(url) {
                // استخراج الإحداثيات من الرابط
                const coordinates = extractCoordinatesFromUrl(url);
                if (coordinates) {
                    const [lat, lng] = coordinates;

                    // تحديث حقول الإحداثيات
                    latField.value = lat;
                    lngField.value = lng;

                    // حفظ الوجهة للاستخدام اللاحق
                    window.lastDestination = { lat, lng };

                    // تحديث روابط الخرائط لفتح نقطة الوصول مع توجيه من الموقع الحالي
                    updateNavigationLinks(lat, lng);

                    // عرض الخريطة والمعلومات
                    showMap(lat, lng);
                    fetchLocationInfo(lat, lng);
                }
            }

            // تحديث روابط التنقل (خرائط Google و Waze)
            function updateNavigationLinks(destLat, destLng) {
                // الحصول على الموقع الحالي للمستخدم إذا كان متاحًا
                if (window.cachedUserLocation) {
                    const startLat = window.cachedUserLocation.latitude;
                    const startLng = window.cachedUserLocation.longitude;

                    // تحديث رابط خرائط Google مع نقطة البداية والوجهة
                    openInGoogleMaps.href = `https://www.google.com/maps/dir/?api=1&origin=${startLat},${startLng}&destination=${destLat},${destLng}&travelmode=driving`;

                    // تحديث رابط Waze مع نقطة البداية والوجهة
                    openInWaze.href = `https://waze.com/ul?ll=${destLat},${destLng}&navigate=yes&from=${startLat},${startLng}`;
                } else {
                    // إذا لم يكن الموقع الحالي متاحًا، استخدم الوجهة فقط
                    openInGoogleMaps.href = `https://www.google.com/maps/dir/?api=1&destination=${destLat},${destLng}&travelmode=driving`;
                    openInWaze.href = `https://waze.com/ul?ll=${destLat},${destLng}&navigate=yes`;
                }
            }

            // استخراج الإحداثيات من رابط الخريطة
            function extractCoordinatesFromUrl(url) {
                // أنماط مختلفة لروابط خرائط Google
                const patterns = [
                    // نمط: https://www.google.com/maps?q=33.3214645,44.4521322
                    /[?&]q=(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://www.google.com/maps/place/33.3214645,44.4521322
                    /\/maps\/place\/(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://www.google.com/maps/@33.3214645,44.4521322,15z
                    /\/maps\/@(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://www.google.com/maps/search/?api=1&query=33.3214645,44.4521322
                    /[?&]query=(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://www.google.com/maps/dir//33.3214645,44.4521322/
                    /\/maps\/dir\/\/(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://www.google.com/maps/dir/?api=1&destination=33.3214645,44.4521322
                    /[?&]destination=(-?\d+\.\d+),(-?\d+\.\d+)/,

                    // نمط: https://goo.gl/maps/ABC123 (روابط مختصرة)
                    // هذه تتطلب طلب HTTP للحصول على الرابط الكامل، وهو أمر معقد في JavaScript

                    // نمط عام للإحداثيات في أي مكان في الرابط
                    /(-?\d+\.\d+),(-?\d+\.\d+)/
                ];

                // محاولة مطابقة الرابط مع الأنماط المختلفة
                for (const pattern of patterns) {
                    const match = url.match(pattern);
                    if (match && match.length >= 3) {
                        const lat = parseFloat(match[1]);
                        const lng = parseFloat(match[2]);

                        // التحقق من صحة الإحداثيات
                        if (isValidLatitude(lat) && isValidLongitude(lng)) {
                            console.log(`تم استخراج الإحداثيات: ${lat}, ${lng}`);
                            return [lat, lng];
                        }
                    }
                }

                // محاولة البحث عن الإحداثيات في صيغة مختلفة (مثل الدرجات والدقائق والثواني)
                const dmsPattern = /(\d+)°(\d+)'([\d.]+)"([NS])\s+(\d+)°(\d+)'([\d.]+)"([EW])/i;
                const dmsMatch = url.match(dmsPattern);
                if (dmsMatch) {
                    try {
                        // تحويل من صيغة الدرجات والدقائق والثواني إلى الصيغة العشرية
                        let lat = parseInt(dmsMatch[1]) + (parseInt(dmsMatch[2]) / 60) + (parseFloat(dmsMatch[3]) / 3600);
                        if (dmsMatch[4].toUpperCase() === 'S') lat = -lat;

                        let lng = parseInt(dmsMatch[5]) + (parseInt(dmsMatch[6]) / 60) + (parseFloat(dmsMatch[7]) / 3600);
                        if (dmsMatch[8].toUpperCase() === 'W') lng = -lng;

                        if (isValidLatitude(lat) && isValidLongitude(lng)) {
                            console.log(`تم استخراج الإحداثيات من صيغة DMS: ${lat}, ${lng}`);
                            return [lat, lng];
                        }
                    } catch (e) {
                        console.error("خطأ في تحويل صيغة DMS:", e);
                    }
                }

                // إذا لم يتم العثور على إحداثيات
                console.warn('لم يتم العثور على إحداثيات في الرابط:', url);
                return null;
            }

            // التحقق من صحة خط العرض
            function isValidLatitude(lat) {
                return !isNaN(lat) && lat >= -90 && lat <= 90;
            }

            // التحقق من صحة خط الطول
            function isValidLongitude(lng) {
                return !isNaN(lng) && lng >= -180 && lng <= 180;
            }

            // عرض الخريطة
            function showMap(lat, lng) {
                const mapContainer = document.getElementById('map');

                // إظهار حاوية معلومات الموقع
                locationInfoContainer.classList.remove('hidden');

                // إذا كانت الخريطة موجودة بالفعل، قم بتحديثها فقط
                if (map) {
                    map.setView([lat, lng], 15);
                    marker.setLatLng([lat, lng]);

                    // تحديث الدائرة حول الموقع
                    if (window.locationCircle) {
                        window.locationCircle.setLatLng([lat, lng]);
                    }

                    return;
                }

                // إنشاء خريطة جديدة
                map = L.map('map', {
                    zoomControl: true,
                    attributionControl: false
                }).setView([lat, lng], 15);

                // إضافة طبقة الخريطة بتصميم مشابه لـ Waze
                // استخدام خريطة Thunderforest Outdoors للحصول على مظهر أقرب إلى Waze
                L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
                    maxZoom: 19,
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                // إضافة علامة مخصصة تشبه علامات Waze مع تحسين الدقة
                const wazeIcon = L.divIcon({
                    className: 'waze-marker',
                    html: `<div style="background-color: #4285F4; width: 24px; height: 24px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-map-marker-alt" style="color: white; font-size: 12px;"></i>
                          </div>`,
                    iconSize: [30, 30],
                    iconAnchor: [15, 30] // تعديل نقطة الارتساء لتكون في أسفل العلامة بدلاً من وسطها
                });

                marker = L.marker([lat, lng], {
                    icon: wazeIcon,
                    draggable: true, // جعل العلامة قابلة للسحب لتحسين الدقة
                    autoPan: true // تحريك الخريطة تلقائيًا عند سحب العلامة
                }).addTo(map);

                // تحديث الإحداثيات عند سحب العلامة
                marker.on('dragend', function(event) {
                    const newPosition = marker.getLatLng();
                    const newLat = newPosition.lat;
                    const newLng = newPosition.lng;

                    // تحديث حقول الإحداثيات
                    latField.value = newLat;
                    lngField.value = newLng;

                    // تحديث الدائرة حول الموقع
                    if (window.locationCircle) {
                        window.locationCircle.setLatLng([newLat, newLng]);
                    }

                    // تحديث معلومات الموقع
                    fetchLocationInfo(newLat, newLng);

                    // تحديث روابط التنقل
                    updateNavigationLinks(newLat, newLng);

                    // حفظ الوجهة للاستخدام اللاحق
                    window.lastDestination = { lat: newLat, lng: newLng };

                    console.log(`تم تحديث الموقع إلى: ${newLat}, ${newLng}`);
                });

                // إضافة دائرة حول الموقع
                window.locationCircle = L.circle([lat, lng], {
                    color: '#4285F4',
                    fillColor: '#4285F4',
                    fillOpacity: 0.1,
                    radius: 500,
                    weight: 2
                }).addTo(map);

                // إضافة زر تكبير/تصغير مخصص
                map.zoomControl.setPosition('bottomright');

                // إضافة مقياس للخريطة
                L.control.scale({
                    imperial: false,
                    position: 'bottomleft'
                }).addTo(map);

                // إضافة تأثير انتقال سلس عند تغيير العرض
                map.on('zoomend', function() {
                    if (marker._icon) {
                        marker._icon.classList.add('marker-animation');
                        setTimeout(function() {
                            if (marker._icon) {
                                marker._icon.classList.remove('marker-animation');
                            }
                        }, 300);
                    }
                });

                // إضافة CSS للتأثيرات البصرية
                if (!document.getElementById('map-animations-css')) {
                    const style = document.createElement('style');
                    style.id = 'map-animations-css';
                    style.textContent = `
                        .marker-animation {
                            transition: transform 0.3s ease-out;
                            transform: scale(1.2);
                        }
                        .waze-marker {
                            transition: transform 0.2s ease;
                        }
                        .waze-marker:hover {
                            transform: scale(1.2);
                        }
                        .route-animation-icon {
                            animation: pulse 1.5s infinite;
                        }
                        @keyframes pulse {
                            0% { transform: scale(0.8); opacity: 1; }
                            50% { transform: scale(1.2); opacity: 0.8; }
                            100% { transform: scale(0.8); opacity: 1; }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }

            // جلب معلومات الموقع
            function fetchLocationInfo(lat, lng) {
                // استخدام Nominatim API للحصول على معلومات الموقع
                fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`)
                    .then(response => response.json())
                    .then(data => {
                        // تحديث اسم المنطقة والعنوان
                        locationName.textContent = data.name || data.display_name.split(',')[0];
                        locationAddress.textContent = data.display_name;

                        // حساب المسافة التقريبية ووقت الوصول
                        // هذه قيم تقريبية للعرض فقط
                        calculateDistanceAndDuration(lat, lng);
                    })
                    .catch(error => {
                        console.error('Error fetching location info:', error);
                        locationName.textContent = 'الموقع المحدد';
                        locationAddress.textContent = `${lat}, ${lng}`;
                    });
            }

            // حساب المسافة ووقت الوصول باستخدام موقع المستخدم الحالي وخدمة توجيه المسارات
            function calculateDistanceAndDuration(destLat, destLng) {
                // عرض حالة "جاري الحساب..." أثناء الانتظار
                locationDistance.textContent = "جاري الحساب...";
                locationDuration.textContent = "جاري الحساب...";

                // إضافة مؤشر تحميل
                showLoadingIndicator();

                // التحقق من وجود موقع محفوظ حديثًا
                if (window.cachedUserLocation && window.cachedUserLocation.timestamp) {
                    const now = new Date().getTime();
                    const cacheAge = now - window.cachedUserLocation.timestamp;

                    // استخدام الموقع المخزن مؤقتًا إذا كان عمره أقل من 5 دقائق
                    if (cacheAge < 5 * 60 * 1000) {
                        console.log("استخدام الموقع المخزن مؤقتًا");
                        fetchOSRMRouteInfo(
                            window.cachedUserLocation.latitude,
                            window.cachedUserLocation.longitude,
                            destLat, destLng
                        );
                        return;
                    }
                }

                // الحصول على موقع المستخدم الحالي بدقة عالية
                if (navigator.geolocation) {
                    // محاولة الحصول على الموقع بدقة عالية أولاً
                    const highAccuracyOptions = {
                        enableHighAccuracy: true,
                        timeout: 15000,
                        maximumAge: 0
                    };

                    const lowAccuracyOptions = {
                        enableHighAccuracy: false,
                        timeout: 10000,
                        maximumAge: 60000
                    };

                    // محاولة الحصول على الموقع بدقة عالية
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            // موقع المستخدم الحالي بدقة عالية
                            const userLat = position.coords.latitude;
                            const userLng = position.coords.longitude;
                            const accuracy = position.coords.accuracy; // دقة الموقع بالمتر

                            console.log(`تم تحديد الموقع بدقة: ${accuracy} متر`);

                            // تخزين الموقع مؤقتًا للاستخدام اللاحق
                            window.cachedUserLocation = {
                                latitude: userLat,
                                longitude: userLng,
                                accuracy: accuracy,
                                timestamp: new Date().getTime()
                            };

                            // إضافة علامة موقع المستخدم على الخريطة
                            addUserLocationMarker(userLat, userLng, accuracy);

                            // محاولة استخدام خدمة OSRM أولاً (أكثر دقة)
                            fetchOSRMRouteInfo(userLat, userLng, destLat, destLng);

                            // إخفاء مؤشر التحميل
                            hideLoadingIndicator();
                        },
                        function(error) {
                            console.warn("فشل تحديد الموقع بدقة عالية، محاولة بدقة منخفضة:", error);

                            // محاولة الحصول على الموقع بدقة منخفضة كخطة بديلة
                            navigator.geolocation.getCurrentPosition(
                                function(position) {
                                    const userLat = position.coords.latitude;
                                    const userLng = position.coords.longitude;
                                    const accuracy = position.coords.accuracy;

                                    console.log(`تم تحديد الموقع بدقة منخفضة: ${accuracy} متر`);

                                    // تخزين الموقع مؤقتًا للاستخدام اللاحق
                                    window.cachedUserLocation = {
                                        latitude: userLat,
                                        longitude: userLng,
                                        accuracy: accuracy,
                                        timestamp: new Date().getTime()
                                    };

                                    // إضافة علامة موقع المستخدم على الخريطة
                                    addUserLocationMarker(userLat, userLng, accuracy);

                                    // استخدام خدمة OSRM
                                    fetchOSRMRouteInfo(userLat, userLng, destLat, destLng);

                                    // إخفاء مؤشر التحميل
                                    hideLoadingIndicator();
                                },
                                function(error) {
                                    console.error("فشل تحديد الموقع بالكامل:", error);
                                    // في حالة فشل تحديد الموقع، استخدم الطريقة البديلة
                                    fallbackCalculation(destLat, destLng);

                                    // إخفاء مؤشر التحميل
                                    hideLoadingIndicator();
                                },
                                lowAccuracyOptions
                            );
                        },
                        highAccuracyOptions
                    );
                } else {
                    console.log("المتصفح لا يدعم تحديد الموقع");
                    // استخدم الطريقة البديلة
                    fallbackCalculation(destLat, destLng);

                    // إخفاء مؤشر التحميل
                    hideLoadingIndicator();
                }
            }

            // إضافة علامة موقع المستخدم على الخريطة
            function addUserLocationMarker(lat, lng, accuracy) {
                if (!map) return;

                // إزالة علامة موقع المستخدم السابقة إذا كانت موجودة
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }

                if (window.userLocationAccuracyCircle) {
                    map.removeLayer(window.userLocationAccuracyCircle);
                }

                // إنشاء أيقونة مخصصة لموقع المستخدم
                const userIcon = L.divIcon({
                    className: 'user-location-marker',
                    html: `<div style="background-color: #4285F4; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.3);"></div>`,
                    iconSize: [22, 22],
                    iconAnchor: [11, 11]
                });

                // إضافة علامة موقع المستخدم
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: userIcon,
                    zIndexOffset: 1000 // لضمان ظهورها فوق العلامات الأخرى
                }).addTo(map);

                // إضافة دائرة تمثل دقة الموقع
                window.userLocationAccuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#4285F4',
                    fillColor: '#4285F4',
                    fillOpacity: 0.1,
                    weight: 1
                }).addTo(map);
            }

            // إظهار مؤشر التحميل
            function showLoadingIndicator() {
                // إضافة مؤشر تحميل إلى حقول المسافة والوقت
                locationDistance.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري الحساب...';
                locationDuration.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري الحساب...';
            }

            // إخفاء مؤشر التحميل
            function hideLoadingIndicator() {
                // سيتم استبدال هذا النص بالقيم الفعلية عند اكتمال الحساب
            }

            // استخدام خدمة OSRM للحصول على معلومات المسار (أكثر دقة)
            function fetchOSRMRouteInfo(startLat, startLng, endLat, endLng) {
                // حفظ الإحداثيات الحالية للمستخدم
                window.userCurrentLat = startLat;
                window.userCurrentLng = startLng;

                // استخدام خدمة OSRM للحصول على معلومات المسار
                const url = `https://router.project-osrm.org/route/v1/driving/${startLng},${startLat};${endLng},${endLat}?overview=full&geometries=geojson&steps=true&annotations=true`;

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('فشل في الاتصال بخدمة OSRM');
                        }
                        return response.json();
                    })
                    .then(data => {
                        try {
                            if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                                // استخراج المسافة والوقت من الاستجابة
                                const route = data.routes[0];

                                // تطبيق عوامل تصحيح مشابهة لـ Waze
                                const { distance, duration } = applyWazeStyleCorrections(
                                    route.distance / 1000, // تحويل من متر إلى كيلومتر
                                    route.duration / 60,   // تحويل من ثانية إلى دقيقة
                                    startLat, startLng, endLat, endLng
                                );

                                // عرض المسافة بدقة
                                locationDistance.textContent = `${distance.toFixed(1)} كم`;

                                // عرض الوقت بدقة
                                if (duration < 60) {
                                    locationDuration.textContent = `${Math.round(duration)} دقيقة`;
                                } else {
                                    const hours = Math.floor(duration / 60);
                                    const minutes = Math.round(duration % 60);
                                    locationDuration.textContent = `${hours} ساعة و ${minutes} دقيقة`;
                                }

                                // إضافة المسار إلى الخريطة
                                if (map && route.geometry) {
                                    addGeoJSONRouteToMap(route.geometry);
                                }

                                // حفظ معلومات المسار للاستخدام اللاحق
                                window.lastRouteInfo = {
                                    startLat, startLng, endLat, endLng,
                                    distance, duration,
                                    timestamp: new Date().getTime()
                                };
                            } else {
                                throw new Error('بيانات المسار غير صالحة');
                            }
                        } catch (error) {
                            console.error("خطأ في معالجة بيانات مسار OSRM:", error);
                            // محاولة استخدام OpenRouteService كبديل
                            fetchOpenRouteServiceInfo(startLat, startLng, endLat, endLng);
                        }
                    })
                    .catch(error => {
                        console.error("خطأ في الحصول على معلومات مسار OSRM:", error);
                        // محاولة استخدام OpenRouteService كبديل
                        fetchOpenRouteServiceInfo(startLat, startLng, endLat, endLng);
                    });
            }

            // تطبيق عوامل تصحيح مشابهة لـ Waze لتحسين دقة المسافة والوقت
            function applyWazeStyleCorrections(distance, duration, startLat, startLng, endLat, endLng) {
                // 1. عامل تصحيح حركة المرور بناءً على الوقت الحالي
                const trafficFactor = getTrafficFactor();

                // 2. عامل تصحيح المسافة بناءً على نوع الطريق (تقدير)
                const distanceFactor = estimateRoadTypeFactor(startLat, startLng, endLat, endLng);

                // 3. عامل تصحيح الوقت بناءً على المنطقة (المدينة مقابل الضواحي)
                const areaFactor = estimateAreaFactor(endLat, endLng);

                // تطبيق العوامل
                const correctedDistance = distance * distanceFactor;
                const correctedDuration = duration * trafficFactor * areaFactor;

                return {
                    distance: correctedDistance,
                    duration: correctedDuration
                };
            }

            // تقدير عامل حركة المرور بناءً على الوقت الحالي
            function getTrafficFactor() {
                const now = new Date();
                const hour = now.getHours();
                const day = now.getDay(); // 0 = الأحد، 6 = السبت

                // عطلة نهاية الأسبوع
                if (day === 5 || day === 6) { // الجمعة أو السبت
                    if (hour >= 10 && hour <= 22) {
                        return 1.2; // ازدحام متوسط في عطلة نهاية الأسبوع
                    }
                    return 1.0; // حركة مرور عادية
                }

                // أوقات الذروة في أيام الأسبوع
                if ((hour >= 7 && hour <= 9) || (hour >= 16 && hour <= 19)) {
                    return 1.5; // ازدحام شديد في أوقات الذروة
                }

                // وقت الغداء
                if (hour >= 12 && hour <= 14) {
                    return 1.2; // ازدحام متوسط في وقت الغداء
                }

                return 1.0; // حركة مرور عادية في باقي الأوقات
            }

            // تقدير عامل نوع الطريق بناءً على المسافة والإحداثيات
            function estimateRoadTypeFactor(startLat, startLng, endLat, endLng) {
                // حساب المسافة المباشرة (بخط مستقيم) بين نقطتي البداية والنهاية
                const directDistance = calculateHaversineDistance(startLat, startLng, endLat, endLng);

                // المسافات الطويلة غالبًا ما تكون على الطرق السريعة
                if (directDistance > 50) {
                    return 1.05; // طرق سريعة، زيادة طفيفة بسبب المنعطفات والمخارج
                }

                // المسافات المتوسطة قد تكون مزيجًا من الطرق السريعة والطرق الرئيسية
                if (directDistance > 10) {
                    return 1.1; // مزيج من الطرق، زيادة متوسطة
                }

                // المسافات القصيرة غالبًا ما تكون في المناطق الحضرية مع طرق متعرجة
                return 1.2; // طرق حضرية، زيادة أكبر بسبب التقاطعات والإشارات
            }

            // تقدير عامل المنطقة بناءً على الإحداثيات
            function estimateAreaFactor(lat, lng) {
                // يمكننا استخدام قاعدة بيانات للمناطق الحضرية، لكن هنا سنستخدم تقديرًا بسيطًا
                // بناءً على المسافة من مركز المدينة (الرياض كمثال)

                // إحداثيات مركز الرياض
                const riyadhCenterLat = 24.7136;
                const riyadhCenterLng = 46.6753;

                // حساب المسافة من مركز المدينة
                const distanceFromCenter = calculateHaversineDistance(lat, lng, riyadhCenterLat, riyadhCenterLng);

                // المناطق القريبة من المركز غالبًا ما تكون أكثر ازدحامًا
                if (distanceFromCenter < 5) {
                    return 1.3; // وسط المدينة، ازدحام شديد
                }

                // المناطق المتوسطة البعد
                if (distanceFromCenter < 15) {
                    return 1.2; // ضواحي داخلية، ازدحام متوسط
                }

                // المناطق البعيدة
                if (distanceFromCenter < 30) {
                    return 1.1; // ضواحي خارجية، ازدحام خفيف
                }

                // المناطق النائية
                return 1.0; // مناطق ريفية، حركة مرور خفيفة
            }

            // استخدام خدمة OpenRouteService كبديل
            function fetchOpenRouteServiceInfo(startLat, startLng, endLat, endLng) {
                // استخدام خدمة OpenRouteService للحصول على معلومات المسار
                const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=5b3ce3597851110001cf6248a9b0a8d9a5e24a9d8ee9e4a03f4e4dbc&start=${startLng},${startLat}&end=${endLng},${endLat}`;

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('فشل في الاتصال بخدمة OpenRouteService');
                        }
                        return response.json();
                    })
                    .then(data => {
                        try {
                            // استخراج المسافة والوقت من الاستجابة
                            const route = data.features[0].properties.segments[0];

                            // تطبيق عوامل تصحيح مشابهة لـ Waze
                            const { distance, duration } = applyWazeStyleCorrections(
                                route.distance / 1000, // تحويل من متر إلى كيلومتر
                                route.duration / 60,   // تحويل من ثانية إلى دقيقة
                                startLat, startLng, endLat, endLng
                            );

                            // عرض المسافة
                            locationDistance.textContent = `${distance.toFixed(1)} كم`;

                            // عرض الوقت
                            if (duration < 60) {
                                locationDuration.textContent = `${Math.round(duration)} دقيقة`;
                            } else {
                                const hours = Math.floor(duration / 60);
                                const minutes = Math.round(duration % 60);
                                locationDuration.textContent = `${hours} ساعة و ${minutes} دقيقة`;
                            }

                            // إضافة المسار إلى الخريطة إذا كانت موجودة
                            if (map && data.features[0].geometry) {
                                addRouteToMap(data.features[0].geometry.coordinates);
                            }

                            // حفظ معلومات المسار للاستخدام اللاحق
                            window.lastRouteInfo = {
                                startLat, startLng, endLat, endLng,
                                distance, duration,
                                timestamp: new Date().getTime()
                            };
                        } catch (error) {
                            console.error("خطأ في معالجة بيانات OpenRouteService:", error);
                            fallbackCalculation(endLat, endLng);
                        }
                    })
                    .catch(error => {
                        console.error("خطأ في الحصول على معلومات OpenRouteService:", error);
                        fallbackCalculation(endLat, endLng);
                    });
            }

            // إضافة المسار بتنسيق GeoJSON إلى الخريطة (لخدمة OSRM)
            function addGeoJSONRouteToMap(geometry) {
                // إزالة المسار السابق إذا كان موجودًا
                if (window.routeLayer) {
                    map.removeLayer(window.routeLayer);
                }

                // إضافة المسار الجديد بتنسيق GeoJSON
                window.routeLayer = L.geoJSON(geometry, {
                    style: {
                        color: '#4285F4',
                        weight: 5,
                        opacity: 0.7,
                        lineCap: 'round',
                        lineJoin: 'round'
                    }
                }).addTo(map);

                // ضبط حدود الخريطة لتشمل المسار كاملًا
                map.fitBounds(window.routeLayer.getBounds(), { padding: [50, 50] });

                // إضافة تأثير الرسوم المتحركة للمسار
                animateRoute();
            }

            // إضافة المسار إلى الخريطة (لخدمة OpenRouteService)
            function addRouteToMap(coordinates) {
                // تحويل تنسيق الإحداثيات من [lng, lat] إلى [lat, lng]
                const routeCoords = coordinates.map(coord => [coord[1], coord[0]]);

                // إزالة المسار السابق إذا كان موجودًا
                if (window.routeLayer) {
                    map.removeLayer(window.routeLayer);
                }

                // إضافة المسار الجديد
                window.routeLayer = L.polyline(routeCoords, {
                    color: '#4285F4',
                    weight: 5,
                    opacity: 0.7,
                    lineCap: 'round',
                    lineJoin: 'round'
                }).addTo(map);

                // ضبط حدود الخريطة لتشمل المسار كاملًا
                map.fitBounds(window.routeLayer.getBounds(), { padding: [50, 50] });

                // إضافة تأثير الرسوم المتحركة للمسار
                animateRoute();
            }

            // إضافة تأثير الرسوم المتحركة للمسار (مشابه لـ Waze)
            function animateRoute() {
                if (!window.routeLayer) return;

                // إنشاء طبقة للرسوم المتحركة إذا لم تكن موجودة
                if (!window.animationLayer) {
                    window.animationLayer = L.layerGroup().addTo(map);
                } else {
                    window.animationLayer.clearLayers();
                }

                // إنشاء رمز للرسوم المتحركة
                const animationIcon = L.divIcon({
                    className: 'route-animation-icon',
                    html: '<div style="background-color: white; width: 10px; height: 10px; border-radius: 50%; box-shadow: 0 0 10px #4285F4;"></div>',
                    iconSize: [10, 10],
                    iconAnchor: [5, 5]
                });

                // الحصول على إحداثيات المسار
                let routeCoords;
                if (window.routeLayer.getLatLngs) {
                    // للمسار من OpenRouteService
                    routeCoords = window.routeLayer.getLatLngs();
                } else if (window.routeLayer.getLayers && window.routeLayer.getLayers()[0]) {
                    // للمسار من OSRM (GeoJSON)
                    routeCoords = window.routeLayer.getLayers()[0].getLatLngs();
                } else {
                    return;
                }

                // تحريك الرمز على طول المسار
                let i = 0;
                const animationMarker = L.marker(routeCoords[0], { icon: animationIcon }).addTo(window.animationLayer);

                function animate() {
                    if (i < routeCoords.length - 1) {
                        i++;
                        animationMarker.setLatLng(routeCoords[i]);
                        setTimeout(animate, 50);
                    }
                }

                animate();
            }

            // تحديث الموقع الحالي للمستخدم
            function updateUserLocation() {
                return new Promise((resolve, reject) => {
                    // إظهار مؤشر تحميل
                    const refreshButton = document.getElementById('refresh-location');
                    if (refreshButton) {
                        refreshButton.disabled = true;
                        refreshButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري التحديث...';
                    }

                    // إزالة الموقع المخزن مؤقتًا
                    window.cachedUserLocation = null;

                    // الحصول على الموقع بدقة عالية
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                const userLat = position.coords.latitude;
                                const userLng = position.coords.longitude;
                                const accuracy = position.coords.accuracy;

                                console.log(`تم تحديث الموقع بدقة: ${accuracy} متر`);

                                // تخزين الموقع مؤقتًا
                                window.cachedUserLocation = {
                                    latitude: userLat,
                                    longitude: userLng,
                                    accuracy: accuracy,
                                    timestamp: new Date().getTime()
                                };

                                // إضافة علامة موقع المستخدم على الخريطة
                                addUserLocationMarker(userLat, userLng, accuracy);

                                // إعادة حساب المسافة والوقت
                                if (map && window.lastDestination) {
                                    fetchOSRMRouteInfo(userLat, userLng, window.lastDestination.lat, window.lastDestination.lng);

                                    // تحديث روابط التنقل مع الموقع الجديد
                                    updateNavigationLinks(window.lastDestination.lat, window.lastDestination.lng);
                                }

                                // إعادة تفعيل الزر
                                if (refreshButton) {
                                    refreshButton.disabled = false;
                                    refreshButton.innerHTML = '<i class="fas fa-location-arrow ml-1"></i> تحديث موقعي';
                                }

                                // إظهار رسالة نجاح
                                showToast('success', 'تم تحديث موقعك بنجاح');

                                resolve({ lat: userLat, lng: userLng, accuracy: accuracy });
                            },
                            function(error) {
                                console.error("خطأ في تحديث الموقع:", error);

                                // إعادة تفعيل الزر
                                if (refreshButton) {
                                    refreshButton.disabled = false;
                                    refreshButton.innerHTML = '<i class="fas fa-location-arrow ml-1"></i> تحديث موقعي';
                                }

                                // إظهار رسالة خطأ
                                let errorMessage = "تعذر تحديد موقعك الحالي";
                                switch(error.code) {
                                    case error.PERMISSION_DENIED:
                                        errorMessage = "تم رفض الوصول إلى موقعك. يرجى السماح للموقع بالوصول إلى موقعك.";
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        errorMessage = "معلومات الموقع غير متوفرة حاليًا.";
                                        break;
                                    case error.TIMEOUT:
                                        errorMessage = "انتهت مهلة طلب الموقع.";
                                        break;
                                }
                                showToast('error', errorMessage);

                                reject(error);
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 15000,
                                maximumAge: 0
                            }
                        );
                    } else {
                        // إعادة تفعيل الزر
                        if (refreshButton) {
                            refreshButton.disabled = false;
                            refreshButton.innerHTML = '<i class="fas fa-location-arrow ml-1"></i> تحديث موقعي';
                        }

                        // إظهار رسالة خطأ
                        showToast('error', 'متصفحك لا يدعم تحديد الموقع');

                        reject(new Error("Geolocation not supported"));
                    }
                });
            }

            // إظهار رسالة إشعار
            function showToast(type, message) {
                // استخدام نظام الإشعارات الموحد في النظام
                if (typeof showToast === 'function' && window.showToast !== showToast) {
                    // استخدام وظيفة showToast العامة في النظام
                    window.showToast(message, type);
                } else if (typeof showNotification === 'function') {
                    // استخدام وظيفة showNotification كبديل
                    showNotification(type, message);
                } else {
                    // إنشاء إشعار مخصص إذا لم تكن الوظائف الأخرى متاحة
                    createCustomToast(type, message);
                }
            }

            // إنشاء إشعار مخصص
            function createCustomToast(type, message) {
                // التحقق من وجود حاوية الإشعارات
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container';
                    document.body.appendChild(toastContainer);
                }

                // تحديد أيقونة الإشعار حسب النوع
                let icon = '';
                let bgColor = '';
                let textColor = '';

                switch (type) {
                    case 'success':
                        icon = '<i class="fas fa-check"></i>';
                        bgColor = 'bg-green-100 dark:bg-green-900';
                        textColor = 'text-green-700 dark:text-green-300';
                        break;
                    case 'error':
                        icon = '<i class="fas fa-exclamation-triangle"></i>';
                        bgColor = 'bg-red-100 dark:bg-red-900';
                        textColor = 'text-red-700 dark:text-red-300';
                        break;
                    case 'warning':
                        icon = '<i class="fas fa-exclamation-circle"></i>';
                        bgColor = 'bg-yellow-100 dark:bg-yellow-900';
                        textColor = 'text-yellow-700 dark:text-yellow-300';
                        break;
                    case 'info':
                    default:
                        icon = '<i class="fas fa-info-circle"></i>';
                        bgColor = 'bg-blue-100 dark:bg-blue-900';
                        textColor = 'text-blue-700 dark:text-blue-300';
                        break;
                }

                // إنشاء عنصر الإشعار
                const toast = document.createElement('div');
                toast.className = `fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex items-center p-4 mb-4 rounded-lg shadow-lg border-r-4 ${bgColor} ${textColor}`;
                toast.style.maxWidth = '24rem';
                toast.style.animation = 'fadeIn 0.3s, fadeOut 0.3s 3.7s';
                toast.innerHTML = `
                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 mr-2">
                        ${icon}
                    </div>
                    <div class="text-sm font-normal">${message}</div>
                    <button type="button" class="mr-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex items-center justify-center h-8 w-8 ${textColor} hover:opacity-75">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                // إضافة الإشعار إلى الحاوية
                document.body.appendChild(toast);

                // إضافة معالج حدث لزر الإغلاق
                const closeButton = toast.querySelector('button');
                closeButton.addEventListener('click', () => {
                    toast.remove();
                });

                // إضافة CSS للتحريكات إذا لم يكن موجودًا
                if (!document.getElementById('toast-animations-css')) {
                    const style = document.createElement('style');
                    style.id = 'toast-animations-css';
                    style.textContent = `
                        @keyframes fadeIn {
                            from { opacity: 0; transform: translate(-50%, -20px); }
                            to { opacity: 1; transform: translate(-50%, 0); }
                        }
                        @keyframes fadeOut {
                            from { opacity: 1; transform: translate(-50%, 0); }
                            to { opacity: 0; transform: translate(-50%, -20px); }
                        }
                    `;
                    document.head.appendChild(style);
                }

                // إخفاء الإشعار تلقائيًا بعد 4 ثوانٍ
                setTimeout(() => {
                    if (toast && document.body.contains(toast)) {
                        toast.remove();
                    }
                }, 4000);
            }

            // طريقة بديلة لحساب المسافة والوقت في حالة فشل الطرق الأخرى
            function fallbackCalculation(destLat, destLng) {
                // استخدام موقع المستخدم الحالي إذا كان متاحًا، وإلا استخدام موقع افتراضي (الرياض)
                let startLat = window.userCurrentLat || 24.7136;
                let startLng = window.userCurrentLng || 46.6753;

                // حساب المسافة المباشرة بين النقطتين (بالكيلومتر)
                let distance = calculateHaversineDistance(startLat, startLng, destLat, destLng);

                // تطبيق عامل تصحيح للمسافة لتقريبها من المسافة الفعلية على الطرق
                // عادة ما تكون المسافة على الطرق أطول من المسافة المباشرة بنسبة 20-30%
                const roadFactor = estimateRoadTypeFactor(startLat, startLng, destLat, destLng);
                distance = distance * roadFactor;

                // عرض المسافة
                locationDistance.textContent = `${distance.toFixed(1)} كم (تقريبي)`;

                // حساب وقت الوصول التقريبي مع مراعاة عوامل مختلفة
                // 1. السرعة المتوسطة تختلف حسب نوع الطريق والمنطقة
                let avgSpeed = 60; // كم/ساعة كقيمة افتراضية

                // تعديل السرعة بناءً على المسافة (المسافات الطويلة عادة ما تكون على طرق أسرع)
                if (distance > 50) {
                    avgSpeed = 90; // طرق سريعة
                } else if (distance > 10) {
                    avgSpeed = 70; // طرق رئيسية
                } else {
                    avgSpeed = 40; // طرق حضرية
                }

                // 2. تطبيق عامل حركة المرور
                const trafficFactor = getTrafficFactor();

                // 3. تطبيق عامل المنطقة
                const areaFactor = estimateAreaFactor(destLat, destLng);

                // حساب الوقت بالساعات مع تطبيق العوامل
                const durationHours = (distance / avgSpeed) * trafficFactor * areaFactor;
                const durationMinutes = Math.round(durationHours * 60);

                // عرض الوقت
                if (durationMinutes < 60) {
                    locationDuration.textContent = `${durationMinutes} دقيقة (تقريبي)`;
                } else {
                    const hours = Math.floor(durationHours);
                    const minutes = Math.round((durationHours - hours) * 60);
                    locationDuration.textContent = `${hours} ساعة و ${minutes} دقيقة (تقريبي)`;
                }

                // حفظ معلومات المسار للاستخدام اللاحق
                window.lastRouteInfo = {
                    startLat, startLng, destLat, destLng,
                    distance, durationMinutes,
                    timestamp: new Date().getTime(),
                    isEstimate: true
                };
            }

            // حساب المسافة بين نقطتين باستخدام صيغة هافرساين
            function calculateHaversineDistance(lat1, lon1, lat2, lon2) {
                const R = 6371; // نصف قطر الأرض بالكيلومتر
                const dLat = deg2rad(lat2 - lat1);
                const dLon = deg2rad(lon2 - lon1);
                const a =
                    Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                const distance = R * c; // المسافة بالكيلومتر
                return distance;
            }

            function deg2rad(deg) {
                return deg * (Math.PI/180);
            }
        }
    });
</script>
{% endblock %}