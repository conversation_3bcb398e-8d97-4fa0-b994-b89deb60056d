<!-- Modal تأكيد الحذف -->
<div
    x-data="{
        open: false,
        deleteUrl: '',
        modalTitle: 'هل أنت متأكد من الحذف؟',
        modalMessage: 'لن تتمكن من التراجع بعد تنفيذ الحذف.',
        modalIcon: 'fa-trash-alt',
        modalIconColor: 'text-red-600 dark:text-red-400',
        confirmButtonText: 'تأكيد الحذف',
        confirmButtonColor: 'bg-red-600 hover:bg-red-700'
    }"
    @open-delete.window="deleteUrl = $event.detail; modalTitle = 'هل أنت متأكد من الحذف؟'; modalMessage = 'لن تتمكن من التراجع بعد تنفيذ الحذف.'; modalIcon = 'fa-trash-alt'; modalIconColor = 'text-red-600 dark:text-red-400'; confirmButtonText = 'تأكيد الحذف'; confirmButtonColor = 'bg-red-600 hover:bg-red-700'; open = true;"
    @open-delete-custom.window="deleteUrl = $event.detail.url; modalTitle = $event.detail.title; modalMessage = $event.detail.message; modalIcon = $event.detail.icon || 'fa-trash-alt'; modalIconColor = $event.detail.iconColor || 'text-red-600 dark:text-red-400'; confirmButtonText = $event.detail.confirmText || 'تأكيد الحذف'; confirmButtonColor = $event.detail.confirmColor || 'bg-red-600 hover:bg-red-700'; open = true;"
    class="delete-confirmation-modal"
    id="delete-confirmation-modal"
>
    <div
        x-show="open"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
        style="display: none;"
    >
        <div
            @click.outside="open = false"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-95"
            class="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl w-full max-w-md text-center"
        >
            <div :class="modalIconColor + ' mb-4'">
                <i :class="'fas ' + modalIcon + ' text-4xl'"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4" x-text="modalTitle">هل أنت متأكد من الحذف؟</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-6" x-text="modalMessage">لن تتمكن من التراجع بعد تنفيذ الحذف.</p>

            <div class="flex justify-center gap-4">
                <button
                    type="button"
                    @click="open = false"
                    class="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors duration-200"
                >
                    <i class="fas fa-times ml-1.5"></i> إلغاء
                </button>
                <button
                    type="button"
                    @click="$dispatch('submit-delete-form', deleteUrl); open = false"
                    :class="'px-4 py-2 text-white rounded-lg transition-colors duration-200 ' + confirmButtonColor"
                >
                    <i class="fas fa-check ml-1.5"></i> <span x-text="confirmButtonText">تأكيد الحذف</span>
                </button>
            </div>
        </div>
    </div>
</div>
