from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

class Client(models.Model):
    """نموذج العملاء"""
    CLIENT_TYPE_CHOICES = (
        ('permanent', 'عقد دائم'),
        ('monthly', 'عقد شهري'),
        ('custom', 'خدمة مخصصة'),
    )
    
    STATUS_CHOICES = (
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('pending', 'معلق'),
    )
    
    # معلومات أساسية
    name = models.CharField(max_length=100, verbose_name="اسم العميل")
    client_type = models.CharField(max_length=20, choices=CLIENT_TYPE_CHOICES, verbose_name="نوع العميل")
    national_id = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم البطاقة الوطنية")
    address = models.TextField(verbose_name="العنوان")
    location_lat = models.FloatField(blank=True, null=True, verbose_name="خط العرض")
    location_lng = models.FloatField(blank=True, null=True, verbose_name="خط الطول")
    
    # معلومات الاتصال
    phone_1 = models.CharField(max_length=20, verbose_name="رقم الهاتف 1")
    phone_2 = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف 2")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    
    # معلومات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    
    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_clients', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "عميل"
        verbose_name_plural = "العملاء"
        ordering = ['-created_at']
        
    def get_client_type_display_class(self):
        """إرجاع فئة CSS حسب نوع العميل"""
        if self.client_type == 'permanent':
            return 'blue'
        elif self.client_type == 'monthly':
            return 'purple'
        else:  # custom
            return 'orange'
            
    def get_documents_count(self):
        """إرجاع عدد المستندات المرفقة للعميل"""
        return self.documents.count()


class ClientDocument(models.Model):
    """نموذج مستندات العميل"""
    DOCUMENT_TYPE_CHOICES = (
        ('national_id_front', 'البطاقة الوطنية (وجه)'),
        ('national_id_back', 'البطاقة الوطنية (ظهر)'),
        ('residence_card_front', 'بطاقة السكن (وجه)'),
        ('residence_card_back', 'بطاقة السكن (ظهر)'),
        ('work_id_front', 'هوية العمل (وجه)'),
        ('work_id_back', 'هوية العمل (ظهر)'),
        ('spouse_id_front', 'هوية الزوجة (وجه)'),
        ('spouse_id_back', 'هوية الزوجة (ظهر)'),
        ('other', 'مستند آخر'),
    )
    
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='documents', verbose_name="العميل")
    document_type = models.CharField(max_length=30, choices=DOCUMENT_TYPE_CHOICES, verbose_name="نوع المستند")
    file = models.FileField(upload_to='clients/documents/%Y/%m/', verbose_name="الملف")
    description = models.CharField(max_length=200, blank=True, null=True, verbose_name="وصف المستند")
    expiry_date = models.DateField(blank=True, null=True, verbose_name="تاريخ انتهاء الصلاحية")
    is_verified = models.BooleanField(default=False, verbose_name="تم التحقق")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_client_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.client.name}"

    class Meta:
        verbose_name = "مستند العميل"
        verbose_name_plural = "مستندات العملاء"
        ordering = ['-uploaded_at']
        
    def get_file_extension(self):
        """إرجاع امتداد الملف"""
        name = self.file.name
        return name.split('.')[-1].lower() if '.' in name else ''
        
    def is_image(self):
        """التحقق مما إذا كان الملف صورة"""
        return self.get_file_extension() in ['jpg', 'jpeg', 'png', 'gif', 'webp']
        
    def is_pdf(self):
        """التحقق مما إذا كان الملف PDF"""
        return self.get_file_extension() == 'pdf'
        
    def days_until_expiry(self):
        """حساب عدد الأيام المتبقية حتى انتهاء الصلاحية"""
        if not self.expiry_date:
            return None
        delta = self.expiry_date - timezone.now().date()
        return delta.days
