"""
أمر إدارة Django لتهيئة قاعدة بيانات شركة موجودة
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from super_admin.models import Company, SystemLog
from super_admin.database_initializer import initialize_company_database

class Command(BaseCommand):
    help = 'تهيئة قاعدة بيانات شركة موجودة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف الشركة المراد تهيئة قاعدة بياناتها'
        )
        parser.add_argument(
            '--serial',
            type=str,
            help='الرقم التسلسلي للشركة المراد تهيئة قاعدة بياناتها'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='إعادة تهيئة قاعدة البيانات حتى لو كانت موجودة بالفعل'
        )

    def handle(self, *args, **options):
        start_time = timezone.now()
        self.stdout.write(self.style.SUCCESS(f'بدء تهيئة قاعدة بيانات الشركة في {start_time}'))

        company_id = options.get('company_id')
        serial = options.get('serial')
        force = options.get('force', False)

        if not company_id and not serial:
            raise CommandError('يجب تحديد معرف الشركة أو الرقم التسلسلي')

        try:
            if company_id:
                company = Company.objects.get(id=company_id)
                self.stdout.write(f'تهيئة قاعدة بيانات الشركة: {company.name} (معرف: {company.id})')
            else:
                company = Company.objects.get(serial_number=serial)
                self.stdout.write(f'تهيئة قاعدة بيانات الشركة: {company.name} (الرقم التسلسلي: {company.serial_number})')

            # تهيئة قاعدة البيانات
            result = initialize_company_database(company)

            if result['success']:
                self.stdout.write(self.style.SUCCESS(f'تم تهيئة قاعدة بيانات الشركة بنجاح: {company.name}'))
            else:
                self.stdout.write(self.style.ERROR(f'فشل في تهيئة قاعدة بيانات الشركة: {company.name}'))
                self.stdout.write(self.style.ERROR(f'السبب: {result["message"]}'))

            # تسجيل العملية
            SystemLog.objects.create(
                action='initialize',
                company=company,
                description=f'تم تهيئة قاعدة البيانات عبر أمر الإدارة: {result["message"]}',
                ip_address='127.0.0.1'  # عنوان IP محلي للأوامر
            )

        except Company.DoesNotExist:
            if company_id:
                self.stdout.write(self.style.ERROR(f'الشركة غير موجودة: معرف {company_id}'))
            else:
                self.stdout.write(self.style.ERROR(f'الشركة غير موجودة: الرقم التسلسلي {serial}'))
            return

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'حدث خطأ: {str(e)}'))
            return

        end_time = timezone.now()
        duration = end_time - start_time
        self.stdout.write(self.style.SUCCESS(f'اكتملت تهيئة قاعدة البيانات في {end_time}'))
        self.stdout.write(self.style.SUCCESS(f'المدة: {duration}'))
