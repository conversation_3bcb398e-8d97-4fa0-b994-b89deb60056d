from django.db import models
from django.contrib.auth.models import User
from backend.super_admin.models import Company

class UserCompany(models.Model):
    """نموذج لربط المستخدمين بالشركات"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='companies', verbose_name='المستخدم')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='users', verbose_name='الشركة')
    is_default = models.BooleanField(default=True, verbose_name='الشركة الافتراضية')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.user.username} - {self.company.name}"

    def save(self, *args, **kwargs):
        # إذا كانت هذه هي الشركة الافتراضية، قم بإلغاء تعيين الشركات الأخرى كافتراضية
        if self.is_default:
            UserCompany.objects.filter(user=self.user, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = 'ربط المستخدم بالشركة'
        verbose_name_plural = 'ربط المستخدمين بالشركات'
        unique_together = ('user', 'company')
