"""
وحدة التحقق عبر البريد الإلكتروني
"""

import random
import string
import time
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

def send_verification_code(user, email):
    """
    إرسال رمز تحقق عبر البريد الإلكتروني

    Args:
        user: كائن المستخدم
        email: البريد الإلكتروني للمستخدم

    Returns:
        tuple: (نجاح العملية, الرمز)
    """
    # إنشاء رمز تحقق عشوائي مكون من 6 أرقام
    verification_code = ''.join(random.choices(string.digits, k=6))

    # تخزين الرمز في الكاش فقط (بدون استخدام ملف)
    cache_key = f"2fa_code_{user.id}"
    cache_expiry = 60  # 60 ثانية = دقيقة واحدة

    # تخزين الرمز والوقت في الكاش
    cache_data = {
        'code': verification_code,
        'expires_at': int(time.time()) + cache_expiry
    }

    # تخزين البيانات في الكاش
    cache.set(cache_key, cache_data, cache_expiry)

    # إرسال البريد الإلكتروني
    subject = "🔐 رمز المصادقة الثنائية - منصة استقدامي"
    message = f"""
مرحباً {user.username}،

رمز المصادقة الثنائية الخاص بك هو: {verification_code}

هذا الرمز صالح لمدة دقيقة واحدة فقط.

إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة.

شكراً لك،
فريق منصة استقدامي السحابية
نظام أمان متقدم
    """

    html_message = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رمز المصادقة الثنائية</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .email-container {{
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }}

        .header {{
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }}

        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }}

        .logo {{
            position: relative;
            z-index: 2;
        }}

        .logo h1 {{
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }}

        .logo h2 {{
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            font-weight: 400;
        }}

        .content {{
            padding: 40px 30px;
            background: #ffffff;
        }}

        .welcome {{
            font-size: 20px;
            color: #1e293b;
            margin-bottom: 25px;
            font-weight: 500;
        }}

        .message {{
            font-size: 16px;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 30px;
        }}

        .code-container {{
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: 2px dashed #cbd5e1;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
            position: relative;
        }}

        .code-label {{
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
            font-weight: 500;
        }}

        .verification-code {{
            font-size: 36px;
            font-weight: 700;
            color: #ef4444;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
        }}

        .timer-info {{
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 25px 0;
            text-align: center;
        }}

        .timer-info .icon {{
            font-size: 20px;
            margin-bottom: 5px;
        }}

        .timer-text {{
            color: #92400e;
            font-weight: 500;
            font-size: 14px;
        }}

        .security-note {{
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 1px solid #3b82f6;
            border-radius: 10px;
            padding: 15px;
            margin: 25px 0;
            font-size: 14px;
            color: #1e40af;
        }}

        .footer {{
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }}

        .footer-text {{
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
        }}

        .brand {{
            color: #ef4444;
            font-weight: 600;
        }}

        .divider {{
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <h1>🔐 رمز المصادقة الثنائية</h1>
                <h2>منصة استقدامي السحابية</h2>
            </div>
        </div>

        <div class="content">
            <div class="welcome">
                مرحباً <strong>{user.username}</strong> 👋
            </div>

            <div class="message">
                تم طلب رمز المصادقة الثنائية لحسابك في منصة استقدامي السحابية.
            </div>

            <div class="code-container">
                <div class="code-label">رمز المصادقة الثنائية الخاص بك هو:</div>
                <div class="verification-code">{verification_code}</div>
            </div>

            <div class="timer-info">
                <div class="icon">⏰</div>
                <div class="timer-text">هذا الرمز صالح لمدة دقيقة واحدة فقط</div>
            </div>

            <div class="security-note">
                <strong>🛡️ ملاحظة أمنية:</strong><br>
                إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة وتأكد من أمان حسابك.
            </div>
        </div>

        <div class="footer">
            <div class="footer-text">
                شكراً لاستخدامك <span class="brand">منصة استقدامي السحابية</span><br>
                نظام أمان متقدم لحماية بياناتك
            </div>
            <div class="divider"></div>
            <div class="footer-text" style="font-size: 12px; color: #94a3b8;">
                هذه رسالة تلقائية، يرجى عدم الرد عليها
            </div>
        </div>
    </div>
</body>
</html>
    """

    # استخدام الطريقة التقليدية مباشرة لضمان الموثوقية
    logger.info(f"إرسال رمز التحقق للمستخدم: {user.username} إلى البريد: {email}")
    return send_verification_code_sync(user, email, verification_code, subject, message, html_message)

def send_verification_code_sync(user, email, verification_code, subject, message, html_message):
    """إرسال رمز التحقق بالطريقة التقليدية (متزامن)"""

    try:
        logger.info(f"بدء إرسال رمز التحقق للمستخدم: {user.username} إلى البريد: {email}")
        logger.info(f"إعدادات البريد الإلكتروني: HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}, USER={settings.EMAIL_HOST_USER}")

        # التحقق من إعدادات البريد الإلكتروني
        if not settings.EMAIL_HOST_USER or not settings.EMAIL_HOST_PASSWORD:
            raise Exception("إعدادات البريد الإلكتروني غير مكتملة")

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False,
        )
        logger.info(f"✅ تم إرسال رمز التحقق بنجاح إلى البريد الإلكتروني: {email}")
        return True, verification_code
    except Exception as e:
        logger.error(f"❌ خطأ مفصل في إرسال البريد الإلكتروني: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"تفاصيل الخطأ الكاملة: {traceback.format_exc()}")

        # محاولة تشخيص المشكلة
        try:
            import smtplib
            logger.info("محاولة اختبار الاتصال بخادم SMTP...")
            server = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
            server.starttls()
            server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
            server.quit()
            logger.info("✅ اختبار الاتصال بخادم SMTP نجح")
        except Exception as smtp_error:
            logger.error(f"❌ فشل اختبار الاتصال بخادم SMTP: {str(smtp_error)}")

        return False, None

def verify_code(user_id, code):
    """
    التحقق من صحة الرمز

    Args:
        user_id: معرف المستخدم
        code: الرمز المدخل

    Returns:
        bool: صحة الرمز
    """
    # الحصول على الرمز من الكاش
    cache_key = f"2fa_code_{user_id}"
    cache_data = cache.get(cache_key)

    if not cache_data:
        logger.warning(f"لم يتم العثور على رمز تحقق للمستخدم: {user_id}")
        return False

    # الحصول على الرمز والوقت من الكاش
    stored_code = cache_data.get('code')
    expires_at = cache_data.get('expires_at')

    # التحقق من انتهاء صلاحية الرمز
    if int(time.time()) > expires_at:
        # حذف الرمز منتهي الصلاحية
        cache.delete(cache_key)
        logger.warning(f"رمز تحقق منتهي الصلاحية للمستخدم: {user_id}")
        return False

    # التحقق من تطابق الرمز
    if code == stored_code:
        # حذف الرمز من الكاش بعد التحقق الناجح
        cache.delete(cache_key)
        logger.info(f"تم التحقق بنجاح من الرمز للمستخدم: {user_id}")
        return True

    logger.warning(f"رمز تحقق غير صحيح للمستخدم: {user_id}")
    return False
