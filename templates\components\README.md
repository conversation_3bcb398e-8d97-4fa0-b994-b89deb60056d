# مكونات قالب الشركات - منصة استقدامي

## 🎨 نظام الألوان المشتق من الشعار

### الألوان الأساسية:
- **الخلفية العليا**: تدرج أزرق → بنفسجي `from-[#1e3a8a] to-[#7c3aed]`
- **عدد العمال**: أخضر بحري `#10b981` (green-500)
- **عدد العملاء**: سماوي `#06b6d4` (cyan-500)
- **عدد الإشعارات**: أحمر فاتح `#ef4444` (red-500)
- **الكروت الأخرى**: أزرق داكن `#1e40af` (blue-800)

---

## 🧩 المكونات المتاحة

### 1. مكون البطاقة الإحصائية (`stat-card.blade.php`)

**الاستخدام:**
```blade
@include('components.stat-card', [
    'label' => 'عدد العمال',
    'value' => '45',
    'icon' => 'fas fa-hard-hat',
    'borderColor' => 'border-green-500',
    'iconColor' => 'text-green-500',
    'trend' => '+12%',
    'trendUp' => true,
    'link' => '/workers'
])
```

**المعاملات:**
- `label`: نص التسمية (مطلوب)
- `value`: القيمة المعروضة (مطلوب)
- `icon`: أيقونة Font Awesome (مطلوب)
- `borderColor`: لون الحد العلوي (اختياري)
- `iconColor`: لون الأيقونة (اختياري)
- `trend`: نسبة التغيير (اختياري)
- `trendUp`: اتجاه التغيير (اختياري)
- `link`: رابط للانتقال (اختياري)

---

### 2. مكون الأنشطة الأخيرة (`recent-activity.blade.php`)

**الاستخدام:**
```blade
@include('components.recent-activity', [
    'title' => 'الأنشطة الأخيرة',
    'activities' => $activities,
    'maxItems' => 5,
    'showAll' => true
])
```

**هيكل بيانات الأنشطة:**
```php
$activities = [
    [
        'type' => 'worker',        // نوع النشاط
        'description' => 'تمت إضافة عامل جديد',
        'time' => 'قبل 5 دقائق',
        'user' => 'مدير الشركة',
        'details' => 'تفاصيل إضافية', // اختياري
        'link' => '/workers/1'      // اختياري
    ]
];
```

**أنواع الأنشطة المدعومة:**
- `worker`: أيقونة عامل (أخضر)
- `client`: أيقونة عميل (سماوي)
- `contract`: أيقونة عقد (أزرق)
- `service`: أيقونة خدمة (بنفسجي)
- `delete`: أيقونة حذف (أحمر)
- `edit`: أيقونة تعديل (أصفر)
- `create`: أيقونة إضافة (أخضر)

---

### 3. مكون الإجراءات السريعة (`quick-actions.blade.php`)

**الاستخدام:**
```blade
@include('components.quick-actions', [
    'title' => 'إجراءات سريعة',
    'columns' => 2,
    'actions' => $actions
])
```

**هيكل بيانات الإجراءات:**
```php
$actions = [
    [
        'label' => 'إضافة عامل',
        'icon' => 'fas fa-user-plus',
        'color' => 'green',
        'link' => '/workers/create',        // للروابط
        'onclick' => 'myFunction()',       // للوظائف JS
        'description' => 'وصف إضافي'      // اختياري
    ]
];
```

**الألوان المدعومة:**
- `blue`, `green`, `cyan`, `purple`, `red`, `yellow`, `indigo`

**خيارات الأعمدة:**
- `1`: عمود واحد
- `2`: عمودان (افتراضي)
- `3`: ثلاثة أعمدة
- `4`: أربعة أعمدة

---

### 4. مكون نظرة عامة على لوحة التحكم (`dashboard-overview.blade.php`)

**الاستخدام:**
```blade
@include('components.dashboard-overview', [
    'company' => $company,
    'stats' => $stats,
    'activities' => $recent_activities
])
```

**هيكل بيانات الإحصائيات:**
```php
$stats = [
    'total_workers' => 150,
    'total_clients' => 89,
    'total_contracts' => 25,
    'total_services' => 12,
    'notifications' => 5,
    'workers_growth' => 12,    // نسبة النمو
    'clients_growth' => 8,
    'contracts_growth' => 15,
    'services_growth' => 5
];
```

---

## 🎯 أمثلة عملية

### مثال شامل لصفحة لوحة التحكم:

```blade
@extends('layouts.company')

@section('content')
<div class="p-6">
    @include('components.dashboard-overview', [
        'company' => $company,
        'stats' => $stats,
        'activities' => $recent_activities
    ])
</div>
@endsection
```

### مثال لبطاقات إحصائية مخصصة:

```blade
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    @include('components.stat-card', [
        'label' => 'العمال النشطون',
        'value' => '142',
        'icon' => 'fas fa-users',
        'borderColor' => 'border-green-500',
        'iconColor' => 'text-green-500',
        'trend' => '+8%',
        'trendUp' => true
    ])
    
    @include('components.stat-card', [
        'label' => 'العقود المنتهية',
        'value' => '8',
        'icon' => 'fas fa-file-times',
        'borderColor' => 'border-red-500',
        'iconColor' => 'text-red-500',
        'trend' => '-3%',
        'trendUp' => false
    ])
    
    @include('components.stat-card', [
        'label' => 'الإيرادات الشهرية',
        'value' => '125,000 ريال',
        'icon' => 'fas fa-money-bill-wave',
        'borderColor' => 'border-blue-800',
        'iconColor' => 'text-blue-800'
    ])
</div>
```

---

## 🎨 ملف CSS المخصص

تم إنشاء ملف `company-dashboard.css` يحتوي على:

### الفئات المخصصة:
- `.btn-action`: أزرار الإجراءات
- `.stat-card`: البطاقات الإحصائية
- `.activity-item`: عناصر الأنشطة
- `.quick-action-card`: بطاقات الإجراءات السريعة
- `.gradient-header`: الخلفية المتدرجة

### التأثيرات:
- `.card-hover`: تأثير التحويم
- `.fade-in`: تأثير الظهور
- `.slide-up`: تأثير الانزلاق
- `.glass-effect`: تأثير الزجاج
- `.shadow-glow`: ظل متوهج

---

## 📱 التصميم المتجاوب

جميع المكونات مصممة لتكون متجاوبة مع:
- **الهواتف**: عمود واحد
- **الأجهزة اللوحية**: عمودان
- **أجهزة سطح المكتب**: 3-5 أعمدة

---

## 🌙 دعم الوضع الليلي

جميع المكونات تدعم الوضع الليلي تلقائياً باستخدام فئات Tailwind CSS:
- `dark:bg-gray-800`
- `dark:text-white`
- `dark:border-gray-700`

---

## 🔧 التخصيص

### إضافة ألوان جديدة:
```css
:root {
    --custom-color: #your-color;
}
```

### إضافة تأثيرات جديدة:
```css
.custom-effect {
    /* تأثيرك المخصص */
}
```

---

## 📋 قائمة المراجعة

- ✅ تصميم متجاوب
- ✅ دعم الوضع الليلي
- ✅ انتقالات ناعمة
- ✅ دعم RTL
- ✅ ألوان من الشعار
- ✅ أيقونات Font Awesome
- ✅ تأثيرات تفاعلية
- ✅ أمثلة شاملة

---

## 🚀 للبدء السريع

1. تأكد من تضمين ملف CSS المخصص
2. استخدم `@include('components.dashboard-overview')` للبدء
3. خصص البيانات حسب احتياجاتك
4. راجع ملف `examples/dashboard-example.blade.php` للأمثلة

---

**تم التطوير بواسطة فريق منصة استقدامي** 🎯
