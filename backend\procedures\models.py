from django.db import models
from django.contrib.auth.models import User
from backend.workers.models import Worker

class Procedure(models.Model):
    """Base model for all procedures."""

    STATUS_CHOICES = (
        ('not_started', 'لم يبدأ'),
        ('pending', 'قيد الإجراء'),
        ('completed', 'مكتمل'),
        ('rejected', 'مرفوض'),
    )

    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='%(class)s_procedures', verbose_name='العامل')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started', verbose_name='الحالة')
    outgoing_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الصادر')
    outgoing_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الصادر')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_%(class)s_procedures', verbose_name='تم الإنشاء بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        abstract = True


class BloodTest(Procedure):
    """Model for blood test procedure."""

    RESULT_CHOICES = (
        ('', 'اختر النتيجة'),
        ('passed', 'مجتاز'),
        ('failed', 'غير مجتاز'),
    )

    reservation_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الحجز')
    reservation_time = models.TimeField(blank=True, null=True, verbose_name='موعد الحجز')
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الحالة المرجعي')
    result = models.CharField(max_length=20, choices=RESULT_CHOICES, blank=True, null=True, verbose_name='نتيجة الفحص')
    result_date = models.DateField(blank=True, null=True, verbose_name='تاريخ النتيجة')

    class Meta:
        verbose_name = 'فحص الدم'
        verbose_name_plural = 'فحوصات الدم'
        ordering = ['-created_at']

    def __str__(self):
        return f"فحص الدم للعامل {self.worker.first_name} {self.worker.last_name}"


class SponsorshipTransfer(Procedure):
    """Model for sponsorship transfer procedure."""

    contract_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم العقد')
    contract_date = models.DateField(blank=True, null=True, verbose_name='تاريخ العقد')
    client_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='اسم العميل')

    class Meta:
        verbose_name = 'تحويل الكفالة'
        verbose_name_plural = 'تحويلات الكفالة'
        ordering = ['-created_at']

    def __str__(self):
        return f"تحويل كفالة العامل {self.worker.first_name} {self.worker.last_name}"


class Operation(Procedure):
    """Model for operation procedure."""

    contract_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم العقد')
    contract_date = models.DateField(blank=True, null=True, verbose_name='تاريخ العقد')
    client_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='اسم العميل')

    class Meta:
        verbose_name = 'التشغيل'
        verbose_name_plural = 'عمليات التشغيل'
        ordering = ['-created_at']

    def __str__(self):
        return f"تشغيل العامل {self.worker.first_name} {self.worker.last_name}"


class WorkPermit(Procedure):
    """Model for work permit procedure."""

    permit_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم إجازة العمل')
    issue_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الإصدار')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')

    class Meta:
        verbose_name = 'إجازة العمل'
        verbose_name_plural = 'إجازات العمل'
        ordering = ['-created_at']

    def __str__(self):
        return f"إجازة عمل العامل {self.worker.first_name} {self.worker.last_name}"


class ResidenceID(Procedure):
    """Model for residence ID procedure."""

    id_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم هوية الإقامة')
    issue_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الإصدار')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')

    class Meta:
        verbose_name = 'هوية الإقامة'
        verbose_name_plural = 'هويات الإقامة'
        ordering = ['-created_at']

    def __str__(self):
        return f"هوية إقامة العامل {self.worker.first_name} {self.worker.last_name}"


class BranchTransfer(Procedure):
    """Model for branch transfer procedure."""

    BRANCH_CHOICES = (
        ('', 'اختر الفرع'),
        ('baghdad', 'فرع بغداد'),
        ('basra', 'فرع البصرة'),
        ('erbil', 'فرع أربيل'),
    )

    transfer_date = models.DateField(blank=True, null=True, verbose_name='تاريخ النقل')
    destination_branch = models.CharField(max_length=50, choices=BRANCH_CHOICES, blank=True, null=True, verbose_name='الفرع المرسل إليه')
    reason = models.CharField(max_length=200, blank=True, null=True, verbose_name='سبب النقل')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'النقل إلى فرع آخر'
        verbose_name_plural = 'عمليات النقل إلى فروع أخرى'
        ordering = ['-created_at']

    def __str__(self):
        return f"نقل العامل {self.worker.first_name} {self.worker.last_name} إلى {self.get_destination_branch_display()}"


class ProcedureAttachment(models.Model):
    """Model for procedure attachments."""

    blood_test = models.ForeignKey(BloodTest, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='فحص الدم')
    sponsorship_transfer = models.ForeignKey(SponsorshipTransfer, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='تحويل الكفالة')
    operation = models.ForeignKey(Operation, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='التشغيل')
    work_permit = models.ForeignKey(WorkPermit, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='إجازة العمل')
    residence_id = models.ForeignKey(ResidenceID, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='هوية الإقامة')
    branch_transfer = models.ForeignKey(BranchTransfer, on_delete=models.CASCADE, related_name='attachments', blank=True, null=True, verbose_name='النقل إلى فرع آخر')

    file = models.FileField(upload_to='procedures/attachments/%Y/%m/', verbose_name='الملف')
    description = models.CharField(max_length=200, blank=True, null=True, verbose_name='وصف الملف')
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='تم الرفع بواسطة')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')

    class Meta:
        verbose_name = 'مرفق إجراء'
        verbose_name_plural = 'مرفقات الإجراءات'
        ordering = ['-uploaded_at']

    def __str__(self):
        if self.blood_test:
            return f"مرفق فحص الدم للعامل {self.blood_test.worker.first_name} {self.blood_test.worker.last_name}"
        elif self.sponsorship_transfer:
            return f"مرفق تحويل كفالة العامل {self.sponsorship_transfer.worker.first_name} {self.sponsorship_transfer.worker.last_name}"
        elif self.operation:
            return f"مرفق تشغيل العامل {self.operation.worker.first_name} {self.operation.worker.last_name}"
        elif self.work_permit:
            return f"مرفق إجازة عمل العامل {self.work_permit.worker.first_name} {self.work_permit.worker.last_name}"
        elif self.residence_id:
            return f"مرفق هوية إقامة العامل {self.residence_id.worker.first_name} {self.residence_id.worker.last_name}"
        elif self.branch_transfer:
            return f"مرفق نقل العامل {self.branch_transfer.worker.first_name} {self.branch_transfer.worker.last_name}"
        return "مرفق إجراء"

    @property
    def filename(self):
        return self.file.name.split('/')[-1]