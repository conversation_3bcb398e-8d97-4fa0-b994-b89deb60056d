# حماية متقدمة لصفحة المسؤول الأعلى
<LocationMatch "^/super_admin">
    # منع الوصول من عناوين IP غير مصرح بها
    Order Deny,Allow
    Deny from all
    Allow from 127.0.0.1
    Allow from localhost
    Allow from ::1
    
    # إضافة headers أمنية
    Header always set X-Robots-Tag "noindex, nofollow, noarchive, nosnippet"
    Header always set Cache-Control "no-cache, no-store, must-revalidate"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
</LocationMatch>

# منع الوصول إلى الملفات الحساسة
<FilesMatch "\.(log|sqlite3|db|env|config|py|pyc)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى مجلدات النظام
RedirectMatch 404 /\.git
RedirectMatch 404 /\.svn
RedirectMatch 404 /\.env
RedirectMatch 404 /logs/
RedirectMatch 404 /data/

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع محاولات الوصول المشبوهة
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC]
    RewriteRule .* - [F]
</IfModule>

# تحديد أنواع الملفات المسموحة
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>
