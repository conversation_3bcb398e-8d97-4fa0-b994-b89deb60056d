#!/usr/bin/env python3
"""
ملف تعطيل جميع السجلات لأسباب أمنية
يجب تشغيل هذا الملف قبل تشغيل Django
"""

import logging
import sys
import os

# تعطيل جميع أنواع السجلات
logging.disable(logging.CRITICAL)

# تعطيل stdout و stderr للأمان القصوى
class NullWriter:
    def write(self, txt):
        pass
    def flush(self):
        pass

# في حالة الإنتاج، تعطيل جميع المخرجات
if os.environ.get('DJANGO_PRODUCTION', 'False') == 'True':
    sys.stdout = NullWriter()
    sys.stderr = NullWriter()

# تعطيل جميع loggers
for name in logging.Logger.manager.loggerDict:
    logging.getLogger(name).disabled = True

# تعطيل root logger
logging.getLogger().disabled = True

print("تم تعطيل جميع السجلات لأسباب أمنية")
