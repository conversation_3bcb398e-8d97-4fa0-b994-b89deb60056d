/**
 * تنسيقات محسنة لأزرار الإجراءات
 * تصميم متقدم مع تأثيرات بصرية وحركية
 */

:root {
    --action-btn-view: #3498db;
    --action-btn-view-hover: #2980b9;
    --action-btn-edit: #2ecc71;
    --action-btn-edit-hover: #27ae60;
    --action-btn-delete: #e74c3c;
    --action-btn-delete-hover: #c0392b;
    --action-btn-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --action-btn-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
    --action-btn-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --action-btn-size: 36px;
    --action-btn-icon-size: 14px;
    --action-btn-border-radius: 8px;
    --action-btn-spacing: 8px;
}

/* حاوية أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: var(--action-btn-spacing);
    justify-content: center;
}

/* زر الإجراء الأساسي */
.action-btn {
    width: var(--action-btn-size);
    height: var(--action-btn-size);
    border-radius: var(--action-btn-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--action-btn-transition);
    border: none;
    box-shadow: var(--action-btn-shadow);
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

/* تأثير التوهج عند التحويم */
.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: var(--action-btn-transition);
    z-index: 1;
}

/* تأثير التموج عند النقر */
.action-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
    transition: all 0.3s ease;
}

.action-btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}

/* تنسيق الأيقونة */
.action-btn i {
    color: white;
    font-size: var(--action-btn-icon-size);
    position: relative;
    z-index: 2;
    transition: var(--action-btn-transition);
}

/* تأثيرات التحويم */
.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--action-btn-shadow-hover);
    color: white;
    text-decoration: none;
}

.action-btn:hover::before {
    opacity: 1;
}

.action-btn:hover i {
    transform: scale(1.2);
    color: white;
}

/* تأثير الضغط */
.action-btn:active {
    transform: translateY(0);
    box-shadow: var(--action-btn-shadow);
}

/* أنواع الأزرار */
.action-btn-view {
    background-color: var(--action-btn-view);
}

.action-btn-view:hover {
    background-color: var(--action-btn-view-hover);
}

.action-btn-edit {
    background-color: var(--action-btn-edit);
}

.action-btn-edit:hover {
    background-color: var(--action-btn-edit-hover);
}

.action-btn-delete {
    background-color: var(--action-btn-delete);
}

.action-btn-delete:hover {
    background-color: var(--action-btn-delete-hover);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    :root {
        --action-btn-size: 32px;
        --action-btn-icon-size: 12px;
        --action-btn-spacing: 6px;
    }
}

@media (max-width: 576px) {
    :root {
        --action-btn-size: 28px;
        --action-btn-icon-size: 12px;
        --action-btn-spacing: 4px;
    }
}
