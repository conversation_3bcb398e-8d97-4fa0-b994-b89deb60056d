# Generated by Django 5.2 on 2025-05-28 20:25

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم العميل')),
                ('client_type', models.CharField(choices=[('individual', 'فرد'), ('company', 'شركة'), ('government', 'جهة حكومية')], max_length=20, verbose_name='نوع العميل')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('blocked', 'محظور')], default='active', max_length=20, verbose_name='الحالة')),
                ('phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator(message='رقم الهاتف يجب أن يكون بين 8-15 رقم', regex='^\\+?[0-9]{8,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('national_id', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهوية الوطنية')),
                ('passport_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم جواز السفر')),
                ('commercial_register', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم السجل التجاري')),
                ('tax_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرقم الضريبي')),
                ('sponsor_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الكفيل')),
                ('sponsor_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف الكفيل')),
                ('visa_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم التأشيرة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ClientContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='الاسم')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='المنصب')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('is_primary', models.BooleanField(default=False, verbose_name='جهة الاتصال الرئيسية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='clients.client', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'جهة اتصال',
                'verbose_name_plural': 'جهات الاتصال',
                'ordering': ['-is_primary', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ClientDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('id_copy', 'صورة الهوية'), ('passport_copy', 'صورة جواز السفر'), ('commercial_register', 'السجل التجاري'), ('tax_certificate', 'الشهادة الضريبية'), ('authorization_letter', 'خطاب تفويض'), ('contract', 'عقد'), ('other', 'أخرى')], max_length=30, verbose_name='نوع المستند')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان المستند')),
                ('file', models.FileField(upload_to='clients/documents/%Y/%m/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='clients.client', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'مستند عميل',
                'verbose_name_plural': 'مستندات العملاء',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
