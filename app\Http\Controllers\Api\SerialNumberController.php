<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\Company;
use App\Services\DatabaseConnectionService;

class SerialNumberController extends Controller
{
    /**
     * خدمة الاتصال بقواعد البيانات
     *
     * @var DatabaseConnectionService
     */
    protected $databaseService;

    /**
     * إنشاء مثيل جديد من المتحكم
     *
     * @param DatabaseConnectionService $databaseService
     * @return void
     */
    public function __construct(DatabaseConnectionService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * التحقق من صحة الرقم التسلسلي
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function check(Request $request)
    {
        try {
            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'serial' => 'required|string|min:16|max:19',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صالحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $serialNumber = $request->input('serial');
            
            Log::info("طلب التحقق من الرقم التسلسلي: {$serialNumber}");
            
            // تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
            $cleanSerial = str_replace(['-', ' '], '', $serialNumber);
            
            // التحقق من طول الرقم التسلسلي
            if (strlen($cleanSerial) !== 16) {
                Log::warning("الرقم التسلسلي غير صالح (الطول = " . strlen($cleanSerial) . "): {$serialNumber}");
                return response()->json([
                    'success' => false,
                    'message' => 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
                ], 400);
            }
            
            // تنسيق الرقم التسلسلي (إضافة الشرطات)
            $formattedSerial = implode('-', str_split($cleanSerial, 4));
            Log::debug("الرقم التسلسلي المنسق: {$formattedSerial}");
            
            // البحث عن الشركة بالرقم التسلسلي
            $company = Company::where('serial_number', $cleanSerial)
                            ->orWhere('serial_number', $formattedSerial)
                            ->first();
            
            if (!$company) {
                Log::warning("لم يتم العثور على شركة بالرقم التسلسلي: {$serialNumber}");
                return response()->json([
                    'success' => false,
                    'message' => 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة'
                ], 404);
            }
            
            Log::info("تم العثور على الشركة: {$company->name} (ID: {$company->id})");
            
            // التحقق من حالة الشركة
            if ($company->status !== 'active') {
                Log::warning("الشركة غير نشطة: {$company->name} (الحالة: {$company->status})");
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير نشطة'
                ], 403);
            }
            
            // اختبار الاتصال بقاعدة البيانات
            Log::info("اختبار الاتصال بقاعدة بيانات الشركة: {$company->name}");
            $connectionTest = $this->databaseService->testConnection($company);
            
            if (!$connectionTest['success']) {
                Log::warning("فشل الاتصال بقاعدة البيانات: {$connectionTest['message']}");
                return response()->json([
                    'success' => false,
                    'message' => 'فشل الاتصال بقاعدة بيانات الشركة'
                ], 500);
            }
            
            // إعداد اتصال قاعدة البيانات في Laravel
            Log::info("إعداد اتصال قاعدة البيانات في Laravel: {$company->database_name}");
            $setupSuccess = $this->databaseService->setupConnection($company);
            
            if (!$setupSuccess) {
                Log::error("فشل في إعداد اتصال قاعدة البيانات في Laravel: {$company->database_name}");
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إعداد اتصال قاعدة البيانات'
                ], 500);
            }
            
            Log::info("تم التحقق من الرقم التسلسلي بنجاح: {$company->name}");
            return response()->json([
                'success' => true,
                'message' => 'تم التحقق من الرقم التسلسلي بنجاح',
                'company' => [
                    'id' => $company->id,
                    'name' => $company->name,
                    'serial_number' => $company->serial_number,
                    'database_name' => $company->database_name,
                    'status' => $company->status
                ],
                'database' => [
                    'type' => $connectionTest['db_type'] ?? 'unknown',
                    'tables_count' => $connectionTest['tables_count'] ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("استثناء غير متوقع: {$e->getMessage()}");
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من الرقم التسلسلي: ' . $e->getMessage()
            ], 500);
        }
    }
}
