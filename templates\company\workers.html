{% extends 'layouts/company_modern.html' %}
{% load static %}

{% block title %}إدارة العمال - منصة استقدامي{% endblock %}

{% block content %}
<div class="container mx-auto px-6 py-8 space-y-8">

   

    <!-- عنوان الصفحة -->
    <section class="card-modern p-8 animate-fadeInUp">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6 space-x-reverse">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg animate-float">
                    <i class="fas fa-users text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">👷‍♂️ إدارة العمال</h1>
                    <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لجميع العمال في الشركة</p>
                </div>
            </div>
            <button class="btn-modern bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <i class="fas fa-plus mr-2"></i>
                إضافة عامل جديد
            </button>
        </div>
    </section>

    <!-- إحصائيات العمال -->
    <section>
        <div class="text-center mb-8 animate-fadeInUp">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">📊 إحصائيات العمال</h2>
            <p class="text-gray-600 dark:text-gray-400">نظرة شاملة على حالة العمال</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="card-modern p-6 hover-lift animate-scaleIn">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">إجمالي العمال</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_workers }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.1s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">العمال النشطون</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ active_workers }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.2s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-pause-circle text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">غير نشط</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ inactive_workers }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.3s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">إجمالي العمال</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_workers }}">0</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- جدول العمال -->
    <section class="card-modern animate-fadeInUp">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-list text-blue-600 dark:text-blue-400 mr-2"></i>
                    قائمة العمال
                </h2>
                <div class="flex items-center gap-4">
                    <form method="GET" class="flex items-center gap-4">
                        <div class="relative">
                            <input type="text" name="search" value="{{ search_query }}" placeholder="البحث..." class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select name="status" onchange="this.form.submit()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                            <option value="on_leave" {% if status_filter == 'on_leave' %}selected{% endif %}>في إجازة</option>
                            <option value="terminated" {% if status_filter == 'terminated' %}selected{% endif %}>منتهي الخدمة</option>
                        </select>
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الاسم</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الجنسية</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">المهنة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">رقم الجواز</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الحالة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">تاريخ الإضافة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for worker in workers %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                {% if worker.photo %}
                                    <img src="{{ worker.photo.url }}" alt="{{ worker.first_name }}" class="w-10 h-10 rounded-full object-cover mr-3">
                                {% else %}
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                {% endif %}
                                <span class="font-medium text-gray-900 dark:text-white">{{ worker.first_name }} {{ worker.last_name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.get_nationality_display }}</td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.job_title|default:"غير محدد" }}</td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300 font-mono">{{ worker.passport_number }}</td>
                        <td class="px-6 py-4">
                            {% if worker.is_active %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.date_joined|date:"Y/m/d" }}</td>
                        <td class="px-6 py-4">
                            <div class="flex items-center gap-2">
                                <button onclick="viewWorker({{ worker.id }})" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="editWorker({{ worker.id }})" class="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteWorker({{ worker.id }})" class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-users text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                                <p class="text-gray-500 dark:text-gray-400 text-lg">لا توجد عمال مسجلين</p>
                                <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">ابدأ بإضافة عامل جديد</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>
</div>

<script>
// وظائف الرسائل والإشعارات
function showMessages() {
    Swal.fire({
        icon: 'info',
        title: 'الرسائل الجديدة',
        text: 'عرض الرسائل الجديدة',
        confirmButtonColor: '#3b82f6'
    });
}

function showNotifications() {
    Swal.fire({
        icon: 'info',
        title: 'الإشعارات الجديدة',
        text: 'عرض الإشعارات الجديدة',
        confirmButtonColor: '#3b82f6'
    });
}

// تحديث الوقت كل دقيقة
function updateTime() {
    const now = new Date();
    const timeElements = document.querySelectorAll('.real-time');
    timeElements.forEach(element => {
        element.textContent = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    });
}

updateTime();
setInterval(updateTime, 60000);

// وظائف إدارة العمال
function viewWorker(workerId) {
    Swal.fire({
        icon: 'info',
        title: 'عرض تفاصيل العامل',
        text: `عرض تفاصيل العامل رقم ${workerId}`,
        confirmButtonColor: '#3b82f6'
    });
}

function editWorker(workerId) {
    Swal.fire({
        icon: 'question',
        title: 'تعديل بيانات العامل',
        text: `تعديل بيانات العامل رقم ${workerId}`,
        showCancelButton: true,
        confirmButtonText: 'تعديل',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#3b82f6'
    });
}

function deleteWorker(workerId) {
    Swal.fire({
        icon: 'warning',
        title: 'حذف العامل',
        text: 'هل أنت متأكد من حذف هذا العامل؟',
        showCancelButton: true,
        confirmButtonText: 'حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                icon: 'success',
                title: 'تم الحذف',
                text: 'تم حذف العامل بنجاح',
                confirmButtonColor: '#10b981'
            });
        }
    });
}

// تأثير العداد للأرقام
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-counter'));
        if (target > 0) {
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current).toLocaleString('ar-SA');
            }, 16);
        }
    });
});
</script>
{% endblock %}
