# Generated by Django 5.2 on 2025-05-26 19:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='companyuser',
            name='hide_clients_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم العملاء'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_contracts_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم العقود'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_finance_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم المالية'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_procedures_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم الإجراءات'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_reports_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم التقارير'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_services_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم الخدمات'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_tools_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم الأدوات المساعدة'),
        ),
        migrations.AddField(
            model_name='companyuser',
            name='hide_workers_section',
            field=models.BooleanField(default=False, verbose_name='إخفاء قسم العمال'),
        ),
    ]
