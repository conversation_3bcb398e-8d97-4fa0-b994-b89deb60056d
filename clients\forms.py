from django import forms
from .models import Client, ClientDocument

class ClientForm(forms.ModelForm):
    """نموذج إنشاء وتعديل العميل"""

    # حقل إضافي لرابط الخريطة (غير مرتبط بالنموذج)
    map_url = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={
            'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white',
            'placeholder': 'https://maps.google.com/...'
        }),
        label="رابط الموقع على خرائط Google"
    )

    class Meta:
        model = Client
        fields = [
            'name', 'client_type', 'national_id', 'address',
            'location_lat', 'location_lng', 'phone_1', 'phone_2',
            'email', 'notes', 'status'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'client_type': forms.Select(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'national_id': forms.TextInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'address': forms.Textarea(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white', 'rows': 3}),
            'location_lat': forms.NumberInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white', 'step': 'any'}),
            'location_lng': forms.NumberInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white', 'step': 'any'}),
            'phone_1': forms.TextInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'phone_2': forms.TextInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'email': forms.EmailInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'notes': forms.Textarea(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تعيين الحقول المطلوبة حسب نوع العميل
        if 'client_type' in self.initial:
            client_type = self.initial['client_type']
            self.set_required_fields(client_type)
        elif self.instance and self.instance.pk:
            client_type = self.instance.client_type
            self.set_required_fields(client_type)

    def set_required_fields(self, client_type):
        """تعيين الحقول المطلوبة حسب نوع العميل"""
        # الحقول المشتركة المطلوبة لجميع أنواع العملاء
        required_fields = ['name', 'phone_1', 'address']

        # الحقول المطلوبة حسب نوع العميل
        if client_type == 'permanent' or client_type == 'monthly':
            required_fields.append('national_id')

        # تعيين الحقول المطلوبة
        for field_name in required_fields:
            self.fields[field_name].required = True

        # تعيين الحقول غير المطلوبة
        for field_name, field in self.fields.items():
            if field_name not in required_fields:
                field.required = False

class ClientDocumentForm(forms.ModelForm):
    """نموذج إضافة مستند للعميل"""

    class Meta:
        model = ClientDocument
        fields = ['document_type', 'file', 'description', 'expiry_date']
        widgets = {
            'document_type': forms.Select(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'file': forms.FileInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'description': forms.TextInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white'}),
            'expiry_date': forms.DateInput(attrs={'class': 'w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white', 'type': 'date'}),
        }
