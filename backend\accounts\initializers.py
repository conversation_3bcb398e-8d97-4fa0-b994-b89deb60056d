"""
وحدة تهيئة بيانات المستخدمين
تتضمن وظائف لتهيئة بيانات المستخدمين في قاعدة البيانات الجديدة
"""

import logging
from django.db import connections
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

# إعداد السجل
logger = logging.getLogger(__name__)

def initialize_data(db_name, company):
    """
    تهيئة بيانات المستخدمين في قاعدة البيانات الجديدة

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
        company: كائن الشركة

    Returns:
        bool: نجاح العملية
    """
    try:
        logger.info(f"بدء تهيئة بيانات المستخدمين في قاعدة البيانات: {company.database_name}")

        # إنشاء المستخدم الرئيسي (مدير النظام)
        create_admin_user(db_name, company)

        # إنشاء المجموعات والصلاحيات
        create_groups_and_permissions(db_name)

        logger.info(f"تم تهيئة بيانات المستخدمين بنجاح في قاعدة البيانات: {company.database_name}")
        return True
    except Exception as e:
        logger.error(f"خطأ في تهيئة بيانات المستخدمين: {str(e)}")
        return False

def create_admin_user(db_name, company):
    """
    إنشاء المستخدم الرئيسي (مدير النظام) في قاعدة البيانات

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
        company: كائن الشركة
    """
    try:
        # الحصول على اتصال قاعدة البيانات
        connection = connections[db_name]

        # استخدام اتصال قاعدة البيانات
        with connection.cursor() as cursor:
            # التحقق من وجود جدول المستخدمين
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth_user'")
            if not cursor.fetchone():
                logger.warning(f"جدول المستخدمين غير موجود في قاعدة البيانات: {company.database_name}")
                return

        # إنشاء المستخدم الرئيسي
        admin_username = f"admin_{company.id}"
        admin_password = company.serial_number.replace('-', '')[:8]  # استخدام جزء من الرقم التسلسلي ككلمة مرور

        # التحقق من وجود المستخدم
        with connection.cursor() as cursor:
            cursor.execute("SELECT id FROM auth_user WHERE username = %s", [admin_username])
            user_exists = cursor.fetchone()

        if not user_exists:
            # استخدام make_password لتشفير كلمة المرور بشكل صحيح
            from django.contrib.auth.hashers import make_password
            hashed_password = make_password(admin_password)

            # إنشاء المستخدم
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO auth_user (
                        username, password, first_name, last_name, email,
                        is_staff, is_active, is_superuser, date_joined, last_login
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, [
                    admin_username,
                    hashed_password,  # كلمة مرور مشفرة بشكل صحيح
                    "مدير",
                    "النظام",
                    "",
                    True,  # is_staff
                    True,  # is_active
                    True,  # is_superuser
                    timezone.now(),
                    timezone.now()
                ])

            logger.info(f"تم إنشاء المستخدم الرئيسي في قاعدة البيانات: {company.database_name}")
            logger.info(f"اسم المستخدم: {admin_username}, كلمة المرور: {admin_password}")
        else:
            logger.info(f"المستخدم الرئيسي موجود بالفعل في قاعدة البيانات: {company.database_name}")

    except Exception as e:
        logger.error(f"خطأ في إنشاء المستخدم الرئيسي: {str(e)}")
        raise

def create_groups_and_permissions(db_name):
    """
    إنشاء المجموعات والصلاحيات في قاعدة البيانات

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
    """
    try:
        # الحصول على اتصال قاعدة البيانات
        connection = connections[db_name]

        # استخدام اتصال قاعدة البيانات
        with connection.cursor() as cursor:
            # التحقق من وجود جدول المجموعات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth_group'")
            if not cursor.fetchone():
                logger.warning(f"جدول المجموعات غير موجود في قاعدة البيانات: {db_name}")
                return

        # قائمة المجموعات
        groups = [
            {"name": "مدراء", "permissions": ["add_user", "change_user", "view_user"]},
            {"name": "موظفون", "permissions": ["view_user"]},
            {"name": "محاسبون", "permissions": ["view_user", "add_invoice", "change_invoice", "view_invoice"]},
            {"name": "مستقدمون", "permissions": ["view_user", "add_contract", "change_contract", "view_contract"]}
        ]

        # إنشاء المجموعات
        for group_data in groups:
            group_name = group_data["name"]

            # التحقق من وجود المجموعة
            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM auth_group WHERE name = %s", [group_name])
                group_id = cursor.fetchone()

            if not group_id:
                # إنشاء المجموعة
                with connection.cursor() as cursor:
                    cursor.execute("INSERT INTO auth_group (name) VALUES (%s)", [group_name])
                    cursor.execute("SELECT id FROM auth_group WHERE name = %s", [group_name])
                    group_id = cursor.fetchone()[0]

                logger.info(f"تم إنشاء مجموعة {group_name} في قاعدة البيانات: {db_name}")
            else:
                group_id = group_id[0]
                logger.info(f"مجموعة {group_name} موجودة بالفعل في قاعدة البيانات: {db_name}")

            # إضافة الصلاحيات للمجموعة
            for permission_codename in group_data["permissions"]:
                # الحصول على معرف الصلاحية
                with connection.cursor() as cursor:
                    cursor.execute("SELECT id FROM auth_permission WHERE codename = %s", [permission_codename])
                    permission_id = cursor.fetchone()

                if permission_id:
                    permission_id = permission_id[0]

                    # التحقق من وجود الصلاحية في المجموعة
                    with connection.cursor() as cursor:
                        cursor.execute(
                            "SELECT id FROM auth_group_permissions WHERE group_id = %s AND permission_id = %s",
                            [group_id, permission_id]
                        )
                        permission_exists = cursor.fetchone()

                    if not permission_exists:
                        # إضافة الصلاحية للمجموعة
                        with connection.cursor() as cursor:
                            cursor.execute(
                                "INSERT INTO auth_group_permissions (group_id, permission_id) VALUES (%s, %s)",
                                [group_id, permission_id]
                            )

                        logger.info(f"تم إضافة صلاحية {permission_codename} لمجموعة {group_name} في قاعدة البيانات: {db_name}")

    except Exception as e:
        logger.error(f"خطأ في إنشاء المجموعات والصلاحيات: {str(e)}")
        raise
