/* نظام تصميم عالمي المستوى - مستوحى من أفضل المواقع العالمية */

/* متغيرات CSS متقدمة */
:root {
    /* ألوان النظام */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* تدرجات متقدمة */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* ظلال متقدمة */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

    /* انتقالات متقدمة */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* مسافات متقدمة */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* أحجام الخط */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;

    /* أشعة الحدود */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;
}

/* إعدادات عامة متقدمة */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    direction: rtl;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* نظام الشبكة المتقدم */
.container-fluid {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.container-sm {
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* نظام البطاقات المتقدم */
.card-world-class {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.card-world-class::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.card-world-class:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.card-world-class:hover::before {
    opacity: 1;
}

/* نظام الأزرار المتقدم */
.btn-world-class {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-base);
    font-weight: 600;
    line-height: 1;
    border: none;
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-glass {
    background: var(--gradient-glass);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #374151;
}

/* تأثيرات الحركة المتقدمة */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
}

@keyframes slide-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-in-left {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fade-in-up {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scale-in {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* فئات الحركة */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 2s infinite;
}

.animate-slide-in-right {
    animation: slide-in-right 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slide-in-left 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
}

.animate-scale-in {
    animation: scale-in 0.4s ease-out;
}

/* تأثيرات التحويم المتقدمة */
.hover-lift {
    transition: all var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.hover-glow {
    transition: all var(--transition-base);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
}

.hover-scale {
    transition: all var(--transition-base);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* نظام الشبكة المرن */
.grid-responsive {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-2 {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(4, 1fr);
}

/* استجابة للشاشات */
@media (max-width: 1024px) {
    .grid-4 { grid-template-columns: repeat(2, 1fr); }
    .grid-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
    .grid-4, .grid-3, .grid-2 {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .container, .container-fluid {
        padding: 0 var(--spacing-md);
    }

    .card-world-class:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

/* تحسينات الأداء */
.gpu-optimized {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تأثيرات النص المتقدمة */
.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تأثيرات الحدود المتقدمة */
.border-gradient {
    position: relative;
    background: white;
    border-radius: var(--radius-xl);
}

.border-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

/* مكونات UI متقدمة */

/* شريط التقدم المتقدم */
.progress-advanced {
    width: 100%;
    height: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar-advanced {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    position: relative;
    transition: width var(--transition-slow);
}

.progress-bar-advanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* بطاقات الإحصائيات المتقدمة */
.stat-card-advanced {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.stat-card-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-base);
}

.stat-card-advanced:hover::before {
    transform: scaleX(1);
}

.stat-card-advanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.stat-icon-advanced {
    width: 64px;
    height: 64px;
    background: var(--gradient-primary);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
}

.stat-card-advanced:hover .stat-icon-advanced {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

/* شريط التنقل المتقدم */
.navbar-world-class {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all var(--transition-base);
}

.navbar-world-class.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-xl);
}

.nav-link-advanced {
    position: relative;
    padding: var(--spacing-md) var(--spacing-lg);
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    overflow: hidden;
}

.nav-link-advanced::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition-base);
    transform: translateX(-50%);
}

.nav-link-advanced:hover::before {
    width: 80%;
}

.nav-link-advanced:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.nav-link-advanced.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

/* أزرار الإجراءات المتقدمة */
.action-button-advanced {
    background: var(--gradient-glass);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-base);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-button-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.action-button-advanced:hover::before {
    left: 100%;
}

.action-button-advanced:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
}

.action-icon-advanced {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    transition: all var(--transition-bounce);
}

.action-button-advanced:hover .action-icon-advanced {
    transform: scale(1.2) rotate(10deg);
}

/* تأثيرات التحميل المتقدمة */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-dots {
    display: inline-flex;
    gap: var(--spacing-xs);
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: var(--gradient-primary);
    border-radius: 50%;
    animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    } 40% {
        transform: scale(1);
    }
}

/* تأثيرات الإشعارات المتقدمة */
.notification-advanced {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    animation: slide-in-right 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

.notification-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

/* تأثيرات الخلفية المتقدمة */
.background-pattern {
    position: relative;
}

.background-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* تحسينات الوضع الليلي */
.dark .card-world-class {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark .navbar-world-class {
    background: rgba(17, 24, 39, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark .nav-link-advanced {
    color: #e5e7eb;
}

.dark .action-button-advanced {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

/* تحسينات الأداء للحركة */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}
