{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--secondary-blue); text-decoration: none;">العمال</a></li>
<li style="margin: 0 8px;"> / </li>
<li><a href="{% url 'workers:worker_detail' worker.id %}" style="color: var(--secondary-blue); text-decoration: none;">{{ worker.first_name }} {{ worker.last_name }}</a></li>
<li style="margin: 0 8px;"> / </li>
<li><span style="color: var(--text-secondary);">حذف</span></li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- بطاقة تأكيد الحذف -->
    <div class="card shadow mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>{{ title }}</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>تأكيد الحذف</h5>
                <p>هل أنت متأكد من رغبتك في حذف العامل <strong>{{ worker.first_name }} {{ worker.last_name }}</strong>؟</p>
                <p>هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">معلومات العامل</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th style="width: 40%;">الاسم</th>
                                            <td>{{ worker.first_name }} {{ worker.last_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>رقم جواز السفر</th>
                                            <td>{{ worker.passport_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>رقم التأشيرة</th>
                                            <td>{{ worker.visa_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>الجنسية</th>
                                            <td>{{ worker.get_nationality_display }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الانضمام</th>
                                            <td>{{ worker.date_joined|date:"Y-m-d" }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">تحذير هام</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>تحذير</h6>
                                <p>حذف العامل سيؤدي إلى:</p>
                                <ul class="mt-2">
                                    <li>حذف جميع بيانات العامل من النظام</li>
                                    <li>حذف جميع المستندات المرتبطة بالعامل</li>
                                    <li>إزالة العامل من جميع الخدمات والعقود</li>
                                </ul>
                                <p class="mt-2">هذا الإجراء نهائي ولا يمكن التراجع عنه.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> تأكيد الحذف
                    </button>
                    <a href="{% url 'workers:worker_detail' worker.id %}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // يمكن إضافة أي كود JavaScript خاص بصفحة تأكيد الحذف هنا
</script>
{% endblock %}
