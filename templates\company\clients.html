{% extends 'layouts/company_modern.html' %}
{% load static %}

{% block title %}إدارة العملاء - منصة استقدامي{% endblock %}

{% block content %}
<div class="container mx-auto px-6 py-8 space-y-8">

    <!-- شريط المعلومات المحسن -->
    <section class="animate-fadeInUp">
        <div class="flex items-center justify-between text-sm py-2 px-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-2xl shadow-xl">

            <!-- اسم المستخدم -->
            <div class="flex items-center gap-2">
                <i class="fa fa-user-circle text-lg"></i>
                <span>المستخدم الحالي: <strong>{{ user.first_name|default:user.username }}</strong></span>
            </div>

            <!-- الرسائل والإشعارات -->
            <div class="flex items-center gap-4">
                <span onclick="showMessages()" class="cursor-pointer hover:underline transition-all duration-200 hover:scale-105 hover:bg-white hover:bg-opacity-10 px-2 py-1 rounded">
                    <i class="fa fa-envelope"></i> 3 رسائل جديدة
                </span>
                <span onclick="showNotifications()" class="cursor-pointer hover:underline transition-all duration-200 hover:scale-105 hover:bg-white hover:bg-opacity-10 px-2 py-1 rounded">
                    <i class="fa fa-bell"></i> 5 إشعارات
                </span>
            </div>

            <!-- التاريخ والوقت -->
            <div>
                <i class="fa fa-calendar-alt"></i> {% now "m/d" %}
                <span class="mx-1">•</span>
                <i class="fa fa-clock"></i> <span class="real-time">{% now "H:i" %}</span>
            </div>

            <!-- حالة الاشتراك -->
            <div class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs">
                <i class="fa fa-check-circle"></i> الاشتراك: نشط حتى 2025/12/31
            </div>

        </div>
    </section>

    <!-- عنوان الصفحة -->
    <section class="card-modern p-8 animate-fadeInUp">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6 space-x-reverse">
                <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg animate-float">
                    <i class="fas fa-user-tie text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">🤝 إدارة العملاء</h1>
                    <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لجميع العملاء والشركات</p>
                </div>
            </div>
            <button class="btn-modern bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <i class="fas fa-plus mr-2"></i>
                إضافة عميل جديد
            </button>
        </div>
    </section>

    <!-- إحصائيات العملاء -->
    <section>
        <div class="text-center mb-8 animate-fadeInUp">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">📊 إحصائيات العملاء</h2>
            <p class="text-gray-600 dark:text-gray-400">نظرة شاملة على قاعدة العملاء</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="card-modern p-6 hover-lift animate-scaleIn">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-user-tie text-emerald-600 dark:text-emerald-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">إجمالي العملاء</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_clients }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.1s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-user text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">عملاء أفراد</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ individual_clients }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.2s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-building text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">شركات</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ company_clients }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.3s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-landmark text-orange-600 dark:text-orange-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">جهات حكومية</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ government_clients }}">0</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- جدول العملاء -->
    <section class="card-modern animate-fadeInUp">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-list text-emerald-600 dark:text-emerald-400 mr-2"></i>
                    قائمة العملاء
                </h2>
                <div class="flex items-center gap-4">
                    <form method="GET" class="flex items-center gap-4">
                        <div class="relative">
                            <input type="text" name="search" value="{{ search_query }}" placeholder="البحث..." class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select name="type" onchange="this.form.submit()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <option value="">جميع الأنواع</option>
                            <option value="individual" {% if type_filter == 'individual' %}selected{% endif %}>أفراد</option>
                            <option value="company" {% if type_filter == 'company' %}selected{% endif %}>شركات</option>
                            <option value="government" {% if type_filter == 'government' %}selected{% endif %}>جهات حكومية</option>
                        </select>
                        <button type="submit" class="px-4 py-2 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-emerald-500 to-cyan-500 text-white">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الاسم</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">النوع</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">رقم الهوية</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الهاتف الأول</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الهاتف الثاني</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">البريد الإلكتروني</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الحالة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">تاريخ التسجيل</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for client in clients %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                {% if client.client_type == 'individual' %}
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                {% elif client.client_type == 'company' %}
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-building text-white text-sm"></i>
                                    </div>
                                {% else %}
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-landmark text-white text-sm"></i>
                                    </div>
                                {% endif %}
                                <span class="font-medium text-gray-900 dark:text-white">{{ client.name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if client.client_type == 'individual' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                    <i class="fas fa-user text-xs mr-1"></i>
                                    فرد
                                </span>
                            {% elif client.client_type == 'company' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                                    <i class="fas fa-building text-xs mr-1"></i>
                                    شركة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                                    <i class="fas fa-landmark text-xs mr-1"></i>
                                    جهة حكومية
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300 font-mono">
                            {{ client.national_id|default:"غير محدد" }}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                            {{ client.phone_1|default:"غير محدد" }}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                            {{ client.phone_2|default:"غير محدد" }}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ client.email|default:"غير محدد" }}</td>
                        <td class="px-6 py-4">
                            {% if client.status == 'active' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    نشط
                                </span>
                            {% elif client.status == 'inactive' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    غير نشط
                                </span>
                            {% elif client.status == 'pending' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                    <i class="fas fa-clock text-xs mr-1"></i>
                                    معلق
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                                    <i class="fas fa-ban text-xs mr-1"></i>
                                    محظور
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ client.created_at|date:"Y/m/d" }}</td>
                        <td class="px-6 py-4">
                            <div class="flex items-center gap-2">
                                <button onclick="viewClient({{ client.id }})" class="text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 transition-colors p-2 rounded-lg hover:bg-emerald-50 dark:hover:bg-emerald-900/20">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="editClient({{ client.id }})" class="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteClient({{ client.id }})" class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-user-tie text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                                <p class="text-gray-500 dark:text-gray-400 text-lg">لا توجد عملاء مسجلين</p>
                                <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">ابدأ بإضافة عميل جديد</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>
</div>

<script>
// وظائف الرسائل والإشعارات
function showMessages() {
    Swal.fire({
        icon: 'info',
        title: 'الرسائل الجديدة',
        text: 'عرض الرسائل الجديدة',
        confirmButtonColor: '#10b981'
    });
}

function showNotifications() {
    Swal.fire({
        icon: 'info',
        title: 'الإشعارات الجديدة',
        text: 'عرض الإشعارات الجديدة',
        confirmButtonColor: '#10b981'
    });
}

// تحديث الوقت كل دقيقة
function updateTime() {
    const now = new Date();
    const timeElements = document.querySelectorAll('.real-time');
    timeElements.forEach(element => {
        element.textContent = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    });
}

updateTime();
setInterval(updateTime, 60000);

// وظائف إدارة العملاء
function viewClient(clientId) {
    Swal.fire({
        icon: 'info',
        title: 'عرض تفاصيل العميل',
        text: `عرض تفاصيل العميل رقم ${clientId}`,
        confirmButtonColor: '#10b981'
    });
}

function editClient(clientId) {
    Swal.fire({
        icon: 'question',
        title: 'تعديل بيانات العميل',
        text: `تعديل بيانات العميل رقم ${clientId}`,
        showCancelButton: true,
        confirmButtonText: 'تعديل',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#10b981'
    });
}

function deleteClient(clientId) {
    Swal.fire({
        icon: 'warning',
        title: 'حذف العميل',
        text: 'هل أنت متأكد من حذف هذا العميل؟',
        showCancelButton: true,
        confirmButtonText: 'حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                icon: 'success',
                title: 'تم الحذف',
                text: 'تم حذف العميل بنجاح',
                confirmButtonColor: '#10b981'
            });
        }
    });
}

// تأثير العداد للأرقام
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-counter'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString('ar-SA');
        }, 16);
    });
});
</script>
{% endblock %}
