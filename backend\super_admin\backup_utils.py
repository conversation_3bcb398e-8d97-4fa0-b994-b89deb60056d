"""
وظائف مساعدة للنسخ الاحتياطي واستعادة قواعد البيانات
"""

import os
import shutil
import datetime
import sqlite3
import json
from django.conf import settings
from django.utils import timezone
from .models import Company, DatabaseBackup, SystemLog

def create_backup_directory():
    """إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا"""
    backup_dir = os.path.join(settings.BASE_DIR, 'backups')
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    return backup_dir

def create_company_backup_directory(company_name):
    """إنشاء مجلد النسخ الاحتياطية للشركة إذا لم يكن موجودًا"""
    company_backup_dir = os.path.join(settings.BASE_DIR, 'backups', company_name)
    if not os.path.exists(company_backup_dir):
        os.makedirs(company_backup_dir)
    return company_backup_dir

def backup_company_database(company_id, backup_type='manual', description=None, user=None):
    """
    إنشاء نسخة احتياطية شاملة من قاعدة بيانات شركة محددة مع جميع الملفات والمستندات

    Args:
        company_id: معرف الشركة
        backup_type: نوع النسخة الاحتياطية (manual, automatic, daily, weekly, monthly)
        description: وصف النسخة الاحتياطية
        user: المستخدم الذي قام بإنشاء النسخة الاحتياطية

    Returns:
        كائن النسخة الاحتياطية أو None في حالة الفشل
    """
    try:
        # الحصول على معلومات الشركة
        company = Company.objects.get(id=company_id)

        # مسار ملف قاعدة البيانات
        db_file_path = settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3"

        # التحقق من وجود ملف قاعدة البيانات
        if not os.path.exists(db_file_path):
            return None, f"ملف قاعدة البيانات غير موجود: {db_file_path}"

        # إنشاء مجلد النسخ الاحتياطية للشركة
        backup_dir = create_company_backup_directory(company.database_name)

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{company.database_name}_complete_{timestamp}"

        # إنشاء مجلد مؤقت للنسخة الاحتياطية الشاملة
        temp_backup_dir = os.path.join(backup_dir, f"temp_{backup_filename}")
        os.makedirs(temp_backup_dir, exist_ok=True)

        # 1. نسخ قاعدة البيانات
        db_backup_path = os.path.join(temp_backup_dir, f"{company.database_name}.sqlite3")
        shutil.copy2(db_file_path, db_backup_path)

        # 2. نسخ ملفات الشركة (المستندات، الصور، إلخ)
        company_media_dir = settings.BASE_DIR / f"data/media/companies/{company.id}"
        if os.path.exists(company_media_dir):
            media_backup_dir = os.path.join(temp_backup_dir, "media")
            shutil.copytree(company_media_dir, media_backup_dir, dirs_exist_ok=True)

        # 3. إنشاء ملف معلومات النسخة الاحتياطية
        backup_info = {
            'company_id': company.id,
            'company_name': company.name,
            'database_name': company.database_name,
            'backup_type': backup_type,
            'created_at': timezone.now().isoformat(),
            'created_by': user.username if user else 'system',
            'description': description or 'نسخة احتياطية شاملة',
            'includes': {
                'database': True,
                'media_files': os.path.exists(company_media_dir),
                'documents': True,
                'settings': True,
                'users': True,
                'workers': True,
                'clients': True,
                'contracts': True,
                'services': True,
                'procedures': True,
                'reports': True
            },
            'version': '2.1.0'
        }

        info_file_path = os.path.join(temp_backup_dir, "backup_info.json")
        with open(info_file_path, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=4)

        # 4. ضغط النسخة الاحتياطية الشاملة
        import zipfile
        zip_backup_path = os.path.join(backup_dir, f"{backup_filename}.zip")

        with zipfile.ZipFile(zip_backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(temp_backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, temp_backup_dir)
                    zipf.write(file_path, arcname)

        # 5. حذف المجلد المؤقت
        shutil.rmtree(temp_backup_dir)

        # الحصول على حجم الملف المضغوط
        file_size = os.path.getsize(zip_backup_path)

        # إنشاء سجل للنسخة الاحتياطية
        backup = DatabaseBackup.objects.create(
            company=company,
            file_name=f"{backup_filename}.zip",
            file_path=zip_backup_path,
            file_size=file_size,
            backup_type=backup_type,
            description=f"{description or 'نسخة احتياطية شاملة'} - تشمل قاعدة البيانات والملفات والمستندات",
            created_by=user
        )

        # تحديث آخر نسخة احتياطية في جدولة النسخ الاحتياطي
        if hasattr(company, 'backup_schedule'):
            company.backup_schedule.last_backup = timezone.now()
            company.backup_schedule.save()

        # تسجيل العملية
        if user:
            SystemLog.objects.create(
                user=user,
                action='create',
                company=company,
                description=f'تم إنشاء نسخة احتياطية شاملة للشركة {company.name} - تشمل قاعدة البيانات والملفات والمستندات',
                ip_address='127.0.0.1'
            )

        return backup, None
    except Exception as e:
        # تنظيف المجلد المؤقت في حالة الخطأ
        if 'temp_backup_dir' in locals() and os.path.exists(temp_backup_dir):
            shutil.rmtree(temp_backup_dir)
        return None, str(e)

def restore_company_database(backup_id, user=None):
    """
    استعادة قاعدة بيانات شركة من نسخة احتياطية شاملة

    Args:
        backup_id: معرف النسخة الاحتياطية
        user: المستخدم الذي قام بالاستعادة

    Returns:
        نجاح العملية (True/False) ورسالة توضيحية
    """
    try:
        # الحصول على معلومات النسخة الاحتياطية
        backup = DatabaseBackup.objects.get(id=backup_id)
        company = backup.company

        # التحقق من وجود ملف النسخة الاحتياطية
        if not os.path.exists(backup.file_path):
            return False, f"ملف النسخة الاحتياطية غير موجود: {backup.file_path}"

        # مسار ملف قاعدة البيانات
        db_file_path = settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3"

        # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
        if os.path.exists(db_file_path):
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            pre_restore_backup_filename = f"{company.database_name}_pre_restore_{timestamp}"
            pre_restore_backup_dir = create_company_backup_directory(company.database_name)

            # نسخ قاعدة البيانات الحالية
            pre_restore_db_path = os.path.join(pre_restore_backup_dir, f"{pre_restore_backup_filename}.sqlite3")
            shutil.copy2(db_file_path, pre_restore_db_path)

        # إنشاء مجلد مؤقت لاستخراج النسخة الاحتياطية
        temp_restore_dir = os.path.join(os.path.dirname(backup.file_path), f"temp_restore_{timezone.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(temp_restore_dir, exist_ok=True)

        try:
            # استخراج النسخة الاحتياطية المضغوطة
            import zipfile
            with zipfile.ZipFile(backup.file_path, 'r') as zipf:
                zipf.extractall(temp_restore_dir)

            # قراءة معلومات النسخة الاحتياطية
            backup_info_path = os.path.join(temp_restore_dir, "backup_info.json")
            backup_info = {}
            if os.path.exists(backup_info_path):
                with open(backup_info_path, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)

            # 1. استعادة قاعدة البيانات
            restored_db_path = os.path.join(temp_restore_dir, f"{company.database_name}.sqlite3")
            if os.path.exists(restored_db_path):
                shutil.copy2(restored_db_path, db_file_path)
            else:
                return False, "ملف قاعدة البيانات غير موجود في النسخة الاحتياطية"

            # 2. استعادة ملفات الشركة
            restored_media_dir = os.path.join(temp_restore_dir, "media")
            if os.path.exists(restored_media_dir):
                company_media_dir = settings.BASE_DIR / f"data/media/companies/{company.id}"

                # حذف الملفات الحالية
                if os.path.exists(company_media_dir):
                    shutil.rmtree(company_media_dir)

                # نسخ الملفات المستعادة
                shutil.copytree(restored_media_dir, company_media_dir, dirs_exist_ok=True)

            # تنظيف المجلد المؤقت
            shutil.rmtree(temp_restore_dir)

            # تسجيل العملية
            if user:
                description = f'تم استعادة البيانات الشاملة للشركة {company.name} من النسخة الاحتياطية {backup.file_name}'
                if backup_info.get('includes'):
                    includes = backup_info['includes']
                    restored_items = []
                    if includes.get('database'): restored_items.append('قاعدة البيانات')
                    if includes.get('media_files'): restored_items.append('الملفات والمستندات')
                    if includes.get('settings'): restored_items.append('الإعدادات')
                    description += f' - تم استعادة: {", ".join(restored_items)}'

                SystemLog.objects.create(
                    user=user,
                    action='restore',
                    company=company,
                    description=description,
                    ip_address='127.0.0.1'
                )

            return True, f"تم استعادة البيانات الشاملة بنجاح من النسخة الاحتياطية {backup.file_name}"

        except Exception as extract_error:
            # تنظيف المجلد المؤقت في حالة الخطأ
            if os.path.exists(temp_restore_dir):
                shutil.rmtree(temp_restore_dir)
            raise extract_error

    except Exception as e:
        return False, f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}"

def delete_backup(backup_id, user=None):
    """
    حذف نسخة احتياطية

    Args:
        backup_id: معرف النسخة الاحتياطية
        user: المستخدم الذي قام بالحذف

    Returns:
        نجاح العملية (True/False) ورسالة توضيحية
    """
    try:
        # الحصول على معلومات النسخة الاحتياطية
        backup = DatabaseBackup.objects.get(id=backup_id)
        company = backup.company
        file_name = backup.file_name

        # حذف ملف النسخة الاحتياطية
        if os.path.exists(backup.file_path):
            os.remove(backup.file_path)

        # حذف سجل النسخة الاحتياطية
        backup.delete()

        # تسجيل العملية
        if user:
            SystemLog.objects.create(
                user=user,
                action='delete',
                company=company,
                description=f'تم حذف النسخة الاحتياطية {file_name} للشركة {company.name}',
                ip_address='127.0.0.1'
            )

        return True, f"تم حذف النسخة الاحتياطية {file_name} بنجاح"
    except Exception as e:
        return False, str(e)

def clean_old_backups(company_id=None, retention_days=30):
    """
    حذف النسخ الاحتياطية القديمة

    Args:
        company_id: معرف الشركة (اختياري، إذا كان None سيتم تنظيف جميع الشركات)
        retention_days: عدد الأيام للاحتفاظ بالنسخ الاحتياطية

    Returns:
        عدد النسخ الاحتياطية التي تم حذفها
    """
    try:
        # تحديد تاريخ الحد الأدنى للاحتفاظ
        cutoff_date = timezone.now() - datetime.timedelta(days=retention_days)

        # تحديد النسخ الاحتياطية القديمة
        if company_id:
            old_backups = DatabaseBackup.objects.filter(company_id=company_id, created_at__lt=cutoff_date)
        else:
            old_backups = DatabaseBackup.objects.filter(created_at__lt=cutoff_date)

        # عدد النسخ الاحتياطية القديمة
        count = old_backups.count()

        # حذف ملفات النسخ الاحتياطية
        for backup in old_backups:
            if os.path.exists(backup.file_path):
                os.remove(backup.file_path)

        # حذف سجلات النسخ الاحتياطية
        old_backups.delete()

        return count
    except Exception as e:
        return 0


def create_comprehensive_backup_for_all_companies(user=None):
    """
    إنشاء نسخة احتياطية شاملة لجميع الشركات النشطة

    Args:
        user: المستخدم الذي قام بإنشاء النسخة الاحتياطية

    Returns:
        tuple: (success_count, failed_count, messages)
    """
    success_count = 0
    failed_count = 0
    messages = []

    try:
        # الحصول على جميع الشركات النشطة
        active_companies = Company.objects.filter(status='active')

        for company in active_companies:
            try:
                backup, error = backup_company_database(
                    company.id,
                    backup_type='automatic',
                    description=f'نسخة احتياطية تلقائية شاملة - {timezone.now().strftime("%Y-%m-%d %H:%M")}',
                    user=user
                )

                if backup:
                    success_count += 1
                    messages.append(f'✅ تم إنشاء نسخة احتياطية للشركة {company.name}')
                else:
                    failed_count += 1
                    messages.append(f'❌ فشل في إنشاء نسخة احتياطية للشركة {company.name}: {error}')

            except Exception as e:
                failed_count += 1
                messages.append(f'❌ خطأ في الشركة {company.name}: {str(e)}')

        # تسجيل العملية الإجمالية
        if user:
            SystemLog.objects.create(
                user=user,
                action='backup_all',
                description=f'تم إنشاء نسخ احتياطية شاملة لجميع الشركات - نجح: {success_count}, فشل: {failed_count}',
                ip_address='127.0.0.1'
            )

        return success_count, failed_count, messages

    except Exception as e:
        return 0, 1, [f'❌ خطأ عام في النسخ الاحتياطي: {str(e)}']


def verify_backup_integrity(backup_id):
    """
    التحقق من سلامة النسخة الاحتياطية

    Args:
        backup_id: معرف النسخة الاحتياطية

    Returns:
        tuple: (is_valid, details)
    """
    try:
        backup = DatabaseBackup.objects.get(id=backup_id)

        # التحقق من وجود الملف
        if not os.path.exists(backup.file_path):
            return False, "ملف النسخة الاحتياطية غير موجود"

        # التحقق من حجم الملف
        actual_size = os.path.getsize(backup.file_path)
        if actual_size != backup.file_size:
            return False, f"حجم الملف غير متطابق - المتوقع: {backup.file_size}, الفعلي: {actual_size}"

        # التحقق من إمكانية قراءة الملف المضغوط
        if backup.file_name.endswith('.zip'):
            import zipfile
            try:
                with zipfile.ZipFile(backup.file_path, 'r') as zipf:
                    # التحقق من وجود ملف معلومات النسخة الاحتياطية
                    if 'backup_info.json' not in zipf.namelist():
                        return False, "ملف معلومات النسخة الاحتياطية مفقود"

                    # التحقق من وجود قاعدة البيانات
                    db_files = [f for f in zipf.namelist() if f.endswith('.sqlite3')]
                    if not db_files:
                        return False, "ملف قاعدة البيانات مفقود في النسخة الاحتياطية"

                    # قراءة معلومات النسخة الاحتياطية
                    with zipf.open('backup_info.json') as info_file:
                        backup_info = json.load(info_file)

                    return True, {
                        'valid': True,
                        'company_name': backup_info.get('company_name'),
                        'created_at': backup_info.get('created_at'),
                        'includes': backup_info.get('includes', {}),
                        'version': backup_info.get('version'),
                        'file_count': len(zipf.namelist())
                    }

            except zipfile.BadZipFile:
                return False, "ملف النسخة الاحتياطية تالف أو غير صالح"

        return True, "النسخة الاحتياطية سليمة"

    except DatabaseBackup.DoesNotExist:
        return False, "النسخة الاحتياطية غير موجودة"
    except Exception as e:
        return False, f"خطأ في التحقق من النسخة الاحتياطية: {str(e)}"
