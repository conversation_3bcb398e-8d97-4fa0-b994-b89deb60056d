#!/usr/bin/env python
"""
سكريبت لإعداد تطبيق المصادقة للمسؤول الأعلى
"""

import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'labor_management.settings')
django.setup()

from django.contrib.auth.models import User
from super_admin.models import SuperAdminUser

def setup_authenticator():
    """إعداد تطبيق المصادقة للمسؤول الأعلى"""
    try:
        # الحصول على المستخدم
        user = User.objects.get(username='MUNTADER_WISSAM')
        print(f"✅ تم العثور على المستخدم: {user.username}")

        # الحصول على ملف المسؤول الأعلى أو إنشاؤه
        super_admin_profile, created = SuperAdminUser.objects.get_or_create(user=user)
        
        if created:
            print("✅ تم إنشاء ملف المسؤول الأعلى")
        else:
            print("✅ ملف المسؤول الأعلى موجود مسبقاً")

        # التحقق من وجود سر تطبيق المصادقة
        if not super_admin_profile.two_factor_secret:
            # إنشاء سر تطبيق المصادقة
            secret = super_admin_profile.generate_totp_secret()
            print(f"✅ تم إنشاء سر تطبيق المصادقة: {secret}")
        else:
            print(f"✅ سر تطبيق المصادقة موجود مسبقاً: {super_admin_profile.two_factor_secret}")

        # التحقق من الحالة النهائية
        print(f"\n📊 الحالة النهائية:")
        print(f"   - تفعيل المصادقة الثنائية: {super_admin_profile.two_factor_enabled}")
        print(f"   - سر التطبيق موجود: {bool(super_admin_profile.two_factor_secret)}")
        
        # إنشاء رمز QR للاختبار
        qr_uri = super_admin_profile.get_totp_uri()
        if qr_uri:
            print(f"\n🔗 رابط QR للتطبيق:")
            print(f"   {qr_uri}")
        
        print(f"\n✅ تم إعداد تطبيق المصادقة بنجاح!")
        return True

    except User.DoesNotExist:
        print("❌ خطأ: المستخدم MUNTADER_WISSAM غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == '__main__':
    setup_authenticator()
