/**
 * نظام التفاعلات عالمي المستوى
 * مستوحى من أفضل المواقع العالمية مثل Apple, Google, Microsoft
 */

class WorldClassUI {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupPerformanceOptimizations();
    }

    init() {
        // تهيئة النظام
        this.setupSmoothScrolling();
        this.setupNavbarEffects();
        this.setupParallaxEffects();
        this.setupCounterAnimations();
        this.setupMorphingEffects();
    }

    // تأثيرات التمرير الناعم
    setupSmoothScrolling() {
        // تحسين التمرير للمتصفحات المختلفة
        if ('scrollBehavior' in document.documentElement.style) {
            document.documentElement.style.scrollBehavior = 'smooth';
        } else {
            // Polyfill للمتصفحات القديمة
            this.smoothScrollPolyfill();
        }
    }

    smoothScrollPolyfill() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    this.smoothScrollTo(target.offsetTop, 800);
                }
            });
        });
    }

    smoothScrollTo(target, duration) {
        const start = window.pageYOffset;
        const distance = target - start;
        let startTime = null;

        const animation = (currentTime) => {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = this.easeInOutQuad(timeElapsed, start, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        };

        requestAnimationFrame(animation);
    }

    easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    // تأثيرات شريط التنقل المتقدمة
    setupNavbarEffects() {
        const navbar = document.querySelector('.navbar-world-class');
        if (!navbar) return;

        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateNavbar = () => {
            const scrollY = window.scrollY;
            
            if (scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // إخفاء/إظهار الشريط عند التمرير
            if (scrollY > lastScrollY && scrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = scrollY;
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavbar);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick, { passive: true });
    }

    // تأثيرات المنظور المتقدمة
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        if (parallaxElements.length === 0) return;

        let ticking = false;

        const updateParallax = () => {
            const scrollY = window.pageYOffset;

            parallaxElements.forEach(element => {
                const speed = element.dataset.parallax || 0.5;
                const yPos = -(scrollY * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick, { passive: true });
    }

    // تحريك الأرقام المتقدم
    setupCounterAnimations() {
        const counters = document.querySelectorAll('[data-counter]');
        
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.counter);
            const duration = parseInt(counter.dataset.duration) || 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            // بدء التحريك عند الظهور في الشاشة
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // تأثيرات التحويل المتقدمة
    setupMorphingEffects() {
        const morphButtons = document.querySelectorAll('.btn-morph');
        
        morphButtons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.morphButton(button, 'expand');
            });

            button.addEventListener('mouseleave', () => {
                this.morphButton(button, 'contract');
            });
        });
    }

    morphButton(button, action) {
        const icon = button.querySelector('i');
        const text = button.querySelector('.btn-text');

        if (action === 'expand') {
            button.style.transform = 'scale(1.05)';
            if (icon) icon.style.transform = 'rotate(360deg) scale(1.2)';
            if (text) text.style.letterSpacing = '0.1em';
        } else {
            button.style.transform = 'scale(1)';
            if (icon) icon.style.transform = 'rotate(0deg) scale(1)';
            if (text) text.style.letterSpacing = '0';
        }
    }

    // مراقب التقاطع المتقدم
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: [0, 0.25, 0.5, 0.75, 1],
            rootMargin: '-50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                const ratio = entry.intersectionRatio;

                if (entry.isIntersecting) {
                    element.classList.add('in-view');
                    
                    // تأثيرات مختلفة حسب نسبة الظهور
                    if (ratio > 0.5) {
                        element.classList.add('fully-visible');
                    }

                    // تحريك العناصر التدريجي
                    this.animateElement(element);
                } else {
                    element.classList.remove('in-view', 'fully-visible');
                }
            });
        }, observerOptions);

        // مراقبة العناصر القابلة للتحريك
        const animatedElements = document.querySelectorAll(
            '.animate-on-scroll, .card-world-class, .stat-card-advanced'
        );

        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }

    animateElement(element) {
        const animationType = element.dataset.animation || 'fadeInUp';
        const delay = element.dataset.delay || 0;

        setTimeout(() => {
            element.classList.add(`animate-${animationType}`);
        }, delay);
    }

    // تحسينات الأداء
    setupPerformanceOptimizations() {
        // تحسين الصور
        this.setupLazyLoading();
        
        // تحسين الحركات
        this.setupWillChange();
        
        // تحسين الذاكرة
        this.setupMemoryOptimization();
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    setupWillChange() {
        const animatedElements = document.querySelectorAll(
            '.card-hover, .btn-world-class, .stat-card-advanced'
        );

        animatedElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.willChange = 'transform, box-shadow';
            });

            element.addEventListener('mouseleave', () => {
                element.style.willChange = 'auto';
            });
        });
    }

    setupMemoryOptimization() {
        // تنظيف المستمعين غير المستخدمة
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    cleanup() {
        // إزالة المستمعين والمراقبين
        // تنظيف الذاكرة
    }

    // تأثيرات الريبل المتقدمة
    createRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تأثيرات الريبل للأزرار
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-ripple')) {
                this.createRippleEffect(e.target.closest('.btn-ripple'), e);
            }
        });

        // تأثيرات الحوم المتقدمة
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.hover-magnetic')) {
                this.setupMagneticEffect(e.target.closest('.hover-magnetic'));
            }
        });

        // تحسين الأداء للشاشات اللمسية
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
        }
    }

    // تأثير المغناطيس للعناصر
    setupMagneticEffect(element) {
        element.addEventListener('mousemove', (e) => {
            const rect = element.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = 'translate(0, 0)';
        });
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new WorldClassUI();
});

// تصدير للاستخدام العالمي
window.WorldClassUI = WorldClassUI;
