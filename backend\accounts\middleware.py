"""
Middleware for dynamic database switching based on user login and login protection.
"""
from django.conf import settings
from django.db import connections
from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
import logging
import os
from backend.super_admin.models import Company
from .router import set_current_db_name
from .models import UserCompany
from .database_config import ensure_database_config_complete

# إعداد السجل
logger = logging.getLogger(__name__)

class AtomicRequestsMiddleware(MiddlewareMixin):
    """
    Middleware that ensures all databases have ATOMIC_REQUESTS set to True.
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        # التأكد من وجود إعداد ATOMIC_REQUESTS في جميع قواعد البيانات
        for db_name, db_config in settings.DATABASES.items():
            if 'ATOMIC_REQUESTS' not in db_config:
                db_config['ATOMIC_REQUESTS'] = True

    def process_request(self, request):
        """
        Process the request and ensure all databases have ATOMIC_REQUESTS.
        """
        # التأكد من وجود إعداد ATOMIC_REQUESTS في جميع قواعد البيانات
        for db_name, db_config in settings.DATABASES.items():
            if 'ATOMIC_REQUESTS' not in db_config:
                db_config['ATOMIC_REQUESTS'] = True
        return None


class DatabaseSwitchMiddleware(MiddlewareMixin):
    """
    Middleware that switches the database connection based on the logged-in user's company.
    """

    def process_request(self, request):
        """
        Process the request and set the appropriate database connection.
        """
        # Default to the main database
        set_current_db_name('default')

        # If user is not authenticated, use default database
        if not request.user.is_authenticated:
            return None

        # If user is a superadmin, use default database
        if request.user.is_superuser and hasattr(request.user, 'super_admin_profile'):
            return None

        try:
            # Check if we have a serial number in the session
            serial_number = request.session.get('serial_number')
            db_name = request.session.get('current_db_name')
            company_id = request.session.get('current_company_id')

            if serial_number and db_name and company_id:
                # Get the company from the database using the serial number
                company = Company.objects.filter(id=company_id, serial_number=serial_number).first()

                if company and company.status == 'active':
                    logger.info(f"تبديل قاعدة البيانات إلى: {db_name} للشركة: {company.name}")

                    # Check if we need to add this database to settings
                    if db_name not in settings.DATABASES:
                        # التحقق من وجود ملف قاعدة البيانات
                        db_file_path = settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3"
                        if not os.path.exists(db_file_path):
                            logger.warning(f"ملف قاعدة البيانات غير موجود: {db_file_path}")
                            # إنشاء قاعدة بيانات جديدة للشركة
                            from backend.super_admin.database_initializer import initialize_company_database
                            result = initialize_company_database(company)
                            logger.info(f"نتيجة تهيئة قاعدة البيانات: {result}")

                            # التحقق من وجود المستخدم الرئيسي
                            try:
                                # إنشاء اتصال مؤقت بقاعدة بيانات الشركة
                                from backend.super_admin.database_connector import setup_company_db_connection
                                setup_success = setup_company_db_connection(company)

                                if setup_success:
                                    # التحقق من وجود المستخدم الرئيسي
                                    admin_username = f"admin_{company.id}"
                                    with connections[db_name].cursor() as cursor:
                                        cursor.execute("SELECT id FROM auth_user WHERE username = %s", [admin_username])
                                        user_exists = cursor.fetchone()

                                        if not user_exists:
                                            # إنشاء المستخدم الرئيسي
                                            logger.warning(f"المستخدم الرئيسي غير موجود في قاعدة البيانات: {company.database_name}")
                                            from backend.super_admin.database_initializer import create_admin_user_manually
                                            create_admin_user_manually(db_name, company)
                            except Exception as user_error:
                                logger.error(f"خطأ في التحقق من وجود المستخدم الرئيسي: {str(user_error)}")

                        # إعداد قاعدة البيانات
                        db_config = {
                            'ENGINE': 'django.db.backends.sqlite3',  # Change as needed
                            'NAME': settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3",
                            'USER': company.database_user,
                            'PASSWORD': company.database_password,
                            'HOST': company.database_host,
                            'ATOMIC_REQUESTS': True,
                        }

                        # التأكد من اكتمال إعدادات قاعدة البيانات
                        db_config = ensure_database_config_complete(db_config)

                        # إضافة قاعدة البيانات إلى الإعدادات
                        settings.DATABASES[db_name] = db_config
                        logger.info(f"تمت إضافة قاعدة البيانات {db_name} إلى الإعدادات")

                    try:
                        # Ensure the connection is established
                        connections.ensure_connection(db_name)
                        logger.info(f"تم التأكد من الاتصال بقاعدة البيانات {db_name}")
                    except Exception as conn_error:
                        logger.error(f"خطأ في الاتصال بقاعدة البيانات {db_name}: {str(conn_error)}")
                        # إعادة تهيئة قاعدة البيانات
                        from backend.super_admin.database_initializer import initialize_company_database
                        result = initialize_company_database(company)
                        logger.info(f"نتيجة إعادة تهيئة قاعدة البيانات: {result}")
                        # محاولة الاتصال مرة أخرى
                        connections.ensure_connection(db_name)

                    # Set the current database name for the router
                    set_current_db_name(db_name)
                    logger.info(f"تم تعيين اسم قاعدة البيانات الحالية: {db_name}")

                    # Store the current company in the request for easy access
                    request.current_company = company

                    return None

            # If no database in session or company not found, try to get from user's default company
            user_company = UserCompany.objects.filter(
                user=request.user,
                is_default=True
            ).select_related('company').first()

            if not user_company:
                logger.warning(f"لم يتم العثور على شركة افتراضية للمستخدم: {request.user.username}")
                return None

            company = user_company.company
            logger.info(f"تم العثور على الشركة الافتراضية للمستخدم: {company.name}")

            # If company is not active, use default database
            if company.status != 'active':
                logger.warning(f"الشركة {company.name} غير نشطة")
                return None

            # Check if we need to add this database to settings
            db_name = f"company_{company.id}"
            logger.info(f"اسم قاعدة البيانات: {db_name}")

            if db_name not in settings.DATABASES:
                # التحقق من وجود ملف قاعدة البيانات
                db_file_path = settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3"
                if not os.path.exists(db_file_path):
                    logger.warning(f"ملف قاعدة البيانات غير موجود: {db_file_path}")
                    # إنشاء قاعدة بيانات جديدة للشركة
                    from backend.super_admin.database_initializer import initialize_company_database
                    result = initialize_company_database(company)
                    logger.info(f"نتيجة تهيئة قاعدة البيانات: {result}")

                    # التحقق من وجود المستخدم الرئيسي
                    try:
                        # إنشاء اتصال مؤقت بقاعدة بيانات الشركة
                        from backend.super_admin.database_connector import setup_company_db_connection
                        setup_success = setup_company_db_connection(company)

                        if setup_success:
                            # التحقق من وجود المستخدم الرئيسي
                            admin_username = f"admin_{company.id}"
                            with connections[db_name].cursor() as cursor:
                                cursor.execute("SELECT id FROM auth_user WHERE username = %s", [admin_username])
                                user_exists = cursor.fetchone()

                                if not user_exists:
                                    # إنشاء المستخدم الرئيسي
                                    logger.warning(f"المستخدم الرئيسي غير موجود في قاعدة البيانات: {company.database_name}")
                                    from backend.super_admin.database_initializer import create_admin_user_manually
                                    create_admin_user_manually(db_name, company)
                    except Exception as user_error:
                        logger.error(f"خطأ في التحقق من وجود المستخدم الرئيسي: {str(user_error)}")

                # إعداد قاعدة البيانات
                db_config = {
                    'ENGINE': 'django.db.backends.sqlite3',  # Change as needed
                    'NAME': settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3",
                    'USER': company.database_user,
                    'PASSWORD': company.database_password,
                    'HOST': company.database_host,
                    'ATOMIC_REQUESTS': True,
                }

                # التأكد من اكتمال إعدادات قاعدة البيانات
                db_config = ensure_database_config_complete(db_config)

                # إضافة قاعدة البيانات إلى الإعدادات
                settings.DATABASES[db_name] = db_config
                logger.info(f"تمت إضافة قاعدة البيانات {db_name} إلى الإعدادات")

            try:
                # Ensure the connection is established
                connections.ensure_connection(db_name)
                logger.info(f"تم التأكد من الاتصال بقاعدة البيانات {db_name}")
            except Exception as conn_error:
                logger.error(f"خطأ في الاتصال بقاعدة البيانات {db_name}: {str(conn_error)}")
                # إعادة تهيئة قاعدة البيانات
                from backend.super_admin.database_initializer import initialize_company_database
                result = initialize_company_database(company)
                logger.info(f"نتيجة إعادة تهيئة قاعدة البيانات: {result}")
                # محاولة الاتصال مرة أخرى
                connections.ensure_connection(db_name)

            # Set the current database name for the router
            set_current_db_name(db_name)
            logger.info(f"تم تعيين اسم قاعدة البيانات الحالية: {db_name}")

            # Store the current company in the request for easy access
            request.current_company = company

            # Store in session for future requests
            request.session['current_db_name'] = db_name
            request.session['current_company_id'] = company.id
            request.session['serial_number'] = company.serial_number
            logger.info(f"تم تخزين معلومات قاعدة البيانات في الجلسة: {db_name}, {company.id}, {company.serial_number}")

        except Exception as e:
            # Log the error and continue with default database
            logger.error(f"خطأ في تبديل قاعدة البيانات: {str(e)}")
            set_current_db_name('default')

        return None
