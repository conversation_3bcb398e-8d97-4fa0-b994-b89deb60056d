<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * The path to the "super admin home" route for your application.
     *
     * @var string
     */
    public const SUPER_ADMIN_HOME = '/super_admin';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            // API Routes
            Route::prefix('api')
                ->middleware('api')
                ->group(base_path('routes/api.php'));

            // Web Routes
            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // Super Admin Routes
            Route::prefix('super_admin')
                ->middleware(['web', 'auth.super_admin'])
                ->name('super_admin.')
                ->group(base_path('routes/super_admin.php'));

            // Company Routes
            Route::prefix('company')
                ->middleware(['web', 'switch.database'])
                ->name('company.')
                ->group(base_path('routes/company.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        // Rate limit for API requests
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        // Rate limit for login attempts
        RateLimiter::for('login', function (Request $request) {
            return Limit::perMinute(5)->by($request->ip());
        });

        // Rate limit for super admin login attempts (more strict)
        RateLimiter::for('super_admin_login', function (Request $request) {
            return Limit::perMinute(3)->by($request->ip());
        });
    }
}
