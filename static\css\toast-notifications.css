/* أنماط نظام الإشعارات المحسن */

/* حاوية الإشعارات */
.toast-container {
    position: fixed;
    top: 1.25rem;
    right: 1.25rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 24rem;
    width: auto;
}

/* الإشعار الأساسي */
.toast {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    width: 100%;
    max-width: 24rem;
    margin-bottom: 0.5rem;
    animation: toast-in 0.3s ease-out forwards;
}

/* أنواع الإشعارات */
.toast-success {
    background-color: #ecfdf5;
    border-right: 4px solid #10b981;
    color: #065f46;
}

.toast-error {
    background-color: #fef2f2;
    border-right: 4px solid #ef4444;
    color: #991b1b;
}

.toast-warning {
    background-color: #fffbeb;
    border-right: 4px solid #f59e0b;
    color: #92400e;
}

.toast-info {
    background-color: #eff6ff;
    border-right: 4px solid #3b82f6;
    color: #1e40af;
}

/* أيقونة الإشعار */
.toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    margin-left: 0.75rem;
    flex-shrink: 0;
}

.toast-success .toast-icon {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.toast-error .toast-icon {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.toast-warning .toast-icon {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.toast-info .toast-icon {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

/* محتوى الإشعار */
.toast-content {
    flex-grow: 1;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

/* زر إغلاق الإشعار */
.toast-close {
    background: transparent;
    border: none;
    color: currentColor;
    opacity: 0.5;
    cursor: pointer;
    padding: 0.25rem;
    margin-right: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

/* تحريكات الإشعارات */
@keyframes toast-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toast-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-in {
    animation: toast-in 0.3s ease-out forwards;
}

.toast-out {
    animation: toast-out 0.3s ease-in forwards;
}

/* الوضع الداكن */

.dark .toast-success {
    background-color: #064e3b;
    color: #d1fae5;
}

.dark .toast-error {
    background-color: #7f1d1d;
    color: #fee2e2;
}

.dark .toast-warning {
    background-color: #78350f;
    color: #fef3c7;
}

.dark .toast-info {
    background-color: #1e3a8a;
    color: #dbeafe;
}
