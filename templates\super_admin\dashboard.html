{% extends 'super_admin/base.html' %}

{% block title %}لوحة تحكم المسؤول الأعلى{% endblock %}

{% block content %}
<div class="p-6">
    <h1 class="text-3xl font-bold text-[#143D8D] mb-6">لوحة تحكم المسؤول الأعلى</h1>

    <!-- إحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white p-4 shadow rounded text-center">
            <h2 class="text-sm text-gray-600">عدد الشركات</h2>
            <p class="text-2xl font-bold text-[#143D8D]">{{ stats.companies_count }}</p>
        </div>
        <div class="bg-white p-4 shadow rounded text-center">
            <h2 class="text-sm text-gray-600">الشركات النشطة</h2>
            <p class="text-2xl font-bold text-green-600">{{ stats.active_companies }}</p>
        </div>
        <div class="bg-white p-4 shadow rounded text-center">
            <h2 class="text-sm text-gray-600">الشركات الموقوفة</h2>
            <p class="text-2xl font-bold text-red-500">{{ stats.inactive_companies }}</p>
        </div>
    </div>

    <!-- زر إضافة -->
    <div class="text-right mb-4">
        <a href="{% url 'super_admin:create_company' %}" class="bg-[#407BFF] hover:bg-[#143D8D] text-white px-5 py-2 rounded flex items-center justify-center w-fit mr-auto">
            <i class="fas fa-plus ml-2"></i> إضافة شركة جديدة
        </a>
    </div>

    <!-- تحكم بعدد الصفوف المعروضة -->
    <div class="flex justify-between items-center mb-4">
        <form method="GET" class="flex items-center space-x-2 rtl:space-x-reverse">
            <label class="text-sm text-gray-600">عدد الصفوف:</label>
            <select name="per_page" onchange="this.form.submit()" class="border rounded p-1 text-sm">
                <option value="10" {% if request.GET.per_page == '10' %}selected{% endif %}>10</option>
                <option value="20" {% if request.GET.per_page == '20' %}selected{% endif %}>20</option>
                <option value="50" {% if request.GET.per_page == '50' %}selected{% endif %}>50</option>
            </select>
        </form>

        <div class="text-sm text-gray-600">
            إجمالي الشركات: <span class="font-bold">{{ stats.companies_count }}</span>
        </div>
    </div>

    <!-- جدول الشركات -->
    <div class="bg-white shadow-md rounded overflow-auto p-2">
        <table class="w-full table-auto text-center text-sm">
            <thead class="bg-[#143D8D] text-white">
                <tr>
                    <th class="px-4 py-3">#</th>
                    <th class="px-4 py-3">اسم الشركة</th>
                    <th class="px-4 py-3">قاعدة البيانات</th>
                    <th class="px-4 py-3">الرقم التسلسلي</th>
                    <th class="px-4 py-3">الحالة</th>
                    <th class="px-4 py-3">إجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for company in companies %}
                    <tr class="border-b hover:bg-gray-50 even:bg-gray-50">
                        <td class="px-4 py-3">{{ forloop.counter }}</td>
                        <td class="px-4 py-3">{{ company.name }}</td>
                        <td class="px-4 py-3">{{ company.database_name }}</td>
                        <td class="px-4 py-3 font-mono text-xs">{{ company.serial_number }}</td>
                        <td class="px-4 py-3">
                            {% if company.status == 'active' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                                    نشطة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <span class="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                                    موقوفة
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3">
                            <div class="flex justify-center space-x-2 rtl:space-x-reverse">
                                <a href="{% url 'super_admin:company_detail' company.id %}" class="text-blue-600 hover:text-blue-800 p-1" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                   class="{% if company.status == 'active' %}text-yellow-600 hover:text-yellow-800{% else %}text-green-600 hover:text-green-800{% endif %} p-1 bg-transparent border-0"
                                   title="{% if company.status == 'active' %}إيقاف{% else %}تشغيل{% endif %}"
                                   data-delete-url="{% url 'super_admin:toggle_company_status' company.id %}"
                                   onclick="window.customizeDeleteModal(
                                       this.getAttribute('data-delete-url'),
                                       '{% if company.status == 'active' %}تأكيد إيقاف الشركة{% else %}تأكيد تفعيل الشركة{% endif %}',
                                       'هل أنت متأكد من {% if company.status == 'active' %}إيقاف{% else %}تفعيل{% endif %} الشركة {{ company.name }}؟',
                                       '{% if company.status == 'active' %}fa-ban{% else %}fa-check-circle{% endif %}',
                                       '{% if company.status == 'active' %}text-yellow-600{% else %}text-green-600{% endif %}',
                                       '{% if company.status == 'active' %}تأكيد الإيقاف{% else %}تأكيد التفعيل{% endif %}',
                                       '{% if company.status == 'active' %}bg-yellow-600 hover:bg-yellow-700{% else %}bg-green-600 hover:bg-green-700{% endif %}'
                                   );">
                                    {% if company.status == 'active' %}
                                        <i class="fas fa-ban"></i>
                                    {% else %}
                                        <i class="fas fa-check-circle"></i>
                                    {% endif %}
                                </button>
                                <button type="button"
                                   class="text-red-600 hover:text-red-800 p-1 bg-transparent border-0"
                                   title="حذف"
                                   data-delete-url="{% url 'super_admin:delete_company' company.id %}"
                                   onclick="window.customizeDeleteModal(
                                       this.getAttribute('data-delete-url'),
                                       'تأكيد حذف الشركة',
                                       'سيتم حذف الشركة {{ company.name }}! هل أنت متأكد؟ هذا الإجراء لا يمكن التراجع عنه.',
                                       'fa-trash-alt',
                                       'text-red-600',
                                       'تأكيد الحذف',
                                       'bg-red-600 hover:bg-red-700'
                                   );">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="6" class="py-8 text-gray-500 text-center">
                            <i class="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
                            <p>لا توجد شركات حالياً.</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ترقيم الصفحات -->
    <div class="mt-4 flex justify-center">
        <div class="flex space-x-1 rtl:space-x-reverse">
            {% if companies.has_previous %}
                <a href="?page=1{% if request.GET.per_page %}&per_page={{ request.GET.per_page }}{% endif %}" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                    <i class="fas fa-angle-double-right"></i>
                </a>
                <a href="?page={{ companies.previous_page_number }}{% if request.GET.per_page %}&per_page={{ request.GET.per_page }}{% endif %}" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                    <i class="fas fa-angle-right"></i>
                </a>
            {% else %}
                <span class="px-3 py-1 bg-gray-100 text-gray-400 rounded cursor-not-allowed">
                    <i class="fas fa-angle-double-right"></i>
                </span>
                <span class="px-3 py-1 bg-gray-100 text-gray-400 rounded cursor-not-allowed">
                    <i class="fas fa-angle-right"></i>
                </span>
            {% endif %}

            <span class="px-3 py-1 bg-[#143D8D] text-white rounded">
                {{ companies.number }} من {{ companies.paginator.num_pages }}
            </span>

            {% if companies.has_next %}
                <a href="?page={{ companies.next_page_number }}{% if request.GET.per_page %}&per_page={{ request.GET.per_page }}{% endif %}" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                    <i class="fas fa-angle-left"></i>
                </a>
                <a href="?page={{ companies.paginator.num_pages }}{% if request.GET.per_page %}&per_page={{ request.GET.per_page }}{% endif %}" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            {% else %}
                <span class="px-3 py-1 bg-gray-100 text-gray-400 rounded cursor-not-allowed">
                    <i class="fas fa-angle-left"></i>
                </span>
                <span class="px-3 py-1 bg-gray-100 text-gray-400 rounded cursor-not-allowed">
                    <i class="fas fa-angle-double-left"></i>
                </span>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة أي سكريبت إضافي هنا إذا لزم الأمر
    });
</script>
{% endblock %}