from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, timedelta
import calendar
import json

from .models import Service, ServiceType, CustomClient, CustomWorker, FixedServiceSchedule, FixedServiceException
from backend.contracts.models import Client
from backend.workers.models import Worker

# الصفحة الرئيسية للخدمات
@login_required
def services_home(request):
    # إحصائيات الخدمات
    total_services = Service.objects.count()
    active_services = Service.objects.filter(status__in=['pending', 'booked', 'confirmed', 'in_progress']).count()
    completed_services = Service.objects.filter(status='completed').count()
    cancelled_services = Service.objects.filter(status='cancelled').count()
    
    # الخدمات الحديثة
    recent_services = Service.objects.all().order_by('-created_at')[:5]
    
    # أنواع الخدمات
    service_types = ServiceType.objects.filter(is_active=True)
    
    context = {
        'total_services': total_services,
        'active_services': active_services,
        'completed_services': completed_services,
        'cancelled_services': cancelled_services,
        'recent_services': recent_services,
        'service_types': service_types,
    }
    
    return render(request, 'services/home.html', context)

# إدارة أنواع الخدمات
@login_required
def service_types(request):
    service_types = ServiceType.objects.all()
    return render(request, 'services/service_types.html', {'service_types': service_types})

@login_required
def add_service_type(request):
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة نوع الخدمة بنجاح')
        return redirect('services:service_types')
    return render(request, 'services/add_service_type.html')

@login_required
def edit_service_type(request, type_id):
    service_type = get_object_or_404(ServiceType, id=type_id)
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم تحديث نوع الخدمة بنجاح')
        return redirect('services:service_types')
    return render(request, 'services/edit_service_type.html', {'service_type': service_type})

@login_required
def delete_service_type(request, type_id):
    service_type = get_object_or_404(ServiceType, id=type_id)
    if request.method == 'POST':
        service_type.delete()
        messages.success(request, 'تم حذف نوع الخدمة بنجاح')
        return redirect('services:service_types')
    return render(request, 'services/delete_service_type.html', {'service_type': service_type})

# إدارة الخدمات
@login_required
def services_list(request):
    services = Service.objects.all().order_by('-created_at')
    return render(request, 'services/services_list.html', {'services': services})

@login_required
def add_service(request):
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة الخدمة بنجاح')
        return redirect('services:services_list')
    
    service_types = ServiceType.objects.filter(is_active=True)
    clients = Client.objects.filter(is_active=True)
    workers = Worker.objects.filter(is_active=True)
    custom_clients = CustomClient.objects.filter(is_active=True)
    custom_workers = CustomWorker.objects.filter(is_active=True)
    
    context = {
        'service_types': service_types,
        'clients': clients,
        'workers': workers,
        'custom_clients': custom_clients,
        'custom_workers': custom_workers,
    }
    
    return render(request, 'services/add_service.html', context)

@login_required
def edit_service(request, service_id):
    service = get_object_or_404(Service, id=service_id)
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم تحديث الخدمة بنجاح')
        return redirect('services:services_list')
    
    service_types = ServiceType.objects.filter(is_active=True)
    clients = Client.objects.filter(is_active=True)
    workers = Worker.objects.filter(is_active=True)
    custom_clients = CustomClient.objects.filter(is_active=True)
    custom_workers = CustomWorker.objects.filter(is_active=True)
    
    context = {
        'service': service,
        'service_types': service_types,
        'clients': clients,
        'workers': workers,
        'custom_clients': custom_clients,
        'custom_workers': custom_workers,
    }
    
    return render(request, 'services/edit_service.html', context)

@login_required
def view_service(request, service_id):
    service = get_object_or_404(Service, id=service_id)
    return render(request, 'services/view_service.html', {'service': service})

@login_required
def delete_service(request, service_id):
    service = get_object_or_404(Service, id=service_id)
    if request.method == 'POST':
        service.delete()
        messages.success(request, 'تم حذف الخدمة بنجاح')
        return redirect('services:services_list')
    return render(request, 'services/delete_service.html', {'service': service})

# إدارة الحجوزات الثابتة
@login_required
def fixed_services(request):
    fixed_services = Service.objects.filter(is_fixed=True)
    return render(request, 'services/fixed_services.html', {'fixed_services': fixed_services})

@login_required
def add_fixed_service(request):
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة الحجز الثابت بنجاح')
        return redirect('services:fixed_services')
    
    service_types = ServiceType.objects.filter(is_active=True)
    clients = Client.objects.filter(is_active=True)
    workers = Worker.objects.filter(is_active=True)
    
    context = {
        'service_types': service_types,
        'clients': clients,
        'workers': workers,
    }
    
    return render(request, 'services/add_fixed_service.html', context)

@login_required
def edit_fixed_service(request, service_id):
    service = get_object_or_404(Service, id=service_id, is_fixed=True)
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم تحديث الحجز الثابت بنجاح')
        return redirect('services:fixed_services')
    
    service_types = ServiceType.objects.filter(is_active=True)
    clients = Client.objects.filter(is_active=True)
    workers = Worker.objects.filter(is_active=True)
    
    context = {
        'service': service,
        'service_types': service_types,
        'clients': clients,
        'workers': workers,
    }
    
    return render(request, 'services/edit_fixed_service.html', context)

@login_required
def delete_fixed_service(request, service_id):
    service = get_object_or_404(Service, id=service_id, is_fixed=True)
    if request.method == 'POST':
        service.delete()
        messages.success(request, 'تم حذف الحجز الثابت بنجاح')
        return redirect('services:fixed_services')
    return render(request, 'services/delete_fixed_service.html', {'service': service})

# إدارة استثناءات الحجوزات الثابتة
@login_required
def fixed_service_exceptions(request, service_id):
    service = get_object_or_404(Service, id=service_id, is_fixed=True)
    schedules = FixedServiceSchedule.objects.filter(service=service)
    exceptions = FixedServiceException.objects.filter(schedule__in=schedules)
    
    context = {
        'service': service,
        'schedules': schedules,
        'exceptions': exceptions,
    }
    
    return render(request, 'services/fixed_service_exceptions.html', context)

@login_required
def add_fixed_service_exception(request, service_id):
    service = get_object_or_404(Service, id=service_id, is_fixed=True)
    schedules = FixedServiceSchedule.objects.filter(service=service)
    
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة الاستثناء بنجاح')
        return redirect('services:fixed_service_exceptions', service_id=service_id)
    
    context = {
        'service': service,
        'schedules': schedules,
    }
    
    return render(request, 'services/add_fixed_service_exception.html', context)

@login_required
def delete_fixed_service_exception(request, service_id, exception_id):
    service = get_object_or_404(Service, id=service_id, is_fixed=True)
    exception = get_object_or_404(FixedServiceException, id=exception_id, schedule__service=service)
    
    if request.method == 'POST':
        exception.delete()
        messages.success(request, 'تم حذف الاستثناء بنجاح')
        return redirect('services:fixed_service_exceptions', service_id=service_id)
    
    return render(request, 'services/delete_fixed_service_exception.html', {'service': service, 'exception': exception})

# إدارة العملاء المخصصين
@login_required
def custom_clients(request):
    clients = CustomClient.objects.all()
    return render(request, 'services/custom_clients.html', {'clients': clients})

@login_required
def add_custom_client(request):
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة العميل المخصص بنجاح')
        return redirect('services:custom_clients')
    return render(request, 'services/add_custom_client.html')

@login_required
def edit_custom_client(request, client_id):
    client = get_object_or_404(CustomClient, id=client_id)
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم تحديث العميل المخصص بنجاح')
        return redirect('services:custom_clients')
    return render(request, 'services/edit_custom_client.html', {'client': client})

@login_required
def delete_custom_client(request, client_id):
    client = get_object_or_404(CustomClient, id=client_id)
    if request.method == 'POST':
        client.delete()
        messages.success(request, 'تم حذف العميل المخصص بنجاح')
        return redirect('services:custom_clients')
    return render(request, 'services/delete_custom_client.html', {'client': client})

# إدارة العمال المخصصين
@login_required
def custom_workers(request):
    workers = CustomWorker.objects.all()
    return render(request, 'services/custom_workers.html', {'workers': workers})

@login_required
def add_custom_worker(request):
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم إضافة العامل المخصص بنجاح')
        return redirect('services:custom_workers')
    return render(request, 'services/add_custom_worker.html')

@login_required
def edit_custom_worker(request, worker_id):
    worker = get_object_or_404(CustomWorker, id=worker_id)
    if request.method == 'POST':
        # معالجة النموذج
        messages.success(request, 'تم تحديث العامل المخصص بنجاح')
        return redirect('services:custom_workers')
    return render(request, 'services/edit_custom_worker.html', {'worker': worker})

@login_required
def delete_custom_worker(request, worker_id):
    worker = get_object_or_404(CustomWorker, id=worker_id)
    if request.method == 'POST':
        worker.delete()
        messages.success(request, 'تم حذف العامل المخصص بنجاح')
        return redirect('services:custom_workers')
    return render(request, 'services/delete_custom_worker.html', {'worker': worker})

# تقويم الخدمات
@login_required
def services_calendar(request):
    today = timezone.now().date()
    return redirect('services:services_calendar_month', year=today.year, month=today.month)

@login_required
def services_calendar_month(request, year, month):
    # التحقق من صحة الشهر والسنة
    if month < 1 or month > 12:
        month = timezone.now().date().month
    
    # الحصول على التاريخ الأول والأخير للشهر
    first_day = datetime(year, month, 1).date()
    last_day = datetime(year, month, calendar.monthrange(year, month)[1]).date()
    
    # الحصول على الخدمات في هذا الشهر
    services = Service.objects.filter(
        Q(start_date__gte=first_day, start_date__lte=last_day) |
        Q(end_date__gte=first_day, end_date__lte=last_day)
    ).order_by('start_date', 'start_time')
    
    # إنشاء بيانات التقويم
    cal_data = []
    for day in range(1, calendar.monthrange(year, month)[1] + 1):
        date = datetime(year, month, day).date()
        day_services = [s for s in services if s.start_date <= date <= s.end_date]
        cal_data.append({
            'date': date,
            'services': day_services,
            'count': len(day_services),
        })
    
    # الحصول على الشهر السابق والتالي
    prev_month = datetime(year, month, 1) - timedelta(days=1)
    next_month = datetime(year, month, calendar.monthrange(year, month)[1]) + timedelta(days=1)
    
    context = {
        'cal_data': cal_data,
        'month_name': first_day.strftime('%B'),
        'year': year,
        'month': month,
        'prev_month': prev_month,
        'next_month': next_month,
    }
    
    return render(request, 'services/calendar_month.html', context)

@login_required
def services_calendar_day(request, year, month, day):
    # التحقق من صحة التاريخ
    try:
        date = datetime(year, month, day).date()
    except ValueError:
        date = timezone.now().date()
    
    # الحصول على الخدمات في هذا اليوم
    services = Service.objects.filter(
        Q(start_date__lte=date, end_date__gte=date)
    ).order_by('start_time')
    
    context = {
        'date': date,
        'services': services,
    }
    
    return render(request, 'services/calendar_day.html', context)
