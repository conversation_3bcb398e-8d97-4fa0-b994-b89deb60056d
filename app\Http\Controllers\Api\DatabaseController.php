<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Company;
use App\Services\DatabaseConnectionService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class DatabaseController extends Controller
{
    /**
     * خدمة الاتصال بقواعد البيانات
     *
     * @var DatabaseConnectionService
     */
    protected $databaseService;

    /**
     * إنشاء مثيل جديد من المتحكم
     *
     * @param DatabaseConnectionService $databaseService
     * @return void
     */
    public function __construct(DatabaseConnectionService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * اختبار الاتصال بقاعدة بيانات شركة
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection(Request $request)
    {
        // التحقق من صحة البيانات
        $validator = Validator::make($request->all(), [
            'serial_number' => 'required|string|min:16|max:19',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صالحة',
                'errors' => $validator->errors()
            ], 422);
        }

        // تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        $serialNumber = $request->input('serial_number');
        $cleanSerial = str_replace(['-', ' '], '', $serialNumber);

        // التحقق من طول الرقم التسلسلي
        if (strlen($cleanSerial) !== 16) {
            return response()->json([
                'success' => false,
                'message' => 'الرقم التسلسلي يجب أن يتكون من 16 حرف',
                'verified' => false
            ], 400);
        }

        // تنسيق الرقم التسلسلي (إضافة شرطات كل 4 أحرف)
        $formattedSerial = implode('-', str_split($cleanSerial, 4));

        // البحث عن الشركة بالرقم التسلسلي
        $company = Company::where('serial_number', $cleanSerial)
                        ->orWhere('serial_number', $formattedSerial)
                        ->first();

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة',
                'verified' => false
            ], 404);
        }

        // التحقق من حالة الشركة
        if ($company->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'الشركة غير نشطة',
                'verified' => false
            ], 403);
        }

        // اختبار الاتصال بقاعدة البيانات
        $connectionResult = $this->databaseService->testConnection($company);

        // تسجيل نتيجة الاختبار
        Log::info('نتيجة اختبار الاتصال بقاعدة البيانات', [
            'company_id' => $company->id,
            'serial_number' => $company->serial_number,
            'result' => $connectionResult
        ]);

        if ($connectionResult['success']) {
            return response()->json([
                'success' => true,
                'message' => 'تم التحقق من قاعدة البيانات بنجاح',
                'verified' => true,
                'company' => [
                    'id' => $company->id,
                    'name' => $company->name,
                    'serial_number' => $company->serial_number,
                    'database_name' => $company->database_name,
                    'status' => $company->status,
                ],
                'database' => [
                    'name' => $company->database_name,
                    'tables_count' => $connectionResult['tables_count'],
                    'type' => $connectionResult['db_type'],
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $connectionResult['message'],
                'verified' => false
            ], 500);
        }
    }

    /**
     * الحصول على معلومات قاعدة بيانات شركة
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDatabaseInfo(Request $request)
    {
        // التحقق من صحة البيانات
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|integer|exists:companies,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صالحة',
                'errors' => $validator->errors()
            ], 422);
        }

        // البحث عن الشركة
        $company = Company::findOrFail($request->input('company_id'));

        // اختبار الاتصال بقاعدة البيانات
        $connectionResult = $this->databaseService->testConnection($company);

        // الحصول على حجم قاعدة البيانات
        $databaseSize = $this->databaseService->getDatabaseSize($company);

        return response()->json([
            'success' => true,
            'company' => [
                'id' => $company->id,
                'name' => $company->name,
                'serial_number' => $company->serial_number,
                'status' => $company->status,
            ],
            'database' => [
                'name' => $company->database_name,
                'host' => $company->database_host,
                'user' => $company->database_user,
                'connected' => $connectionResult['success'],
                'tables_count' => $connectionResult['tables_count'],
                'size' => $databaseSize,
                'type' => $connectionResult['db_type'],
            ]
        ]);
    }

    /**
     * الحصول على قائمة الجداول في قاعدة بيانات شركة
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTables(Request $request)
    {
        // التحقق من صحة البيانات
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|integer|exists:companies,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صالحة',
                'errors' => $validator->errors()
            ], 422);
        }

        // البحث عن الشركة
        $company = Company::findOrFail($request->input('company_id'));

        try {
            // تكوين اتصال قاعدة البيانات
            $this->databaseService->configureConnection($company);

            // الحصول على قائمة الجداول
            $tables = $this->getTablesList($company);

            return response()->json([
                'success' => true,
                'tables' => $tables
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على قائمة الجداول: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على قائمة الجداول في قاعدة بيانات
     *
     * @param Company $company
     * @return array
     */
    private function getTablesList(Company $company)
    {
        $connection = \DB::connection('tenant');
        $driver = $connection->getDriverName();

        if ($driver === 'mysql') {
            $query = "SELECT table_name as name,
                            table_rows as rows,
                            data_length as size,
                            create_time as created_at,
                            update_time as updated_at
                     FROM information_schema.tables
                     WHERE table_schema = ?
                     ORDER BY table_name";
            $tables = $connection->select($query, [$company->database_name]);
        } elseif ($driver === 'sqlite') {
            $query = "SELECT name, 0 as rows, 0 as size, NULL as created_at, NULL as updated_at
                     FROM sqlite_master
                     WHERE type='table' AND name NOT LIKE 'sqlite_%'
                     ORDER BY name";
            $tables = $connection->select($query);
        } elseif ($driver === 'pgsql') {
            $query = "SELECT tablename as name,
                            0 as rows,
                            0 as size,
                            NULL as created_at,
                            NULL as updated_at
                     FROM pg_catalog.pg_tables
                     WHERE schemaname='public'
                     ORDER BY tablename";
            $tables = $connection->select($query);
        } else {
            $tables = [];
        }

        // تنسيق البيانات
        return collect($tables)->map(function($table) {
            return [
                'name' => $table->name,
                'rows' => $table->rows,
                'size' => $this->formatSize($table->size ?? 0),
                'created_at' => $table->created_at,
                'updated_at' => $table->updated_at,
            ];
        })->toArray();
    }

    /**
     * تنسيق حجم الملف
     *
     * @param int $bytes
     * @return string
     */
    private function formatSize($bytes)
    {
        if ($bytes <= 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes, 1024));
        return round($bytes / pow(1024, $i), 2) . ' ' . $units[$i];
    }

    /**
     * التحقق من قاعدة بيانات الشركة وإعدادها
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyDatabase(Request $request)
    {
        try {
            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'serial_number' => 'required|string|min:16|max:19',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صالحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $serialNumber = $request->input('serial_number');

            Log::info("طلب التحقق من قاعدة البيانات للرقم التسلسلي: {$serialNumber}");

            // تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
            $cleanSerial = str_replace(['-', ' '], '', $serialNumber);

            // التحقق من طول الرقم التسلسلي
            if (strlen($cleanSerial) !== 16) {
                Log::warning("الرقم التسلسلي غير صالح (الطول = " . strlen($cleanSerial) . "): {$serialNumber}");
                return response()->json([
                    'success' => false,
                    'message' => 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
                ], 400);
            }

            // تنسيق الرقم التسلسلي (إضافة الشرطات)
            $formattedSerial = implode('-', str_split($cleanSerial, 4));
            Log::debug("الرقم التسلسلي المنسق: {$formattedSerial}");

            // البحث عن الشركة بالرقم التسلسلي
            $company = Company::where('serial_number', $cleanSerial)
                            ->orWhere('serial_number', $formattedSerial)
                            ->first();

            if (!$company) {
                Log::warning("لم يتم العثور على شركة بالرقم التسلسلي: {$serialNumber}");
                return response()->json([
                    'success' => false,
                    'message' => 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة'
                ], 404);
            }

            Log::info("تم العثور على الشركة: {$company->name} (ID: {$company->id})");

            // التحقق من حالة الشركة
            if ($company->status !== 'active') {
                Log::warning("الشركة غير نشطة: {$company->name} (الحالة: {$company->status})");
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير نشطة'
                ], 403);
            }

            // اختبار الاتصال بقاعدة البيانات
            Log::info("اختبار الاتصال بقاعدة بيانات الشركة: {$company->name}");
            $connectionTest = $this->databaseService->testConnection($company);

            if (!$connectionTest['success']) {
                Log::warning("فشل الاتصال بقاعدة البيانات: {$connectionTest['message']}");

                // محاولة إعادة تهيئة قاعدة البيانات
                try {
                    Log::info("محاولة إعادة تهيئة قاعدة البيانات: {$company->database_name}");
                    $initResult = $this->databaseService->initializeDatabase($company);
                    Log::info("نتيجة إعادة تهيئة قاعدة البيانات: " . json_encode($initResult));

                    // إعادة اختبار الاتصال
                    $connectionTest = $this->databaseService->testConnection($company);

                    if (!$connectionTest['success']) {
                        Log::error("فشل الاتصال بقاعدة البيانات بعد إعادة التهيئة: {$connectionTest['message']}");
                        return response()->json([
                            'success' => false,
                            'message' => 'تعذر الاتصال بقاعدة بيانات الشركة. يرجى التواصل مع الدعم الفني.'
                        ], 500);
                    }
                } catch (\Exception $e) {
                    Log::error("استثناء أثناء تهيئة قاعدة البيانات: {$e->getMessage()}");
                    return response()->json([
                        'success' => false,
                        'message' => 'حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى التواصل مع الدعم الفني.'
                    ], 500);
                }
            }

            // إعداد اتصال قاعدة البيانات في Laravel
            Log::info("إعداد اتصال قاعدة البيانات في Laravel: {$company->database_name}");
            $setupSuccess = $this->databaseService->setupConnection($company);

            if (!$setupSuccess) {
                Log::error("فشل في إعداد اتصال قاعدة البيانات في Laravel: {$company->database_name}");
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إعداد اتصال قاعدة البيانات'
                ], 500);
            }

            Log::info("تم التحقق من قاعدة البيانات بنجاح: {$company->name}");
            return response()->json([
                'success' => true,
                'message' => 'تم التحقق من قاعدة البيانات بنجاح',
                'company' => [
                    'id' => $company->id,
                    'name' => $company->name,
                    'serial_number' => $company->serial_number,
                    'database_name' => $company->database_name,
                    'status' => $company->status
                ],
                'database' => [
                    'type' => $connectionTest['db_type'] ?? 'unknown',
                    'tables_count' => $connectionTest['tables_count'] ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("استثناء غير متوقع: {$e->getMessage()}");
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من قاعدة البيانات: ' . $e->getMessage()
            ], 500);
        }
    }
}
