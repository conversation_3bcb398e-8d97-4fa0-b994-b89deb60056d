from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from backend.workers.models import Worker
from backend.contracts.models import Contract, Client
from backend.services.models import ServiceType, Booking
from backend.dashboard.models import Activity, Alert
from backend.system_settings.models import UserProfile

# Create your views here.

@login_required
def dashboard(request):
    """View function for the main dashboard page."""
    # Redirect to company dashboard
    return redirect('company:dashboard')

@login_required
def modern_dashboard(request):
    """View function for the modern dashboard page with Tailwind CSS."""
    # Redirect to company dashboard
    return redirect('company:dashboard')

@login_required
def unified_dashboard(request):
    """View function for the unified dashboard page with consistent design."""
    # Redirect to company dashboard
    return redirect('company:dashboard')

@login_required
def dashboard_statistics_api(request):
    """
    API للحصول على إحصائيات لوحة التحكم في الوقت الفعلي
    تستخدم للتحديث المستمر للإحصائيات في واجهة المستخدم
    """
    # الحصول على الإحصائيات
    today = timezone.now().date()

    # إحصائيات العمال
    total_workers = Worker.objects.count()
    active_workers = Worker.objects.filter(status='active').count()

    # إحصائيات العملاء
    total_clients = Client.objects.count()
    active_clients = Client.objects.filter(is_active=True).count()

    # إحصائيات العقود
    active_contracts = Contract.objects.filter(status='active').count()
    expired_contracts = Contract.objects.filter(status='terminated').count()
    custom_contracts = Contract.objects.filter(contract_type='custom').count()

    # العقود التي ستنتهي قريباً (خلال 7 أيام)
    seven_days_later = today + timedelta(days=7)
    expiring_contracts = Contract.objects.filter(
        status='active',
        end_date__range=[today, seven_days_later]
    ).count()

    # الإقامات التي ستنتهي قريباً (خلال 30 يوم)
    thirty_days_later = today + timedelta(days=30)
    expiring_residencies = Worker.objects.filter(
        status='active',
        visa_expiry__range=[today, thirty_days_later]
    ).count()

    # إحصائيات الخدمات
    daily_services = Booking.objects.filter(service_type__service_type='regular').count()
    monthly_services = Booking.objects.filter(service_type__service_type='full_day').count()

    # عدد المستخدمين
    users_count = UserProfile.objects.count()

    # تجميع الإحصائيات
    stats = {
        'workers_count': total_workers,
        'active_workers': active_workers,
        'clients_count': total_clients,
        'active_clients': active_clients,
        'active_contracts': active_contracts,
        'expired_contracts': expired_contracts,
        'custom_contracts': custom_contracts,
        'daily_services': daily_services,
        'monthly_services': monthly_services,
        'users_count': users_count,
        'expiring_contracts': expiring_contracts,
        'expiring_residencies': expiring_residencies,
    }

    # إرجاع الإحصائيات كـ JSON
    return JsonResponse(stats)

@login_required
def alerts_api(request):
    """
    API للحصول على التنبيهات في الوقت الفعلي
    تستخدم للتحديث المستمر للتنبيهات في واجهة المستخدم
    """
    today = timezone.now().date()

    # العقود التي ستنتهي قريباً (خلال 7 أيام)
    seven_days_later = today + timedelta(days=7)
    expiring_contracts = Contract.objects.filter(
        status='active',
        end_date__range=[today, seven_days_later]
    ).select_related('client', 'worker')

    # الإقامات التي ستنتهي قريباً (خلال 30 يوم)
    thirty_days_later = today + timedelta(days=30)
    expiring_residencies = Worker.objects.filter(
        status='active',
        visa_expiry__range=[today, thirty_days_later]
    )

    # تحضير بيانات التنبيهات
    contract_alerts = []
    for contract in expiring_contracts:
        days_remaining = (contract.end_date - today).days
        contract_alerts.append({
            'id': contract.id,
            'title': f'عقد سينتهي خلال {days_remaining} يوم',
            'description': f'عقد {contract.client.name} - {contract.worker.full_name if contract.worker else "بدون عامل"} سينتهي بتاريخ {contract.end_date}',
            'days_remaining': days_remaining,
            'type': 'contract',
            'url': f'/contracts/{contract.id}/'
        })

    residency_alerts = []
    for worker in expiring_residencies:
        days_remaining = (worker.visa_expiry - today).days
        residency_alerts.append({
            'id': worker.id,
            'title': f'إقامة ستنتهي خلال {days_remaining} يوم',
            'description': f'إقامة العامل {worker.first_name} {worker.last_name} ستنتهي بتاريخ {worker.visa_expiry}',
            'days_remaining': days_remaining,
            'type': 'residency',
            'url': f'/workers/{worker.id}/'
        })

    # تجميع التنبيهات
    alerts = {
        'contract_alerts': contract_alerts,
        'residency_alerts': residency_alerts,
        'expiring_contracts_count': len(contract_alerts),
        'expiring_residencies_count': len(residency_alerts),
    }

    # إرجاع التنبيهات كـ JSON
    return JsonResponse(alerts)

@login_required
def activities_api(request):
    """
    API للحصول على الأنشطة الأخيرة في الوقت الفعلي
    تستخدم للتحديث المستمر للأنشطة في واجهة المستخدم
    """
    # الحصول على آخر 10 أنشطة
    recent_activities = Activity.objects.all().order_by('-date_created')[:10]

    # تحويل الأنشطة إلى قائمة من القواميس
    activities_list = []
    for activity in recent_activities:
        activities_list.append({
            'id': activity.id,
            'description': activity.description,
            'date': activity.date_created.strftime('%Y-%m-%d %H:%M'),
            'user': activity.related_worker.first_name + ' ' + activity.related_worker.last_name if activity.related_worker else 'النظام',
            'type': activity.activity_type
        })

    # إرجاع الأنشطة كـ JSON
    return JsonResponse({'activities': activities_list})

@login_required
def final_dashboard(request):
    """View function for the final dashboard page using company dashboard template."""
    # Redirect to company dashboard
    return redirect('company:dashboard')
