#!/usr/bin/env python3
"""
Script لإرسال البريد الإلكتروني الفعلي
"""
import smtplib
import sys
import random
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime


def send_verification_email(username="MUNTADER_WISSAM", verification_code=None):
    """إرسال رمز التحقق عبر البريد الإلكتروني"""

    # إعدادات البريد الإلكتروني
    sender_email = "<EMAIL>"
    sender_password = "svtp dksv utqp nphc"  # كلمة مرور التطبيق
    recipient_email = "<EMAIL>"

    # توليد رمز التحقق إذا لم يتم توفيره
    if not verification_code:
        verification_code = str(random.randint(100000, 999999))

    # تم تعطيل رسائل السجل لأسباب أمنية

    # إنشاء الرسالة
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = recipient_email
    message["Subject"] = "🔐 رمز التحقق الآمن - نظام استقدامي السحابي"

    # تحويل اللوجو إلى base64
    import base64
    import os

    logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'images', 'logo.webp')
    logo_base64 = ""

    try:
        with open(logo_path, 'rb') as logo_file:
            logo_base64 = base64.b64encode(logo_file.read()).decode('utf-8')
    except FileNotFoundError:
        logo_base64 = ""

    # محتوى الرسالة بتنسيق HTML جميل فقط
    html_body = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رمز التحقق - نظام استقدامي</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }}
        .container {{
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }}
        .content {{
            padding: 40px 30px;
            text-align: center;
        }}
        .greeting {{
            font-size: 20px;
            color: #333;
            margin-bottom: 30px;
        }}
        .code-container {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }}
        .code {{
            font-size: 36px;
            font-weight: bold;
            color: white;
            letter-spacing: 8px;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }}
        .code-label {{
            color: white;
            font-size: 14px;
            margin-bottom: 10px;
            opacity: 0.9;
        }}
        .warning {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            color: #856404;
        }}
        .warning-icon {{
            font-size: 20px;
            margin-bottom: 10px;
        }}
        .info {{
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            color: #1565c0;
        }}
        .footer {{
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }}
        .footer p {{
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }}
        .logo {{
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            display: block;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="data:image/webp;base64,{logo_base64}" alt="نظام استقدامي" class="logo">
            <h1>نظام استقدامي السحابي</h1>
            <p>نظام إدارة العمالة المتقدم</p>
        </div>

        <div class="content">
            <div class="greeting">
                مرحباً <strong>{username}</strong> 👋
            </div>

            <p style="color: #666; font-size: 16px; line-height: 1.6;">
                تم طلب رمز التحقق للدخول إلى حسابك في نظام استقدامي السحابي.
                يرجى استخدام الرمز التالي لإكمال عملية تسجيل الدخول:
            </p>

            <div class="code-container">
                <div class="code-label">رمز التحقق الخاص بك</div>
                <div class="code">{verification_code}</div>
            </div>

            <div class="warning">
                <div class="warning-icon">⚠️</div>
                <strong>تنبيه أمني:</strong><br>
                هذا الرمز صالح لمدة <strong>دقيقة واحدة فقط</strong> من وقت الإرسال.<br>
                لا تشارك هذا الرمز مع أي شخص آخر لحماية حسابك.
            </div>

            <div class="info">
                <strong>💡 معلومة:</strong><br>
                إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة وتأكد من أمان حسابك.
            </div>
        </div>

        <div class="footer">
            <p><strong>تم إرسال هذه الرسالة في:</strong></p>
            <p>{datetime.now().strftime('%Y-%m-%d الساعة %H:%M:%S')}</p>
            <p style="margin-top: 20px;">
                <strong>فريق نظام استقدامي السحابي</strong><br>
                نظام إدارة العمالة المتقدم
            </p>
            <p style="font-size: 12px; color: #999; margin-top: 20px;">
                هذه رسالة تلقائية، يرجى عدم الرد عليها
            </p>
        </div>
    </div>
</body>
</html>
    """

    # إضافة فقط النسخة HTML الجميلة
    message.attach(MIMEText(html_body, "html", "utf-8"))

    try:
        # إرسال البريد الإلكتروني بصمت (بدون رسائل سجل لأسباب أمنية)
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()
        server.login(sender_email, sender_password)
        text = message.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()

        return True, verification_code

    except Exception as e:
        # فشل في الإرسال (بدون تسجيل تفاصيل لأسباب أمنية)
        return False, None


def main():
    """الدالة الرئيسية - تشغيل صامت لأسباب أمنية"""
    username = sys.argv[1] if len(sys.argv) > 1 else "MUNTADER_WISSAM"
    verification_code = sys.argv[2] if len(sys.argv) > 2 else None

    success, code = send_verification_email(username, verification_code)

    if success:
        # إرسال ناجح (بدون تفاصيل)
        sys.exit(0)
    else:
        # فشل في الإرسال
        sys.exit(1)


if __name__ == "__main__":
    main()
