{% extends 'layouts/super_admin.html' %}

{% block title %}وضع الصيانة{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-orange-600 to-red-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-tools ml-2"></i>
                    وضع الصيانة
                </h1>
                <p class="text-orange-100">
                    إدارة وتفعيل وضع الصيانة للنظام
                </p>
            </div>
            <div class="text-center">
                {% if maintenance.is_active %}
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                        <i class="fas fa-exclamation-triangle text-2xl text-white"></i>
                    </div>
                    <div class="text-sm mt-2">نشط</div>
                {% else %}
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-2xl text-white"></i>
                    </div>
                    <div class="text-sm mt-2">معطل</div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Current Status -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-info-circle ml-2"></i>
                الحالة الحالية
            </h2>

            <!-- Toggle Button -->
            <form method="post" class="inline">
                {% csrf_token %}
                <input type="hidden" name="action" value="toggle">
                <button type="submit"
                        onclick="return confirm('هل أنت متأكد من {% if maintenance.is_active %}تعطيل{% else %}تفعيل{% endif %} وضع الصيانة؟')"
                        class="px-4 py-2 {% if maintenance.is_active %}bg-green-600 hover:bg-green-700{% else %}bg-red-600 hover:bg-red-700{% endif %} text-white rounded-lg transition-colors">
                    <i class="fas fa-{% if maintenance.is_active %}play{% else %}pause{% endif %} ml-2"></i>
                    {% if maintenance.is_active %}تعطيل الصيانة{% else %}تفعيل الصيانة{% endif %}
                </button>
            </form>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Status -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-toggle-{% if maintenance.is_active %}on{% else %}off{% endif %} ml-2"></i>
                    حالة الصيانة
                </h3>
                {% if maintenance.is_active %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <i class="fas fa-exclamation-triangle ml-1"></i>
                        نشط
                    </span>
                    <p class="text-sm text-gray-500 mt-2">النظام في وضع الصيانة</p>
                {% else %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check-circle ml-1"></i>
                        معطل
                    </span>
                    <p class="text-sm text-gray-500 mt-2">النظام يعمل بشكل طبيعي</p>
                {% endif %}
            </div>

            <!-- Activated By -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-user ml-2"></i>
                    آخر تحديث
                </h3>
                {% if maintenance.activated_by %}
                    <p class="text-sm font-medium text-gray-900">{{ maintenance.activated_by.username }}</p>
                    <p class="text-sm text-gray-500">{{ maintenance.updated_at|date:"Y/m/d H:i" }}</p>
                {% else %}
                    <p class="text-sm text-gray-500">لم يتم التحديث بعد</p>
                {% endif %}
            </div>

            <!-- Estimated Completion -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-clock ml-2"></i>
                    الوقت المتوقع للانتهاء
                </h3>
                {% if maintenance.estimated_completion %}
                    <p class="text-sm font-medium text-gray-900">{{ maintenance.estimated_completion|date:"Y/m/d H:i" }}</p>
                    <p class="text-sm text-gray-500">متوقع</p>
                {% else %}
                    <p class="text-sm text-gray-500">غير محدد</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Maintenance Settings -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-cog ml-2"></i>
            إعدادات وضع الصيانة
        </h2>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            <input type="hidden" name="action" value="update_settings">

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    عنوان صفحة الصيانة
                </label>
                <input type="text"
                       id="title"
                       name="title"
                       value="{{ maintenance.title }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 bg-white">
            </div>

            <!-- Message -->
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                    رسالة الصيانة
                </label>
                <textarea id="message"
                          name="message"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 bg-white">{{ maintenance.message }}</textarea>
            </div>

            <!-- Estimated Completion -->
            <div>
                <label for="estimated_completion" class="block text-sm font-medium text-gray-700 mb-2">
                    الوقت المتوقع للانتهاء
                </label>
                <input type="datetime-local"
                       id="estimated_completion"
                       name="estimated_completion"
                       value="{% if maintenance.estimated_completion %}{{ maintenance.estimated_completion|date:'Y-m-d\TH:i' }}{% endif %}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 bg-white">
            </div>

            <!-- Allowed IPs -->
            <div>
                <label for="allowed_ips" class="block text-sm font-medium text-gray-700 mb-2">
                    عناوين IP المسموح لها بالوصول أثناء الصيانة
                </label>
                <textarea id="allowed_ips"
                          name="allowed_ips"
                          rows="3"
                          placeholder="127.0.0.1, *************, ***********/24"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 bg-white placeholder-gray-500">{{ maintenance.allowed_ips|default:'' }}</textarea>
                <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-info-circle ml-1"></i>
                    عناوين IP مفصولة بفواصل. عنوان IP الحالي: <strong>{{ current_ip }}</strong>
                </p>
            </div>

            <!-- Bypass for Superusers -->
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                    <h3 class="font-medium text-gray-900">السماح للمسؤولين الأعلى بالوصول</h3>
                    <p class="text-sm text-gray-500">السماح للمسؤولين الأعلى بتجاوز وضع الصيانة</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox"
                           name="bypass_for_superusers"
                           {% if maintenance.bypass_for_superusers %}checked{% endif %}
                           class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </label>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit"
                        class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </form>
    </div>

    <!-- Preview -->
    {% if maintenance.is_active %}
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-eye ml-2"></i>
            معاينة صفحة الصيانة
        </h2>

        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <div class="max-w-md mx-auto">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-tools text-2xl text-orange-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ maintenance.title }}</h3>
                <p class="text-gray-600 mb-4">{{ maintenance.message }}</p>
                {% if maintenance.estimated_completion %}
                <p class="text-sm text-gray-500">
                    الوقت المتوقع للانتهاء: {{ maintenance.estimated_completion|date:"Y/m/d H:i" }}
                </p>
                {% endif %}
            </div>
        </div>

        <div class="mt-4 text-center">
            <a href="/maintenance-preview/"
               target="_blank"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-external-link-alt ml-2"></i>
                عرض الصفحة الكاملة
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Warning -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">
                    تحذير مهم
                </h3>
                <div class="text-yellow-700 space-y-2 text-sm">
                    <p>• تفعيل وضع الصيانة سيمنع جميع المستخدمين من الوصول للنظام</p>
                    <p>• المسؤولون الأعلى يمكنهم الوصول إذا كان الخيار مفعل</p>
                    <p>• تأكد من إضافة عنوان IP الخاص بك للقائمة المسموحة</p>
                    <p>• يمكن تعطيل وضع الصيانة في أي وقت من هذه الصفحة</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
