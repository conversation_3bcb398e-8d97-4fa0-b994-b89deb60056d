from django.contrib import admin
from .models import Document, DocumentAttachment

class DocumentAttachmentInline(admin.TabularInline):
    model = DocumentAttachment
    extra = 1

@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('document_number', 'document_type', 'entity', 'subject', 'document_date', 'status')
    list_filter = ('document_type', 'status', 'document_date')
    search_fields = ('document_number', 'entity', 'subject', 'details')
    date_hierarchy = 'document_date'
    inlines = [DocumentAttachmentInline]

@admin.register(DocumentAttachment)
class DocumentAttachmentAdmin(admin.ModelAdmin):
    list_display = ('document', 'description', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('document__document_number', 'description')
