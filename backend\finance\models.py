from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User


class FinancialAccount(models.Model):
    """حسابات مالية"""
    ACCOUNT_TYPE_CHOICES = [
        ('bank', 'حساب بنكي'),
        ('cash', 'نقدي'),
        ('credit', 'ائتماني'),
        ('investment', 'استثماري'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='اسم الحساب')
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPE_CHOICES, verbose_name='نوع الحساب')
    account_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الحساب')
    bank_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='اسم البنك')
    balance = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='الرصيد')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'حساب مالي'
        verbose_name_plural = 'الحسابات المالية'


class Transaction(models.Model):
    """المعاملات المالية"""
    TRANSACTION_TYPE_CHOICES = [
        ('income', 'دخل'),
        ('expense', 'مصروف'),
        ('transfer', 'تحويل'),
    ]
    
    CATEGORY_CHOICES = [
        ('salary', 'راتب'),
        ('service_fee', 'رسوم خدمة'),
        ('commission', 'عمولة'),
        ('office_expense', 'مصروفات مكتبية'),
        ('travel', 'سفر'),
        ('maintenance', 'صيانة'),
        ('other', 'أخرى'),
    ]
    
    transaction_number = models.CharField(max_length=20, unique=True, verbose_name='رقم المعاملة')
    account = models.ForeignKey(FinancialAccount, on_delete=models.PROTECT, related_name='transactions', verbose_name='الحساب')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, verbose_name='نوع المعاملة')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name='الفئة')
    amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='المبلغ')
    description = models.TextField(verbose_name='الوصف')
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم المرجع')
    transaction_date = models.DateField(default=timezone.now, verbose_name='تاريخ المعاملة')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='تم الإنشاء بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if not self.transaction_number:
            import uuid
            self.transaction_number = f"TXN-{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.transaction_number} - {self.get_transaction_type_display()}"
    
    class Meta:
        verbose_name = 'معاملة مالية'
        verbose_name_plural = 'المعاملات المالية'
        ordering = ['-transaction_date', '-created_at']


class Budget(models.Model):
    """الميزانيات"""
    name = models.CharField(max_length=200, verbose_name='اسم الميزانية')
    category = models.CharField(max_length=20, choices=Transaction.CATEGORY_CHOICES, verbose_name='الفئة')
    planned_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='المبلغ المخطط')
    actual_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='المبلغ الفعلي')
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ النهاية')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = 'ميزانية'
        verbose_name_plural = 'الميزانيات'


class Invoice(models.Model):
    """الفواتير"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]
    
    invoice_number = models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة')
    client_name = models.CharField(max_length=200, verbose_name='اسم العميل')
    client_email = models.EmailField(blank=True, null=True, verbose_name='بريد العميل')
    client_phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='هاتف العميل')
    amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='المبلغ')
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='مبلغ الضريبة')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='المبلغ الإجمالي')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='الحالة')
    issue_date = models.DateField(default=timezone.now, verbose_name='تاريخ الإصدار')
    due_date = models.DateField(verbose_name='تاريخ الاستحقاق')
    description = models.TextField(verbose_name='الوصف')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='تم الإنشاء بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if not self.invoice_number:
            import uuid
            self.invoice_number = f"INV-{uuid.uuid4().hex[:8].upper()}"
        self.total_amount = self.amount + self.tax_amount
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.invoice_number} - {self.client_name}"
    
    class Meta:
        verbose_name = 'فاتورة'
        verbose_name_plural = 'الفواتير'
        ordering = ['-issue_date']
