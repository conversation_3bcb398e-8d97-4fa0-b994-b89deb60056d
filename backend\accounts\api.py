"""
واجهة برمجة التطبيقات (API) للتحقق من صحة قواعد البيانات
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
import json
import logging
from backend.super_admin.models import Company, SystemLog
from backend.super_admin.database_connector import test_company_db_connection

logger = logging.getLogger(__name__)

def api_response(success, data=None, message=None, status=200):
    """
    إنشاء استجابة API موحدة
    
    Args:
        success: نجاح العملية
        data: البيانات (اختياري)
        message: رسالة (اختياري)
        status: رمز الحالة HTTP (اختياري)
    
    Returns:
        JsonResponse: استجابة JSON
    """
    response = {
        'success': success,
    }
    
    if data is not None:
        response['data'] = data
    
    if message is not None:
        response['message'] = message
    
    return JsonResponse(response, status=status)

@csrf_exempt
@require_http_methods(["POST"])
def verify_database(request):
    """
    التحقق من صحة قاعدة البيانات
    
    Args:
        request: طلب HTTP
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # الحصول على بيانات الطلب
        try:
            data = json.loads(request.body)
            serial_number = data.get('serial_number')
        except json.JSONDecodeError:
            return api_response(False, message="بيانات JSON غير صالحة", status=400)
        
        if not serial_number:
            return api_response(False, message="الرقم التسلسلي مطلوب", status=400)
        
        # البحث عن الشركة
        try:
            company = Company.objects.get(serial_number=serial_number)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # التحقق من حالة الشركة
        if company.status != 'active':
            return api_response(False, message="الشركة غير نشطة", status=403)
        
        # اختبار الاتصال بقاعدة البيانات
        connection_test = test_company_db_connection(company)
        
        if connection_test['success']:
            # تسجيل العملية
            SystemLog.objects.create(
                action='verify_database',
                company=company,
                details=f"تم التحقق من صحة قاعدة البيانات للشركة: {company.name}",
                ip_address=request.META.get('REMOTE_ADDR')
            )
            
            return api_response(True, data={
                'company_name': company.name,
                'database_name': company.database_name,
                'database_type': connection_test['db_type'],
                'tables_count': connection_test['tables_count'],
                'tables': connection_test['tables']
            }, message="تم التحقق من صحة قاعدة البيانات بنجاح")
        else:
            return api_response(False, message=connection_test['message'], status=400)
    
    except Exception as e:
        logger.error(f"خطأ في التحقق من صحة قاعدة البيانات: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@csrf_exempt
@require_http_methods(["POST"])
def check_serial_number(request):
    """
    التحقق من صحة الرقم التسلسلي
    
    Args:
        request: طلب HTTP
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # الحصول على بيانات الطلب
        try:
            data = json.loads(request.body)
            serial_number = data.get('serial_number')
        except json.JSONDecodeError:
            return api_response(False, message="بيانات JSON غير صالحة", status=400)
        
        if not serial_number:
            return api_response(False, message="الرقم التسلسلي مطلوب", status=400)
        
        # البحث عن الشركة
        try:
            company = Company.objects.get(serial_number=serial_number)
        except Company.DoesNotExist:
            return api_response(False, message="الرقم التسلسلي غير صحيح", status=404)
        
        # التحقق من حالة الشركة
        if company.status != 'active':
            return api_response(False, message="الشركة غير نشطة", status=403)
        
        return api_response(True, data={
            'company_name': company.name,
            'company_id': company.id,
            'status': company.status
        }, message="الرقم التسلسلي صحيح")
    
    except Exception as e:
        logger.error(f"خطأ في التحقق من صحة الرقم التسلسلي: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)
