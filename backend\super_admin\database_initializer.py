"""
وحدة تهيئة قواعد البيانات
تتضمن وظائف لتهيئة قواعد البيانات الجديدة وتحديثها تلقائيًا
"""

import os
import sqlite3
import logging
import importlib
import inspect
from django.conf import settings
from django.db import connections
from django.apps import apps
from django.core.management import call_command
from django.db.migrations.recorder import MigrationRecorder
from .database_utils import get_database_path, extract_database_schema, clone_database_structure
from backend.accounts.database_config import ensure_database_config_complete

# إعداد السجل
logger = logging.getLogger(__name__)

def initialize_company_database(company):
    """
    تهيئة قاعدة بيانات الشركة الجديدة بالجداول والهياكل اللازمة

    Args:
        company: كائن الشركة

    Returns:
        dict: نتيجة العملية
    """
    db_name = f"company_{company.id}"

    try:
        logger.info(f"بدء تهيئة قاعدة بيانات الشركة: {company.name} ({company.database_name})")

        # مسار ملف قاعدة البيانات
        db_path = get_database_path(company.database_name)

        # إنشاء مجلد قواعد البيانات إذا لم يكن موجودًا
        try:
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            logger.info(f"تم التأكد من وجود مجلد قواعد البيانات: {os.path.dirname(db_path)}")
        except Exception as dir_error:
            logger.error(f"خطأ في إنشاء مجلد قواعد البيانات: {str(dir_error)}")
            return {
                'success': False,
                'message': f'خطأ في إنشاء مجلد قواعد البيانات: {str(dir_error)}'
            }

        # التحقق مما إذا كان الملف موجودًا بالفعل
        if os.path.exists(db_path) and os.path.getsize(db_path) > 0:
            logger.info(f"قاعدة البيانات موجودة بالفعل: {db_path}")

            # التحقق من هيكل قاعدة البيانات وتحديثه إذا لزم الأمر
            return update_database_schema(company)

        # إنشاء قاعدة بيانات جديدة
        try:
            logger.info(f"إنشاء قاعدة بيانات جديدة: {db_path}")
            conn = sqlite3.connect(db_path)
            conn.close()
            logger.info(f"تم إنشاء ملف قاعدة البيانات بنجاح: {db_path}")
        except Exception as db_error:
            logger.error(f"خطأ في إنشاء ملف قاعدة البيانات: {str(db_error)}")
            return {
                'success': False,
                'message': f'خطأ في إنشاء ملف قاعدة البيانات: {str(db_error)}'
            }

        # إعداد قاعدة البيانات
        db_config = {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': str(db_path),  # تحويل المسار إلى نص
            'USER': company.database_user,
            'PASSWORD': company.database_password,
            'HOST': company.database_host,
        }

        # التأكد من اكتمال إعدادات قاعدة البيانات
        db_config = ensure_database_config_complete(db_config)

        # إضافة قاعدة البيانات إلى إعدادات Django
        settings.DATABASES[db_name] = db_config

        migration_success = True
        data_init_success = True

        # تطبيق الهجرات على قاعدة البيانات الجديدة
        try:
            logger.info(f"تطبيق الهجرات على قاعدة البيانات: {company.database_name}")
            apply_migrations_to_database(db_name)
        except Exception as migrate_error:
            logger.error(f"خطأ في تطبيق الهجرات: {str(migrate_error)}")
            # في وضع التطوير، نتجاوز أخطاء الهجرات
            if settings.DEBUG:
                logger.warning(f"تجاوز خطأ الهجرات في وضع التطوير: {str(migrate_error)}")
                migration_success = True
            else:
                migration_success = False

        # إضافة البيانات الأولية
        try:
            logger.info(f"إضافة البيانات الأولية إلى قاعدة البيانات: {company.database_name}")
            initialize_database_data(db_name, company)
        except Exception as data_error:
            logger.error(f"خطأ في إضافة البيانات الأولية: {str(data_error)}")
            # في وضع التطوير، نتجاوز أخطاء إضافة البيانات الأولية
            if settings.DEBUG:
                logger.warning(f"تجاوز خطأ إضافة البيانات الأولية في وضع التطوير: {str(data_error)}")
                data_init_success = True
            else:
                data_init_success = False

        # إزالة قاعدة البيانات من الإعدادات بعد الانتهاء
        if db_name in settings.DATABASES:
            settings.DATABASES.pop(db_name, None)

        # تحديد نتيجة العملية
        if migration_success and data_init_success:
            logger.info(f"تم تهيئة قاعدة بيانات الشركة بنجاح: {company.name} ({company.database_name})")
            return {
                'success': True,
                'message': f'تم تهيئة قاعدة بيانات الشركة بنجاح: {company.name}'
            }
        elif migration_success:
            logger.warning(f"تم تهيئة هيكل قاعدة البيانات بنجاح، لكن فشلت إضافة البيانات الأولية: {company.name}")
            return {
                'success': True,  # نعتبر العملية ناجحة حتى لو فشلت إضافة البيانات الأولية
                'message': f'تم تهيئة هيكل قاعدة البيانات بنجاح، لكن فشلت إضافة البيانات الأولية: {company.name}'
            }
        else:
            logger.warning(f"فشلت تهيئة هيكل قاعدة البيانات، لكن تم إنشاء الملف: {company.name}")
            return {
                'success': True,  # نعتبر العملية ناجحة حتى لو فشلت تهيئة الهيكل
                'message': f'فشلت تهيئة هيكل قاعدة البيانات، لكن تم إنشاء الملف: {company.name}'
            }

    except Exception as e:
        logger.error(f"خطأ في تهيئة قاعدة بيانات الشركة: {str(e)}")

        # تنظيف الموارد
        if db_name in settings.DATABASES:
            settings.DATABASES.pop(db_name, None)

        return {
            'success': False,
            'message': f'حدث خطأ أثناء تهيئة قاعدة بيانات الشركة: {str(e)}'
        }

def apply_migrations_to_database(db_name):
    """
    تطبيق الهجرات على قاعدة بيانات محددة

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
    """
    # الحصول على قائمة التطبيقات التي تستخدم قاعدة بيانات الشركة
    company_apps = getattr(settings, 'COMPANY_APPS', [])

    # تعطيل قيود المفاتيح الأجنبية في قاعدة البيانات
    try:
        # الحصول على اتصال قاعدة البيانات
        from django.db import connections
        connection = connections[db_name]

        # تعطيل قيود المفاتيح الأجنبية
        with connection.cursor() as cursor:
            cursor.execute('PRAGMA foreign_keys = OFF;')
            logger.info(f"تم تعطيل قيود المفاتيح الأجنبية في قاعدة البيانات {db_name}")
    except Exception as e:
        logger.warning(f"تحذير: فشل تعطيل قيود المفاتيح الأجنبية: {str(e)}")

    try:
        # تطبيق الهجرات الأساسية أولاً
        logger.info(f"تطبيق الهجرات الأساسية على قاعدة البيانات {db_name}")

        # تطبيق هجرات auth و contenttypes أولاً
        for app_name in ['auth', 'contenttypes']:
            try:
                logger.info(f"تطبيق هجرات التطبيق {app_name} على قاعدة البيانات {db_name}")
                call_command('migrate', app_name, database=db_name, interactive=False)
            except Exception as e:
                logger.warning(f"تحذير: فشل تطبيق هجرات {app_name}: {str(e)}")

        # تطبيق هجرة الجداول الأساسية
        try:
            logger.info(f"تطبيق هجرات الجداول الأساسية على قاعدة البيانات {db_name}")
            call_command('migrate', '--run-syncdb', database=db_name, interactive=False)
        except Exception as e:
            logger.warning(f"تحذير: فشل تطبيق هجرات الجداول الأساسية: {str(e)}")

        # تطبيق الهجرات لكل تطبيق
        for app_name in company_apps:
            try:
                logger.info(f"تطبيق هجرات التطبيق {app_name} على قاعدة البيانات {db_name}")

                # تطبيق الهجرات
                call_command('migrate', app_name, database=db_name, interactive=False)
                logger.info(f"تم تطبيق هجرات التطبيق {app_name} بنجاح")
            except Exception as e:
                logger.error(f"خطأ في تطبيق هجرات التطبيق {app_name}: {str(e)}")
                logger.error(f"تفاصيل الخطأ: {type(e).__name__}")
                # نستمر في تطبيق الهجرات للتطبيقات الأخرى
                continue
    except Exception as e:
        logger.error(f"خطأ عام في تطبيق الهجرات: {str(e)}")
        raise
    finally:
        # إعادة تفعيل قيود المفاتيح الأجنبية
        try:
            # الحصول على اتصال قاعدة البيانات
            from django.db import connections
            connection = connections[db_name]

            # إعادة تفعيل قيود المفاتيح الأجنبية
            with connection.cursor() as cursor:
                cursor.execute('PRAGMA foreign_keys = ON;')
                logger.info(f"تم إعادة تفعيل قيود المفاتيح الأجنبية في قاعدة البيانات {db_name}")
        except Exception as e:
            logger.warning(f"تحذير: فشل إعادة تفعيل قيود المفاتيح الأجنبية: {str(e)}")

def initialize_database_data(db_name, company):
    """
    إضافة البيانات الأولية إلى قاعدة البيانات

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
        company: كائن الشركة

    Returns:
        bool: نجاح العملية
    """
    # الحصول على قائمة التطبيقات التي تستخدم قاعدة بيانات الشركة
    company_apps = getattr(settings, 'COMPANY_APPS', [])

    success_count = 0
    total_count = 0

    # التأكد من تهيئة المستخدمين أولاً
    try:
        # استيراد وحدة تهيئة المستخدمين
        from backend.accounts.initializers import initialize_data as initialize_users
        logger.info(f"تنفيذ وظيفة تهيئة المستخدمين")
        result = initialize_users(db_name, company)

        if result:
            logger.info(f"تم تهيئة بيانات المستخدمين بنجاح")
            success_count += 1
        else:
            logger.warning(f"فشلت تهيئة بيانات المستخدمين")
    except Exception as e:
        logger.error(f"خطأ في تهيئة بيانات المستخدمين: {str(e)}")
        # محاولة إنشاء المستخدم الرئيسي يدويًا
        try:
            logger.info(f"محاولة إنشاء المستخدم الرئيسي يدويًا")
            create_admin_user_manually(db_name, company)
        except Exception as manual_error:
            logger.error(f"فشل في إنشاء المستخدم الرئيسي يدويًا: {str(manual_error)}")

    # البحث عن وظائف التهيئة في كل تطبيق
    for app_name in company_apps:
        total_count += 1
        try:
            # محاولة استيراد وحدة initializers.py من التطبيق
            module_path = f"{app_name}.initializers"
            try:
                initializers_module = importlib.import_module(module_path)

                # البحث عن وظيفة initialize_data
                if hasattr(initializers_module, 'initialize_data'):
                    logger.info(f"تنفيذ وظيفة تهيئة البيانات للتطبيق {app_name}")
                    result = initializers_module.initialize_data(db_name, company)

                    if result:
                        logger.info(f"تم تهيئة بيانات التطبيق {app_name} بنجاح")
                        success_count += 1
                    else:
                        logger.warning(f"فشلت تهيئة بيانات التطبيق {app_name}")
                else:
                    logger.debug(f"لا توجد وظيفة initialize_data في وحدة {module_path}")
            except ImportError:
                # تجاهل الخطأ إذا لم تكن الوحدة موجودة
                logger.debug(f"لا توجد وحدة initializers.py في التطبيق {app_name}")
                # لا نعتبر هذا فشلاً
                success_count += 1
        except Exception as e:
            logger.error(f"خطأ في تهيئة بيانات التطبيق {app_name}: {str(e)}")

    # تسجيل ملخص العملية
    if total_count > 0:
        success_rate = (success_count / total_count) * 100
        logger.info(f"تم تهيئة بيانات {success_count} من أصل {total_count} تطبيق ({success_rate:.1f}%)")
    else:
        logger.warning("لم يتم العثور على أي تطبيقات لتهيئة البيانات")

    # نعتبر العملية ناجحة إذا نجحت تهيئة بيانات أي تطبيق واحد على الأقل
    return success_count > 0

def create_admin_user_manually(db_name, company):
    """
    إنشاء المستخدم الرئيسي يدويًا في قاعدة بيانات الشركة

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
        company: كائن الشركة
    """
    from django.db import connections
    from django.utils import timezone
    from django.contrib.auth.hashers import make_password

    # اسم المستخدم وكلمة المرور
    admin_username = f"admin_{company.id}"
    admin_password = company.serial_number.replace('-', '')[:8]  # استخدام جزء من الرقم التسلسلي ككلمة مرور

    # تشفير كلمة المرور
    hashed_password = make_password(admin_password)

    # التحقق من وجود المستخدم
    with connections[db_name].cursor() as cursor:
        cursor.execute("SELECT id FROM auth_user WHERE username = %s", [admin_username])
        user_exists = cursor.fetchone()

        if not user_exists:
            # إنشاء المستخدم
            cursor.execute("""
                INSERT INTO auth_user (
                    username, password, first_name, last_name, email,
                    is_staff, is_active, is_superuser, date_joined, last_login
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                admin_username,
                hashed_password,
                "مدير",
                "النظام",
                "",
                1,  # is_staff
                1,  # is_active
                1,  # is_superuser
                timezone.now(),
                timezone.now()
            ])

            logger.info(f"تم إنشاء المستخدم الرئيسي يدويًا في قاعدة البيانات: {company.database_name}")
            logger.info(f"اسم المستخدم: {admin_username}, كلمة المرور: {admin_password}")
        else:
            logger.info(f"المستخدم الرئيسي موجود بالفعل في قاعدة البيانات: {company.database_name}")

def update_database_schema(company):
    """
    تحديث هيكل قاعدة بيانات الشركة

    Args:
        company: كائن الشركة

    Returns:
        dict: نتيجة العملية
    """
    db_name = f"company_{company.id}"

    try:
        logger.info(f"بدء تحديث هيكل قاعدة بيانات الشركة: {company.name} ({company.database_name})")

        # التحقق من وجود ملف قاعدة البيانات
        db_path = get_database_path(company.database_name)
        if not os.path.exists(db_path):
            logger.warning(f"ملف قاعدة البيانات غير موجود: {db_path}")
            # إنشاء ملف قاعدة البيانات فارغ
            try:
                # إنشاء المجلد إذا لم يكن موجودًا
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                # إنشاء ملف قاعدة البيانات
                conn = sqlite3.connect(db_path)
                conn.close()
                logger.info(f"تم إنشاء ملف قاعدة البيانات: {db_path}")
            except Exception as file_error:
                logger.error(f"خطأ في إنشاء ملف قاعدة البيانات: {str(file_error)}")
                return {
                    'success': False,
                    'message': f'خطأ في إنشاء ملف قاعدة البيانات: {str(file_error)}'
                }

        # إعداد قاعدة البيانات
        db_config = {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': str(db_path),  # تحويل المسار إلى نص
            'USER': company.database_user,
            'PASSWORD': company.database_password,
            'HOST': company.database_host,
        }

        # التأكد من اكتمال إعدادات قاعدة البيانات
        db_config = ensure_database_config_complete(db_config)

        # إضافة قاعدة البيانات إلى إعدادات Django
        settings.DATABASES[db_name] = db_config

        # تطبيق الهجرات على قاعدة البيانات
        logger.info(f"تطبيق الهجرات على قاعدة البيانات: {company.database_name}")

        try:
            apply_migrations_to_database(db_name)
            migration_success = True
        except Exception as migrate_error:
            logger.error(f"خطأ في تطبيق الهجرات: {str(migrate_error)}")
            # في وضع التطوير، نتجاوز أخطاء الهجرات
            if settings.DEBUG:
                logger.warning(f"تجاوز خطأ الهجرات في وضع التطوير: {str(migrate_error)}")
                migration_success = True
            else:
                migration_success = False

        # إزالة قاعدة البيانات من الإعدادات بعد الانتهاء
        if db_name in settings.DATABASES:
            settings.DATABASES.pop(db_name, None)

        if migration_success:
            logger.info(f"تم تحديث هيكل قاعدة بيانات الشركة بنجاح: {company.name} ({company.database_name})")
            return {
                'success': True,
                'message': f'تم تحديث هيكل قاعدة بيانات الشركة بنجاح: {company.name}'
            }
        else:
            return {
                'success': True,  # نعتبر العملية ناجحة حتى لو فشلت بعض الهجرات
                'message': f'تم تحديث هيكل قاعدة بيانات الشركة بشكل جزئي: {company.name} (قد تكون هناك بعض الأخطاء)'
            }

    except Exception as e:
        logger.error(f"خطأ في تحديث هيكل قاعدة بيانات الشركة: {str(e)}")

        # تنظيف الموارد
        if db_name in settings.DATABASES:
            settings.DATABASES.pop(db_name, None)

        return {
            'success': False,
            'message': f'حدث خطأ أثناء تحديث هيكل قاعدة بيانات الشركة: {str(e)}'
        }

def update_all_company_databases():
    """
    تحديث هيكل جميع قواعد بيانات الشركات

    Returns:
        dict: نتيجة العملية
    """
    try:
        from backend.super_admin.models import Company

        logger.info("بدء تحديث هيكل جميع قواعد بيانات الشركات")

        # الحصول على جميع الشركات النشطة
        companies = Company.objects.filter(status='active')

        results = {
            'total': companies.count(),
            'success': 0,
            'failed': 0,
            'details': []
        }

        # تحديث كل قاعدة بيانات
        for company in companies:
            try:
                result = update_database_schema(company)

                if result['success']:
                    results['success'] += 1
                else:
                    results['failed'] += 1

                results['details'].append({
                    'company_id': company.id,
                    'company_name': company.name,
                    'database_name': company.database_name,
                    'success': result['success'],
                    'message': result['message']
                })

            except Exception as e:
                logger.error(f"خطأ في تحديث قاعدة بيانات الشركة {company.name}: {str(e)}")
                results['failed'] += 1
                results['details'].append({
                    'company_id': company.id,
                    'company_name': company.name,
                    'database_name': company.database_name,
                    'success': False,
                    'message': f'حدث خطأ غير متوقع: {str(e)}'
                })

        logger.info(f"اكتمل تحديث قواعد البيانات: {results['success']} ناجحة، {results['failed']} فاشلة")

        return {
            'success': True,
            'message': f'تم تحديث {results["success"]} قاعدة بيانات بنجاح، فشل تحديث {results["failed"]} قاعدة بيانات',
            'results': results
        }

    except Exception as e:
        logger.error(f"خطأ في تحديث قواعد بيانات الشركات: {str(e)}")
        return {
            'success': False,
            'message': f'حدث خطأ أثناء تحديث قواعد بيانات الشركات: {str(e)}'
        }
