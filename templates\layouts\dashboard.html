{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}منصة استقدامي السحابية{% endblock %}</title>

    <!-- Google Fonts - Tajawal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#143D8D',
                            light: '#407BFF',
                            dark: '#0A2A6B'
                        },
                        dark: {
                            DEFAULT: '#1a1a2e',
                            card: '#252547',
                            input: '#1e293b',
                            button: '#334155',
                            header: '#0f172a',
                            accent: '#2563eb'
                        }
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- استيراد الأنماط المشتركة -->
    {% include 'shared/styles.html' %}

    <!-- استيراد أنماط الإشعارات المحسنة -->
    <link rel="stylesheet" href="{% static 'css/toast-notifications.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="min-h-screen flex flex-col bg-gray-100 font-tajawal">
    <!-- الشريط العلوي الموحد -->
    {% if user.is_authenticated %}
        {% include 'shared/header.html' %}
    {% endif %}

    <!-- نظام الإشعارات -->
    {% include 'components/toast_notifications.html' %}

    <!-- نظام تأكيد الحذف -->
    {% include 'components/delete-modal.html' %}

    <!-- المحتوى الرئيسي -->
    <main class="flex-grow container mx-auto px-4 py-2">
        {% if not user.is_authenticated %}
            <div class="flex items-center justify-center min-h-screen">
                {% block content_unauthenticated %}{% endblock %}
            </div>
        {% else %}
            {% block content %}{% endblock %}
        {% endif %}
    </main>

    <!-- الشريط السفلي الموحد -->
    {% include 'shared/footer.html' %}

    <!-- استيراد السكربتات المشتركة -->
    {% include 'shared/scripts.html' %}

    <!-- استيراد سكربت الإشعارات المحسنة -->
    <script src="{% static 'js/toast-notifications.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
