/**
 * منصة استقدامي السحابية - تأثيرات لوحة التحكم
 * الإصدار 1.0.0
 */

/* تأثير تحديث الإحصائيات */
.stat-updated {
    animation: stat-flash 1s ease-in-out;
}

@keyframes stat-flash {
    0% {
        background-color: rgba(59, 130, 246, 0);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.2);
    }
    100% {
        background-color: rgba(59, 130, 246, 0);
    }
}

/* تأثيرات التنبيهات */
.alert-item {
    transition: all 0.3s ease-in-out;
}

.alert-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* تأثير ظهور التنبيهات */
.alert-appear {
    animation: alert-appear 0.5s ease-in-out;
}

@keyframes alert-appear {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير الوميض للتنبيهات الجديدة */
.alert-new {
    animation: alert-new-flash 2s ease-in-out;
}

@keyframes alert-new-flash {
    0% {
        background-color: rgba(239, 68, 68, 0);
    }
    25% {
        background-color: rgba(239, 68, 68, 0.1);
    }
    50% {
        background-color: rgba(239, 68, 68, 0);
    }
    75% {
        background-color: rgba(239, 68, 68, 0.1);
    }
    100% {
        background-color: rgba(239, 68, 68, 0);
    }
}

/* تأثير الوميض للتنبيهات العاجلة */
.alert-urgent {
    animation: alert-urgent-flash 1.5s ease-in-out infinite;
}

@keyframes alert-urgent-flash {
    0% {
        border-color: rgba(239, 68, 68, 0.5);
    }
    50% {
        border-color: rgba(239, 68, 68, 1);
    }
    100% {
        border-color: rgba(239, 68, 68, 0.5);
    }
}

/* تأثير تحديث الأنشطة الأخيرة */
.activity-item.activity-updated {
    animation: activity-flash 1s ease-in-out;
}

@keyframes activity-flash {
    0% {
        background-color: rgba(59, 130, 246, 0);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.1);
    }
    100% {
        background-color: rgba(59, 130, 246, 0);
    }
}

/* تأثير ظهور الأنشطة الجديدة */
.activity-item.activity-new {
    animation: activity-appear 0.5s ease-in-out;
}

@keyframes activity-appear {
    0% {
        opacity: 0;
        transform: translateX(10px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأثيرات الوضع الليلي */
.dark .stat-updated {
    animation: dark-stat-flash 1s ease-in-out;
}

@keyframes dark-stat-flash {
    0% {
        background-color: rgba(96, 165, 250, 0);
    }
    50% {
        background-color: rgba(96, 165, 250, 0.2);
    }
    100% {
        background-color: rgba(96, 165, 250, 0);
    }
}

.dark .alert-new {
    animation: dark-alert-new-flash 2s ease-in-out;
}

@keyframes dark-alert-new-flash {
    0% {
        background-color: rgba(248, 113, 113, 0);
    }
    25% {
        background-color: rgba(248, 113, 113, 0.1);
    }
    50% {
        background-color: rgba(248, 113, 113, 0);
    }
    75% {
        background-color: rgba(248, 113, 113, 0.1);
    }
    100% {
        background-color: rgba(248, 113, 113, 0);
    }
}

.dark .alert-urgent {
    animation: dark-alert-urgent-flash 1.5s ease-in-out infinite;
}

@keyframes dark-alert-urgent-flash {
    0% {
        border-color: rgba(248, 113, 113, 0.5);
    }
    50% {
        border-color: rgba(248, 113, 113, 1);
    }
    100% {
        border-color: rgba(248, 113, 113, 0.5);
    }
}

/* تأثيرات الوضع الليلي للأنشطة */
.dark .activity-item.activity-updated {
    animation: dark-activity-flash 1s ease-in-out;
}

@keyframes dark-activity-flash {
    0% {
        background-color: rgba(96, 165, 250, 0);
    }
    50% {
        background-color: rgba(96, 165, 250, 0.15);
    }
    100% {
        background-color: rgba(96, 165, 250, 0);
    }
}
