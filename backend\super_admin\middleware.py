from django.shortcuts import redirect, render
from django.urls import reverse
from django.contrib import messages
from backend.super_admin.models import SystemLog

class SuperAdminMiddleware:
    """
    ميدلوير للتحقق من صلاحيات المسؤول الأعلى
    يتأكد من أن المستخدم مسجل الدخول ولديه صلاحيات المسؤول الأعلى
    تقييد الوصول بشكل صارم لمنشئ النظام فقط
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # المسارات المستثناة من التحقق
        exempt_paths = [
            '/super_admin/login/',  # صفحة تسجيل دخول المسؤول الأعلى
            '/super_admin/logout/',
            '/super_admin/resend-2fa-code/',
            '/super_admin/send-verification-code/',
            '/super-admin/login/',  # صفحة تسجيل دخول المسؤول الأعلى
            '/super-admin/logout/',
            '/super-admin/resend-2fa-code/',
            '/super-admin/send-verification-code/',
        ]

        # التحقق من المسار (يدعم كلا النمطين super_admin و super-admin)
        if (request.path.startswith('/super_admin/') or request.path.startswith('/super-admin/')) and request.path not in exempt_paths:
            # التحقق من تسجيل الدخول
            if not request.user.is_authenticated:
                # لا نعرض رسالة خطأ، فقط إعادة توجيه صامتة
                return redirect('/super_admin/login/' + f'?next={request.path}')

            # التحقق من صلاحيات المسؤول الأعلى (منشئ النظام فقط)
            if not (request.user.is_superuser and request.user.username == 'MUNTADER_WISSAM'):
                # تسجيل محاولة الوصول غير المصرح بها
                if request.user.is_authenticated:
                    SystemLog.objects.create(
                        user=request.user,
                        action='unauthorized_access',
                        description=f'محاولة وصول غير مصرح بها إلى صفحة المسؤول الأعلى: {request.path}',
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                # لا نعرض رسالة خطأ، فقط إعادة توجيه صامتة
                return redirect('/super_admin/login/')

        response = self.get_response(request)
        return response

class MaintenanceModeMiddleware:
    """Middleware لإدارة وضع الصيانة"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # الحصول على إعدادات وضع الصيانة
        try:
            from .models import MaintenanceMode
            maintenance = MaintenanceMode.get_current()
        except:
            # في حالة عدم وجود إعدادات، لا نفعل شيء
            return self.get_response(request)

        # إذا لم يكن وضع الصيانة مفعل، لا نفعل شيء
        if not maintenance.is_active:
            return self.get_response(request)

        # السماح للمسؤولين الأعلى بالوصول
        if maintenance.bypass_for_superusers and request.user.is_authenticated and request.user.is_superuser:
            return self.get_response(request)

        # السماح لعناوين IP المحددة
        client_ip = self.get_client_ip(request)
        if maintenance.is_ip_allowed(client_ip):
            return self.get_response(request)

        # السماح بالوصول لصفحات المسؤول الأعلى
        if request.path.startswith('/super_admin/'):
            return self.get_response(request)

        # السماح بالوصول للملفات الثابتة
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return self.get_response(request)

        # عرض صفحة الصيانة
        return self.render_maintenance_page(request, maintenance)

    def get_client_ip(self, request):
        """الحصول على عنوان IP الحقيقي للعميل"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def render_maintenance_page(self, request, maintenance):
        """عرض صفحة الصيانة"""
        context = {
            'maintenance': maintenance,
            'title': maintenance.title,
            'message': maintenance.message,
            'estimated_completion': maintenance.estimated_completion,
        }

        # استخدام قالب مخصص لصفحة الصيانة
        return render(request, 'maintenance.html', context, status=503)
