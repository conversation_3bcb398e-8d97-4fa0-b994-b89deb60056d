from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.cache import cache
import uuid
import string
import random
import datetime
import json

class Company(models.Model):
    """نموذج لتخزين معلومات الشركات وقواعد بياناتها"""
    STATUS_CHOICES = (
        ('active', 'نشطة'),
        ('inactive', 'غير نشطة'),
        ('suspended', 'معلقة'),
        ('trial', 'تجريبية'),
    )

    name = models.CharField(max_length=200, verbose_name='اسم الشركة')
    serial_number = models.CharField(max_length=16, unique=True, verbose_name='الرقم التسلسلي')
    database_name = models.CharField(max_length=100, unique=True, verbose_name='اسم قاعدة البيانات')
    database_user = models.CharField(max_length=100, verbose_name='مستخدم قاعدة البيانات')
    database_password = models.CharField(max_length=100, verbose_name='كلمة مرور قاعدة البيانات')
    database_host = models.CharField(max_length=100, default='localhost', verbose_name='مضيف قاعدة البيانات')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')

    # بيانات مدير الشركة
    admin_username = models.CharField(max_length=50, blank=True, null=True, verbose_name='اسم مستخدم مدير الشركة')
    admin_password = models.CharField(max_length=50, blank=True, null=True, verbose_name='كلمة مرور مدير الشركة')

    # إعدادات العملة للشركة
    preferred_currency = models.CharField(
        max_length=3,
        choices=(('IQD', 'الدينار العراقي'), ('USD', 'الدولار الأمريكي')),
        default='IQD',
        verbose_name='العملة المفضلة'
    )
    custom_exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        null=True,
        blank=True,
        verbose_name='سعر الصرف المخصص'
    )
    use_custom_exchange_rate = models.BooleanField(
        default=False,
        verbose_name='استخدام سعر صرف مخصص'
    )
    currency_display_format = models.CharField(
        max_length=20,
        choices=(
            ('symbol_after', 'الرمز بعد الرقم'),
            ('symbol_before', 'الرمز قبل الرقم'),
            ('code_after', 'الكود بعد الرقم'),
            ('code_before', 'الكود قبل الرقم'),
        ),
        default='symbol_after',
        verbose_name='تنسيق عرض العملة'
    )
    show_decimal_places = models.BooleanField(
        default=True,
        verbose_name='إظهار الخانات العشرية'
    )
    decimal_places_count = models.IntegerField(
        default=2,
        choices=((0, 'بدون خانات عشرية'), (1, 'خانة واحدة'), (2, 'خانتان'), (3, 'ثلاث خانات')),
        verbose_name='عدد الخانات العشرية'
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    expiry_date = models.DateField(null=True, blank=True, verbose_name='تاريخ انتهاء الاشتراك')

    def __str__(self):
        return self.name

    def generate_serial_number(self):
        """توليد رقم تسلسلي فريد مكون من 16 خانة بتنسيق XXXX-XXXX-XXXX-XXXX"""
        import secrets
        import hashlib
        import time
        import uuid

        # استخدام مزيج من الأحرف الكبيرة والأرقام فقط (لسهولة القراءة)
        # استبعاد الأحرف المتشابهة مثل O و 0، I و 1، لتجنب الالتباس
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'

        # إنشاء قيمة عشوائية فريدة باستخدام مزيج من:
        # - الوقت الحالي بدقة ميكروثانية
        # - UUID فريد
        # - اسم الشركة (إذا كان متاحًا)
        # - قيمة عشوائية إضافية
        unique_value = f"{time.time()}-{uuid.uuid4().hex}-{secrets.token_hex(8)}-{self.name if self.name else ''}"

        # إنشاء هاش SHA-256 للقيمة الفريدة
        hash_obj = hashlib.sha256(unique_value.encode())
        hash_hex = hash_obj.hexdigest()

        # اختيار 16 حرف من الهاش وتحويلها إلى أحرف وأرقام مقبولة
        serial_chars = []

        # استخدام أول 4 أحرف من الهاش للمجموعة الأولى
        for i in range(4):
            index = int(hash_hex[i:i+2], 16) % len(chars)
            serial_chars.append(chars[index])

        # استخدام أحرف من وسط الهاش للمجموعة الثانية
        for i in range(16, 20):
            index = int(hash_hex[i:i+2], 16) % len(chars)
            serial_chars.append(chars[index])

        # استخدام أحرف من وسط الهاش للمجموعة الثالثة
        for i in range(32, 36):
            index = int(hash_hex[i:i+2], 16) % len(chars)
            serial_chars.append(chars[index])

        # استخدام آخر 4 أحرف من الهاش للمجموعة الرابعة
        for i in range(60, 64):
            index = int(hash_hex[i:i+2], 16) % len(chars)
            serial_chars.append(chars[index])

        # تقسيم الأحرف إلى 4 مجموعات كل منها 4 أحرف
        serial = '-'.join([''.join(serial_chars[i:i+4]) for i in range(0, 16, 4)])

        # التأكد من عدم وجود رقم تسلسلي مطابق في قاعدة البيانات
        attempts = 0
        max_attempts = 10  # تحديد عدد المحاولات القصوى لتجنب الحلقات اللانهائية

        while (Company.objects.filter(serial_number=serial).exists() or
               BlockedSerial.objects.filter(serial_number=serial).exists()):
            # إذا كان الرقم موجودًا بالفعل، قم بتوليد رقم جديد
            if attempts >= max_attempts:
                # إذا وصلنا للحد الأقصى من المحاولات، نستخدم طريقة مختلفة تمامًا
                serial_chars = [secrets.choice(chars) for _ in range(16)]
            else:
                # تعديل بعض الأحرف بشكل عشوائي مع الحفاظ على بعض الأحرف الأصلية
                # للحفاظ على بعض الفريدة من الهاش الأصلي
                for i in range(0, 16, 2):  # تغيير كل حرف ثاني
                    serial_chars[i] = secrets.choice(chars)

            serial = '-'.join([''.join(serial_chars[i:i+4]) for i in range(0, 16, 4)])
            attempts += 1

        return serial

    def format_serial_number(self):
        """تنسيق الرقم التسلسلي بشكل XXXX-XXXX-XXXX-XXXX"""
        if not self.serial_number:
            return ""

        # إزالة أي شرطات موجودة
        clean_serial = self.serial_number.replace('-', '')

        # التأكد من أن الطول هو 16 حرف
        if len(clean_serial) != 16:
            return self.serial_number

        # تقسيم إلى 4 مجموعات
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])
        return formatted_serial

    def get_formatted_serial(self):
        """الحصول على الرقم التسلسلي منسقًا"""
        return self.format_serial_number()

    def get_currency_symbol(self):
        """الحصول على رمز العملة المفضلة للشركة"""
        try:
            currency = Currency.objects.get(code=self.preferred_currency, is_active=True)
            return currency.symbol
        except Currency.DoesNotExist:
            return 'د.ع' if self.preferred_currency == 'IQD' else 'DUR'

    def get_exchange_rate(self):
        """الحصول على سعر الصرف المستخدم للشركة"""
        if self.use_custom_exchange_rate and self.custom_exchange_rate:
            return float(self.custom_exchange_rate)

        try:
            currency = Currency.objects.get(code=self.preferred_currency, is_active=True)
            return float(currency.exchange_rate_to_usd)
        except Currency.DoesNotExist:
            return 1320.0 if self.preferred_currency == 'IQD' else 1.0

    def format_currency_amount(self, amount):
        """تنسيق المبلغ حسب إعدادات العملة للشركة"""
        try:
            amount = float(amount)

            # تحديد عدد الخانات العشرية
            if not self.show_decimal_places:
                decimal_places = 0
            else:
                decimal_places = self.decimal_places_count

            # تنسيق الرقم
            if decimal_places > 0:
                formatted_amount = f"{amount:,.{decimal_places}f}"
            else:
                formatted_amount = f"{int(amount):,}"

            # الحصول على رمز العملة
            symbol = self.get_currency_symbol()

            # تطبيق تنسيق العرض
            if self.currency_display_format == 'symbol_before':
                return f"{symbol} {formatted_amount}"
            elif self.currency_display_format == 'code_before':
                return f"{self.preferred_currency} {formatted_amount}"
            elif self.currency_display_format == 'code_after':
                return f"{formatted_amount} {self.preferred_currency}"
            else:  # symbol_after (default)
                return f"{formatted_amount} {symbol}"

        except (ValueError, TypeError):
            return f"0 {self.get_currency_symbol()}"

    def convert_to_company_currency(self, amount, from_currency='USD'):
        """تحويل مبلغ من عملة أخرى إلى عملة الشركة"""
        try:
            amount = float(amount)

            if from_currency == self.preferred_currency:
                return amount

            # الحصول على أسعار الصرف
            if from_currency == 'USD':
                if self.preferred_currency == 'IQD':
                    rate = self.get_exchange_rate()
                    return amount * rate
                else:
                    return amount
            elif from_currency == 'IQD':
                if self.preferred_currency == 'USD':
                    rate = self.get_exchange_rate()
                    return amount / rate
                else:
                    return amount

            return amount

        except (ValueError, TypeError, ZeroDivisionError):
            return 0

    def get_currency_settings_summary(self):
        """الحصول على ملخص إعدادات العملة"""
        return {
            'currency': self.preferred_currency,
            'symbol': self.get_currency_symbol(),
            'exchange_rate': self.get_exchange_rate(),
            'custom_rate': self.use_custom_exchange_rate,
            'display_format': self.get_currency_display_format_display(),
            'decimal_places': self.decimal_places_count if self.show_decimal_places else 0,
            'example': self.format_currency_amount(1000)
        }

    def get_currency_example(self):
        """الحصول على مثال لتنسيق العملة (1000)"""
        return self.format_currency_amount(1000)

    def generate_admin_username(self):
        """توليد اسم مستخدم لمدير الشركة"""
        import random
        import string

        # استخدام اسم الشركة كأساس
        base_name = self.name.replace(' ', '_').replace('-', '_')
        # إزالة الأحرف غير المسموح بها
        base_name = ''.join(c for c in base_name if c.isalnum() or c == '_')

        # إضافة رقم عشوائي
        random_num = random.randint(100, 999)
        username = f"{base_name}_{random_num}"

        # التأكد من أن اسم المستخدم فريد
        from django.contrib.auth.models import User
        while User.objects.filter(username=username).exists():
            random_num = random.randint(100, 999)
            username = f"{base_name}_{random_num}"

        return username[:30]  # تحديد الطول الأقصى

    def generate_admin_password(self):
        """توليد كلمة مرور قوية لمدير الشركة"""
        import random
        import string

        # إنشاء كلمة مرور قوية
        length = 12
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choice(characters) for _ in range(length))

        return password

    def save(self, *args, **kwargs):
        # تحديد ما إذا كانت الشركة جديدة
        is_new = self.pk is None

        if not self.serial_number:
            self.serial_number = self.generate_serial_number()
        else:
            # تنسيق الرقم التسلسلي إذا لم يكن بالتنسيق الصحيح
            clean_serial = self.serial_number.replace('-', '')
            if len(clean_serial) == 16 and '-' not in self.serial_number:
                self.serial_number = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])

        # إنشاء بيانات مدير الشركة إذا كانت جديدة
        if is_new and not self.admin_username:
            self.admin_username = self.generate_admin_username()
            self.admin_password = self.generate_admin_password()

        # حفظ الشركة في قاعدة البيانات
        super().save(*args, **kwargs)

        # إنشاء وتهيئة قاعدة بيانات للشركة فقط إذا كانت جديدة
        if is_new:
            self.initialize_company_database()

    def initialize_company_database(self):
        """إنشاء وتهيئة قاعدة بيانات للشركة الجديدة"""
        # استدعاء وظيفة التهيئة من وحدة database_initializer
        from .database_initializer import initialize_company_database

        # تنفيذ عملية التهيئة
        result = initialize_company_database(self)

        # تسجيل نتيجة العملية
        import logging
        logger = logging.getLogger(__name__)

        if result['success']:
            logger.info(f"تم تهيئة قاعدة بيانات الشركة بنجاح: {self.name} ({self.database_name})")
        else:
            logger.error(f"فشل في تهيئة قاعدة بيانات الشركة: {self.name} ({self.database_name}): {result['message']}")

        return result

    class Meta:
        verbose_name = 'شركة'
        verbose_name_plural = 'الشركات'
        ordering = ['-created_at']

class SuperAdminUser(models.Model):
    """نموذج لتخزين معلومات مستخدمي المسؤول الأعلى"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='super_admin_profile', verbose_name='المستخدم')
    two_factor_enabled = models.BooleanField(default=False, verbose_name='تفعيل التحقق الثنائي')
    two_factor_secret = models.CharField(max_length=100, blank=True, null=True, verbose_name='سر التحقق الثنائي')
    last_login_ip = models.GenericIPAddressField(blank=True, null=True, verbose_name='IP آخر تسجيل دخول')
    access_token = models.CharField(max_length=100, blank=True, null=True, verbose_name='رمز الوصول')
    token_expiry = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الرمز')
    ip_restriction_enabled = models.BooleanField(default=False, verbose_name='تفعيل تقييد عناوين IP')
    allowed_ips = models.TextField(blank=True, null=True, verbose_name='عناوين IP المسموح بها')
    failed_login_attempts = models.IntegerField(default=0, verbose_name='عدد محاولات تسجيل الدخول الفاشلة')
    last_failed_login = models.DateTimeField(blank=True, null=True, verbose_name='آخر محاولة تسجيل دخول فاشلة')
    account_locked = models.BooleanField(default=False, verbose_name='الحساب مقفل')
    account_locked_until = models.DateTimeField(blank=True, null=True, verbose_name='الحساب مقفل حتى')

    def __str__(self):
        return self.user.username

    def generate_access_token(self):
        """توليد رمز وصول جديد"""
        token = uuid.uuid4().hex
        self.access_token = token
        self.token_expiry = timezone.now() + timezone.timedelta(hours=24)
        self.save()
        return token

    def is_ip_allowed(self, ip_address):
        """التحقق مما إذا كان عنوان IP مسموحًا به"""
        if not self.ip_restriction_enabled:
            return True

        if not self.allowed_ips:
            return True

        allowed_ip_list = [ip.strip() for ip in self.allowed_ips.split(',')]

        # التحقق من تطابق عنوان IP مع القائمة المسموح بها
        for allowed_ip in allowed_ip_list:
            # دعم نطاقات IP (مثل 192.168.1.*)
            if allowed_ip.endswith('*'):
                prefix = allowed_ip[:-1]
                if ip_address.startswith(prefix):
                    return True
            # دعم نطاقات CIDR (مثل ***********/24)
            elif '/' in allowed_ip:
                try:
                    from ipaddress import ip_network, ip_address as ip_addr
                    network = ip_network(allowed_ip, strict=False)
                    if ip_addr(ip_address) in network:
                        return True
                except:
                    pass
            # تطابق مباشر
            elif allowed_ip == ip_address:
                return True

        return False

    def record_failed_login(self):
        """تسجيل محاولة تسجيل دخول فاشلة"""
        self.failed_login_attempts += 1
        self.last_failed_login = timezone.now()

        # قفل الحساب بعد 5 محاولات فاشلة
        if self.failed_login_attempts >= 5:
            self.account_locked = True
            # قفل الحساب لمدة 30 دقيقة
            self.account_locked_until = timezone.now() + timezone.timedelta(minutes=30)

        self.save()

    def reset_failed_login_attempts(self):
        """إعادة تعيين عدد محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts = 0
        self.account_locked = False
        self.account_locked_until = None
        self.save()

    def is_account_locked(self):
        """التحقق مما إذا كان الحساب مقفلًا"""
        if not self.account_locked:
            return False

        # التحقق مما إذا كانت فترة القفل قد انتهت
        if self.account_locked_until and timezone.now() > self.account_locked_until:
            self.account_locked = False
            self.account_locked_until = None
            self.save()
            return False

        return True

    def send_2fa_code_email(self):
        """إرسال رمز المصادقة الثنائية عبر البريد الإلكتروني"""
        from .email_verification import send_verification_code

        # تفعيل التحقق الثنائي
        self.two_factor_enabled = True
        self.save()

        # إرسال رمز التحقق عبر البريد الإلكتروني
        return send_verification_code(self.user, self.user.email)

    def generate_totp_secret(self):
        """توليد سر TOTP جديد لاستخدامه مع تطبيق Authenticator"""
        import pyotp

        # توليد سر جديد
        secret = pyotp.random_base32()
        self.two_factor_secret = secret
        self.two_factor_enabled = True
        self.save()

        return secret

    def get_totp_uri(self):
        """الحصول على URI لإنشاء رمز QR لتطبيق Authenticator"""
        import pyotp

        if not self.two_factor_secret:
            return None

        totp = pyotp.TOTP(self.two_factor_secret)
        return totp.provisioning_uri(
            name=self.user.email,
            issuer_name="منصة استقدامي السحابية"
        )

    def verify_totp_code(self, code):
        """التحقق من رمز TOTP المدخل"""
        import pyotp

        if not self.two_factor_secret:
            return False

        totp = pyotp.TOTP(self.two_factor_secret)
        return totp.verify(code)

    def generate_qr_code_image(self):
        """توليد صورة رمز QR لاستخدامها مع تطبيق Authenticator"""
        import qrcode
        import io
        import base64

        if not self.two_factor_secret:
            return None

        uri = self.get_totp_uri()
        if not uri:
            return None

        # إنشاء رمز QR
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(uri)
        qr.make(fit=True)

        # إنشاء صورة
        img = qr.make_image(fill_color="black", back_color="white")

        # تحويل الصورة إلى سلسلة Base64
        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        return base64.b64encode(buffer.getvalue()).decode("utf-8")

    class Meta:
        verbose_name = 'مستخدم المسؤول الأعلى'
        verbose_name_plural = 'مستخدمي المسؤول الأعلى'

class SystemLog(models.Model):
    """نموذج لتسجيل أحداث النظام"""
    ACTION_CHOICES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('activate', 'تفعيل'),
        ('deactivate', 'تعطيل'),
    )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='system_logs', verbose_name='المستخدم')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name='الإجراء')
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True, related_name='logs', verbose_name='الشركة')
    description = models.TextField(verbose_name='الوصف')
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.get_action_display()} - {self.created_at}"

    class Meta:
        verbose_name = 'سجل النظام'
        verbose_name_plural = 'سجلات النظام'
        ordering = ['-created_at']

class SystemSettings(models.Model):
    """نموذج لتخزين إعدادات النظام العامة"""
    TWO_FACTOR_TYPE_CHOICES = (
        ('otp', 'رمز OTP لمرة واحدة'),
        ('google_auth', 'Google Authenticator'),
        ('sms', 'رسائل SMS'),
    )

    app_name = models.CharField(max_length=100, default='نظام إدارة العمالة', verbose_name='اسم التطبيق')
    default_language = models.CharField(max_length=10, default='ar', verbose_name='اللغة الافتراضية')
    enable_registration = models.BooleanField(default=True, verbose_name='تفعيل التسجيل للشركات الجديدة')
    maintenance_mode = models.BooleanField(default=False, verbose_name='وضع الصيانة')
    two_factor_for_admins = models.BooleanField(default=False, verbose_name='تفعيل التحقق الثنائي للمسؤولين')
    two_factor_type = models.CharField(max_length=20, choices=TWO_FACTOR_TYPE_CHOICES, default='otp', verbose_name='نوع المصادقة الثنائية')
    session_timeout = models.IntegerField(default=60, verbose_name='مدة صلاحية الجلسة (بالدقائق)')
    last_backup_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ آخر نسخة احتياطية')

    # إعدادات العملة
    default_currency = models.CharField(
        max_length=3,
        choices=(('IQD', 'الدينار العراقي'), ('USD', 'الدولار الأمريكي')),
        default='IQD',
        verbose_name='العملة الافتراضية'
    )
    show_currency_converter = models.BooleanField(default=True, verbose_name='إظهار محول العملات')
    auto_update_exchange_rates = models.BooleanField(default=False, verbose_name='تحديث أسعار الصرف تلقائياً')

    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم التحديث بواسطة')

    def __str__(self):
        return "إعدادات النظام"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام (إنشاء إذا لم تكن موجودة)"""
        settings = cache.get('system_settings')
        if not settings:
            settings, _ = cls.objects.get_or_create(pk=1)
            cache.set('system_settings', settings, 3600)  # تخزين مؤقت لمدة ساعة
        return settings

    def save(self, *args, **kwargs):
        """حفظ الإعدادات وتحديث ذاكرة التخزين المؤقت"""
        super().save(*args, **kwargs)
        cache.set('system_settings', self, 3600)  # تحديث التخزين المؤقت

    class Meta:
        verbose_name = 'إعدادات النظام'
        verbose_name_plural = 'إعدادات النظام'

class FailedLoginAttempt(models.Model):
    """نموذج لتسجيل محاولات تسجيل الدخول الفاشلة"""
    serial_number = models.CharField(max_length=20, verbose_name='الرقم التسلسلي')
    reason = models.CharField(max_length=255, verbose_name='سبب الفشل')
    ip_address = models.GenericIPAddressField(verbose_name='عنوان IP')
    attempt_time = models.DateTimeField(auto_now_add=True, verbose_name='وقت المحاولة')
    is_blocked = models.BooleanField(default=False, verbose_name='تم الحظر')

    def __str__(self):
        return f"{self.serial_number} - {self.attempt_time}"

    @classmethod
    def check_attempts(cls, serial_number, ip_address):
        """التحقق من عدد المحاولات الفاشلة خلال الساعة الماضية"""
        one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
        attempts = cls.objects.filter(
            serial_number=serial_number,
            ip_address=ip_address,
            attempt_time__gte=one_hour_ago
        ).count()

        # إذا كان عدد المحاولات أكثر من 5، يتم حظر المستخدم
        if attempts >= 5:
            # تحديث جميع المحاولات لتكون محظورة
            cls.objects.filter(
                serial_number=serial_number,
                ip_address=ip_address,
                attempt_time__gte=one_hour_ago
            ).update(is_blocked=True)
            return True

        return False

    @classmethod
    def unblock_serial(cls, serial_number):
        """إلغاء حظر الرقم التسلسلي"""
        # تحديث جميع المحاولات المحظورة للرقم التسلسلي
        updated = cls.objects.filter(
            serial_number=serial_number,
            is_blocked=True
        ).update(is_blocked=False)

        return updated

    @classmethod
    def get_blocked_serials(cls):
        """الحصول على قائمة بالأرقام التسلسلية المحظورة"""
        # الحصول على الأرقام التسلسلية المحظورة مع آخر وقت للمحاولة وعدد المحاولات
        blocked_serials = cls.objects.filter(
            is_blocked=True
        ).values('serial_number').annotate(
            last_attempt=models.Max('attempt_time'),
            attempts_count=models.Count('id')
        ).order_by('-last_attempt')

        return blocked_serials

    class Meta:
        verbose_name = 'محاولة تسجيل دخول فاشلة'
        verbose_name_plural = 'محاولات تسجيل الدخول الفاشلة'
        ordering = ['-attempt_time']

class AdminLoginAttempt(models.Model):
    """نموذج لتسجيل محاولات تسجيل دخول المسؤول الأعلى"""
    username = models.CharField(max_length=150, verbose_name='اسم المستخدم')
    ip_address = models.GenericIPAddressField(verbose_name='عنوان IP')
    user_agent = models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='وقت المحاولة')
    is_successful = models.BooleanField(default=False, verbose_name='نجاح المحاولة')

    def __str__(self):
        status = "ناجحة" if self.is_successful else "فاشلة"
        return f"محاولة {status} لـ {self.username} - {self.created_at}"

    class Meta:
        verbose_name = 'محاولة تسجيل دخول المسؤول'
        verbose_name_plural = 'محاولات تسجيل دخول المسؤول'
        ordering = ['-created_at']

class DatabaseBackup(models.Model):
    """نموذج لتتبع النسخ الاحتياطية لقواعد البيانات"""
    BACKUP_TYPE_CHOICES = (
        ('manual', 'يدوي'),
        ('automatic', 'تلقائي'),
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
    )

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='backups', verbose_name="الشركة")
    file_name = models.CharField(max_length=255, verbose_name="اسم الملف")
    file_path = models.CharField(max_length=500, verbose_name="مسار الملف")
    file_size = models.BigIntegerField(default=0, verbose_name="حجم الملف (بايت)")
    backup_type = models.CharField(max_length=20, choices=BACKUP_TYPE_CHOICES, default='manual', verbose_name="نوع النسخة الاحتياطية")
    is_encrypted = models.BooleanField(default=False, verbose_name="مشفرة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_backups', verbose_name="تم الإنشاء بواسطة")

    def __str__(self):
        return f"نسخة احتياطية لـ {self.company.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def get_size_formatted(self):
        """تحويل حجم الملف إلى صيغة مقروءة"""
        if self.file_size < 1024:
            return f"{self.file_size} بايت"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.2f} كيلوبايت"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.2f} ميجابايت"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.2f} جيجابايت"

    def delete_file(self):
        """حذف ملف النسخة الاحتياطية من القرص"""
        import os
        if os.path.exists(self.file_path):
            os.remove(self.file_path)
            return True
        return False

    class Meta:
        verbose_name = "نسخة احتياطية"
        verbose_name_plural = "النسخ الاحتياطية"
        ordering = ['-created_at']

class BackupSchedule(models.Model):
    """نموذج جدولة النسخ الاحتياطي التلقائي"""
    FREQUENCY_CHOICES = (
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
    )

    DAY_OF_WEEK_CHOICES = (
        (0, 'الاثنين'),
        (1, 'الثلاثاء'),
        (2, 'الأربعاء'),
        (3, 'الخميس'),
        (4, 'الجمعة'),
        (5, 'السبت'),
        (6, 'الأحد'),
    )

    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='backup_schedule', verbose_name="الشركة", null=True, blank=True)
    is_active = models.BooleanField(default=False, verbose_name="مفعل")
    frequency = models.CharField(max_length=10, choices=FREQUENCY_CHOICES, default='weekly', verbose_name="التكرار")
    time = models.TimeField(default=datetime.time(0, 0), verbose_name="وقت النسخ الاحتياطي")
    day_of_week = models.IntegerField(choices=DAY_OF_WEEK_CHOICES, default=0, verbose_name="يوم الأسبوع")
    day_of_month = models.IntegerField(default=1, verbose_name="يوم الشهر")
    retention_period = models.IntegerField(default=30, verbose_name="فترة الاحتفاظ (بالأيام)")
    last_backup = models.DateTimeField(null=True, blank=True, verbose_name="آخر نسخة احتياطية")
    next_backup = models.DateTimeField(null=True, blank=True, verbose_name="النسخة الاحتياطية التالية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_backup_schedules', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_backup_schedules', verbose_name="تم التحديث بواسطة")

    def __str__(self):
        if self.company:
            return f"جدولة النسخ الاحتياطي لـ {self.company.name}"
        else:
            return "جدولة النسخ الاحتياطي للنظام"

    def calculate_next_backup(self):
        """حساب موعد النسخة الاحتياطية التالية"""
        now = timezone.now()
        backup_time = datetime.time(hour=self.time.hour, minute=self.time.minute)

        if self.frequency == 'daily':
            # النسخ الاحتياطي اليومي
            next_date = now.date()
            if now.time() >= backup_time:
                next_date = now.date() + datetime.timedelta(days=1)

            next_backup = timezone.make_aware(datetime.datetime.combine(next_date, backup_time))

        elif self.frequency == 'weekly':
            # النسخ الاحتياطي الأسبوعي
            days_ahead = self.day_of_week - now.weekday()
            if days_ahead <= 0 or (days_ahead == 0 and now.time() >= backup_time):
                days_ahead += 7

            next_date = now.date() + datetime.timedelta(days=days_ahead)
            next_backup = timezone.make_aware(datetime.datetime.combine(next_date, backup_time))

        else:  # monthly
            # النسخ الاحتياطي الشهري
            year = now.year
            month = now.month + 1
            if month > 12:
                month = 1
                year += 1

            # التأكد من أن اليوم صالح للشهر
            day = min(self.day_of_month, 28)  # استخدام 28 كحد أقصى لضمان صلاحية التاريخ

            next_date = datetime.date(year, month, day)
            next_backup = timezone.make_aware(datetime.datetime.combine(next_date, backup_time))

            # إذا كان الوقت الحالي بعد وقت النسخ الاحتياطي وكان اليوم هو يوم النسخ الاحتياطي
            if now.date().day == day and now.time() >= backup_time:
                # الانتقال إلى الشهر التالي
                month = month + 1
                if month > 12:
                    month = 1
                    year += 1
                next_date = datetime.date(year, month, day)
                next_backup = timezone.make_aware(datetime.datetime.combine(next_date, backup_time))

        return next_backup

    def save(self, *args, **kwargs):
        """حفظ النموذج مع حساب موعد النسخة الاحتياطية التالية"""
        if self.is_active and not self.next_backup:
            self.next_backup = self.calculate_next_backup()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "جدولة النسخ الاحتياطي"
        verbose_name_plural = "جدولات النسخ الاحتياطي"


class BlockedSerial(models.Model):
    """نموذج لتخزين الأرقام التسلسلية المحظورة"""
    serial_number = models.CharField(max_length=20, unique=True, verbose_name="الرقم التسلسلي")
    reason = models.TextField(verbose_name="سبب الحظر")
    is_permanent = models.BooleanField(default=True, verbose_name="حظر دائم")
    blocked_until = models.DateField(null=True, blank=True, verbose_name="محظور حتى تاريخ")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='blocked_serials', verbose_name="تم الحظر بواسطة")

    def __str__(self):
        return self.serial_number

    def is_active(self):
        """التحقق مما إذا كان الحظر نشطًا"""
        if self.is_permanent:
            return True

        if self.blocked_until and self.blocked_until >= timezone.now().date():
            return True

        return False

    def get_status_display(self):
        """الحصول على حالة الحظر للعرض"""
        if self.is_permanent:
            return "دائم"

        if self.blocked_until:
            if self.blocked_until >= timezone.now().date():
                return f"مؤقت (حتى {self.blocked_until})"
            else:
                return "منتهي"

        return "غير معروف"

    def save(self, *args, **kwargs):
        """حفظ النموذج مع تنسيق الرقم التسلسلي"""
        # تنسيق الرقم التسلسلي
        if self.serial_number:
            # إزالة أي شرطات موجودة
            clean_serial = self.serial_number.replace('-', '')

            # التأكد من أن الطول هو 16 حرف
            if len(clean_serial) == 16:
                # تقسيم إلى 4 مجموعات
                self.serial_number = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])

        # إذا كان الحظر دائمًا، إزالة تاريخ انتهاء الحظر
        if self.is_permanent:
            self.blocked_until = None

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "رقم تسلسلي محظور"
        verbose_name_plural = "الأرقام التسلسلية المحظورة"
        ordering = ['-created_at']

class SchemaChange(models.Model):
    """نموذج لتتبع التغييرات في هيكل قاعدة البيانات"""
    CHANGE_TYPE_CHOICES = (
        ('migrate', 'هجرة'),
        ('manual', 'يدوي'),
        ('reset', 'إعادة تعيين'),
    )

    app_name = models.CharField(max_length=100, verbose_name="اسم التطبيق")
    change_type = models.CharField(max_length=20, choices=CHANGE_TYPE_CHOICES, default='migrate', verbose_name="نوع التغيير")
    description = models.TextField(verbose_name="وصف التغيير")
    details = models.TextField(blank=True, null=True, verbose_name="تفاصيل التغيير")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    applied_to_all = models.BooleanField(default=False, verbose_name="تم تطبيقه على جميع الشركات")
    applied_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ التطبيق")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='schema_changes', verbose_name="تم الإنشاء بواسطة")

    def __str__(self):
        return f"تغيير في هيكل {self.app_name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def set_details(self, details_dict):
        """تعيين تفاصيل التغيير كـ JSON"""
        self.details = json.dumps(details_dict, ensure_ascii=False)

    def get_details(self):
        """الحصول على تفاصيل التغيير من JSON"""
        if self.details:
            try:
                return json.loads(self.details)
            except json.JSONDecodeError:
                return {}
        return {}

    def mark_as_applied(self):
        """تحديد التغيير كمطبق على جميع الشركات"""
        self.applied_to_all = True
        self.applied_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = "تغيير في هيكل قاعدة البيانات"
        verbose_name_plural = "تغييرات هيكل قاعدة البيانات"
        ordering = ['-created_at']

class SubscriptionPlan(models.Model):
    """نموذج خطط الاشتراك"""
    PLAN_TYPE_CHOICES = (
        ('basic', 'أساسي'),
        ('standard', 'قياسي'),
        ('premium', 'متميز'),
        ('enterprise', 'مؤسسي'),
    )

    BILLING_CYCLE_CHOICES = (
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('semi_annual', 'نصف سنوي'),
        ('annual', 'سنوي'),
    )

    name = models.CharField(max_length=100, verbose_name='اسم الخطة')
    plan_type = models.CharField(max_length=20, choices=PLAN_TYPE_CHOICES, verbose_name='نوع الخطة')
    description = models.TextField(verbose_name='وصف الخطة')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر')
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES, verbose_name='دورة الفوترة')
    max_users = models.IntegerField(verbose_name='الحد الأقصى للمستخدمين')
    max_workers = models.IntegerField(verbose_name='الحد الأقصى للعمال')
    max_clients = models.IntegerField(verbose_name='الحد الأقصى للعملاء')
    storage_limit_gb = models.IntegerField(verbose_name='حد التخزين (جيجابايت)')
    trial_days = models.IntegerField(default=14, verbose_name='أيام الفترة التجريبية')
    features = models.JSONField(default=dict, verbose_name='الميزات المتاحة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.name} - {self.get_billing_cycle_display()}"

    def get_price_per_month(self):
        """حساب السعر الشهري"""
        if self.billing_cycle == 'monthly':
            return self.price
        elif self.billing_cycle == 'quarterly':
            return self.price / 3
        elif self.billing_cycle == 'semi_annual':
            return self.price / 6
        elif self.billing_cycle == 'annual':
            return self.price / 12
        return self.price

    class Meta:
        verbose_name = 'خطة اشتراك'
        verbose_name_plural = 'خطط الاشتراك'
        ordering = ['price']

class CompanySubscription(models.Model):
    """نموذج اشتراكات الشركات"""
    STATUS_CHOICES = (
        ('trial', 'فترة تجريبية'),
        ('active', 'نشط'),
        ('expired', 'منتهي الصلاحية'),
        ('suspended', 'معلق'),
        ('cancelled', 'ملغي'),
    )

    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='subscription', verbose_name='الشركة')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT, verbose_name='خطة الاشتراك')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='trial', verbose_name='حالة الاشتراك')
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ الانتهاء')
    trial_end_date = models.DateField(null=True, blank=True, verbose_name='تاريخ انتهاء الفترة التجريبية')
    auto_renewal = models.BooleanField(default=True, verbose_name='التجديد التلقائي')
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name='نسبة الخصم')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ الإجمالي')
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='المبلغ المدفوع')
    remaining_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='المبلغ المتبقي')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.company.name} - {self.plan.name}"

    def is_trial(self):
        """التحقق من كون الاشتراك في فترة تجريبية"""
        return self.status == 'trial' and self.trial_end_date and timezone.now().date() <= self.trial_end_date

    def is_active(self):
        """التحقق من كون الاشتراك نشط"""
        return self.status == 'active' and timezone.now().date() <= self.end_date

    def is_expired(self):
        """التحقق من انتهاء صلاحية الاشتراك"""
        return timezone.now().date() > self.end_date

    def days_remaining(self):
        """حساب الأيام المتبقية"""
        if self.is_trial():
            return (self.trial_end_date - timezone.now().date()).days
        return (self.end_date - timezone.now().date()).days

    def calculate_total_amount(self):
        """حساب المبلغ الإجمالي مع الخصم"""
        base_amount = self.plan.price
        discount_amount = base_amount * (self.discount_percentage / 100)
        return base_amount - discount_amount

    def save(self, *args, **kwargs):
        # حساب المبلغ الإجمالي
        self.total_amount = self.calculate_total_amount()
        self.remaining_amount = self.total_amount - self.paid_amount

        # تحديث حالة الشركة بناءً على حالة الاشتراك
        if self.status == 'trial':
            self.company.status = 'trial'
        elif self.status == 'active':
            self.company.status = 'active'
        elif self.status in ['expired', 'suspended', 'cancelled']:
            self.company.status = 'suspended'

        self.company.save()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = 'اشتراك شركة'
        verbose_name_plural = 'اشتراكات الشركات'
        ordering = ['-created_at']

class Payment(models.Model):
    """نموذج المدفوعات"""
    PAYMENT_METHOD_CHOICES = (
        ('bank_transfer', 'تحويل بنكي'),
        ('credit_card', 'بطاقة ائتمان'),
        ('cash', 'نقدي'),
        ('check', 'شيك'),
        ('online', 'دفع إلكتروني'),
    )

    STATUS_CHOICES = (
        ('pending', 'في الانتظار'),
        ('completed', 'مكتمل'),
        ('failed', 'فاشل'),
        ('cancelled', 'ملغي'),
        ('refunded', 'مسترد'),
    )

    subscription = models.ForeignKey(CompanySubscription, on_delete=models.CASCADE, related_name='payments', verbose_name='الاشتراك')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, verbose_name='طريقة الدفع')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='حالة الدفع')
    transaction_id = models.CharField(max_length=100, unique=True, verbose_name='رقم المعاملة')
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name='الرقم المرجعي')
    payment_date = models.DateTimeField(verbose_name='تاريخ الدفع')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    receipt_file = models.FileField(upload_to='receipts/', blank=True, null=True, verbose_name='إيصال الدفع')
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم المعالجة بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.subscription.company.name} - {self.amount} ريال"

    def generate_transaction_id(self):
        """توليد رقم معاملة فريد"""
        import uuid
        return f"PAY-{uuid.uuid4().hex[:8].upper()}"

    def save(self, *args, **kwargs):
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()

        # تحديث المبلغ المدفوع في الاشتراك عند اكتمال الدفع
        if self.status == 'completed' and self.pk:
            old_payment = Payment.objects.get(pk=self.pk)
            if old_payment.status != 'completed':
                self.subscription.paid_amount += self.amount
                self.subscription.remaining_amount = self.subscription.total_amount - self.subscription.paid_amount
                self.subscription.save()

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'المدفوعات'
        ordering = ['-payment_date']

class MaintenanceMode(models.Model):
    """نموذج وضع الصيانة"""
    is_active = models.BooleanField(default=False, verbose_name='وضع الصيانة نشط')
    title = models.CharField(max_length=200, default='النظام تحت الصيانة', verbose_name='عنوان الصيانة')
    message = models.TextField(default='نعتذر، النظام تحت الصيانة حالياً. سيعود للعمل قريباً.', verbose_name='رسالة الصيانة')
    estimated_completion = models.DateTimeField(null=True, blank=True, verbose_name='الوقت المتوقع للانتهاء')
    allowed_ips = models.TextField(blank=True, null=True, verbose_name='عناوين IP المسموح لها بالوصول')
    bypass_for_superusers = models.BooleanField(default=True, verbose_name='السماح للمسؤولين بالوصول')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    activated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم التفعيل بواسطة')

    def __str__(self):
        status = "نشط" if self.is_active else "غير نشط"
        return f"وضع الصيانة - {status}"

    @classmethod
    def get_current(cls):
        """الحصول على إعدادات وضع الصيانة الحالية"""
        maintenance, _ = cls.objects.get_or_create(pk=1)
        return maintenance

    def is_ip_allowed(self, ip_address):
        """التحقق من السماح لعنوان IP بالوصول أثناء الصيانة"""
        if not self.allowed_ips:
            return False

        allowed_ip_list = [ip.strip() for ip in self.allowed_ips.split(',')]
        return ip_address in allowed_ip_list

    class Meta:
        verbose_name = 'وضع الصيانة'
        verbose_name_plural = 'وضع الصيانة'

class Currency(models.Model):
    """نموذج العملات المدعومة"""
    CURRENCY_CHOICES = (
        ('IQD', 'الدينار العراقي'),
        ('USD', 'الدولار الأمريكي'),
    )

    code = models.CharField(max_length=3, choices=CURRENCY_CHOICES, unique=True, verbose_name='رمز العملة')
    name = models.CharField(max_length=50, verbose_name='اسم العملة')
    symbol = models.CharField(max_length=10, verbose_name='رمز العملة المختصر')
    symbol_position = models.CharField(
        max_length=10,
        choices=(('before', 'قبل الرقم'), ('after', 'بعد الرقم')),
        default='after',
        verbose_name='موضع رمز العملة'
    )
    decimal_places = models.IntegerField(default=0, verbose_name='عدد الخانات العشرية')
    thousands_separator = models.CharField(max_length=1, default=',', verbose_name='فاصل الآلاف')
    decimal_separator = models.CharField(max_length=1, default='.', verbose_name='فاصل العشرية')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    exchange_rate_to_usd = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=1.0000,
        verbose_name='سعر الصرف مقابل الدولار'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    def __str__(self):
        return f"{self.name} ({self.code})"

    def format_amount(self, amount):
        """تنسيق المبلغ حسب إعدادات العملة"""
        try:
            # تحويل المبلغ إلى رقم
            amount = float(amount)

            # تنسيق الرقم حسب الخانات العشرية
            if self.decimal_places > 0:
                formatted_amount = f"{amount:,.{self.decimal_places}f}"
            else:
                formatted_amount = f"{int(amount):,}"

            # استبدال فواصل التنسيق الافتراضية
            if self.thousands_separator != ',':
                formatted_amount = formatted_amount.replace(',', '|TEMP|')
                formatted_amount = formatted_amount.replace('.', self.decimal_separator)
                formatted_amount = formatted_amount.replace('|TEMP|', self.thousands_separator)
            elif self.decimal_separator != '.':
                formatted_amount = formatted_amount.replace('.', self.decimal_separator)

            # إضافة رمز العملة
            if self.symbol_position == 'before':
                return f"{self.symbol} {formatted_amount}"
            else:
                return f"{formatted_amount} {self.symbol}"

        except (ValueError, TypeError):
            return f"0 {self.symbol}"

    def convert_from_usd(self, usd_amount):
        """تحويل من الدولار إلى هذه العملة"""
        try:
            usd_amount = float(usd_amount)
            if self.code == 'USD':
                return usd_amount
            return usd_amount * float(self.exchange_rate_to_usd)
        except (ValueError, TypeError):
            return 0

    def convert_to_usd(self, amount):
        """تحويل من هذه العملة إلى الدولار"""
        try:
            amount = float(amount)
            if self.code == 'USD':
                return amount
            return amount / float(self.exchange_rate_to_usd)
        except (ValueError, TypeError, ZeroDivisionError):
            return 0

    @classmethod
    def get_default_currency(cls):
        """الحصول على العملة الافتراضية"""
        from .models import SystemSettings
        try:
            settings = SystemSettings.get_settings()
            return cls.objects.get(code=settings.default_currency)
        except (cls.DoesNotExist, AttributeError):
            # إرجاع الدينار العراقي كافتراضي
            return cls.objects.filter(code='IQD').first()

    @classmethod
    def get_active_currencies(cls):
        """الحصول على العملات النشطة"""
        return cls.objects.filter(is_active=True).order_by('name')

    class Meta:
        verbose_name = 'عملة'
        verbose_name_plural = 'العملات'
        ordering = ['name']
