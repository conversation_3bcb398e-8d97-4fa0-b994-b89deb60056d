{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}{{ client.name }} | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تفاصيل العميل</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                عرض بيانات العميل {{ client.name }}
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'clients:client_edit' client.id %}"
               class="bg-yellow-500 text-white dark:bg-yellow-600 dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-yellow-600 hover:shadow-md dark:hover:bg-yellow-700 transition-all duration-300 flex items-center">
                <i class="fas fa-edit ml-2 text-sm"></i>
                <span>تعديل</span>
            </a>
            <a href="{% url 'clients:client_list' %}"
               class="bg-gray-500 text-white dark:bg-gray-600 dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-gray-600 hover:shadow-md dark:hover:bg-gray-700 transition-all duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                <span>العودة للقائمة</span>
            </a>
        </div>
    </div>

    <!-- Client Info -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Main Info Card -->
        <div class="lg:col-span-2 bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
            <div class="border-b border-gray-200 dark:border-slate-700 p-4 bg-gray-50 dark:bg-slate-800 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white">المعلومات الأساسية</h3>
                <span class="px-3 py-1 rounded-full text-xs font-semibold
                    {% if client.client_type == 'permanent' %}
                        bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                    {% elif client.client_type == 'monthly' %}
                        bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                    {% else %}
                        bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                    {% endif %}">
                    {{ client.get_client_type_display }}
                </span>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">الاسم</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.name }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">رقم البطاقة الوطنية</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.national_id|default:"غير متوفر" }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">رقم الهاتف 1</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.phone_1 }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">رقم الهاتف 2</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.phone_2|default:"غير متوفر" }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">الحالة</h4>
                        <span class="px-2 py-1 rounded-full text-xs font-semibold
                            {% if client.status == 'active' %}
                                bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            {% elif client.status == 'inactive' %}
                                bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                            {% else %}
                                bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            {% endif %}">
                            {{ client.get_status_display }}
                        </span>
                    </div>

                    <div class="md:col-span-2">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">العنوان</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.address }}</p>
                    </div>

                    {% if client.notes %}
                    <div class="md:col-span-2">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">ملاحظات</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Status Card -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
            <div class="border-b border-gray-200 dark:border-slate-700 p-4 bg-gray-50 dark:bg-slate-800">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white">معلومات النظام</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">تاريخ الإضافة</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.created_at|date:"Y-m-d H:i" }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">آخر تحديث</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.updated_at|date:"Y-m-d H:i" }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">تمت الإضافة بواسطة</h4>
                        <p class="text-gray-800 dark:text-white">{{ client.created_by.get_full_name|default:client.created_by.username }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">عدد المستندات</h4>
                        <p class="text-gray-800 dark:text-white">{{ documents|length }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map for Custom Service Client -->
    {% if client.client_type == 'custom' and client.location_lat and client.location_lng %}
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700 mb-6">
        <div class="border-b border-gray-200 dark:border-slate-700 p-4 bg-gray-50 dark:bg-slate-800">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">موقع العميل</h3>
        </div>
        <div class="p-6">
            <div id="map" class="w-full h-96 rounded-lg"></div>
        </div>
    </div>
    {% endif %}

    <!-- Documents -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
        <div class="border-b border-gray-200 dark:border-slate-700 p-4 bg-gray-50 dark:bg-slate-800 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">المستندات</h3>
            <a href="{% url 'clients:client_document_add' client.id %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-300 flex items-center text-sm">
                <i class="fas fa-plus ml-2"></i>
                إضافة مستند
            </a>
        </div>

        {% if documents %}
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for document in documents %}
                <div class="border border-gray-200 dark:border-slate-700 rounded-lg overflow-hidden">
                    <div class="p-4 bg-gray-50 dark:bg-slate-800 flex justify-between items-center">
                        <h4 class="font-medium text-gray-700 dark:text-gray-300">{{ document.get_document_type_display }}</h4>
                        <div class="flex space-x-1 space-x-reverse">
                            <a href="{{ document.file.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ document.file.url }}" download class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300" title="تنزيل">
                                <i class="fas fa-download"></i>
                            </a>
                            <a href="{% url 'clients:client_document_delete' document.id %}" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300" title="حذف"
                               onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </div>
                    </div>

                    <div class="p-4">
                        {% if document.is_image %}
                        <div class="mb-3">
                            <img src="{{ document.file.url }}" alt="{{ document.get_document_type_display }}" class="w-full h-40 object-cover rounded">
                        </div>
                        {% elif document.is_pdf %}
                        <div class="mb-3 flex justify-center items-center h-40 bg-gray-100 dark:bg-slate-700 rounded">
                            <i class="fas fa-file-pdf text-red-500 text-4xl"></i>
                        </div>
                        {% else %}
                        <div class="mb-3 flex justify-center items-center h-40 bg-gray-100 dark:bg-slate-700 rounded">
                            <i class="fas fa-file text-gray-500 text-4xl"></i>
                        </div>
                        {% endif %}

                        {% if document.description %}
                        <div class="mb-2">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">الوصف</h5>
                            <p class="text-gray-800 dark:text-white">{{ document.description }}</p>
                        </div>
                        {% endif %}

                        {% if document.expiry_date %}
                        <div class="mb-2">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الانتهاء</h5>
                            <p class="text-gray-800 dark:text-white">{{ document.expiry_date|date:"Y-m-d" }}</p>

                            {% if document.days_until_expiry < 30 and document.days_until_expiry > 0 %}
                            <p class="text-yellow-600 dark:text-yellow-400 text-sm mt-1">
                                <i class="fas fa-exclamation-triangle ml-1"></i>
                                ينتهي خلال {{ document.days_until_expiry }} يوم
                            </p>
                            {% elif document.days_until_expiry <= 0 %}
                            <p class="text-red-600 dark:text-red-400 text-sm mt-1">
                                <i class="fas fa-exclamation-circle ml-1"></i>
                                منتهي الصلاحية
                            </p>
                            {% endif %}
                        </div>
                        {% endif %}

                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            تم الرفع: {{ document.uploaded_at|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="p-6 text-center">
            <div class="text-gray-400 dark:text-slate-500 mb-2">
                <i class="fas fa-file-alt text-5xl"></i>
            </div>
            <p class="text-gray-500 dark:text-slate-400">لا توجد مستندات مرفقة لهذا العميل</p>
            <p class="text-sm text-gray-400 dark:text-slate-500 mt-1">يمكنك إضافة مستندات باستخدام زر "إضافة مستند"</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if client.client_type == 'custom' and client.location_lat and client.location_lng %}
<!-- Include Leaflet for map -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة الخريطة
        const map = L.map('map', {
            zoomControl: true,
            attributionControl: false
        }).setView([{{ client.location_lat }}, {{ client.location_lng }}], 15);

        // إضافة طبقة الخريطة بتصميم مشابه لـ Waze
        L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // إضافة زر تكبير/تصغير مخصص
        map.zoomControl.setPosition('bottomright');

        // إضافة مقياس للخريطة
        L.control.scale({
            imperial: false,
            position: 'bottomleft'
        }).addTo(map);

        // إضافة علامة مخصصة تشبه علامات Waze مع تحسين الدقة
        const wazeIcon = L.divIcon({
            className: 'waze-marker',
            html: `<div style="background-color: #4285F4; width: 24px; height: 24px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-map-marker-alt" style="color: white; font-size: 12px;"></i>
                  </div>`,
            iconSize: [30, 30],
            iconAnchor: [15, 30] // تعديل نقطة الارتساء لتكون في أسفل العلامة بدلاً من وسطها
        });

        // إضافة العلامة
        const marker = L.marker([{{ client.location_lat }}, {{ client.location_lng }}], {
            icon: wazeIcon
        }).addTo(map)
            .bindPopup('{{ client.name }}');

        // إضافة دائرة حول الموقع
        const locationCircle = L.circle([{{ client.location_lat }}, {{ client.location_lng }}], {
            color: '#4285F4',
            fillColor: '#4285F4',
            fillOpacity: 0.1,
            radius: 500,
            weight: 2
        }).addTo(map);
    });
</script>
{% endif %}
{% endblock %}
