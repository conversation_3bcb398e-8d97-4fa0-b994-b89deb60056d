{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl" x-data="{ darkMode: false, mobileMenuOpen: false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="منصة استقدامي - نظام إدارة العمالة الحديث">
    <meta name="keywords" content="استقدام, عمالة, إدارة, شركات, حديث">
    <meta name="author" content="منصة استقدامي">

    <!-- تحسينات الأداء -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    <!-- خطوط حديثة -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- أيقونات حديثة -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- مكتبات أساسية -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- CSS الحديث -->
    <link rel="stylesheet" href="{% static 'css/professional-design.css' %}">

    <title>{% block title %}منصة استقدامي - نظام إدارة العمالة الحديث{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Meta Tags للشبكات الاجتماعية -->
    <meta property="og:title" content="منصة استقدامي الحديثة">
    <meta property="og:description" content="نظام إدارة العمالة الحديث والمتطور">
    <meta property="og:image" content="{% static 'images/og-image.jpg' %}">
    <meta property="og:type" content="website">

    <!-- PWA Support -->
    <meta name="theme-color" content="#2563eb">
    <link rel="manifest" href="{% static 'manifest.json' %}">

    <!-- تكوين Tailwind -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    },
                    animation: {
                        'fadeInUp': 'fadeInUp 0.8s ease-out',
                        'slideInRight': 'slideInRight 0.8s ease-out',
                        'slideInLeft': 'slideInLeft 0.8s ease-out',
                        'scaleIn': 'scaleIn 0.6s ease-out',
                        'bounce': 'bounce 1s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                        'shimmer': 'shimmer 2s infinite',
                        'glow': 'glow 2s ease-in-out infinite',
                    }
                }
            }
        }
    </script>

    <!-- CSS مخصص للحيوية -->
    <style>
        /* خلفية متدرجة حيوية */
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* تأثيرات الحركة المحسنة */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-item-modern {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .nav-item-modern::before {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            transition: width 0.3s ease;
        }

        .nav-item-modern:hover::before,
        .nav-item-modern.active::before {
            width: 100%;
        }

        .nav-item-modern:hover {
            transform: translateY(-2px);
            background: rgba(37, 99, 235, 0.05);
        }

        /* تأثيرات البطاقات المحسنة */
        .card-modern {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2563eb, #3b82f6, #06b6d4);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card-modern:hover::before {
            transform: scaleX(1);
        }

        .card-modern:hover {
            transform: translateY(-12px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        /* أزرار حيوية */
        .btn-modern {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .btn-modern:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        /* تأثيرات الأيقونات */
        .icon-bounce {
            transition: transform 0.3s ease;
        }

        .icon-bounce:hover {
            transform: scale(1.2) rotate(5deg);
        }

        /* شريط التقدم المحسن */
        .progress-modern {
            background: rgba(37, 99, 235, 0.1);
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        .progress-bar-modern {
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        /* تحسينات الوضع الليلي */
        .dark body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }

        .dark .glass-effect {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dark .card-modern {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* تأثيرات التحميل */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        /* أنماط شريط الأدوات المتطور */
        .nav-item-premium {
            @apply flex flex-col items-center px-3 py-2 rounded-xl text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-300 relative overflow-hidden group;
            min-width: 70px;
        }

        .nav-item-premium::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: all 0.3s ease;
            transform: translateX(-50%);
            border-radius: 1px;
        }

        .nav-item-premium:hover::before,
        .nav-item-premium.active::before {
            width: 80%;
        }

        .nav-item-premium.active {
            @apply text-primary-600 dark:text-primary-400 bg-gradient-to-br from-primary-50 to-blue-50 dark:from-primary-900/30 dark:to-blue-900/30;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .nav-item-premium:hover {
            @apply bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 transform -translate-y-1;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .nav-icon-premium {
            @apply w-7 h-7 flex items-center justify-center mb-1 text-base transition-transform duration-300;
        }

        .nav-item-premium:hover .nav-icon-premium {
            @apply transform scale-110;
        }

        .nav-text-premium {
            @apply text-xs font-medium transition-all duration-300;
        }

        .nav-item-premium:hover .nav-text-premium {
            @apply font-semibold;
        }

        /* أنماط التنقل الموسع */
        .nav-item-expanded {
            @apply flex flex-col items-center px-6 py-4 rounded-2xl text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-300 relative overflow-hidden group;
            min-width: 110px;
            flex: 1;
        }

        .nav-item-expanded::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-item-expanded:hover::before,
        .nav-item-expanded.active::before {
            width: 85%;
        }

        .nav-item-expanded.active {
            @apply text-primary-600 dark:text-primary-400 bg-gradient-to-br from-blue-50 via-purple-50 to-cyan-50 dark:from-blue-900/30 dark:via-purple-900/30 dark:to-cyan-900/30;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
            transform: translateY(-3px);
        }

        .nav-item-expanded.active .nav-icon-expanded {
            @apply shadow-2xl;
            transform: scale(1.05);
        }

        .nav-item-expanded:hover {
            @apply bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-700 dark:via-blue-900/20 dark:to-purple-900/20 transform -translate-y-3;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .nav-icon-expanded {
            @apply w-14 h-14 flex items-center justify-center mb-3 text-2xl transition-all duration-500 shadow-lg;
        }

        .nav-item-expanded:hover .nav-icon-expanded {
            @apply transform scale-110 -rotate-6 shadow-2xl;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0) scale(1.1) rotate(-6deg);
            }
            40% {
                transform: translateY(-10px) scale(1.15) rotate(-8deg);
            }
            60% {
                transform: translateY(-5px) scale(1.12) rotate(-4deg);
            }
        }

        .nav-text-expanded {
            @apply text-base font-medium transition-all duration-300;
        }

        .nav-item-expanded:hover .nav-text-expanded {
            @apply font-bold text-lg;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-item-expanded.active .nav-text-expanded {
            @apply font-bold;
            text-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        /* أنماط التنقل المضمن */
        .nav-item-inline {
            @apply transition-all duration-300 hover:scale-105;
        }

        .nav-pill {
            @apply flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-300;
            min-width: 100px;
            backdrop-filter: blur(10px);
        }

        .nav-pill i {
            @apply text-sm;
        }

        .nav-pill span {
            @apply text-xs font-semibold;
        }

        .nav-item-inline:hover .nav-pill {
            @apply transform scale-105 shadow-lg;
            filter: brightness(1.05);
        }

        .nav-item-inline.active .nav-pill {
            @apply shadow-lg;
            transform: scale(1.02);
            filter: brightness(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* أنماط التنقل المبسط */
        .nav-simple {
            @apply flex items-center space-x-1.5 space-x-reverse px-3 py-2 rounded-full text-gray-600 hover:text-blue-600 transition-all duration-300 text-sm font-medium;
            position: relative;
        }

        .nav-simple i {
            @apply text-xs;
        }

        .nav-simple span {
            @apply text-xs font-medium;
        }

        .nav-simple:hover {
            @apply bg-blue-50 text-blue-600 transform scale-105;
        }

        .nav-simple.active {
            @apply bg-blue-600 text-white shadow-md;
        }

        .nav-simple.active:hover {
            @apply bg-blue-700;
        }

        /* أنماط التنقل المتطور */
        .nav-modern {
            @apply flex flex-col items-center justify-center px-4 py-3 rounded-xl text-gray-600 hover:text-blue-600 transition-all duration-300 relative overflow-hidden group cursor-pointer;
            min-width: 80px;
            position: relative;
        }

        .nav-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 0.75rem;
        }

        .nav-modern:hover::before {
            opacity: 1;
        }

        .nav-icon {
            @apply w-8 h-8 flex items-center justify-center rounded-lg mb-1 transition-all duration-300 relative z-10;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        }

        .nav-modern:hover .nav-icon {
            @apply transform scale-110;
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .nav-icon i {
            @apply text-sm transition-all duration-300;
        }

        .nav-modern:hover .nav-icon i {
            @apply text-blue-600;
        }

        .nav-label {
            @apply text-xs font-medium transition-all duration-300 relative z-10;
        }

        .nav-modern:hover .nav-label {
            @apply text-blue-600 font-semibold;
        }

        .nav-modern.active {
            @apply text-white;
        }

        .nav-modern.active::before {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            opacity: 1;
        }

        .nav-modern.active .nav-icon {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .nav-modern.active .nav-icon i {
            @apply text-white;
        }

        .nav-modern.active .nav-label {
            @apply text-white font-semibold;
        }

        .nav-modern.active:hover .nav-icon {
            @apply transform scale-110;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
        }

        /* أنماط شريط الأدوات المحسن */
        .nav-link {
            @apply flex items-center gap-4 px-4 py-2.5 rounded-xl text-white hover:text-white transition-all duration-300 relative;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
            border: 1px solid rgba(255,255,255,0.1);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(34,197,94,0.3), rgba(22,163,74,0.2));
            opacity: 0;
            transition: all 0.3s ease;
            border-radius: 0.75rem;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 80%;
        }

        .nav-link i {
            @apply text-sm;
            position: relative;
            z-index: 2;
        }

        .nav-link span {
            @apply text-sm font-bold text-white;
            position: relative;
            z-index: 2;
        }

        .nav-link.active {
            @apply text-white;
            background: linear-gradient(135deg, rgba(34,197,94,0.4), rgba(22,163,74,0.3));
            border: 1px solid rgba(34,197,94,0.5);
            box-shadow: 0 4px 20px rgba(34,197,94,0.3), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .nav-link.active::after {
            width: 80%;
        }

        .nav-link:hover {
            @apply transform scale-105;
            box-shadow: 0 6px 25px rgba(34,197,94,0.2);
        }

        .toolbar-icon {
            @apply w-10 h-10 flex items-center justify-center rounded-xl text-white/70 hover:text-white transition-all duration-300 relative;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03));
            border: 1px solid rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .toolbar-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(124,58,237,0.4), rgba(147,51,234,0.2), transparent);
            opacity: 0;
            transition: all 0.3s ease;
            border-radius: 0.75rem;
        }

        .toolbar-icon:hover::before {
            opacity: 1;
        }

        .toolbar-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .toolbar-icon:hover::after {
            transform: translateX(100%);
        }

        .toolbar-icon i {
            @apply text-sm;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .toolbar-icon:hover {
            @apply transform scale-110;
            box-shadow: 0 8px 25px rgba(124,58,237,0.3), 0 4px 12px rgba(0,0,0,0.2);
        }

        .toolbar-icon:hover i {
            transform: scale(1.1);
        }

        /* تأثير النبض للإشعارات */
        .notification-pulse {
            animation: notification-pulse 2s infinite;
        }

        @keyframes notification-pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
            }
        }

        /* تأثيرات إضافية للشريط */
        nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        nav::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0,0,0,0.2), transparent);
        }

        /* تأثير الضوء المتحرك */
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .shimmer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        .toolbar-btn {
            @apply p-3 rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 hover-lift text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400;
        }

        .toolbar-btn:hover {
            @apply shadow-lg border-primary-200 dark:border-primary-700;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .hover-lift:hover {
                transform: translateY(-4px) scale(1.01);
            }

            .card-modern:hover {
                transform: translateY(-6px);
            }

            .nav-item-enhanced {
                min-width: 60px;
                padding: 0.5rem;
            }

            .nav-item-enhanced span {
                display: none;
            }
        }

        @media (max-width: 1280px) {
            .nav-item-enhanced span {
                font-size: 0.65rem;
            }
        }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- شريط التنقل العلوي المحسن -->
    <nav class="w-full bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 text-white shadow-2xl px-6 py-3 sticky top-0 z-50 backdrop-blur-md border-b border-white/10" style="background: linear-gradient(135deg, #0f172a 0%, #581c87 25%, #7c3aed 50%, #581c87 75%, #0f172a 100%); box-shadow: 0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1);">
        <div class="flex items-center justify-between max-w-full relative">
            <!-- يسار الشريط - الشعار واسم الشركة -->
            <div class="flex items-center gap-4">
                <div class="relative group">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-110 hover:rotate-6 border border-white/20">
                        <i class="fas fa-crown text-white text-xl drop-shadow-lg"></i>
                    </div>
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse border-2 border-white shadow-lg"></div>
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-400/50 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="text-white">
                    <div class="text-xl font-black bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent drop-shadow-lg">
                        منصة استقدامي
                    </div>
                    {% if company %}
                        <div class="text-sm text-purple-200 font-semibold tracking-wide">{{ company.name }}</div>
                    {% else %}
                        <div class="text-sm text-purple-200 font-semibold tracking-wide">نظام إدارة العمالة المتطور</div>
                    {% endif %}
                </div>
                <div class="relative">
                    <span class="px-4 py-1.5 text-xs text-white bg-gradient-to-r from-amber-500 via-yellow-500 to-amber-500 rounded-full font-black shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border border-yellow-300/30 relative overflow-hidden">
                        <span class="relative z-10">PRO</span>
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </span>
                </div>
            </div>

            <!-- وسط الشريط - التنقل الأفقي -->
            <div class="hidden lg:flex items-center gap-4 text-sm font-semibold">
                <a href="/company/dashboard/" class="nav-link active" data-tooltip="لوحة التحكم الرئيسية">
                    <i class="fas fa-home text-amber-400 ml-2"></i>
                    <span class="text-white font-bold">الرئيسية</span>
                </a>
                <a href="/company/workers/" class="nav-link" data-tooltip="إدارة العمال">
                    <i class="fas fa-hard-hat text-emerald-400 ml-2"></i>
                    <span class="text-white font-bold">العمال</span>
                </a>
                <a href="/company/clients/" class="nav-link" data-tooltip="إدارة العملاء">
                    <i class="fas fa-user-tie text-blue-400 ml-2"></i>
                    <span class="text-white font-bold">العملاء</span>
                </a>
                <a href="/company/contracts/" class="nav-link" data-tooltip="إدارة العقود">
                    <i class="fas fa-file-contract text-purple-400 ml-2"></i>
                    <span class="text-white font-bold">العقود</span>
                </a>
                <a href="/company/procedures/" class="nav-link" data-tooltip="الإجراءات والعمليات">
                    <i class="fas fa-list-check text-indigo-400 ml-2"></i>
                    <span class="text-white font-bold">الإجراءات</span>
                </a>
                <a href="/company/services/" class="nav-link" data-tooltip="الخدمات المقدمة">
                    <i class="fas fa-hands-helping text-orange-400 ml-2"></i>
                    <span class="text-white font-bold">الخدمات</span>
                </a>
                <a href="/company/finance/" class="nav-link" data-tooltip="الإدارة المالية">
                    <i class="fas fa-coins text-green-400 ml-2"></i>
                    <span class="text-white font-bold">المالية</span>
                </a>
                <a href="/company/reports/" class="nav-link" data-tooltip="التقارير والتحليلات">
                    <i class="fas fa-chart-bar text-cyan-400 ml-2"></i>
                    <span class="text-white font-bold">التقارير</span>
                </a>
                <a href="/company/tools/" class="nav-link" data-tooltip="الأدوات المساعدة">
                    <i class="fas fa-wrench text-pink-400 ml-2"></i>
                    <span class="text-white font-bold">الأدوات</span>
                </a>
            </div>
            <!-- يمين الشريط - الأدوات -->
            <div class="flex items-center gap-3">
                <!-- زر البحث -->
                <button class="toolbar-icon group" data-tooltip="البحث الذكي" onclick="toggleSearch()">
                    <i class="fas fa-search group-hover:text-cyan-300 transition-colors duration-300"></i>
                </button>

                <!-- زر الإضافة -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="toolbar-icon bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white shadow-lg hover:shadow-xl border-emerald-400/30" data-tooltip="إضافة سريع">
                        <i class="fas fa-plus"></i>
                    </button>
                    <div x-show="open" @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-56 bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-white/20 py-3 z-50">
                        <div class="px-4 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider border-b border-gray-100">إضافة سريعة</div>
                        <a href="/company/workers/create" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-emerald-50 hover:text-emerald-700 transition-all">
                            <i class="fas fa-hard-hat mr-3 text-emerald-500"></i>إضافة عامل
                        </a>
                        <a href="/company/clients/create" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all">
                            <i class="fas fa-user-tie mr-3 text-blue-500"></i>إضافة عميل
                        </a>
                        <a href="/company/contracts/create" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-all">
                            <i class="fas fa-file-contract mr-3 text-purple-500"></i>إنشاء عقد
                        </a>
                        <a href="/company/services/create" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all">
                            <i class="fas fa-hands-helping mr-3 text-orange-500"></i>إضافة خدمة
                        </a>
                    </div>
                </div>

                <!-- الإشعارات -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="toolbar-icon relative group" data-tooltip="الإشعارات">
                        <i class="fas fa-bell group-hover:text-amber-300 transition-colors duration-300"></i>
                        <span class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full notification-pulse shadow-xl border border-red-300/50">3</span>
                    </button>
                    <div x-show="open" @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-80 bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-white/20 py-3 z-50">
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-bold text-gray-900 flex items-center">
                                    <i class="fas fa-bell text-amber-500 mr-2"></i>الإشعارات
                                </h3>
                                <span class="text-sm text-red-600 font-bold bg-red-50 px-2 py-1 rounded-full">3 جديدة</span>
                            </div>
                        </div>
                        <div class="max-h-80 overflow-y-auto">
                            <div class="p-3 space-y-3">
                                <div class="flex items-start gap-3 p-3 rounded-xl bg-emerald-50 border border-emerald-200 hover:bg-emerald-100 transition-all">
                                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-hard-hat text-white"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-bold text-gray-900">عامل جديد</p>
                                        <p class="text-xs text-gray-600">تم إضافة أحمد محمد بنجاح</p>
                                        <p class="text-xs text-gray-500 mt-1">منذ 5 دقائق</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3 p-3 rounded-xl bg-purple-50 border border-purple-200 hover:bg-purple-100 transition-all">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-file-contract text-white"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-bold text-gray-900">عقد جديد</p>
                                        <p class="text-xs text-gray-600">تم إنشاء عقد رقم #2024001</p>
                                        <p class="text-xs text-gray-500 mt-1">منذ 15 دقيقة</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3 p-3 rounded-xl bg-amber-50 border border-amber-200 hover:bg-amber-100 transition-all">
                                    <div class="w-10 h-10 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-clock text-white"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-bold text-gray-900">تذكير مهم</p>
                                        <p class="text-xs text-gray-600">انتهاء صلاحية عقد قريباً</p>
                                        <p class="text-xs text-gray-500 mt-1">منذ ساعة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="border-t border-gray-100 p-3">
                            <a href="/notifications" class="block text-center text-sm text-blue-600 hover:text-blue-800 font-bold transition-colors">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الوضع الليلي -->
                <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)" class="toolbar-icon group" data-tooltip="الوضع الليلي">
                    <i class="fas fa-moon dark:hidden group-hover:text-indigo-300 transition-all duration-300 group-hover:rotate-12"></i>
                    <i class="fas fa-sun hidden dark:block text-yellow-400 group-hover:text-yellow-200 transition-all duration-300 group-hover:rotate-180"></i>
                </button>

                <!-- الإعدادات -->
                <button class="toolbar-icon group" data-tooltip="الإعدادات" onclick="window.location.href='/company/settings/'">
                    <i class="fas fa-cog group-hover:text-slate-300 transition-all duration-300 group-hover:rotate-180"></i>
                </button>

                <!-- صورة الحساب -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="w-11 h-11 rounded-full border-2 border-gradient-to-r from-purple-400 to-pink-400 overflow-hidden hover:border-white transition-all duration-300 hover:scale-110 hover:shadow-2xl relative group">
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <img src="{% static 'images/default-avatar.png' %}" alt="User" class="w-full h-full object-cover relative z-10" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                        <div class="w-full h-full bg-gradient-to-br from-purple-500 via-purple-600 to-pink-500 backdrop-blur-sm flex items-center justify-center relative z-10" style="display: none;">
                            <i class="fas fa-user text-white text-lg drop-shadow-lg"></i>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-lg"></div>
                    </button>

                    <div x-show="open" @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        <div class="px-4 py-2 border-b border-gray-200">
                            <div class="text-sm font-medium text-gray-900">مدير النظام</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                        <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user mr-2"></i>حسابي
                        </a>
                        <a href="/change-password" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-key mr-2"></i>تغيير كلمة المرور
                        </a>
                        <hr class="my-1">
                        <a href="/logout" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                            <i class="fas fa-sign-out-alt mr-2"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
                <!-- زر القائمة للشاشات الصغيرة -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="lg:hidden toolbar-icon">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- القائمة المحمولة -->
            <div x-show="mobileMenuOpen" x-transition class="lg:hidden py-4 border-t border-white/20 bg-black/20 backdrop-blur-md">
                <div class="space-y-2 px-4">
                    <a href="/company/dashboard/" class="flex items-center px-4 py-3 rounded-xl bg-white/20 text-white font-bold">
                        <i class="fas fa-home mr-3 text-amber-400"></i>
                        <span class="text-white font-bold">الرئيسية</span>
                    </a>
                    <a href="/company/workers/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-hard-hat mr-3 text-emerald-400"></i>
                        <span class="text-white font-semibold">العمال</span>
                    </a>
                    <a href="/company/clients/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-user-tie mr-3 text-blue-400"></i>
                        <span class="text-white font-semibold">العملاء</span>
                    </a>
                    <a href="/company/contracts/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-file-contract mr-3 text-purple-400"></i>
                        <span class="text-white font-semibold">العقود</span>
                    </a>
                    <a href="/company/procedures/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-list-check mr-3 text-indigo-400"></i>
                        <span class="text-white font-semibold">الإجراءات</span>
                    </a>
                    <a href="/company/services/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-hands-helping mr-3 text-orange-400"></i>
                        <span class="text-white font-semibold">الخدمات</span>
                    </a>
                    <a href="/company/finance/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-coins mr-3 text-green-400"></i>
                        <span class="text-white font-semibold">المالية</span>
                    </a>
                    <a href="/company/reports/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-chart-bar mr-3 text-cyan-400"></i>
                        <span class="text-white font-semibold">التقارير</span>
                    </a>
                    <a href="/company/tools/" class="flex items-center px-4 py-3 rounded-xl text-white hover:bg-white/10 transition-all">
                        <i class="fas fa-wrench mr-3 text-pink-400"></i>
                        <span class="text-white font-semibold">الأدوات</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen">
        {% block content %}{% endblock %}
    </main>

    <!-- تذييل الصفحة الحديث -->
    {% include 'components/footer.html' %}

    <!-- JavaScript -->
    <script>
        // إعداد SweetAlert2 الحديث
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true,
            background: 'rgba(255, 255, 255, 0.95)',
            backdrop: 'rgba(0, 0, 0, 0.1)',
            customClass: {
                popup: 'rounded-xl shadow-xl border border-gray-200'
            },
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        window.showSuccess = (message) => Toast.fire({
            icon: 'success',
            title: message,
            iconColor: '#10b981'
        });

        window.showError = (message) => Toast.fire({
            icon: 'error',
            title: message,
            iconColor: '#ef4444'
        });

        window.showWarning = (message) => Toast.fire({
            icon: 'warning',
            title: message,
            iconColor: '#f59e0b'
        });

        window.showInfo = (message) => Toast.fire({
            icon: 'info',
            title: message,
            iconColor: '#3b82f6'
        });

        // تأثيرات الحركة عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fadeInUp');
                }
            });
        }, observerOptions);

        // مراقبة العناصر للحركة
        document.addEventListener('DOMContentLoaded', () => {
            const animatedElements = document.querySelectorAll('.card-modern, .stat-card');
            animatedElements.forEach(el => observer.observe(el));

            // تحميل الوضع الليلي من localStorage
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode === 'true') {
                document.documentElement.classList.add('dark');
            }
        });

        // وظيفة تبديل البحث المحسنة
        function toggleSearch() {
            Swal.fire({
                title: '<i class="fas fa-search text-blue-500"></i> البحث الذكي في النظام',
                html: `
                    <div class="text-right">
                        <input type="text" id="search-input" class="w-full p-3 border border-gray-300 rounded-lg text-right"
                               placeholder="ابحث عن العمال، العملاء، العقود، الخدمات..."
                               style="direction: rtl;">
                        <div class="mt-3 text-sm text-gray-600">
                            <div class="flex justify-center gap-4 flex-wrap">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">العمال</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded">العملاء</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">العقود</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded">الخدمات</span>
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-search"></i> بحث',
                cancelButtonText: '<i class="fas fa-times"></i> إلغاء',
                confirmButtonColor: '#3b82f6',
                cancelButtonColor: '#ef4444',
                customClass: {
                    popup: 'rounded-xl',
                    title: 'text-lg font-bold',
                    confirmButton: 'px-6 py-2 rounded-lg',
                    cancelButton: 'px-6 py-2 rounded-lg'
                },
                preConfirm: () => {
                    const searchValue = document.getElementById('search-input').value;
                    if (!searchValue) {
                        Swal.showValidationMessage('يرجى إدخال كلمة البحث');
                        return false;
                    }
                    return searchValue;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // تنفيذ البحث مع تأثير تحميل
                    Swal.fire({
                        title: 'جاري البحث...',
                        html: '<i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i>',
                        showConfirmButton: false,
                        timer: 1000
                    }).then(() => {
                        window.location.href = `/search?q=${encodeURIComponent(result.value)}`;
                    });
                }
            });

            // التركيز على حقل البحث عند فتح النافذة
            setTimeout(() => {
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
