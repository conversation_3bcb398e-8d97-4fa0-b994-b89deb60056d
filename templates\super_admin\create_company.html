{% extends 'super_admin/base.html' %}

{% block title %}إنشاء شركة جديدة | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    /* تحسين تباين النصوص والعناوين */
    h2.page-title {
        font-size: 2.25rem;
        color: #1e40af;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    /* تحسين مظهر الملاحظات */
    .note-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }

    .note-icon {
        margin-left: 0.5rem;
        color: #1e40af;
        flex-shrink: 0;
    }

    /* تحسين مظهر الحقول المطلوبة */
    .required-field label::after {
        content: " *";
        color: #ef4444;
    }

    /* تحسين مظهر رسائل الخطأ */
    .toast-message {
        position: fixed;
        top: 1rem;
        left: 1rem;
        right: 1rem;
        z-index: 50;
        transition: all 0.3s ease;
        transform: translateY(-100%);
        opacity: 0;
    }

    .toast-message.show {
        transform: translateY(0);
        opacity: 1;
    }

    /* تحسين مظهر زر التحميل */
    .btn-loading {
        position: relative;
    }

    .btn-loading .spinner {
        display: none;
    }

    .btn-loading.loading .spinner {
        display: inline-block;
    }

    .btn-loading.loading .btn-text {
        visibility: hidden;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-5xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex flex-col md:flex-row justify-between items-center gap-4">
        <h2 class="text-4xl font-bold text-blue-800 page-title">إنشاء شركة جديدة</h2>
        <a href="{% url 'super_admin:dashboard' %}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-right ml-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <!-- رسائل النظام -->
    <div id="toast-container" class="toast-message">
        {% if messages %}
            {% for message in messages %}
                <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg shadow-md relative mb-4 show" role="alert">
                    <span class="block sm:inline">{{ message }}</span>
                    <button type="button" class="absolute top-0 bottom-0 left-0 px-4 py-3 close-toast">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div class="bg-gray-50 p-6 rounded-xl border border-gray-200 shadow-sm space-y-6">
        <form method="post" id="company-form" class="space-y-8">
            {% csrf_token %}
            <input type="hidden" name="create_company" value="1">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <!-- الرقم التسلسلي -->
                <div class="space-y-2 col-span-2">
                    <label for="{{ form.serial_number_preview.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.serial_number_preview.label }}</label>
                    <div class="flex items-center gap-2">
                        {{ form.serial_number_preview }}
                        <button type="button" id="generate-serial" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md transition-colors duration-200 flex items-center">
                            <i class="fas fa-sync-alt mr-1"></i> توليد رقم جديد
                        </button>
                    </div>
                    <div class="mt-2 bg-blue-50 p-3 rounded-lg border border-blue-100 text-sm">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-1 ml-2"></i>
                            <div>
                                <p class="text-blue-800 font-medium">معلومات عن الرقم التسلسلي</p>
                                <p class="text-blue-600 mt-1">الرقم التسلسلي هو معرف فريد مكون من 16 حرف ورقم، يستخدم للتعرف على الشركة في النظام. يتم توليده تلقائيًا ويمكن استخدامه لاحقًا لتسجيل الدخول إلى النظام.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اسم الشركة -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.name.label }}</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- اسم قاعدة البيانات -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.database_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.database_name.label }}</label>
                    {{ form.database_name }}
                    {% if form.database_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.database_name.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- مستخدم قاعدة البيانات -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.database_user.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.database_user.label }}</label>
                    {{ form.database_user }}
                    {% if form.database_user.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.database_user.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- كلمة مرور قاعدة البيانات -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.database_password.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.database_password.label }}</label>
                    {{ form.database_password }}
                    {% if form.database_password.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.database_password.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- مضيف قاعدة البيانات -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.database_host.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.database_host.label }}</label>
                    <div class="flex items-center gap-2">
                        {{ form.database_host }}
                        <button type="button" id="test-connection" class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md transition-colors duration-200 flex items-center">
                            <i class="fas fa-database"></i>
                        </button>
                    </div>
                    <div id="connection-result" class="text-xs mt-1 hidden"></div>
                </div>

                <!-- حالة الشركة -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.status.label }}</label>
                    {{ form.status }}
                    {% if form.status.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.status.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- تاريخ انتهاء الاشتراك -->
                <div class="space-y-2 col-span-1">
                    <label for="{{ form.expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.expiry_date.label }}</label>
                    {{ form.expiry_date }}
                    {% if form.expiry_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.expiry_date.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <div class="pt-4 border-t border-gray-200 flex justify-end">
                <button type="submit" id="submit-btn" class="btn-loading flex items-center justify-center py-2 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <span class="spinner mr-2">
                        <i class="fas fa-circle-notch fa-spin"></i>
                    </span>
                    <span class="btn-text">
                        <i class="fas fa-plus ml-1"></i> إنشاء الشركة
                    </span>
                </button>
            </div>
        </form>
    </div>

    <div class="bg-blue-50 p-6 rounded-xl border border-blue-200 shadow-sm">
        <h3 class="text-xl font-semibold text-blue-800 mb-4">ملاحظات هامة</h3>
        <div class="space-y-3">
            <div class="note-item">
                <i class="fas fa-key note-icon"></i>
                <span>سيتم توليد رقم تسلسلي فريد للشركة تلقائيًا، ويمكنك إعادة توليده بالضغط على زر التحديث</span>
            </div>
            <div class="note-item">
                <i class="fas fa-database note-icon"></i>
                <span>تأكد من صحة بيانات قاعدة البيانات قبل الإنشاء باستخدام زر اختبار الاتصال</span>
            </div>
            <div class="note-item">
                <i class="fas fa-edit note-icon"></i>
                <span>يمكنك تعديل بيانات الشركة لاحقًا من صفحة تفاصيل الشركة</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار رسائل النظام
        const toastMessages = document.querySelectorAll('.toast-message > div');
        toastMessages.forEach(function(toast) {
            toast.classList.add('show');
            setTimeout(function() {
                toast.classList.remove('show');
            }, 5000);
        });

        // إغلاق رسائل النظام
        document.querySelectorAll('.close-toast').forEach(function(btn) {
            btn.addEventListener('click', function() {
                this.parentElement.classList.remove('show');
            });
        });

        // توليد رقم تسلسلي جديد
        document.getElementById('generate-serial').addEventListener('click', function() {
            fetch('?generate_serial=1', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('serial_number_preview').value = data.serial_number;
            });
        });

        // اختبار الاتصال بقاعدة البيانات
        document.getElementById('test-connection').addEventListener('click', function() {
            const dbName = document.getElementById('id_database_name').value;
            const dbUser = document.getElementById('id_database_user').value;
            const dbPassword = document.getElementById('id_database_password').value;
            const dbHost = document.getElementById('id_database_host').value;

            const resultDiv = document.getElementById('connection-result');
            resultDiv.classList.remove('hidden', 'text-green-500', 'text-red-500');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.classList.add('text-gray-500');

            const formData = new FormData();
            formData.append('test_connection', '1');
            formData.append('database_name', dbName);
            formData.append('database_user', dbUser);
            formData.append('database_password', dbPassword);
            formData.append('database_host', dbHost);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.classList.remove('text-gray-500');
                if (data.success) {
                    resultDiv.textContent = data.message;
                    resultDiv.classList.add('text-green-500');
                } else {
                    resultDiv.textContent = data.message;
                    resultDiv.classList.add('text-red-500');
                }
            })
            .catch(error => {
                resultDiv.textContent = 'حدث خطأ أثناء الاختبار';
                resultDiv.classList.add('text-red-500');
            });
        });

        // إضافة تأثير التحميل عند إرسال النموذج
        document.getElementById('company-form').addEventListener('submit', function() {
            document.getElementById('submit-btn').classList.add('loading');
        });
    });
</script>
{% endblock %}
