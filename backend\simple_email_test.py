#!/usr/bin/env python
"""
اختبار بسيط لإعدادات البريد الإلكتروني بدون Django
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultip<PERSON>

def test_gmail_smtp():
    """اختبار الاتصال بخادم Gmail SMTP"""
    
    # إعدادات البريد الإلكتروني
    EMAIL_HOST = 'smtp.gmail.com'
    EMAIL_PORT = 587
    EMAIL_HOST_USER = '<EMAIL>'
    EMAIL_HOST_PASSWORD = 'svtp dksv utqp nphc'
    
    print("🔧 اختبار الاتصال بخادم Gmail SMTP...")
    print(f"HOST: {EMAIL_HOST}")
    print(f"PORT: {EMAIL_PORT}")
    print(f"USER: {EMAIL_HOST_USER}")
    print(f"PASSWORD: {'*' * len(EMAIL_HOST_PASSWORD)}")
    
    try:
        # إنشاء اتصال SMTP
        print("\n📡 محاولة الاتصال بالخادم...")
        server = smtplib.SMTP(EMAIL_HOST, EMAIL_PORT)
        
        # تفعيل TLS
        print("🔐 تفعيل TLS...")
        server.starttls()
        
        # تسجيل الدخول
        print("🔑 محاولة تسجيل الدخول...")
        server.login(EMAIL_HOST_USER, EMAIL_HOST_PASSWORD)
        
        print("✅ نجح الاتصال وتسجيل الدخول!")
        
        # إرسال رسالة اختبار
        print("\n📧 إرسال رسالة اختبار...")
        
        # إنشاء الرسالة
        msg = MIMEMultipart()
        msg['From'] = EMAIL_HOST_USER
        msg['To'] = EMAIL_HOST_USER
        msg['Subject'] = 'اختبار البريد الإلكتروني - منصة استقدامي'
        
        # محتوى الرسالة
        body = """
        مرحباً،
        
        هذه رسالة اختبار من منصة استقدامي السحابية.
        
        إذا وصلتك هذه الرسالة، فإن إعدادات البريد الإلكتروني تعمل بشكل صحيح.
        
        شكراً لك،
        فريق منصة استقدامي
        """
        
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # إرسال الرسالة
        text = msg.as_string()
        server.sendmail(EMAIL_HOST_USER, [EMAIL_HOST_USER], text)
        
        print("✅ تم إرسال الرسالة بنجاح!")
        
        # إغلاق الاتصال
        server.quit()
        print("🔚 تم إغلاق الاتصال")
        
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ خطأ في المصادقة: {str(e)}")
        print("💡 تأكد من:")
        print("   - صحة البريد الإلكتروني وكلمة المرور")
        print("   - تفعيل المصادقة الثنائية في Google")
        print("   - استخدام كلمة مرور التطبيق وليس كلمة المرور العادية")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        print("💡 تأكد من:")
        print("   - الاتصال بالإنترنت")
        print("   - عدم حجب المنافذ من قبل الجدار الناري")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إعدادات البريد الإلكتروني...\n")
    
    success = test_gmail_smtp()
    
    print(f"\n📊 نتيجة الاختبار:")
    if success:
        print("🎉 جميع الاختبارات نجحت! إعدادات البريد الإلكتروني تعمل بشكل صحيح.")
        print("📧 تحقق من صندوق الوارد في: <EMAIL>")
    else:
        print("⚠️ هناك مشاكل في إعدادات البريد الإلكتروني تحتاج إلى إصلاح.")
