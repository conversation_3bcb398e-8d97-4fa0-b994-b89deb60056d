# دليل الصيانة لنظام إدارة العمالة

## مقدمة

هذا الدليل يشرح هيكل المشروع وكيفية الوصول إلى الملفات المهمة لتسهيل عمليات الصيانة والتطوير.

## هيكل المشروع

```
labor_management/
├── api/                    # واجهة برمجة التطبيقات (API)
│   ├── contracts/          # API للعقود
│   ├── documents/          # API للمستندات
│   ├── procedures/         # API للإجراءات
│   ├── services/           # API للخدمات
│   ├── settings/           # API للإعدادات
│   └── workers/            # API للعمال
├── accounts/               # إدارة الحسابات والمصادقة
├── backups/                # مجلد النسخ الاحتياطية
├── company_dbs/            # قواعد بيانات الشركات
├── config/                 # ملفات التكوين
├── contracts/              # تطبيق العقود
├── dashboard/              # تطبيق لوحة التحكم
├── documents/              # تطبيق المستندات
├── frontend/               # واجهة المستخدم الرئيسية (React)
│   ├── public/             # الملفات العامة
│   └── src/                # كود المصدر
├── labor_management/       # التطبيق الرئيسي
├── media/                  # ملفات الوسائط المرفوعة
├── procedures/             # تطبيق الإجراءات
├── react-template-editor/  # محرر القوالب (React)
├── reports/                # تطبيق التقارير
├── scanner/                # تطبيق الماسح الضوئي
├── services/               # تطبيق الخدمات
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   ├── images/             # الصور
│   └── dwt-resources/      # موارد Dynamic Web TWAIN
├── super_admin/            # تطبيق المسؤول الأعلى
├── system_settings/        # تطبيق إعدادات النظام
├── templates/              # قوالب HTML
├── workers/                # تطبيق العمال
└── venv/                   # البيئة الافتراضية
```

## الملفات الرئيسية

### ملفات التكوين

- `labor_management/settings.py`: إعدادات Django الرئيسية
- `labor_management/urls.py`: مسارات URL الرئيسية
- `config/database_custom.json`: تكوين قواعد بيانات الشركات
- `tailwind.config.js`: تكوين Tailwind CSS

### نصوص التشغيل

- `start_server.bat`: تشغيل خادم Django
- `manage.py`: أداة إدارة Django

## تطبيقات النظام

### 1. المسؤول الأعلى (super_admin)

هذا التطبيق مخصص لمنشئ النظام فقط ويوفر:
- إدارة الشركات
- إنشاء قواعد بيانات جديدة
- النسخ الاحتياطي واستعادة البيانات
- مراقبة النظام

**الملفات الرئيسية:**
- `super_admin/views.py`: وظائف العرض الرئيسية
- `super_admin/models.py`: نماذج البيانات
- `super_admin/database_utils.py`: أدوات إدارة قواعد البيانات
- `super_admin/backup_utils.py`: أدوات النسخ الاحتياطي

### 2. الحسابات (accounts)

يدير المصادقة وتسجيل الدخول وإدارة المستخدمين:
- تسجيل الدخول والخروج
- التحقق من الرقم التسلسلي
- الاتصال بقاعدة البيانات
- توجيه قاعدة البيانات

**الملفات الرئيسية:**
- `accounts/views.py`: وظائف العرض
- `accounts/models.py`: نماذج البيانات
- `accounts/router.py`: موجه قاعدة البيانات
- `accounts/database_config.py`: تكوين قاعدة البيانات

### 3. لوحة التحكم (dashboard)

يوفر واجهة لوحة التحكم الرئيسية:
- الإحصائيات والرسوم البيانية
- التنبيهات والإشعارات
- الأنشطة الأخيرة

### 4. العمال (workers)

يدير بيانات العمال:
- معلومات العمال
- جوازات السفر والوثائق
- المهارات والمؤهلات

### 5. العقود (contracts)

يدير العقود والاتفاقيات:
- إنشاء وتحرير العقود
- تتبع حالة العقود
- المدفوعات والفواتير

### 6. الخدمات (services)

يدير الخدمات المقدمة:
- خدمات التنظيف
- جدولة الخدمات
- تقييم الخدمات

### 7. المستندات (documents)

يدير المستندات والملفات:
- تحميل وتنظيم المستندات
- تصنيف المستندات
- البحث في المستندات

### 8. الإجراءات (procedures)

يدير الإجراءات الإدارية:
- نقل الكفالة
- تصاريح العمل
- الإقامات

### 9. الماسح الضوئي (scanner)

يوفر وظائف المسح الضوئي:
- مسح جوازات السفر
- استخراج البيانات من الصور
- معالجة الصور

### 10. التقارير (reports)

يوفر تقارير متنوعة:
- التقارير المالية
- تقارير العمال
- تقارير العقود
- تقارير الخدمات

### 11. إعدادات النظام (system_settings)

يدير إعدادات النظام:
- الإعدادات العامة
- إدارة المستخدمين
- تخصيص النظام

## واجهات المستخدم

### 1. الواجهة الرئيسية (frontend)

تطبيق React لواجهة المستخدم الرئيسية:
- تسجيل الدخول
- لوحة التحكم
- إدارة العمال والعقود
- التقارير والإحصائيات

**الملفات الرئيسية:**
- `frontend/src/App.jsx`: المكون الرئيسي
- `frontend/src/pages/`: صفحات التطبيق
- `frontend/src/components/`: مكونات واجهة المستخدم
- `frontend/src/services/`: خدمات الاتصال بالـ API

### 2. محرر القوالب (react-template-editor)

تطبيق React لإنشاء وتحرير قوالب العقود:
- إنشاء قوالب جديدة
- تحرير القوالب الموجودة
- معاينة وتصدير القوالب

**الملفات الرئيسية:**
- `react-template-editor/src/pages/EditorPage.jsx`: صفحة المحرر
- `react-template-editor/src/components/editor/`: مكونات المحرر
- `react-template-editor/src/store/`: مخازن حالة المحرر

## واجهة برمجة التطبيقات (API)

توفر واجهة RESTful للتفاعل مع النظام:
- مصادقة JWT
- نقاط نهاية لجميع موارد النظام
- توثيق Swagger/OpenAPI

**الملفات الرئيسية:**
- `api/urls.py`: مسارات API الرئيسية
- `api/views.py`: وظائف العرض الرئيسية
- `api/serializers.py`: محولات البيانات

## كيفية إجراء الصيانة

### 1. تحديث واجهة المستخدم

1. انتقل إلى مجلد `frontend`
2. قم بتعديل الملفات في `src/pages` أو `src/components`
3. اختبر التغييرات باستخدام `npm start`
4. قم ببناء الإصدار النهائي باستخدام `npm run build`

### 2. تعديل قاعدة البيانات

1. قم بعمل نسخة احتياطية أولاً باستخدام واجهة المسؤول الأعلى
2. قم بتعديل نماذج البيانات في ملفات `models.py` المناسبة
3. قم بإنشاء ملفات الهجرة باستخدام `python manage.py makemigrations`
4. قم بتطبيق الهجرات باستخدام `python manage.py migrate`

### 3. إضافة ميزات جديدة

1. حدد التطبيق المناسب للميزة الجديدة
2. قم بتعديل الملفات المناسبة (models.py, views.py, urls.py)
3. قم بإضافة قوالب HTML جديدة في مجلد `templates`
4. قم بإضافة أي ملفات ثابتة جديدة في مجلد `static`
5. قم بتحديث واجهة API إذا لزم الأمر

### 4. النسخ الاحتياطي واستعادة البيانات

1. استخدم واجهة المسؤول الأعلى للنسخ الاحتياطي اليدوي
2. يمكن جدولة النسخ الاحتياطي التلقائي من خلال إعدادات النظام
3. لاستعادة البيانات، استخدم واجهة المسؤول الأعلى

## استكشاف الأخطاء وإصلاحها

### 1. مشاكل الاتصال بقاعدة البيانات

1. تحقق من ملف `config/database_custom.json`
2. تأكد من صحة الرقم التسلسلي
3. تحقق من سجلات النظام في واجهة المسؤول الأعلى

### 2. مشاكل واجهة المستخدم

1. افتح وحدة تحكم المتصفح للتحقق من أخطاء JavaScript
2. تحقق من سجلات الخادم للأخطاء في الخلفية
3. تأكد من تحميل جميع الملفات الثابتة بشكل صحيح

### 3. مشاكل API

1. استخدم واجهة Swagger للتحقق من نقاط النهاية
2. تحقق من سجلات الخادم للأخطاء
3. تأكد من صحة رموز المصادقة JWT
