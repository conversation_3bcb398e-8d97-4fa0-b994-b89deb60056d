"""
واجهة برمجة التطبيقات (API) للتواصل بين قواعد البيانات المنفصلة
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
import json
import logging
from .models import Company, SystemLog
from .database_connector import (
    get_company_tables, get_table_structure, get_table_data,
    test_company_db_connection, execute_query_on_company_db
)

logger = logging.getLogger(__name__)

def api_response(success, data=None, message=None, status=200):
    """
    إنشاء استجابة API موحدة
    
    Args:
        success: نجاح العملية
        data: البيانات (اختياري)
        message: رسالة (اختياري)
        status: رمز الحالة HTTP (اختياري)
    
    Returns:
        JsonResponse: استجابة JSON
    """
    response = {
        'success': success,
    }
    
    if data is not None:
        response['data'] = data
    
    if message is not None:
        response['message'] = message
    
    return JsonResponse(response, status=status)

@login_required
@require_http_methods(["GET"])
def get_companies(request):
    """
    الحصول على قائمة الشركات
    
    Args:
        request: طلب HTTP
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركات
        companies = Company.objects.all()
        
        # تحويل الشركات إلى قائمة
        companies_list = []
        for company in companies:
            companies_list.append({
                'id': company.id,
                'name': company.name,
                'serial_number': company.serial_number,
                'database_name': company.database_name,
                'database_host': company.database_host,
                'status': company.status,
                'created_at': company.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return api_response(True, data=companies_list)
    
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة الشركات: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@login_required
@require_http_methods(["GET"])
def get_company_details(request, company_id):
    """
    الحصول على تفاصيل شركة
    
    Args:
        request: طلب HTTP
        company_id: معرف الشركة
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركة
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # اختبار الاتصال بقاعدة البيانات
        connection_test = test_company_db_connection(company)
        
        # تحويل الشركة إلى قاموس
        company_data = {
            'id': company.id,
            'name': company.name,
            'serial_number': company.serial_number,
            'database_name': company.database_name,
            'database_host': company.database_host,
            'status': company.status,
            'created_at': company.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'connection_test': connection_test
        }
        
        return api_response(True, data=company_data)
    
    except Exception as e:
        logger.error(f"خطأ في الحصول على تفاصيل الشركة: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@login_required
@require_http_methods(["GET"])
def get_company_tables_api(request, company_id):
    """
    الحصول على قائمة الجداول في قاعدة بيانات الشركة
    
    Args:
        request: طلب HTTP
        company_id: معرف الشركة
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركة
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # الحصول على قائمة الجداول
        tables = get_company_tables(company)
        
        return api_response(True, data=tables)
    
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة الجداول: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@login_required
@require_http_methods(["GET"])
def get_table_structure_api(request, company_id, table_name):
    """
    الحصول على هيكل جدول في قاعدة بيانات الشركة
    
    Args:
        request: طلب HTTP
        company_id: معرف الشركة
        table_name: اسم الجدول
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركة
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # الحصول على هيكل الجدول
        structure = get_table_structure(company, table_name)
        
        return api_response(True, data=structure)
    
    except Exception as e:
        logger.error(f"خطأ في الحصول على هيكل الجدول: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@login_required
@require_http_methods(["GET"])
def get_table_data_api(request, company_id, table_name):
    """
    الحصول على بيانات جدول في قاعدة بيانات الشركة
    
    Args:
        request: طلب HTTP
        company_id: معرف الشركة
        table_name: اسم الجدول
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركة
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # الحصول على معلمات الاستعلام
        limit = int(request.GET.get('limit', 100))
        offset = int(request.GET.get('offset', 0))
        where = request.GET.get('where', None)
        order_by = request.GET.get('order_by', None)
        
        # الحصول على بيانات الجدول
        data = get_table_data(company, table_name, limit, offset, where, order_by)
        
        return api_response(True, data=data)
    
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات الجدول: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)

@login_required
@csrf_exempt
@require_http_methods(["POST"])
def execute_query_api(request, company_id):
    """
    تنفيذ استعلام على قاعدة بيانات الشركة
    
    Args:
        request: طلب HTTP
        company_id: معرف الشركة
    
    Returns:
        JsonResponse: استجابة JSON
    """
    try:
        # التحقق من صلاحيات المستخدم
        if not request.user.is_superuser:
            return api_response(False, message="غير مصرح لك بالوصول إلى هذه البيانات", status=403)
        
        # الحصول على الشركة
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return api_response(False, message="الشركة غير موجودة", status=404)
        
        # الحصول على الاستعلام
        try:
            data = json.loads(request.body)
            query = data.get('query')
            params = data.get('params', None)
        except json.JSONDecodeError:
            return api_response(False, message="بيانات JSON غير صالحة", status=400)
        
        if not query:
            return api_response(False, message="الاستعلام مطلوب", status=400)
        
        # تنفيذ الاستعلام
        result = execute_query_on_company_db(company, query, params)
        
        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='execute_query',
            company=company,
            details=f"تنفيذ استعلام على قاعدة بيانات الشركة: {company.name}",
            ip_address=request.META.get('REMOTE_ADDR')
        )
        
        return api_response(True, data=result)
    
    except Exception as e:
        logger.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
        return api_response(False, message=f"حدث خطأ: {str(e)}", status=500)
