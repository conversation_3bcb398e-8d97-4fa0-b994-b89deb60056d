from django.urls import path
from . import views

app_name = 'clients'

urlpatterns = [
    # الصفحة الرئيسية للعملاء
    path('', views.client_dashboard, name='dashboard'),
    
    # قائمة العملاء
    path('list/', views.client_list, name='client_list'),
    
    # إضافة عميل جديد
    path('create/', views.client_create, name='client_create'),
    
    # تفاصيل العميل
    path('<int:client_id>/', views.client_detail, name='client_detail'),
    
    # تعديل العميل
    path('<int:client_id>/edit/', views.client_edit, name='client_edit'),
    
    # حذف العميل
    path('<int:client_id>/delete/', views.client_delete, name='client_delete'),
    
    # إضافة مستند للعميل
    path('<int:client_id>/documents/add/', views.client_document_add, name='client_document_add'),
    
    # حذف مستند العميل
    path('documents/<int:document_id>/delete/', views.client_document_delete, name='client_document_delete'),
]
