from django import forms
from .models import SystemSetting, UserProfile, UserPermission, WorkerSetting, NotificationSetting, Currency

class SystemSettingForm(forms.ModelForm):
    class Meta:
        model = SystemSetting
        fields = ['key', 'value', 'description', 'category', 'is_active']
        widgets = {
            'key': forms.TextInput(attrs={'class': 'form-control'}),
            'value': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'category': forms.TextInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class UserProfileForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ['role', 'phone', 'address', 'profile_picture', 'is_active']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
        }

class UserPermissionForm(forms.ModelForm):
    class Meta:
        model = UserPermission
        fields = ['module', 'can_view', 'can_add', 'can_edit', 'can_delete']

class WorkerSettingForm(forms.ModelForm):
    class Meta:
        model = WorkerSetting
        fields = ['setting_key', 'setting_value', 'description', 'is_active']
        widgets = {
            'setting_key': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: available_nationalities'}),
            'setting_value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'أدخل القيمة هنا'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف توضيحي للإعداد'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class NotificationSettingForm(forms.ModelForm):
    class Meta:
        model = NotificationSetting
        fields = ['notification_type', 'email_enabled', 'sms_enabled', 'in_app_enabled']

class CurrencyForm(forms.ModelForm):
    class Meta:
        model = Currency
        fields = ['code', 'name', 'symbol', 'exchange_rate', 'is_default', 'is_active']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: SAR, USD, EUR'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: ريال سعودي, دولار أمريكي'}),
            'symbol': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: ر.س, $, €'}),
            'exchange_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.0001'}),
        }
