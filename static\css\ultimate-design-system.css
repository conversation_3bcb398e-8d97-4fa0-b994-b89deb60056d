/* نظام التصميم النهائي - مستوى عالمي */

/* متغيرات CSS متقدمة جداً */
:root {
    /* نظام الألوان المتقدم */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;

    /* تدرجات ديناميكية */
    --gradient-aurora: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    --gradient-ocean: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
    --gradient-sunset: linear-gradient(135deg, #FA709A 0%, #FEE140 100%);
    --gradient-forest: linear-gradient(135deg, #134E5E 0%, #71B280 100%);
    --gradient-space: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
    --gradient-fire: linear-gradient(135deg, #FA8072 0%, #FF6347 100%);

    /* ظلال متعددة الطبقات */
    --shadow-soft: 0 2px 4px rgba(0,0,0,0.02), 0 1px 2px rgba(0,0,0,0.06);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.04), 0 2px 4px rgba(0,0,0,0.08);
    --shadow-large: 0 8px 16px rgba(0,0,0,0.06), 0 4px 8px rgba(0,0,0,0.12);
    --shadow-extra-large: 0 16px 32px rgba(0,0,0,0.08), 0 8px 16px rgba(0,0,0,0.16);
    --shadow-massive: 0 32px 64px rgba(0,0,0,0.12), 0 16px 32px rgba(0,0,0,0.24);
    --shadow-glow-blue: 0 0 20px rgba(59, 130, 246, 0.4);
    --shadow-glow-purple: 0 0 20px rgba(139, 92, 246, 0.4);
    --shadow-glow-green: 0 0 20px rgba(16, 185, 129, 0.4);
    --shadow-glow-red: 0 0 20px rgba(239, 68, 68, 0.4);

    /* انتقالات متقدمة */
    --transition-micro: 100ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-extra-slow: 700ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: 800ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* مسافات نظام 8px */
    --space-0: 0;
    --space-1: 0.25rem;  /* 4px */
    --space-2: 0.5rem;   /* 8px */
    --space-3: 0.75rem;  /* 12px */
    --space-4: 1rem;     /* 16px */
    --space-5: 1.25rem;  /* 20px */
    --space-6: 1.5rem;   /* 24px */
    --space-8: 2rem;     /* 32px */
    --space-10: 2.5rem;  /* 40px */
    --space-12: 3rem;    /* 48px */
    --space-16: 4rem;    /* 64px */
    --space-20: 5rem;    /* 80px */
    --space-24: 6rem;    /* 96px */
    --space-32: 8rem;    /* 128px */

    /* نظام الخطوط المتقدم */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */
    --font-size-6xl: 3.75rem;   /* 60px */
    --font-size-7xl: 4.5rem;    /* 72px */
    --font-size-8xl: 6rem;      /* 96px */
    --font-size-9xl: 8rem;      /* 128px */

    /* أوزان الخطوط */
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* أشعة الحدود */
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-4xl: 2rem;      /* 32px */
    --radius-full: 9999px;

    /* متغيرات الحركة */
    --animation-duration-fast: 0.2s;
    --animation-duration-normal: 0.3s;
    --animation-duration-slow: 0.5s;
    --animation-duration-extra-slow: 0.8s;

    /* متغيرات الشفافية */
    --opacity-0: 0;
    --opacity-5: 0.05;
    --opacity-10: 0.1;
    --opacity-20: 0.2;
    --opacity-25: 0.25;
    --opacity-30: 0.3;
    --opacity-40: 0.4;
    --opacity-50: 0.5;
    --opacity-60: 0.6;
    --opacity-70: 0.7;
    --opacity-75: 0.75;
    --opacity-80: 0.8;
    --opacity-90: 0.9;
    --opacity-95: 0.95;
    --opacity-100: 1;

    /* متغيرات Z-Index */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
    --z-index-toast: 1080;
}

/* إعدادات عامة متقدمة */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    direction: rtl;
    color: #1f2937;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1;
    font-variant-ligatures: common-ligatures;
}

/* نظام الشبكة المتقدم */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.container-2xl {
    max-width: 1536px;
}

.container-fluid {
    width: 100%;
    max-width: none;
    padding: 0 var(--space-4);
}

/* نظام الشبكة المرن */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* فجوات الشبكة */
.gap-0 { gap: var(--space-0); }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }
.gap-10 { gap: var(--space-10); }
.gap-12 { gap: var(--space-12); }

/* نظام Flexbox المتقدم */
.flex { display: flex; }
.inline-flex { display: inline-flex; }

.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* نظام المسافات */
.m-0 { margin: var(--space-0); }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }
.m-10 { margin: var(--space-10); }
.m-12 { margin: var(--space-12); }
.m-16 { margin: var(--space-16); }
.m-20 { margin: var(--space-20); }
.m-24 { margin: var(--space-24); }
.m-32 { margin: var(--space-32); }

.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.p-10 { padding: var(--space-10); }
.p-12 { padding: var(--space-12); }
.p-16 { padding: var(--space-16); }
.p-20 { padding: var(--space-20); }
.p-24 { padding: var(--space-24); }
.p-32 { padding: var(--space-32); }

/* مسافات اتجاهية */
.mt-auto { margin-top: auto; }
.mr-auto { margin-right: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* نظام البطاقات المتقدم جداً */
.card-ultimate {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-large);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    will-change: transform, box-shadow;
}

.card-ultimate::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-aurora);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card-ultimate:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--shadow-massive), var(--shadow-glow-blue);
}

.card-ultimate:hover::before {
    opacity: 1;
}

.card-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
}

.card-gradient {
    background: var(--gradient-aurora);
    color: white;
    border: none;
}

.card-neumorphism {
    background: #e0e5ec;
    box-shadow:
        20px 20px 60px #bebebe,
        -20px -20px 60px #ffffff;
    border: none;
}

/* نظام الأزرار المتقدم */
.btn-ultimate {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    border: none;
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    will-change: transform, box-shadow;
}

.btn-primary {
    background: var(--gradient-ocean);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large), var(--shadow-glow-blue);
}

.btn-secondary {
    background: var(--gradient-sunset);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large), var(--shadow-glow-purple);
}

.btn-success {
    background: var(--gradient-forest);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large), var(--shadow-glow-green);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #374151;
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-morphism {
    background: #e0e5ec;
    color: #333;
    box-shadow:
        8px 8px 16px #bebebe,
        -8px -8px 16px #ffffff;
}

.btn-morphism:hover {
    box-shadow:
        4px 4px 8px #bebebe,
        -4px -4px 8px #ffffff;
}

/* تأثيرات الحركة المتقدمة */
@keyframes float-gentle {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes pulse-glow-advanced {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        opacity: 0.9;
        transform: scale(1.05);
        box-shadow: 0 0 0 15px rgba(59, 130, 246, 0);
    }
}

@keyframes slide-in-bottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slide-in-top {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoom-in {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes rotate-in {
    from {
        transform: rotate(-180deg) scale(0.8);
        opacity: 0;
    }
    to {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
}

@keyframes bounce-in {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    70% {
        transform: scale(0.9);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    14% { transform: scale(1.1); }
    28% { transform: scale(1); }
    42% { transform: scale(1.1); }
    70% { transform: scale(1); }
}

/* فئات الحركة */
.animate-float { animation: float-gentle 3s ease-in-out infinite; }
.animate-pulse-glow { animation: pulse-glow-advanced 2s infinite; }
.animate-slide-in-bottom { animation: slide-in-bottom 0.6s ease-out; }
.animate-slide-in-top { animation: slide-in-top 0.6s ease-out; }
.animate-zoom-in { animation: zoom-in 0.5s ease-out; }
.animate-rotate-in { animation: rotate-in 0.6s ease-out; }
.animate-bounce-in { animation: bounce-in 0.8s ease-out; }
.animate-shake { animation: shake 0.5s ease-in-out; }
.animate-heartbeat { animation: heartbeat 1.5s ease-in-out infinite; }

/* تأثيرات التحويم المتقدمة */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-large);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow-blue);
}

.hover-scale {
    transition: all var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: all var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-skew {
    transition: all var(--transition-normal);
}

.hover-skew:hover {
    transform: skew(-5deg, -2deg);
}

.hover-magnetic {
    transition: all var(--transition-fast);
}

/* تأثيرات النص المتقدمة */
.text-gradient-aurora {
    background: var(--gradient-aurora);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
}

.text-gradient-ocean {
    background: var(--gradient-ocean);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
}

.text-gradient-sunset {
    background: var(--gradient-sunset);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
}

.text-shadow-soft {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-medium {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-shadow-large {
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}
