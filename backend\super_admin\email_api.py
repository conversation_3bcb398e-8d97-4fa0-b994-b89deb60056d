"""
API لإرسال البريد الإلكتروني
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.mail import send_mail
from django.conf import settings
import random
import json


@csrf_exempt
def send_verification_email_api(request):
    """إرسال رمز التحقق عبر البريد الإلكتروني"""
    if request.method == 'POST':
        try:
            # الحصول على اسم المستخدم
            username = request.POST.get('username', 'MUNTADER_WISSAM')
            
            # توليد رمز التحقق
            verification_code = str(random.randint(100000, 999999))
            
            # إرسال البريد الإلكتروني الفعلي
            subject = 'رمز التحقق - نظام استقدامي'
            message = f'''
مرحباً {username},

رمز التحقق الخاص بك هو: {verification_code}

هذا الرمز صالح لمدة دقيقة واحدة فقط.

لا تشارك هذا الرمز مع أي شخص آخر.

تحياتي,
فريق نظام استقدامي
            '''
            
            # البريد الإلكتروني المستهدف
            recipient_email = '<EMAIL>'
            
            try:
                result = send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[recipient_email],
                    fail_silently=False,
                )
                
                if result:
                    return JsonResponse({
                        'success': True,
                        'message': f'تم إرسال رمز التحقق إلى {recipient_email}',
                        'verification_code': verification_code,  # للاختبار فقط
                        'status': 'sent'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'فشل في إرسال البريد الإلكتروني'
                    }, status=500)
                
            except Exception as email_error:
                return JsonResponse({
                    'success': False,
                    'message': f'فشل في إرسال البريد الإلكتروني: {str(email_error)}'
                }, status=500)

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'خطأ في الخادم: {str(e)}'
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': 'طريقة الطلب غير مدعومة'
    }, status=405)
