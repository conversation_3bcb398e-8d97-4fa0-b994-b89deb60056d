from django.urls import path
from . import views

app_name = 'contracts'

urlpatterns = [
    # لوحة تحكم العقود
    path('', views.contract_dashboard, name='dashboard'),
    
    # قائمة العقود
    path('list/', views.contract_list, name='contract_list'),
    
    # تفاصيل العقد
    path('<int:contract_id>/', views.contract_detail, name='contract_detail'),
    
    # إضافة عقد جديد
    path('create/', views.contract_create, name='contract_create'),
    
    # تعديل العقد
    path('<int:contract_id>/edit/', views.contract_edit, name='contract_edit'),
    
    # حذف العقد
    path('<int:contract_id>/delete/', views.contract_delete, name='contract_delete'),
    
    # API للبحث السريع
    path('api/search/', views.contract_api_search, name='contract_api_search'),
]
