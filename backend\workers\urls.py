from django.urls import path
from django.views.generic import RedirectView
from . import views

app_name = 'workers'

urlpatterns = [
    # 1. الصفحة الرئيسية لقسم العمال
    path('', views.workers_dashboard, name='dashboard'),

    # API للإحصائيات
    path('api/statistics/', views.worker_statistics_api, name='worker_statistics_api'),

    # 2. قائمة العمال حسب النوع
    path('list/', views.new_worker_list, name='worker_list'),
    path('contracts/', views.contract_workers, name='contract_workers'),
    path('custom-services/', views.custom_service_workers, name='custom_service_workers'),
    path('monthly/', views.monthly_workers, name='monthly_workers'),

    # 3. إضافة عامل جديد
    path('create/', views.worker_create, name='worker_create'),
    path('create/permanent/', views.create_permanent_worker, name='create_permanent_worker'),
    path('create/custom/', views.create_custom_worker, name='create_custom_worker'),
    path('create/monthly/', views.create_monthly_worker, name='create_monthly_worker'),

    # 4. عرض وتعديل وحذف العمال
    path('<int:worker_id>/', views.new_worker_detail, name='worker_detail'),
    path('<int:worker_id>/update/', views.worker_update, name='worker_update'),
    path('<int:worker_id>/delete/', views.worker_delete, name='worker_delete'),

    # 5. مستندات العمال
    path('<int:worker_id>/documents/', views.worker_documents, name='worker_documents'),
    path('<int:worker_id>/documents/add/', views.add_worker_document, name='add_worker_document'),
    path('documents/<int:document_id>/delete/', views.delete_worker_document, name='delete_worker_document'),
]
