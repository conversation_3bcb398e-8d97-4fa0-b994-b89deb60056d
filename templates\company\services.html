{% extends 'layouts/company_modern.html' %}
{% load static %}

{% block title %}إدارة الخدمات - منصة استقدامي{% endblock %}

{% block content %}
<div class="container mx-auto px-6 py-8 space-y-8">

  
    <!-- عنوان الصفحة -->
    <section class="card-modern p-8 animate-fadeInUp">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6 space-x-reverse">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg animate-float">
                    <i class="fas fa-tools text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">🛠️ إدارة الخدمات</h1>
                    <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لجميع أنواع الخدمات</p>
                </div>
            </div>
            <button class="btn-modern bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <i class="fas fa-plus mr-2"></i>
                إضافة خدمة جديدة
            </button>
        </div>
    </section>

    <!-- إحصائيات الخدمات -->
    <section>
        <div class="text-center mb-8 animate-fadeInUp">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">📊 إحصائيات الخدمات</h2>
            <p class="text-gray-600 dark:text-gray-400">نظرة شاملة على جميع الخدمات</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="card-modern p-6 hover-lift animate-scaleIn">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-tools text-orange-600 dark:text-orange-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">إجمالي الخدمات</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_services }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.1s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-clock text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">خدمات يومية</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_daily_services }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.2s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-calendar-day text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">خدمات 24 ساعة</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_24h_services }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.3s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-cogs text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">خدمات مخصصة</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_custom_services }}">0</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- تبويبات الخدمات -->
    <section class="card-modern animate-fadeInUp">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-list text-orange-600 dark:text-orange-400 mr-2"></i>
                    قائمة الخدمات
                </h2>
                <div class="flex items-center gap-4">
                    <form method="GET" class="flex items-center gap-4">
                        <div class="relative">
                            <input type="text" name="search" value="{{ search_query }}" placeholder="البحث..." class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select name="type" onchange="this.form.submit()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <option value="all">جميع الأنواع</option>
                            <option value="daily" {% if service_type == 'daily' %}selected{% endif %}>خدمات يومية</option>
                            <option value="24h" {% if service_type == '24h' %}selected{% endif %}>خدمات 24 ساعة</option>
                            <option value="custom" {% if service_type == 'custom' %}selected{% endif %}>خدمات مخصصة</option>
                        </select>
                        <button type="submit" class="px-4 py-2 bg-orange-500 text-white rounded-xl hover:bg-orange-600 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- تبويبات -->
            <div class="flex space-x-1 space-x-reverse bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
                <button onclick="showTab('daily')" id="daily-tab" class="tab-button active flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-clock mr-2"></i>
                    الخدمات اليومية ({{ total_daily_services }})
                </button>
                <button onclick="showTab('24h')" id="24h-tab" class="tab-button flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-calendar-day mr-2"></i>
                    خدمات 24 ساعة ({{ total_24h_services }})
                </button>
                <button onclick="showTab('custom')" id="custom-tab" class="tab-button flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-cogs mr-2"></i>
                    الخدمات المخصصة ({{ total_custom_services }})
                </button>
            </div>
        </div>

        <!-- محتوى التبويبات -->
        <div class="p-6">
            <!-- الخدمات اليومية -->
            <div id="daily-content" class="tab-content">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                            <tr>
                                <th class="px-6 py-4 text-right text-sm font-semibold">رقم الخدمة</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">العميل</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">تاريخ الخدمة</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">عدد الساعات</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">عدد العمال</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">الحالة</th>
                                <th class="px-6 py-4 text-right text-sm font-semibold">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {% for service in daily_services %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-6 py-4 font-mono text-blue-600 dark:text-blue-400">{{ service.service_number }}</td>
                                <td class="px-6 py-4 text-gray-900 dark:text-white">{{ service.client.name }}</td>
                                <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ service.service_date|date:"Y/m/d" }}</td>
                                <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ service.hours }} ساعة</td>
                                <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ service.workers_required }}</td>
                                <td class="px-6 py-4">
                                    {% if service.status == 'active' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                            نشط
                                        </span>
                                    {% elif service.status == 'pending' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                            معلق
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                            غير نشط
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center gap-2">
                                        <button onclick="viewService('daily', {{ service.id }})" class="text-blue-600 hover:text-blue-700 transition-colors p-2 rounded-lg hover:bg-blue-50">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editService('daily', {{ service.id }})" class="text-gray-600 hover:text-gray-700 transition-colors p-2 rounded-lg hover:bg-gray-50">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteService('daily', {{ service.id }})" class="text-red-600 hover:text-red-700 transition-colors p-2 rounded-lg hover:bg-red-50">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center">
                                        <i class="fas fa-clock text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                                        <p class="text-gray-500 dark:text-gray-400 text-lg">لا توجد خدمات يومية</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- خدمات 24 ساعة -->
            <div id="24h-content" class="tab-content hidden">
                <div class="text-center py-12">
                    <i class="fas fa-calendar-day text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400 text-lg">خدمات 24 ساعة</p>
                    <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">{{ total_24h_services }} خدمة متاحة</p>
                </div>
            </div>

            <!-- الخدمات المخصصة -->
            <div id="custom-content" class="tab-content hidden">
                <div class="text-center py-12">
                    <i class="fas fa-cogs text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400 text-lg">الخدمات المخصصة</p>
                    <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">{{ total_custom_services }} خدمة متاحة</p>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// وظائف الرسائل والإشعارات
function showMessages() {
    Swal.fire({
        icon: 'info',
        title: 'الرسائل الجديدة',
        text: 'عرض الرسائل الجديدة',
        confirmButtonColor: '#f97316'
    });
}

function showNotifications() {
    Swal.fire({
        icon: 'info',
        title: 'الإشعارات الجديدة',
        text: 'عرض الإشعارات الجديدة',
        confirmButtonColor: '#f97316'
    });
}

// وظائف التبويبات
function showTab(tabName) {
    // إخفاء جميع المحتويات
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // إظهار المحتوى المحدد
    document.getElementById(tabName + '-content').classList.remove('hidden');
    document.getElementById(tabName + '-tab').classList.add('active');
}

// وظائف إدارة الخدمات
function viewService(type, serviceId) {
    Swal.fire({
        icon: 'info',
        title: 'عرض تفاصيل الخدمة',
        text: `عرض تفاصيل الخدمة ${type} رقم ${serviceId}`,
        confirmButtonColor: '#f97316'
    });
}

function editService(type, serviceId) {
    Swal.fire({
        icon: 'question',
        title: 'تعديل الخدمة',
        text: `تعديل الخدمة ${type} رقم ${serviceId}`,
        showCancelButton: true,
        confirmButtonText: 'تعديل',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#f97316'
    });
}

function deleteService(type, serviceId) {
    Swal.fire({
        icon: 'warning',
        title: 'حذف الخدمة',
        text: 'هل أنت متأكد من حذف هذه الخدمة؟',
        showCancelButton: true,
        confirmButtonText: 'حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                icon: 'success',
                title: 'تم الحذف',
                text: 'تم حذف الخدمة بنجاح',
                confirmButtonColor: '#10b981'
            });
        }
    });
}

// تحديث الوقت كل دقيقة
function updateTime() {
    const now = new Date();
    const timeElements = document.querySelectorAll('.real-time');
    timeElements.forEach(element => {
        element.textContent = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    });
}

updateTime();
setInterval(updateTime, 60000);

// تأثير العداد للأرقام
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-counter'));
        if (target > 0) {
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current).toLocaleString('ar-SA');
            }, 16);
        }
    });
});

// تنسيق التبويبات
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .tab-button {
            color: #6b7280;
            background-color: transparent;
        }
        .tab-button:hover {
            color: #374151;
            background-color: rgba(249, 115, 22, 0.1);
        }
        .tab-button.active {
            color: #f97316;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .dark .tab-button.active {
            background-color: #374151;
            color: #f97316;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
