# Generated by Django 5.2 on 2025-04-10 14:40

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workers', '0004_worker_entry_date_worker_passport_image_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecruitmentCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الشركة')),
                ('country', models.CharField(max_length=50, verbose_name='الدولة')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message='الرجاء إدخال رقم هاتف صحيح', regex='^\\+?\\d{8,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('license_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الترخيص')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'شركة استقدام',
                'verbose_name_plural': 'شركات الاستقدام',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='worker',
            name='recruitment_company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workers', to='workers.recruitmentcompany', verbose_name='الشركة المستقدمة'),
        ),
    ]
