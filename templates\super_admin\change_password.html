{% extends 'layouts/super_admin.html' %}

{% block title %}تغيير كلمة المرور{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-orange-600 to-red-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-key ml-2"></i>
                    تغيير كلمة المرور
                </h1>
                <p class="text-orange-100">
                    تحديث كلمة مرور حساب المسؤول الأعلى
                </p>
            </div>
            <div class="text-center">
                <i class="fas fa-shield-alt text-4xl text-orange-200"></i>
            </div>
        </div>
    </div>

    <!-- Security Guidelines -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 class="text-lg font-medium text-blue-800 mb-3">
            <i class="fas fa-info-circle ml-2"></i>
            إرشادات كلمة المرور الآمنة
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-700 text-sm">
            <div class="space-y-2">
                <p><i class="fas fa-check text-green-600 ml-1"></i> على الأقل 8 أحرف</p>
                <p><i class="fas fa-check text-green-600 ml-1"></i> أحرف كبيرة وصغيرة</p>
                <p><i class="fas fa-check text-green-600 ml-1"></i> أرقام ورموز خاصة</p>
            </div>
            <div class="space-y-2">
                <p><i class="fas fa-times text-red-600 ml-1"></i> تجنب المعلومات الشخصية</p>
                <p><i class="fas fa-times text-red-600 ml-1"></i> لا تستخدم كلمات شائعة</p>
                <p><i class="fas fa-times text-red-600 ml-1"></i> لا تكرر كلمات المرور القديمة</p>
            </div>
        </div>
    </div>

    <!-- Change Password Form -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">
            <i class="fas fa-edit ml-2"></i>
            تحديث كلمة المرور
        </h2>

        <form method="post" class="space-y-6" id="passwordForm">
            {% csrf_token %}
            
            <!-- Current Password -->
            <div>
                <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الحالية
                    <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" 
                           id="current_password" 
                           name="current_password" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" onclick="togglePassword('current_password')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye" id="current_password_icon"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- New Password -->
            <div>
                <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الجديدة
                    <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" 
                           id="new_password" 
                           name="new_password" 
                           required
                           oninput="checkPasswordStrength()"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" onclick="togglePassword('new_password')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye" id="new_password_icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Password Strength Indicator -->
                <div class="mt-2">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">قوة كلمة المرور:</span>
                        <span id="strength_text" class="font-medium">ضعيفة</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div id="strength_bar" class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Password Requirements -->
                <div class="mt-3 space-y-1 text-sm">
                    <div id="req_length" class="flex items-center text-gray-500">
                        <i class="fas fa-circle text-xs ml-2"></i>
                        على الأقل 8 أحرف
                    </div>
                    <div id="req_uppercase" class="flex items-center text-gray-500">
                        <i class="fas fa-circle text-xs ml-2"></i>
                        حرف كبير واحد على الأقل
                    </div>
                    <div id="req_lowercase" class="flex items-center text-gray-500">
                        <i class="fas fa-circle text-xs ml-2"></i>
                        حرف صغير واحد على الأقل
                    </div>
                    <div id="req_number" class="flex items-center text-gray-500">
                        <i class="fas fa-circle text-xs ml-2"></i>
                        رقم واحد على الأقل
                    </div>
                    <div id="req_special" class="flex items-center text-gray-500">
                        <i class="fas fa-circle text-xs ml-2"></i>
                        رمز خاص واحد على الأقل (!@#$%^&*)
                    </div>
                </div>
            </div>

            <!-- Confirm New Password -->
            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور الجديدة
                    <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" 
                           id="confirm_password" 
                           name="confirm_password" 
                           required
                           oninput="checkPasswordMatch()"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" onclick="togglePassword('confirm_password')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye" id="confirm_password_icon"></i>
                        </button>
                    </div>
                </div>
                <div id="password_match" class="mt-1 text-sm"></div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{% url 'super_admin:user_profile' %}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    إلغاء
                </a>
                
                <button type="submit" 
                        id="submit_btn"
                        disabled
                        class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                    <i class="fas fa-key ml-2"></i>
                    تغيير كلمة المرور
                </button>
            </div>
        </form>
    </div>

    <!-- Security Notice -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">
                    ملاحظات أمنية مهمة
                </h3>
                <div class="text-yellow-700 space-y-2 text-sm">
                    <p>• سيتم تسجيل خروجك تلقائياً بعد تغيير كلمة المرور</p>
                    <p>• ستحتاج لتسجيل الدخول مرة أخرى بكلمة المرور الجديدة</p>
                    <p>• تأكد من حفظ كلمة المرور الجديدة في مكان آمن</p>
                    <p>• سيتم تسجيل هذه العملية في سجل النظام</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthBar = document.getElementById('strength_bar');
    const strengthText = document.getElementById('strength_text');
    
    let score = 0;
    let feedback = [];
    
    // Check length
    if (password.length >= 8) {
        score += 20;
        updateRequirement('req_length', true);
    } else {
        updateRequirement('req_length', false);
    }
    
    // Check uppercase
    if (/[A-Z]/.test(password)) {
        score += 20;
        updateRequirement('req_uppercase', true);
    } else {
        updateRequirement('req_uppercase', false);
    }
    
    // Check lowercase
    if (/[a-z]/.test(password)) {
        score += 20;
        updateRequirement('req_lowercase', true);
    } else {
        updateRequirement('req_lowercase', false);
    }
    
    // Check numbers
    if (/\d/.test(password)) {
        score += 20;
        updateRequirement('req_number', true);
    } else {
        updateRequirement('req_number', false);
    }
    
    // Check special characters
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        score += 20;
        updateRequirement('req_special', true);
    } else {
        updateRequirement('req_special', false);
    }
    
    // Update strength bar and text
    strengthBar.style.width = score + '%';
    
    if (score < 40) {
        strengthBar.className = 'bg-red-500 h-2 rounded-full transition-all duration-300';
        strengthText.textContent = 'ضعيفة';
        strengthText.className = 'font-medium text-red-600';
    } else if (score < 80) {
        strengthBar.className = 'bg-yellow-500 h-2 rounded-full transition-all duration-300';
        strengthText.textContent = 'متوسطة';
        strengthText.className = 'font-medium text-yellow-600';
    } else {
        strengthBar.className = 'bg-green-500 h-2 rounded-full transition-all duration-300';
        strengthText.textContent = 'قوية';
        strengthText.className = 'font-medium text-green-600';
    }
    
    checkFormValidity();
}

function updateRequirement(reqId, met) {
    const element = document.getElementById(reqId);
    if (met) {
        element.className = 'flex items-center text-green-600';
        element.querySelector('i').className = 'fas fa-check text-xs ml-2';
    } else {
        element.className = 'flex items-center text-gray-500';
        element.querySelector('i').className = 'fas fa-circle text-xs ml-2';
    }
}

function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchDiv = document.getElementById('password_match');
    
    if (confirmPassword === '') {
        matchDiv.textContent = '';
        matchDiv.className = 'mt-1 text-sm';
    } else if (newPassword === confirmPassword) {
        matchDiv.textContent = '✓ كلمات المرور متطابقة';
        matchDiv.className = 'mt-1 text-sm text-green-600';
    } else {
        matchDiv.textContent = '✗ كلمات المرور غير متطابقة';
        matchDiv.className = 'mt-1 text-sm text-red-600';
    }
    
    checkFormValidity();
}

function checkFormValidity() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const submitBtn = document.getElementById('submit_btn');
    
    // Check if all requirements are met
    const lengthOk = newPassword.length >= 8;
    const uppercaseOk = /[A-Z]/.test(newPassword);
    const lowercaseOk = /[a-z]/.test(newPassword);
    const numberOk = /\d/.test(newPassword);
    const specialOk = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
    const passwordsMatch = newPassword === confirmPassword;
    const allFieldsFilled = currentPassword && newPassword && confirmPassword;
    
    const allRequirementsMet = lengthOk && uppercaseOk && lowercaseOk && numberOk && specialOk && passwordsMatch && allFieldsFilled;
    
    submitBtn.disabled = !allRequirementsMet;
}

// Add event listeners
document.getElementById('current_password').addEventListener('input', checkFormValidity);
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من تغيير كلمة المرور؟\n\nسيتم تسجيل خروجك تلقائياً وستحتاج لتسجيل الدخول مرة أخرى.')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
