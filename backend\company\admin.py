from django.contrib import admin
from .models import CompanyUser


@admin.register(CompanyUser)
class CompanyUserAdmin(admin.ModelAdmin):
    list_display = ['user', 'company', 'role', 'is_active', 'created_at']
    list_filter = ['role', 'is_active', 'created_at']
    search_fields = ['user__username', 'company__name']
    readonly_fields = ['created_at', 'last_login']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'company', 'created_by')
