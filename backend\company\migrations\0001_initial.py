# Generated by Django 5.2 on 2025-05-26 18:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('super_admin', '0011_superadminuser_account_locked_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'مدير الشركة'), ('hr_manager', 'مدير الموارد البشرية'), ('finance_manager', 'مدير المالية'), ('operations_manager', 'مدير العمليات'), ('employee', 'موظف'), ('viewer', 'مشاهد فقط')], default='employee', max_length=50, verbose_name='الدور')),
                ('can_manage_users', models.BooleanField(default=False, verbose_name='إدارة المستخدمين')),
                ('can_manage_workers', models.BooleanField(default=True, verbose_name='إدارة العمال')),
                ('can_manage_clients', models.BooleanField(default=False, verbose_name='إدارة العملاء')),
                ('can_manage_contracts', models.BooleanField(default=False, verbose_name='إدارة العقود')),
                ('can_manage_finance', models.BooleanField(default=False, verbose_name='إدارة المالية')),
                ('can_view_reports', models.BooleanField(default=True, verbose_name='عرض التقارير')),
                ('can_export_data', models.BooleanField(default=False, verbose_name='تصدير البيانات')),
                ('can_manage_settings', models.BooleanField(default=False, verbose_name='إدارة الإعدادات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='آخر تسجيل دخول')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='super_admin.company', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_company_users', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'مستخدم الشركة',
                'verbose_name_plural': 'مستخدمو الشركات',
                'db_table': 'company_companyuser',
                'unique_together': {('user', 'company')},
            },
        ),
    ]
