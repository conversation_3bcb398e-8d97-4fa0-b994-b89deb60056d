{% comment %}
استخدام المكون:
{% include 'components/button.html' with text="حفظ" color="blue" icon="save" type="submit" %}
{% include 'components/button.html' with text="إلغاء" color="gray" icon="times" href="/cancel" %}

الخيارات:
- text: نص الزر (إلزامي)
- color: لون الزر (blue, green, red, yellow, gray) - الافتراضي: blue
- icon: أيقونة Font Awesome (اختياري)
- type: نوع الزر (button, submit, reset) - الافتراضي: button
- href: رابط إذا كان الزر يعمل كرابط (اختياري)
- size: حجم الزر (sm, md, lg) - الافتراضي: md
- full_width: إذا كان الزر يأخذ العرض الكامل (true, false) - الافتراضي: false
- classes: فئات CSS إضافية (اختياري)
{% endcomment %}

{% with color=color|default:"blue" size=size|default:"md" type=type|default:"button" full_width=full_width|default:False %}
{% with 
    base_classes="inline-flex items-center justify-center font-medium rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2"
    color_classes=color|yesno:"
        blue:bg-primary hover:bg-primary-dark text-white focus:ring-primary-light,
        green:bg-green-600 hover:bg-green-700 text-white focus:ring-green-500,
        red:bg-red-600 hover:bg-red-700 text-white focus:ring-red-500,
        yellow:bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-400,
        gray:bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-400,
        blue"
    size_classes=size|yesno:"
        sm:px-2 py-1 text-xs,
        md:px-4 py-2 text-sm,
        lg:px-6 py-3 text-base,
        md"
    width_classes=full_width|yesno:"w-full,,"
%}

{% with final_classes=base_classes|add:" "|add:color_classes|add:" "|add:size_classes|add:" "|add:width_classes|add:" "|add:classes|default:"" %}
    {% if href %}
        <a href="{{ href }}" class="{{ final_classes }}">
            {% if icon %}<i class="fas fa-{{ icon }} {% if text %}ml-2{% endif %}"></i>{% endif %}
            {% if text %}{{ text }}{% endif %}
        </a>
    {% else %}
        <button type="{{ type }}" class="{{ final_classes }}">
            {% if icon %}<i class="fas fa-{{ icon }} {% if text %}ml-2{% endif %}"></i>{% endif %}
            {% if text %}{{ text }}{% endif %}
        </button>
    {% endif %}
{% endwith %}

{% endwith %}
{% endwith %}
