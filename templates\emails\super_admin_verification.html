<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رمز المصادقة الثنائية - المسؤول الأعلى</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #0f172a;
            margin: 0;
            padding: 20px;
            color: #ffffff;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            overflow: hidden;
            border: 2px solid #dc2626;
        }
        .header {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .security-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            line-height: 76px;
            font-size: 32px;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }
        .content {
            padding: 40px 30px;
            background: #1e293b;
        }
        .warning-banner {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border: 2px solid #ef4444;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            animation: blink 1.5s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        .code-container {
            background: linear-gradient(135deg, #334155, #475569);
            border: 3px solid #dc2626;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
            box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
        }
        .code {
            font-size: 48px;
            font-weight: bold;
            color: #ffffff;
            letter-spacing: 12px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            margin: 15px 0;
        }
        .code-label {
            color: #dc2626;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .timer {
            background: #dc2626;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin-top: 15px;
            animation: pulse 1s infinite;
        }
        .security-info {
            background: rgba(220, 38, 38, 0.1);
            border: 2px solid rgba(220, 38, 38, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
        }
        .security-info h3 {
            color: #dc2626;
            margin-top: 0;
            font-size: 18px;
        }
        .security-info ul {
            margin: 15px 0;
            padding-right: 20px;
        }
        .security-info li {
            margin: 8px 0;
            color: #e2e8f0;
        }
        .footer {
            background: #0f172a;
            padding: 25px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
            border-top: 1px solid #334155;
        }
        .logo {
            width: 40px;
            height: 40px;
            margin: 0 auto 10px;
            background: #dc2626;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .restricted-access {
            background: linear-gradient(45deg, #7f1d1d, #991b1b);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            color: #fecaca;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="security-badge">🔒</div>
            <h1 style="margin: 0; font-size: 24px; position: relative; z-index: 1;">
                رمز المصادقة الثنائية
            </h1>
            <p style="margin: 10px 0 0; font-size: 16px; position: relative; z-index: 1;">
                المسؤول الأعلى - منطقة محمية
            </p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Warning Banner -->
            <div class="warning-banner">
                <h2 style="margin: 0; font-size: 20px;">⚠️ تحذير أمني عالي المستوى</h2>
                <p style="margin: 10px 0 0; font-size: 14px;">
                    هذا رمز وصول للمسؤول الأعلى - منطقة محظورة
                </p>
            </div>
            
            <h2 style="color: #ffffff; margin-bottom: 20px;">مرحباً أيها المسؤول،</h2>
            <p style="color: #e2e8f0; line-height: 1.6;">
                تم طلب رمز المصادقة الثنائية للوصول إلى لوحة تحكم المسؤول الأعلى في منصة استقدامي.
            </p>
            
            <!-- Code Container -->
            <div class="code-container">
                <div class="code-label">🔐 رمز التحقق الآمن</div>
                <div class="code">{{ verification_code }}</div>
                <div class="timer">⏰ صالح لمدة {{ expiry_minutes }} دقيقة فقط</div>
            </div>
            
            <!-- Restricted Access Notice -->
            <div class="restricted-access">
                <strong>🚫 منطقة وصول محدود</strong><br>
                هذا الرمز مخصص للمسؤولين المعتمدين فقط
            </div>
            
            <!-- Security Information -->
            <div class="security-info">
                <h3>🛡️ إرشادات الأمان الحرجة:</h3>
                <ul>
                    <li><strong>لا تشارك هذا الرمز مع أي شخص مطلقاً</strong></li>
                    <li>الرمز صالح لمدة دقيقة واحدة فقط من وقت الإرسال</li>
                    <li>إذا لم تطلب هذا الرمز، قم بتغيير كلمة المرور فوراً</li>
                    <li>جميع محاولات الوصول مسجلة ومراقبة</li>
                    <li>استخدم هذا الرمز فقط في الموقع الرسمي</li>
                </ul>
            </div>
            
            <p style="color: #e2e8f0; margin-top: 30px;">
                شكراً لحفاظك على أمان النظام.
            </p>
            
            <p style="color: #64748b; font-size: 14px; margin-top: 20px;">
                <strong>ملاحظة:</strong> إذا واجهت أي مشاكل، تواصل مع فريق الدعم التقني فوراً.
            </p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="logo">🏢</div>
            <p style="margin: 0; font-weight: bold; color: #dc2626;">منصة استقدامي</p>
            <p style="margin: 5px 0;">&copy; 2025 جميع الحقوق محفوظة</p>
            <p style="margin: 0; font-size: 12px;">
                نظام إدارة محمي | الإصدار 2.1.0 | مستوى الأمان: أقصى
            </p>
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #334155;">
                <p style="margin: 0; font-size: 11px; color: #475569;">
                    هذه رسالة آلية من نظام الأمان - لا ترد على هذا البريد
                </p>
            </div>
        </div>
    </div>
</body>
</html>
