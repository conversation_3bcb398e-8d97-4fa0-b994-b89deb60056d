/**
 * منصة استقدامي السحابية - تحديث لوحة التحكم في الوقت الفعلي
 * الإصدار 1.0.0
 *
 * هذا الملف يحتوي على وظائف تحديث لوحة التحكم في الوقت الفعلي
 * يستخدم لتحديث الإحصائيات والتنبيهات بشكل مستمر
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التحديث المستمر
    initializeRealtimeUpdates();
});

/**
 * تهيئة التحديث المستمر للوحة التحكم
 */
function initializeRealtimeUpdates() {
    console.log('تهيئة التحديث المستمر للوحة التحكم...');

    // تحديث الإحصائيات عند تحميل الصفحة
    updateDashboardStatistics();

    // تحديث التنبيهات عند تحميل الصفحة
    updateDashboardAlerts();

    // تحديث الأنشطة الأخيرة عند تحميل الصفحة
    updateDashboardActivities();

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateDashboardStatistics, 30000);

    // تحديث التنبيهات كل 60 ثانية
    setInterval(updateDashboardAlerts, 60000);

    // تحديث الأنشطة الأخيرة كل 20 ثانية
    setInterval(updateDashboardActivities, 20000);
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStatistics() {
    console.log('تحديث إحصائيات لوحة التحكم...');

    // طلب الإحصائيات من الخادم
    fetch('/dashboard/api/statistics/')
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في الحصول على الإحصائيات');
            }
            return response.json();
        })
        .then(data => {
            // تحديث الإحصائيات في واجهة المستخدم
            updateStatisticsUI(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * تحديث التنبيهات في لوحة التحكم
 */
function updateDashboardAlerts() {
    console.log('تحديث التنبيهات في لوحة التحكم...');

    // طلب التنبيهات من الخادم
    fetch('/dashboard/api/alerts/')
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في الحصول على التنبيهات');
            }
            return response.json();
        })
        .then(data => {
            // تحديث التنبيهات في واجهة المستخدم
            updateAlertsUI(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث التنبيهات:', error);
        });
}

/**
 * تحديث واجهة المستخدم بالإحصائيات الجديدة
 * @param {Object} data - بيانات الإحصائيات
 */
function updateStatisticsUI(data) {
    console.log('تحديث واجهة المستخدم بالإحصائيات الجديدة:', data);

    // تحديث عدد العمال
    updateStatElement('workers_count', data.workers_count);

    // تحديث عدد العملاء
    updateStatElement('clients_count', data.clients_count);

    // تحديث العقود النشطة
    updateStatElement('active_contracts', data.active_contracts);

    // تحديث العقود المنتهية
    updateStatElement('expired_contracts', data.expired_contracts);

    // تحديث العقود المخصصة
    updateStatElement('custom_contracts', data.custom_contracts);

    // تحديث الخدمات اليومية
    updateStatElement('daily_services', data.daily_services);

    // تحديث الخدمات الشهرية
    updateStatElement('monthly_services', data.monthly_services);

    // تحديث عدد المستخدمين
    updateStatElement('users_count', data.users_count);

    // تحديث العقود التي ستنتهي قريباً
    updateStatElement('expiring_contracts', data.expiring_contracts);

    // تحديث الإقامات التي ستنتهي قريباً
    updateStatElement('expiring_residencies', data.expiring_residencies);
}

/**
 * تحديث واجهة المستخدم بالتنبيهات الجديدة
 * @param {Object} data - بيانات التنبيهات
 */
function updateAlertsUI(data) {
    console.log('تحديث واجهة المستخدم بالتنبيهات الجديدة:', data);

    // تحديث عدد التنبيهات
    updateAlertCount('contract_alerts_count', data.expiring_contracts_count);
    updateAlertCount('residency_alerts_count', data.expiring_residencies_count);

    // تحديث قائمة تنبيهات العقود
    updateAlertsList('contract_alerts_list', data.contract_alerts);

    // تحديث قائمة تنبيهات الإقامات
    updateAlertsList('residency_alerts_list', data.residency_alerts);

    // إظهار أو إخفاء قسم التنبيهات
    toggleAlertsSection(data.expiring_contracts_count, data.expiring_residencies_count);
}

/**
 * تحديث عنصر إحصائي في واجهة المستخدم
 * @param {string} elementId - معرف العنصر
 * @param {number} value - القيمة الجديدة
 */
function updateStatElement(elementId, value) {
    // البحث عن جميع العناصر التي تحتوي على data-stat بالمعرف المحدد
    document.querySelectorAll(`[data-stat="${elementId}"]`).forEach(element => {
        // تحديث النص
        element.textContent = value;

        // إضافة تأثير وميض للتنبيه على التغيير
        element.classList.add('stat-updated');

        // إزالة التأثير بعد ثانية واحدة
        setTimeout(() => {
            element.classList.remove('stat-updated');
        }, 1000);
    });
}

/**
 * تحديث عدد التنبيهات في واجهة المستخدم
 * @param {string} elementId - معرف العنصر
 * @param {number} count - العدد الجديد
 */
function updateAlertCount(elementId, count) {
    // البحث عن جميع العناصر التي تحتوي على data-alert-count بالمعرف المحدد
    document.querySelectorAll(`[data-alert-count="${elementId}"]`).forEach(element => {
        // تحديث النص
        element.textContent = count;
    });
}

/**
 * تحديث قائمة التنبيهات في واجهة المستخدم
 * @param {string} elementId - معرف العنصر
 * @param {Array} alerts - قائمة التنبيهات
 */
function updateAlertsList(elementId, alerts) {
    // البحث عن عنصر القائمة
    const listElement = document.getElementById(elementId);

    // التحقق من وجود العنصر
    if (!listElement) return;

    // مسح القائمة الحالية
    listElement.innerHTML = '';

    // إضافة التنبيهات الجديدة
    if (alerts.length === 0) {
        // إذا لم تكن هناك تنبيهات، عرض رسالة
        const emptyItem = document.createElement('div');
        emptyItem.className = 'p-4 text-center text-gray-500 dark:text-gray-400';
        emptyItem.textContent = 'لا توجد تنبيهات حالياً';
        listElement.appendChild(emptyItem);
    } else {
        // إضافة كل تنبيه إلى القائمة
        alerts.forEach(alert => {
            const alertItem = createAlertItem(alert);
            listElement.appendChild(alertItem);
        });
    }
}

/**
 * إنشاء عنصر تنبيه
 * @param {Object} alert - بيانات التنبيه
 * @returns {HTMLElement} - عنصر التنبيه
 */
function createAlertItem(alert) {
    // إنشاء عنصر التنبيه
    const alertItem = document.createElement('div');
    alertItem.className = 'p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200';

    // إنشاء محتوى التنبيه
    alertItem.innerHTML = `
        <div class="flex justify-between items-center">
            <div>
                <h4 class="font-semibold text-gray-800 dark:text-white">${alert.title}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300">${alert.description}</p>
            </div>
            <a href="${alert.url}" class="px-3 py-1 bg-blue-500 text-white rounded-full text-xs hover:bg-blue-600 transition-colors">عرض</a>
        </div>
    `;

    return alertItem;
}

/**
 * إظهار أو إخفاء قسم التنبيهات
 * @param {number} contractAlertsCount - عدد تنبيهات العقود
 * @param {number} residencyAlertsCount - عدد تنبيهات الإقامات
 */
function toggleAlertsSection(contractAlertsCount, residencyAlertsCount) {
    // البحث عن قسم التنبيهات
    const alertsSection = document.getElementById('alerts_section');

    // التحقق من وجود العنصر
    if (!alertsSection) return;

    // إظهار أو إخفاء القسم
    if (contractAlertsCount > 0 || residencyAlertsCount > 0) {
        alertsSection.classList.remove('hidden');
    } else {
        alertsSection.classList.add('hidden');
    }
}

/**
 * تحديث الأنشطة الأخيرة في لوحة التحكم
 */
function updateDashboardActivities() {
    console.log('تحديث الأنشطة الأخيرة في لوحة التحكم...');

    // طلب الأنشطة الأخيرة من الخادم
    fetch('/dashboard/api/activities/')
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في الحصول على الأنشطة الأخيرة');
            }
            return response.json();
        })
        .then(data => {
            // تحديث الأنشطة الأخيرة في واجهة المستخدم
            updateActivitiesUI(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث الأنشطة الأخيرة:', error);
        });
}

/**
 * تحديث واجهة المستخدم بالأنشطة الأخيرة الجديدة
 * @param {Object} data - بيانات الأنشطة الأخيرة
 */
function updateActivitiesUI(data) {
    console.log('تحديث واجهة المستخدم بالأنشطة الأخيرة الجديدة:', data);

    // البحث عن قائمة الأنشطة الأخيرة
    const activitiesList = document.getElementById('recent_activities_list');

    // التحقق من وجود العنصر
    if (!activitiesList) return;

    // الحصول على معرفات الأنشطة الحالية
    const currentActivityIds = Array.from(activitiesList.querySelectorAll('.activity-item'))
        .map(item => item.getAttribute('data-activity-id'))
        .filter(id => id !== null);

    // إذا لم تكن هناك أنشطة، مسح القائمة وعرض رسالة
    if (data.activities.length === 0) {
        activitiesList.innerHTML = '';
        const emptyItem = document.createElement('li');
        emptyItem.className = 'text-center text-gray-500 py-4';
        emptyItem.innerHTML = `
            <i class="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
            <p>لا توجد أنشطة حديثة</p>
        `;
        activitiesList.appendChild(emptyItem);
        return;
    }

    // تحديد الأنشطة الجديدة
    const newActivityIds = data.activities.map(activity => activity.id.toString());
    const addedActivityIds = newActivityIds.filter(id => !currentActivityIds.includes(id));

    // إذا كانت جميع الأنشطة جديدة أو لا توجد أنشطة حالية، مسح القائمة وإضافة الكل
    if (currentActivityIds.length === 0 || addedActivityIds.length === newActivityIds.length) {
        activitiesList.innerHTML = '';
        data.activities.slice(0, 4).forEach(activity => {
            const activityItem = createActivityItem(activity);
            activityItem.classList.add('activity-new');
            activitiesList.appendChild(activityItem);
        });
        return;
    }

    // إذا كانت هناك أنشطة جديدة، أضفها في بداية القائمة
    if (addedActivityIds.length > 0) {
        // إزالة العناصر الزائدة للحفاظ على 4 أنشطة فقط
        while (activitiesList.children.length + addedActivityIds.length > 4 && activitiesList.lastChild) {
            activitiesList.removeChild(activitiesList.lastChild);
        }

        // إضافة الأنشطة الجديدة في بداية القائمة
        addedActivityIds.forEach(id => {
            const activity = data.activities.find(a => a.id.toString() === id);
            if (activity) {
                const activityItem = createActivityItem(activity);
                activityItem.classList.add('activity-new');
                activitiesList.insertBefore(activityItem, activitiesList.firstChild);
            }
        });
    }
}

/**
 * إنشاء عنصر نشاط
 * @param {Object} activity - بيانات النشاط
 * @returns {HTMLElement} - عنصر النشاط
 */
function createActivityItem(activity) {
    // إنشاء عنصر النشاط
    const activityItem = document.createElement('li');
    activityItem.className = 'relative activity-item';
    activityItem.setAttribute('data-activity-id', activity.id);

    // تحديد لون النقطة حسب نوع النشاط
    let dotColor = 'bg-[#407BFF]';
    if (activity.type === 'worker_added' || activity.type === 'worker_updated') {
        dotColor = 'bg-[#3b82f6]'; // أزرق للعمال
    } else if (activity.type === 'contract_added' || activity.type === 'contract_updated') {
        dotColor = 'bg-[#10b981]'; // أخضر للعقود
    } else if (activity.type === 'client_added' || activity.type === 'client_updated') {
        dotColor = 'bg-[#f97316]'; // برتقالي للعملاء
    } else if (activity.type === 'service_added' || activity.type === 'service_updated') {
        dotColor = 'bg-[#c084fc]'; // بنفسجي للخدمات
    }

    // إنشاء محتوى النشاط
    activityItem.innerHTML = `
        <span class="absolute -right-8 top-0 w-4 h-4 ${dotColor} rounded-full border-4 border-white dark:border-slate-800"></span>
        <div class="flex justify-between items-center w-full border-b border-gray-100 dark:border-gray-700 pb-2">
            <div class="font-bold text-gray-800 dark:text-white">${activity.description}</div>
            <div class="text-gray-400 text-xs text-left w-40 text-left">${activity.date}</div>
        </div>
        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">${activity.user}</div>
    `;

    return activityItem;
}
