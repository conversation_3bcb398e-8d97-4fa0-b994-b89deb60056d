# Generated by Django 5.2 on 2025-04-08 21:44

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workers', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='worker',
            name='company',
        ),
        migrations.RemoveField(
            model_name='worker',
            name='visa_type',
        ),
        migrations.RemoveField(
            model_name='workerdocument',
            name='worker',
        ),
        migrations.AlterModelOptions(
            name='worker',
            options={'verbose_name': 'عامل', 'verbose_name_plural': 'العمال'},
        ),
        migrations.RemoveField(
            model_name='worker',
            name='arrival_date',
        ),
        migrations.RemoveField(
            model_name='worker',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='worker',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='worker',
            name='address',
            field=models.TextField(blank=True, null=True, verbose_name='العنوان'),
        ),
        migrations.AddField(
            model_name='worker',
            name='date_joined',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الانضمام'),
        ),
        migrations.AddField(
            model_name='worker',
            name='salary',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الراتب'),
        ),
        migrations.AddField(
            model_name='worker',
            name='skills',
            field=models.TextField(blank=True, null=True, verbose_name='المهارات'),
        ),
        migrations.AddField(
            model_name='worker',
            name='visa_number',
            field=models.CharField(default='V0000001', max_length=20, unique=True, verbose_name='رقم التأشيرة'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='date_of_birth',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الميلاد'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='first_name',
            field=models.CharField(default='', max_length=100, verbose_name='الاسم الأول'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='gender',
            field=models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], default='M', max_length=1, verbose_name='الجنس'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='last_name',
            field=models.CharField(default='', max_length=100, verbose_name='الاسم الأخير'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='nationality',
            field=models.CharField(choices=[('EG', 'مصر'), ('IN', 'الهند'), ('PK', 'باكستان'), ('BD', 'بنغلاديش'), ('PH', 'الفلبين'), ('NP', 'نيبال'), ('LK', 'سريلانكا'), ('ET', 'إثيوبيا'), ('KE', 'كينيا'), ('ID', 'إندونيسيا')], default='EG', max_length=2, verbose_name='الجنسية'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='passport_expiry',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ انتهاء جواز السفر'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='passport_number',
            field=models.CharField(default='P0000001', max_length=20, unique=True, verbose_name='رقم جواز السفر'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='workers/', verbose_name='الصورة'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='visa_expiry',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ انتهاء التأشيرة'),
        ),
        migrations.DeleteModel(
            name='Company',
        ),
        migrations.DeleteModel(
            name='VisaType',
        ),
        migrations.DeleteModel(
            name='WorkerDocument',
        ),
    ]
