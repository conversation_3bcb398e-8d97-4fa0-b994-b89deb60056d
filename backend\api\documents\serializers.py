from rest_framework import serializers
from documents.models import Document, DocumentAttachment
from django.utils.translation import gettext_lazy as _


class DocumentSerializer(serializers.ModelSerializer):
    """مسلسل بيانات المستند"""
    
    document_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    days_until_expiry = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    file_extension = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'document_number', 'document_type', 'document_type_display',
            'issuing_authority', 'issue_date', 'expiry_date', 'days_until_expiry',
            'status', 'status_display', 'reference_number', 'related_to_type',
            'related_to_worker', 'related_to_client', 'related_to_contract',
            'file', 'file_url', 'file_size', 'file_extension', 'notes',
            'is_active', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_by_name', 'created_at', 'updated_at', 'days_until_expiry', 'file_size', 'file_extension']
    
    def get_document_type_display(self, obj):
        return obj.get_document_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_days_until_expiry(self, obj):
        return obj.days_until_expiry if hasattr(obj, 'days_until_expiry') else None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return obj.created_by.get_full_name() or obj.created_by.username
        return None
    
    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None
    
    def get_file_size(self, obj):
        if obj.file and hasattr(obj.file, 'size'):
            return obj.file_size
        return None
    
    def get_file_extension(self, obj):
        if obj.file:
            return obj.file_extension
        return None


class DocumentCreateSerializer(serializers.ModelSerializer):
    """مسلسل إنشاء مستند جديد"""
    
    class Meta:
        model = Document
        fields = [
            'title', 'document_number', 'document_type', 'issuing_authority',
            'issue_date', 'expiry_date', 'status', 'reference_number',
            'related_to_type', 'related_to_worker', 'related_to_client',
            'related_to_contract', 'file', 'notes', 'is_active'
        ]
    
    def validate_expiry_date(self, value):
        """التحقق من تاريخ انتهاء المستند"""
        if value and value < self.context['request'].data.get('issue_date'):
            raise serializers.ValidationError(_('تاريخ انتهاء المستند يجب أن يكون بعد تاريخ الإصدار'))
        return value
    
    def create(self, validated_data):
        """إنشاء مستند جديد مع تعيين المستخدم الحالي كمنشئ"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DocumentAttachmentSerializer(serializers.ModelSerializer):
    """مسلسل بيانات مرفق المستند"""
    
    file_url = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    file_extension = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentAttachment
        fields = [
            'id', 'document', 'title', 'description', 'file', 'file_url',
            'file_size', 'file_extension', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_by_name', 'created_at', 'updated_at', 'file_size', 'file_extension']
    
    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None
    
    def get_file_size(self, obj):
        if obj.file and hasattr(obj.file, 'size'):
            return obj.file_size
        return None
    
    def get_file_extension(self, obj):
        if obj.file:
            return obj.file_extension
        return None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return obj.created_by.get_full_name() or obj.created_by.username
        return None
    
    def create(self, validated_data):
        """إنشاء مرفق جديد مع تعيين المستخدم الحالي كمنشئ"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
