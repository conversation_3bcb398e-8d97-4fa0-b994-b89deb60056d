from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class CompanyUser(models.Model):
    """نموذج مستخدم الشركة"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    company_serial = models.CharField(max_length=16, verbose_name='الرقم التسلسلي للشركة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'مستخدم الشركة'
        verbose_name_plural = 'مستخدمي الشركة'

    def __str__(self):
        return f"{self.user.username} - {self.company_serial}"

class CompanyLoginAttempt(models.Model):
    """نموذج محاولات تسجيل الدخول للشركة"""
    username = models.CharField(max_length=150, verbose_name='اسم المستخدم')
    company_serial = models.CharField(max_length=16, verbose_name='الرقم التسلسلي للشركة')
    ip_address = models.GenericIPAddressField(verbose_name='عنوان IP')
    user_agent = models.TextField(verbose_name='معلومات المتصفح')
    success = models.BooleanField(default=False, verbose_name='نجح')
    failure_reason = models.CharField(max_length=255, blank=True, null=True, verbose_name='سبب الفشل')
    timestamp = models.DateTimeField(default=timezone.now, verbose_name='وقت المحاولة')

    class Meta:
        verbose_name = 'محاولة تسجيل دخول الشركة'
        verbose_name_plural = 'محاولات تسجيل دخول الشركة'
        ordering = ['-timestamp']

    def __str__(self):
        status = "نجح" if self.success else "فشل"
        return f"{self.username} - {self.company_serial} - {status}"
