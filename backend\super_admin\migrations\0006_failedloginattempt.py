# Generated by Django 5.2 on 2025-04-30 15:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0005_alter_backupschedule_company'),
    ]

    operations = [
        migrations.CreateModel(
            name='FailedLoginAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=20, verbose_name='الرقم التسلسلي')),
                ('reason', models.CharField(max_length=255, verbose_name='سبب الفشل')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('attempt_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت المحاولة')),
                ('is_blocked', models.BooleanField(default=False, verbose_name='تم الحظر')),
            ],
            options={
                'verbose_name': 'محاولة تسجيل دخول فاشلة',
                'verbose_name_plural': 'محاولات تسجيل الدخول الفاشلة',
                'ordering': ['-attempt_time'],
            },
        ),
    ]
