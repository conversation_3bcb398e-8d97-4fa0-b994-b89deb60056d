"""
وحدة تهيئة البيانات الأولية لتطبيق العمال
"""

import logging
from django.db import connections
from django.utils import timezone

# إعداد السجل
logger = logging.getLogger(__name__)

def initialize_data(db_name, company):
    """
    إضافة البيانات الأولية إلى قاعدة بيانات الشركة

    Args:
        db_name: اسم قاعدة البيانات في إعدادات Django
        company: كائن الشركة
    """
    try:
        logger.info(f"بدء تهيئة البيانات الأولية لتطبيق العمال في قاعدة بيانات: {db_name}")

        # الحصول على اتصال قاعدة البيانات
        connection = connections[db_name]

        # إنشاء مؤشر لتنفيذ استعلامات SQL
        with connection.cursor() as cursor:
            # استيراد أنواع العمال من ملف تكوين
            import json
            import os
            from django.conf import settings

            # محاولة قراءة أنواع العمال من ملف التكوين
            config_file = os.path.join(settings.BASE_DIR, 'config', 'worker_types.json')
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    worker_types_data = json.load(f)
                    worker_types = [(wt['name'], wt['description']) for wt in worker_types_data]
            except (FileNotFoundError, json.JSONDecodeError):
                # استخدام القيم الافتراضية إذا لم يتم العثور على ملف التكوين أو كان غير صالح
                worker_types = [
                    ('دائم', 'عامل بدوام كامل ودائم'),
                    ('مؤقت', 'عامل بعقد مؤقت')
                ]
                logger.warning(f"لم يتم العثور على ملف تكوين أنواع العمال أو كان غير صالح: {config_file}")
                logger.warning("تم استخدام القيم الافتراضية بدلاً من ذلك")

            # التحقق من وجود جدول أنواع العمال
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='workers_workertype'")
            if cursor.fetchone():
                # إضافة أنواع العمال
                for worker_type in worker_types:
                    # التحقق من وجود نوع العامل
                    cursor.execute(
                        "SELECT id FROM workers_workertype WHERE name = ?",
                        [worker_type[0]]
                    )
                    if not cursor.fetchone():
                        # إضافة نوع العامل
                        cursor.execute(
                            "INSERT INTO workers_workertype (name, description, created_at) VALUES (?, ?, ?)",
                            [worker_type[0], worker_type[1], timezone.now()]
                        )
                        logger.info(f"تم إضافة نوع العامل: {worker_type[0]}")

            # استيراد المهارات من ملف تكوين
            # محاولة قراءة المهارات من ملف التكوين
            config_file = os.path.join(settings.BASE_DIR, 'config', 'worker_skills.json')
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    skills_data = json.load(f)
                    skills = [(skill['name'], skill['description']) for skill in skills_data]
            except (FileNotFoundError, json.JSONDecodeError):
                # استخدام القيم الافتراضية إذا لم يتم العثور على ملف التكوين أو كان غير صالح
                skills = [
                    ('لحام', 'مهارات اللحام والتركيب'),
                    ('نجارة', 'مهارات النجارة وأعمال الخشب'),
                    ('سباكة', 'مهارات السباكة وأعمال المياه')
                ]
                logger.warning(f"لم يتم العثور على ملف تكوين المهارات أو كان غير صالح: {config_file}")
                logger.warning("تم استخدام القيم الافتراضية بدلاً من ذلك")

            # التحقق من وجود جدول المهارات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='workers_skill'")
            if cursor.fetchone():
                # إضافة المهارات
                for skill in skills:
                    # التحقق من وجود المهارة
                    cursor.execute(
                        "SELECT id FROM workers_skill WHERE name = ?",
                        [skill[0]]
                    )
                    if not cursor.fetchone():
                        # إضافة المهارة
                        cursor.execute(
                            "INSERT INTO workers_skill (name, description, created_at) VALUES (?, ?, ?)",
                            [skill[0], skill[1], timezone.now()]
                        )
                        logger.info(f"تم إضافة المهارة: {skill[0]}")

        logger.info(f"تم الانتهاء من تهيئة البيانات الأولية لتطبيق العمال في قاعدة بيانات: {db_name}")

        return True

    except Exception as e:
        logger.error(f"خطأ في تهيئة البيانات الأولية لتطبيق العمال: {str(e)}")
        return False
