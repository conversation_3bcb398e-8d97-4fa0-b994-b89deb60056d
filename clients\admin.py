from django.contrib import admin
from .models import Client, ClientDocument

class ClientDocumentInline(admin.TabularInline):
    model = ClientDocument
    extra = 0
    readonly_fields = ['uploaded_at', 'uploaded_by']
    fields = ['document_type', 'file', 'description', 'expiry_date', 'is_verified', 'uploaded_at', 'uploaded_by']

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ['name', 'client_type', 'phone_1', 'status', 'created_at']
    list_filter = ['client_type', 'status', 'created_at']
    search_fields = ['name', 'phone_1', 'phone_2', 'national_id', 'email']
    readonly_fields = ['created_at', 'updated_at', 'created_by']
    fieldsets = [
        ('معلومات أساسية', {
            'fields': ['name', 'client_type', 'national_id', 'status']
        }),
        ('معلومات الاتصال', {
            'fields': ['phone_1', 'phone_2', 'email']
        }),
        ('العنوان والموقع', {
            'fields': ['address', 'location_lat', 'location_lng']
        }),
        ('ملاحظات', {
            'fields': ['notes']
        }),
        ('معلومات النظام', {
            'fields': ['created_by', 'created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    inlines = [ClientDocumentInline]

@admin.register(ClientDocument)
class ClientDocumentAdmin(admin.ModelAdmin):
    list_display = ['client', 'document_type', 'uploaded_at', 'is_verified']
    list_filter = ['document_type', 'is_verified', 'uploaded_at']
    search_fields = ['client__name', 'description']
    readonly_fields = ['uploaded_at', 'uploaded_by']
    fieldsets = [
        ('معلومات المستند', {
            'fields': ['client', 'document_type', 'file', 'description', 'expiry_date', 'is_verified']
        }),
        ('معلومات النظام', {
            'fields': ['uploaded_by', 'uploaded_at'],
            'classes': ['collapse']
        }),
    ]
