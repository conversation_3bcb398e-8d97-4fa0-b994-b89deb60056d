# واجهة المستخدم الجديدة لنظام إدارة العمالة

## نظرة عامة

هذا المشروع هو إعادة بناء كاملة لواجهة المستخدم الخاصة بنظام إدارة العمالة، باستخدام تقنيات حديثة وتصميم متجاوب يعمل على جميع الأجهزة.

## المميزات الرئيسية

- تصميم عصري وبسيط وسهل الاستخدام
- دعم كامل للغة العربية واتجاه RTL
- تجاوب كامل مع جميع أحجام الشاشات (الحواسيب، الأجهزة اللوحية، الهواتف الذكية)
- أداء عالي وسرعة تحميل
- واجهة مستخدم موحدة عبر جميع أجزاء النظام
- تجربة مستخدم محسنة مع انتقالات سلسة وتفاعلات حيوية

## التقنيات المستخدمة

- **React.js**: إطار عمل JavaScript لبناء واجهات المستخدم
- **Tailwind CSS**: إطار عمل CSS للتصميم السريع والمرن
- **React Router**: للتنقل بين الصفحات
- **Axios**: لإجراء طلبات HTTP إلى واجهة برمجة التطبيقات
- **React Context API**: لإدارة حالة التطبيق
- **React Icons**: لاستخدام الأيقونات
- **React Hook Form**: للتعامل مع النماذج
- **Framer Motion**: لإضافة الحركة والانتقالات

## هيكل المشروع

```
frontend/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── assets/
│       ├── images/
│       └── fonts/
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   ├── Button.jsx
│   │   │   ├── Card.jsx
│   │   │   ├── Input.jsx
│   │   │   ├── Table.jsx
│   │   │   └── ...
│   │   ├── layout/
│   │   │   ├── Sidebar.jsx
│   │   │   ├── Header.jsx
│   │   │   ├── Footer.jsx
│   │   │   └── Layout.jsx
│   │   ├── dashboard/
│   │   ├── workers/
│   │   ├── contracts/
│   │   ├── services/
│   │   └── ...
│   ├── pages/
│   │   ├── Dashboard.jsx
│   │   ├── Login.jsx
│   │   ├── Workers/
│   │   │   ├── WorkerList.jsx
│   │   │   ├── WorkerDetail.jsx
│   │   │   └── WorkerForm.jsx
│   │   ├── Contracts/
│   │   ├── Services/
│   │   └── ...
│   ├── context/
│   │   ├── AuthContext.jsx
│   │   ├── ThemeContext.jsx
│   │   └── ...
│   ├── hooks/
│   │   ├── useAuth.js
│   │   ├── useApi.js
│   │   └── ...
│   ├── services/
│   │   ├── api.js
│   │   ├── auth.js
│   │   └── ...
│   ├── utils/
│   │   ├── formatters.js
│   │   ├── validators.js
│   │   └── ...
│   ├── styles/
│   │   ├── tailwind.css
│   │   └── global.css
│   ├── App.jsx
│   ├── index.jsx
│   └── routes.jsx
├── package.json
├── tailwind.config.js
└── README.md
```

## متطلبات التشغيل

- Node.js (الإصدار 14 أو أحدث)
- npm (الإصدار 6 أو أحدث)

## تثبيت وتشغيل المشروع

1. تثبيت الاعتماديات:
   ```bash
   npm install
   ```

2. تشغيل خادم التطوير:
   ```bash
   npm start
   ```

3. بناء الإصدار النهائي:
   ```bash
   npm run build
   ```

## دليل التصميم

### الألوان الرئيسية

- **الأزرق الأساسي**: `#174785`
- **الأزرق الداكن**: `#0f3a6d`
- **الأزرق المتوسط**: `#5a96d4`
- **الأزرق الفاتح**: `#e6f0ff`

### الخطوط

- **الخط الرئيسي**: `'Cairo', sans-serif`

### المكونات الرئيسية

#### الأزرار

- **الزر الأساسي**: أزرق داكن مع نص أبيض
- **الزر الثانوي**: رمادي فاتح مع نص داكن
- **زر الخطر**: أحمر مع نص أبيض
- **زر النجاح**: أخضر مع نص أبيض

#### البطاقات

- خلفية بيضاء
- ظل خفيف
- حواف دائرية
- تباعد داخلي متناسق

#### الجداول

- رأس جدول بخلفية زرقاء ونص أبيض
- صفوف متناوبة بألوان مختلفة للقراءة السهلة
- أزرار إجراءات مدمجة في كل صف

## الصفحات الرئيسية

1. **صفحة تسجيل الدخول**: تصميم بسيط وأنيق مع شعار النظام وحقول تسجيل الدخول
2. **لوحة التحكم**: عرض إحصائيات ومؤشرات الأداء الرئيسية
3. **قائمة العمال**: جدول متجاوب مع خيارات البحث والتصفية
4. **تفاصيل العامل**: عرض معلومات العامل بتنسيق منظم وسهل القراءة
5. **قائمة العقود**: جدول متجاوب مع خيارات البحث والتصفية
6. **تفاصيل العقد**: عرض معلومات العقد بتنسيق منظم وسهل القراءة
7. **الإعدادات**: واجهة بسيطة لتكوين إعدادات النظام

## المساهمة

نرحب بمساهماتكم في تحسين واجهة المستخدم. يرجى اتباع الخطوات التالية:

1. انسخ المستودع (Fork)
2. أنشئ فرعًا جديدًا (Branch)
3. قم بإجراء التغييرات
4. أرسل طلب سحب (Pull Request)

## الترخيص

هذا المشروع مرخص بموجب [ترخيص MIT](LICENSE).
