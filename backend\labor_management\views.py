from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse

from .models import (
    Branch, Worker, Client, Contract, DailyService, Daily24HService,
    SecurityClearance, EntryVisa, WorkPermit, WorkerOperation,
    BloodTestRequest, BloodTestRequestWorker, SponsorshipTransfer, WorkerTransfer
)

# Vista del panel de control
@login_required
def dashboard(request):
    """Panel de control principal"""
    # Estadísticas generales
    total_workers = Worker.objects.filter(status='active').count()
    total_clients = Client.objects.filter(status='active').count()
    total_contracts = Contract.objects.filter(status='active').count()

    # Contratos por tipo
    contracts_by_type = Contract.objects.filter(status='active').values('contract_type').annotate(count=Count('id'))

    # Trabajadores por tipo
    workers_by_type = Worker.objects.filter(status='active').values('worker_type').annotate(count=Count('id'))

    # Clientes por tipo
    clients_by_type = Client.objects.filter(status='active').values('client_type').annotate(count=Count('id'))

    # Servicios recientes
    recent_daily_services = DailyService.objects.order_by('-created_at')[:5]
    recent_24h_services = Daily24HService.objects.order_by('-created_at')[:5]

    # Operaciones recientes
    recent_operations = WorkerOperation.objects.order_by('-created_at')[:5]

    context = {
        'total_workers': total_workers,
        'total_clients': total_clients,
        'total_contracts': total_contracts,
        'contracts_by_type': contracts_by_type,
        'workers_by_type': workers_by_type,
        'clients_by_type': clients_by_type,
        'recent_daily_services': recent_daily_services,
        'recent_24h_services': recent_24h_services,
        'recent_operations': recent_operations,
    }

    return render(request, 'labor_management/dashboard.html', context)

# Vistas para trabajadores
@login_required
def worker_list(request):
    """Lista de trabajadores"""
    # Filtros
    status = request.GET.get('status', 'active')
    worker_type = request.GET.get('worker_type', '')
    search_query = request.GET.get('q', '')

    # Consulta base
    workers = Worker.objects.all()

    # Aplicar filtros
    if status:
        workers = workers.filter(status=status)

    if worker_type:
        workers = workers.filter(worker_type=worker_type)

    if search_query:
        workers = workers.filter(
            Q(full_name__icontains=search_query) |
            Q(passport_number__icontains=search_query) |
            Q(iqama_number__icontains=search_query)
        )

    # Ordenar
    workers = workers.order_by('full_name')

    context = {
        'workers': workers,
        'status': status,
        'worker_type': worker_type,
        'search_query': search_query,
    }

    return render(request, 'labor_management/worker_list.html', context)

@login_required
def worker_detail(request, worker_id):
    """Detalles de un trabajador"""
    worker = get_object_or_404(Worker, id=worker_id)

    # Obtener contratos relacionados
    contracts = worker.contracts.all().order_by('-start_date')

    # Obtener servicios relacionados
    daily_services = worker.daily_services.all().order_by('-service_date')
    daily_24h_services = worker.daily_24h_services.all().order_by('-start_date')

    # Obtener operaciones relacionadas
    operations = worker.operations.all().order_by('-operation_date')

    # Obtener pruebas de sangre relacionadas
    blood_tests = worker.blood_tests.all().order_by('-test_date')

    # Obtener transferencias de patrocinio relacionadas
    sponsorship_transfers = worker.sponsorship_transfers.all().order_by('-transfer_date')

    # Obtener transferencias entre sucursales relacionadas
    branch_transfers = worker.branch_transfers.all().order_by('-transfer_date')

    context = {
        'worker': worker,
        'contracts': contracts,
        'daily_services': daily_services,
        'daily_24h_services': daily_24h_services,
        'operations': operations,
        'blood_tests': blood_tests,
        'sponsorship_transfers': sponsorship_transfers,
        'branch_transfers': branch_transfers,
    }

    return render(request, 'labor_management/worker_detail.html', context)

@login_required
def worker_add(request):
    """Añadir un nuevo trabajador"""
    # Implementar lógica para añadir trabajador
    return render(request, 'labor_management/worker_form.html')

@login_required
def worker_edit(request, worker_id):
    """Editar un trabajador existente"""
    worker = get_object_or_404(Worker, id=worker_id)
    # Implementar lógica para editar trabajador
    return render(request, 'labor_management/worker_form.html', {'worker': worker})

# Vistas para clientes
@login_required
def client_list(request):
    """Lista de clientes"""
    # Implementar lógica para listar clientes
    return render(request, 'labor_management/client_list.html')

@login_required
def client_detail(request, client_id):
    """Detalles de un cliente"""
    # Implementar lógica para mostrar detalles de cliente
    return render(request, 'labor_management/client_detail.html')

@login_required
def client_add(request):
    """Añadir un nuevo cliente"""
    # Implementar lógica para añadir cliente
    return render(request, 'labor_management/client_form.html')

@login_required
def client_edit(request, client_id):
    """Editar un cliente existente"""
    # Implementar lógica para editar cliente
    return render(request, 'labor_management/client_form.html')

# Vistas para contratos
@login_required
def contract_list(request):
    """Lista de contratos"""
    # Implementar lógica para listar contratos
    return render(request, 'labor_management/contract_list.html')

@login_required
def contract_detail(request, contract_id):
    """Detalles de un contrato"""
    # Implementar lógica para mostrar detalles de contrato
    return render(request, 'labor_management/contract_detail.html')

@login_required
def contract_add(request):
    """Añadir un nuevo contrato"""
    # Implementar lógica para añadir contrato
    return render(request, 'labor_management/contract_form.html')

@login_required
def contract_edit(request, contract_id):
    """Editar un contrato existente"""
    # Implementar lógica para editar contrato
    return render(request, 'labor_management/contract_form.html')

# Vistas para servicios diarios
@login_required
def daily_service_list(request):
    """Lista de servicios diarios"""
    # Implementar lógica para listar servicios diarios
    return render(request, 'labor_management/services/daily_service_list.html')

@login_required
def daily_service_detail(request, service_id):
    """Detalles de un servicio diario"""
    # Implementar lógica para mostrar detalles de servicio diario
    return render(request, 'labor_management/services/daily_service_detail.html')

@login_required
def daily_service_add(request):
    """Añadir un nuevo servicio diario"""
    # Implementar lógica para añadir servicio diario
    return render(request, 'labor_management/services/daily_service_form.html')

@login_required
def daily_service_edit(request, service_id):
    """Editar un servicio diario existente"""
    # Implementar lógica para editar servicio diario
    return render(request, 'labor_management/services/daily_service_form.html')

@login_required
def daily_service_delete(request, service_id):
    """Eliminar un servicio diario"""
    # Implementar lógica para eliminar servicio diario
    return render(request, 'labor_management/services/daily_service_confirm_delete.html')

@login_required
def daily_service_calendar(request):
    """Calendario de servicios diarios"""
    # Implementar lógica para mostrar calendario de servicios diarios
    return render(request, 'labor_management/services/daily_service_calendar.html')

@login_required
def daily_service_summary(request):
    """Resumen y cálculo del neto diario para servicios diarios"""
    # Implementar lógica para mostrar resumen de servicios diarios
    return render(request, 'labor_management/services/daily_service_summary.html')

# Vistas para servicios de 24 horas
@login_required
def daily_24h_service_list(request):
    """Lista de servicios de 24 horas"""
    # Implementar lógica para listar servicios de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_list.html')

@login_required
def daily_24h_service_detail(request, service_id):
    """Detalles de un servicio de 24 horas"""
    # Implementar lógica para mostrar detalles de servicio de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_detail.html')

@login_required
def daily_24h_service_add(request):
    """Añadir un nuevo servicio de 24 horas"""
    # Implementar lógica para añadir servicio de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_form.html')

@login_required
def daily_24h_service_edit(request, service_id):
    """Editar un servicio de 24 horas existente"""
    # Implementar lógica para editar servicio de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_form.html')

@login_required
def daily_24h_service_delete(request, service_id):
    """Eliminar un servicio de 24 horas"""
    # Implementar lógica para eliminar servicio de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_confirm_delete.html')

@login_required
def daily_24h_service_calendar(request):
    """Calendario de servicios de 24 horas"""
    # Implementar lógica para mostrar calendario de servicios de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_calendar.html')

@login_required
def daily_24h_service_summary(request):
    """Resumen y cálculo del neto diario para servicios de 24 horas"""
    # Implementar lógica para mostrar resumen de servicios de 24 horas
    return render(request, 'labor_management/services/daily_24h_service_summary.html')

# Vistas para operaciones de trabajadores
@login_required
def worker_operation_list(request):
    """Lista de operaciones de trabajadores"""
    # Implementar lógica para listar operaciones
    return render(request, 'labor_management/worker_operation_list.html')

@login_required
def worker_operation_detail(request, operation_id):
    """Detalles de una operación de trabajador"""
    # Implementar lógica para mostrar detalles de operación
    return render(request, 'labor_management/worker_operation_detail.html')

@login_required
def worker_operation_add(request):
    """Añadir una nueva operación de trabajador"""
    # Implementar lógica para añadir operación
    return render(request, 'labor_management/worker_operation_form.html')

@login_required
def worker_operation_edit(request, operation_id):
    """Editar una operación de trabajador existente"""
    # Implementar lógica para editar operación
    return render(request, 'labor_management/worker_operation_form.html')

# Vistas para pruebas de sangre
@login_required
def blood_test_list(request):
    """Lista de pruebas de sangre"""
    # Implementar lógica para listar pruebas de sangre
    return render(request, 'labor_management/blood_test_list.html')

@login_required
def blood_test_detail(request, test_id):
    """Detalles de una prueba de sangre"""
    # Implementar lógica para mostrar detalles de prueba de sangre
    return render(request, 'labor_management/blood_test_detail.html')

@login_required
def blood_test_add(request):
    """Añadir una nueva prueba de sangre"""
    # Implementar lógica para añadir prueba de sangre
    return render(request, 'labor_management/blood_test_form.html')

@login_required
def blood_test_edit(request, test_id):
    """Editar una prueba de sangre existente"""
    # Implementar lógica para editar prueba de sangre
    return render(request, 'labor_management/blood_test_form.html')

@login_required
def blood_test_add_worker(request, test_id):
    """Añadir un trabajador a una prueba de sangre"""
    # Implementar lógica para añadir trabajador a prueba de sangre
    return render(request, 'labor_management/blood_test_add_worker_form.html')

# Vistas para transferencias de patrocinio
@login_required
def sponsorship_transfer_list(request):
    """Lista de transferencias de patrocinio"""
    # Implementar lógica para listar transferencias de patrocinio
    return render(request, 'labor_management/sponsorship_transfer_list.html')

@login_required
def sponsorship_transfer_detail(request, transfer_id):
    """Detalles de una transferencia de patrocinio"""
    # Implementar lógica para mostrar detalles de transferencia de patrocinio
    return render(request, 'labor_management/sponsorship_transfer_detail.html')

@login_required
def sponsorship_transfer_add(request):
    """Añadir una nueva transferencia de patrocinio"""
    # Implementar lógica para añadir transferencia de patrocinio
    return render(request, 'labor_management/sponsorship_transfer_form.html')

@login_required
def sponsorship_transfer_edit(request, transfer_id):
    """Editar una transferencia de patrocinio existente"""
    # Implementar lógica para editar transferencia de patrocinio
    return render(request, 'labor_management/sponsorship_transfer_form.html')

# Vistas para transferencias de trabajadores entre sucursales
@login_required
def worker_transfer_list(request):
    """Lista de transferencias de trabajadores entre sucursales"""
    # Implementar lógica para listar transferencias de trabajadores
    return render(request, 'labor_management/worker_transfer_list.html')

@login_required
def worker_transfer_detail(request, transfer_id):
    """Detalles de una transferencia de trabajador entre sucursales"""
    # Implementar lógica para mostrar detalles de transferencia de trabajador
    return render(request, 'labor_management/worker_transfer_detail.html')

@login_required
def worker_transfer_add(request):
    """Añadir una nueva transferencia de trabajador entre sucursales"""
    # Implementar lógica para añadir transferencia de trabajador
    return render(request, 'labor_management/worker_transfer_form.html')

@login_required
def worker_transfer_edit(request, transfer_id):
    """Editar una transferencia de trabajador entre sucursales existente"""
    # Implementar lógica para editar transferencia de trabajador
    return render(request, 'labor_management/worker_transfer_form.html')

# Vistas para sucursales
@login_required
def branch_list(request):
    """Lista de sucursales"""
    # Implementar lógica para listar sucursales
    return render(request, 'labor_management/branch_list.html')

@login_required
def branch_detail(request, branch_id):
    """Detalles de una sucursal"""
    # Implementar lógica para mostrar detalles de sucursal
    return render(request, 'labor_management/branch_detail.html')

@login_required
def branch_add(request):
    """Añadir una nueva sucursal"""
    # Implementar lógica para añadir sucursal
    return render(request, 'labor_management/branch_form.html')

@login_required
def branch_edit(request, branch_id):
    """Editar una sucursal existente"""
    # Implementar lógica para editar sucursal
    return render(request, 'labor_management/branch_form.html')

# Vistas para servicios personalizados
@login_required
def custom_service_list(request):
    """Lista de servicios personalizados"""
    # Implementar lógica para listar servicios personalizados
    return render(request, 'labor_management/services/custom_service_list.html')

@login_required
def custom_service_create(request):
    """Añadir un nuevo servicio personalizado"""
    # Implementar lógica para añadir servicio personalizado
    return render(request, 'labor_management/services/custom_service_form.html')

@login_required
def custom_service_detail(request, service_id):
    """Detalles de un servicio personalizado"""
    # Implementar lógica para mostrar detalles de servicio personalizado
    return render(request, 'labor_management/services/custom_service_detail.html')

@login_required
def custom_service_edit(request, service_id):
    """Editar un servicio personalizado existente"""
    # Implementar lógica para editar servicio personalizado
    return render(request, 'labor_management/services/custom_service_form.html')

@login_required
def custom_service_delete(request, service_id):
    """Eliminar un servicio personalizado"""
    # Implementar lógica para eliminar servicio personalizado
    return render(request, 'labor_management/services/custom_service_confirm_delete.html')

@login_required
def custom_service_calendar(request):
    """Calendario de servicios personalizados"""
    # Implementar lógica para mostrar calendario de servicios personalizados
    return render(request, 'labor_management/services/custom_service_calendar.html')

# APIs de utilidad para servicios
@login_required
def available_workers(request):
    """API para obtener los trabajadores disponibles para una fecha específica"""
    # Implementar lógica para obtener trabajadores disponibles
    return JsonResponse({})

@login_required
def service_statistics(request):
    """API para obtener estadísticas de servicios"""
    # Implementar lógica para obtener estadísticas de servicios
    return JsonResponse({})

# Vistas para documentos oficiales
@login_required
def security_clearance_list(request):
    """Lista de autorizaciones de seguridad"""
    # Implementar lógica para listar autorizaciones de seguridad
    return render(request, 'labor_management/security_clearance_list.html')

@login_required
def entry_visa_list(request):
    """Lista de visados de entrada"""
    # Implementar lógica para listar visados de entrada
    return render(request, 'labor_management/entry_visa_list.html')

@login_required
def work_permit_list(request):
    """Lista de permisos de trabajo"""
    # Implementar lógica para listar permisos de trabajo
    return render(request, 'labor_management/work_permit_list.html')
