"""
Context processors for the accounts app.
"""
from .models import UserCompany

def company_context(request):
    """
    Add company-related context to all templates.
    """
    context = {
        'user_companies': None,
        'current_company': getattr(request, 'current_company', None),
    }
    
    if request.user.is_authenticated:
        # Get all companies associated with the user
        context['user_companies'] = UserCompany.objects.filter(
            user=request.user
        ).select_related('company')
    
    return context
