{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--secondary-blue); text-decoration: none;">العمال</a></li>
<li style="margin: 0 8px;"> / </li>
<li><span style="color: var(--text-secondary);">{% if worker %}تعديل عامل{% else %}إضافة عامل{% endif %}</span></li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- أدوات مساعدة -->
    <div class="card bg-primary mb-4">
        <div class="card-body p-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="text-white mb-0">أدوات مساعدة</h3>
                    <p class="text-white-50 mb-0">استخدم هذه الأدوات لتسهيل عملية إدخال البيانات</p>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                    <button type="button" class="btn btn-lg btn-light me-2" id="scanPassportBtn" style="min-width: 200px;" disabled>
                        <i class="fas fa-camera me-2 fa-lg"></i> <strong>مسح جواز السفر</strong>
                        <small class="d-block text-muted">تم إيقاف هذه الميزة</small>
                    </button>
                    <a href="{% url 'workers:import_workers' %}" class="btn btn-lg btn-success" style="min-width: 200px;">
                        <i class="fas fa-file-import me-2 fa-lg"></i> <strong>استيراد العمال</strong>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل العامل -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>{{ title }}</h5>
        </div>
        <div class="alert alert-info m-3">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-info-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading">ميزات النظام</h5>
                    <p class="mb-0">يمكنك استخدام <strong>استيراد العمال</strong> من ملف إكسل لتسهيل عملية إدخال البيانات.</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" id="workerForm">
                {% csrf_token %}

                <div class="row mb-4">
                    <!-- المعلومات الشخصية -->
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-id-card me-2"></i>المعلومات الشخصية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                        {{ form.first_name }}
                                        {% if form.first_name.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.first_name.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                        {{ form.last_name }}
                                        {% if form.last_name.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.last_name.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                                        {{ form.gender }}
                                        {% if form.gender.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.gender.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.nationality.id_for_label }}" class="form-label">{{ form.nationality.label }}</label>
                                        {{ form.nationality }}
                                        {% if form.nationality.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.nationality.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">{{ form.date_of_birth.label }}</label>
                                        {{ form.date_of_birth }}
                                        {% if form.date_of_birth.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.date_of_birth.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }}</label>
                                        {{ form.phone_number }}
                                        {% if form.phone_number.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.phone_number.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات جواز السفر والتأشيرة -->
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-passport me-2"></i>معلومات جواز السفر والتأشيرة</h6>
                                <button type="button" class="btn btn-sm btn-danger" id="scanPassportBtnSmall">
                                    <i class="fas fa-camera me-1"></i> مسح جواز السفر
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.passport_number.id_for_label }}" class="form-label">{{ form.passport_number.label }}</label>
                                        {{ form.passport_number }}
                                        {% if form.passport_number.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.passport_number.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.passport_expiry.id_for_label }}" class="form-label">{{ form.passport_expiry.label }}</label>
                                        {{ form.passport_expiry }}
                                        {% if form.passport_expiry.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.passport_expiry.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.visa_number.id_for_label }}" class="form-label">{{ form.visa_number.label }}</label>
                                        {{ form.visa_number }}
                                        {% if form.visa_number.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.visa_number.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.visa_expiry.id_for_label }}" class="form-label">{{ form.visa_expiry.label }}</label>
                                        {{ form.visa_expiry }}
                                        {% if form.visa_expiry.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.visa_expiry.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <!-- معلومات العمل -->
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.date_joined.id_for_label }}" class="form-label">{{ form.date_joined.label }}</label>
                                        {{ form.date_joined }}
                                        {% if form.date_joined.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.date_joined.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.salary.id_for_label }}" class="form-label">{{ form.salary.label }}</label>
                                        {{ form.salary }}
                                        {% if form.salary.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.salary.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12">
                                        <label for="{{ form.skills.id_for_label }}" class="form-label">{{ form.skills.label }}</label>
                                        {{ form.skills }}
                                        {% if form.skills.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.skills.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12">
                                        <div class="form-check">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                {{ form.is_active.label }}
                                            </label>
                                        </div>
                                        {% if form.is_active.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.is_active.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                        {{ form.address }}
                                        {% if form.address.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.address.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12">
                                        <label for="{{ form.photo.id_for_label }}" class="form-label">{{ form.photo.label }}</label>
                                        {{ form.photo }}
                                        {% if form.photo.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.photo.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="alert alert-warning mt-4">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading">لإنهاء العملية</h6>
                            <p class="mb-0">انقر على زر "حفظ" لحفظ البيانات أو "إلغاء" للعودة بدون حفظ.</p>
                        </div>
                    </div>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <button type="submit" class="btn btn-lg btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ
                    </button>
                    <a href="{% url 'workers:worker_list' %}" class="btn btn-lg btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal for Passport Scanner -->
<div class="modal fade" id="passportScannerModal" tabindex="-1" aria-labelledby="passportScannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passportScannerModalLabel">مسح جواز السفر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> قم بوضع جواز السفر على الماسح الضوئي وانقر على زر "التقاط صورة" أو قم بتحميل صورة لجواز السفر.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">التقاط صورة</h6>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <button type="button" class="btn btn-primary" id="captureBtn">
                                        <i class="fas fa-camera me-1"></i> التقاط صورة
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label for="passportImageUpload" class="form-label">أو تحميل صورة</label>
                                    <input type="file" class="form-control" id="passportImageUpload" accept="image/*">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معاينة الصورة</h6>
                            </div>
                            <div class="card-body text-center">
                                <img id="passportPreview" src="{% static 'img/passport-placeholder.png' %}" alt="معاينة جواز السفر" class="img-fluid mb-3" style="max-height: 200px;">
                                <div id="scanStatus" class="alert alert-warning d-none">
                                    جاري معالجة الصورة...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">النتائج</h6>
                            </div>
                            <div class="card-body">
                                <div id="scanResults" class="d-none">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tbody>
                                                <tr>
                                                    <th style="width: 30%;">الاسم الأول</th>
                                                    <td id="result_first_name"></td>
                                                </tr>
                                                <tr>
                                                    <th>الاسم الأخير</th>
                                                    <td id="result_last_name"></td>
                                                </tr>
                                                <tr>
                                                    <th>رقم جواز السفر</th>
                                                    <td id="result_passport_number"></td>
                                                </tr>
                                                <tr>
                                                    <th>الجنسية</th>
                                                    <td id="result_nationality"></td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ الميلاد</th>
                                                    <td id="result_date_of_birth"></td>
                                                </tr>
                                                <tr>
                                                    <th>تاريخ انتهاء جواز السفر</th>
                                                    <td id="result_passport_expiry"></td>
                                                </tr>
                                                <tr>
                                                    <th>الجنس</th>
                                                    <td id="result_gender"></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div id="noResults" class="alert alert-warning">
                                    لم يتم مسح أي جواز سفر بعد.
                                </div>
                                <div id="scanError" class="alert alert-danger d-none">
                                    حدث خطأ أثناء معالجة الصورة. يرجى المحاولة مرة أخرى.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="useDataBtn" disabled>استخدام البيانات</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const scanPassportBtn = document.getElementById('scanPassportBtn');
        const scanPassportBtnSmall = document.getElementById('scanPassportBtnSmall');
        const passportScannerModal = new bootstrap.Modal(document.getElementById('passportScannerModal'));
        const captureBtn = document.getElementById('captureBtn');
        const passportImageUpload = document.getElementById('passportImageUpload');
        const passportPreview = document.getElementById('passportPreview');
        const scanStatus = document.getElementById('scanStatus');
        const scanResults = document.getElementById('scanResults');
        const noResults = document.getElementById('noResults');
        const scanError = document.getElementById('scanError');
        const useDataBtn = document.getElementById('useDataBtn');

        // Form fields
        const firstNameField = document.getElementById('{{ form.first_name.id_for_label }}');
        const lastNameField = document.getElementById('{{ form.last_name.id_for_label }}');
        const passportNumberField = document.getElementById('{{ form.passport_number.id_for_label }}');
        const nationalityField = document.getElementById('{{ form.nationality.id_for_label }}');
        const dateOfBirthField = document.getElementById('{{ form.date_of_birth.id_for_label }}');
        const passportExpiryField = document.getElementById('{{ form.passport_expiry.id_for_label }}');
        const genderField = document.getElementById('{{ form.gender.id_for_label }}');

        // Result fields
        const resultFirstName = document.getElementById('result_first_name');
        const resultLastName = document.getElementById('result_last_name');
        const resultPassportNumber = document.getElementById('result_passport_number');
        const resultNationality = document.getElementById('result_nationality');
        const resultDateOfBirth = document.getElementById('result_date_of_birth');
        const resultPassportExpiry = document.getElementById('result_passport_expiry');
        const resultGender = document.getElementById('result_gender');

        // Open scanner modal
        scanPassportBtn.addEventListener('click', function() {
            passportScannerModal.show();
        });

        // Open scanner modal from small button
        scanPassportBtnSmall.addEventListener('click', function() {
            passportScannerModal.show();
        });

        // Capture image from scanner
        captureBtn.addEventListener('click', function() {
            // In a real implementation, this would connect to a scanner device
            // For now, we'll simulate by showing an alert
            alert('هذه الميزة تتطلب توصيل ماسح ضوئي بالجهاز. يرجى تحميل صورة بدلاً من ذلك.');
        });

        // Handle image upload
        passportImageUpload.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const file = e.target.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Display the image
                    passportPreview.src = e.target.result;

                    // Show processing status
                    scanStatus.classList.remove('d-none');
                    scanResults.classList.add('d-none');
                    noResults.classList.add('d-none');
                    scanError.classList.add('d-none');
                    useDataBtn.disabled = true;

                    // Process the image
                    processPassportImage(e.target.result);
                };

                reader.readAsDataURL(file);
            }
        });

        // Process passport image
        function processPassportImage(imageData) {
            // تم إزالة وظيفة مسح جواز السفر
            console.log('تم إزالة وظيفة مسح جواز السفر');

            // إظهار رسالة للمستخدم
            scanStatus.classList.add('d-none');
            scanError.classList.remove('d-none');
            scanError.textContent = 'تم إزالة وظيفة مسح جواز السفر من النظام. يرجى إدخال البيانات يدوياً.';
        }

        // Use the scanned data
        useDataBtn.addEventListener('click', function() {
            if (window.passportData) {
                // Populate form fields
                if (window.passportData.first_name) firstNameField.value = window.passportData.first_name;
                if (window.passportData.last_name) lastNameField.value = window.passportData.last_name;
                if (window.passportData.passport_number) passportNumberField.value = window.passportData.passport_number;
                if (window.passportData.nationality) {
                    // Find the option with the matching value
                    const option = Array.from(nationalityField.options).find(opt => opt.value === window.passportData.nationality);
                    if (option) nationalityField.value = window.passportData.nationality;
                }
                if (window.passportData.date_of_birth) dateOfBirthField.value = window.passportData.date_of_birth;
                if (window.passportData.passport_expiry) passportExpiryField.value = window.passportData.passport_expiry;
                if (window.passportData.gender) {
                    // Find the option with the matching value
                    const option = Array.from(genderField.options).find(opt => opt.value === window.passportData.gender);
                    if (option) genderField.value = window.passportData.gender;
                }

                // Close the modal
                passportScannerModal.hide();
            }
        });
    });
</script>
{% endblock %}
{% endblock %}