<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>تسجيل الدخول | استقدامي</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#174785',
                        secondary: '#42d3d8',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - <PERSON><PERSON>wal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="flex-1 flex items-center justify-center">
        <div class="max-w-md w-full mx-4" x-data="loginForm">
            <!-- Logo -->
            <div class="text-center mb-6">
                <img src="/static/images/logo.webp" alt="شعار استقدامي" class="h-20 mx-auto hover:animate-pulse transition-all duration-300" onerror="this.src='/static/images/logo-placeholder.png'; this.onerror=null;">
                <h1 class="text-2xl font-bold text-primary mt-2">منصة استقدامي</h1>
                <p class="text-gray-600">نظام إدارة العمالة</p>
            </div>

            <!-- Login Card -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4 bg-primary text-white">
                    <h2 class="text-xl font-bold">تسجيل دخول الشركات</h2>
                </div>

                <!-- Step Indicator -->
                <div class="flex border-b border-gray-200">
                    <div class="w-1/2 py-2 text-center" :class="step === 1 ? 'bg-blue-50 text-primary font-bold border-b-2 border-primary' : 'text-gray-500'">
                        <span class="inline-block w-6 h-6 rounded-full bg-primary text-white text-xs leading-6 ml-1">1</span>
                        التحقق من الشركة
                    </div>
                    <div class="w-1/2 py-2 text-center" :class="step === 2 ? 'bg-blue-50 text-primary font-bold border-b-2 border-primary' : 'text-gray-500'">
                        <span class="inline-block w-6 h-6 rounded-full" :class="dbVerified ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'" class="text-xs leading-6 ml-1">2</span>
                        تسجيل الدخول
                    </div>
                </div>

                <!-- Error Messages -->
                @if(session('error'))
                <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-4">
                    <p>{{ session('error') }}</p>
                </div>
                @endif

                @if($errors->any())
                <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-4">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Step 1: Verify Company -->
                <div x-show="step === 1" class="p-6">
                    <div class="mb-6">
                        <label for="serial_number" class="block text-gray-700 font-medium mb-2">الرقم التسلسلي للشركة</label>
                        <div class="relative">
                            <input type="text" id="serial_number" x-model="serialNumber" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19" @input="formatSerialNumber">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">أدخل الرقم التسلسلي المكون من 16 حرف</p>
                    </div>

                    <div class="mb-6" x-show="dbInfo !== null">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center text-green-700 font-medium mb-2">
                                <i class="fas fa-check-circle ml-2"></i>
                                <span>تم التحقق من الشركة بنجاح</span>
                            </div>
                            <div class="text-gray-700">
                                <p><span class="font-medium">اسم الشركة:</span> <span x-text="companyName"></span></p>
                                <p><span class="font-medium">نوع قاعدة البيانات:</span> <span x-text="dbInfo.type"></span></p>
                                <p><span class="font-medium">عدد الجداول:</span> <span x-text="dbInfo.tables_count"></span></p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6" x-show="errorMessage" x-cloak>
                        <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4">
                            <div class="flex items-center font-medium mb-2">
                                <i class="fas fa-exclamation-circle ml-2"></i>
                                <span>خطأ في التحقق</span>
                            </div>
                            <div class="text-gray-700" x-text="errorMessage"></div>
                            <p class="mt-2 text-sm">رقم التسلسلي غير صحيح أو غير موجود، يرجى التحقق والمحاولة مجددًا.</p>
                        </div>
                    </div>

                    <button @click="verifyCompany" :disabled="loading || serialNumber.length < 16" class="w-full bg-primary hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors" :class="{ 'opacity-50 cursor-not-allowed': loading || serialNumber.length < 16 }">
                        <span x-show="!loading">
                            <i class="fas fa-database ml-2"></i>
                            التحقق من الشركة
                        </span>
                        <span x-show="loading">
                            <i class="fas fa-spinner fa-spin ml-2"></i>
                            جاري التحقق...
                        </span>
                    </button>
                </div>

                <!-- Step 2: Login -->
                <div x-show="step === 2" class="p-6">
                    <form action="{{ route('company.authenticate') }}" method="POST">
                        @csrf
                        <input type="hidden" name="serial_number" :value="serialNumber">
                        <input type="hidden" name="db_verified" value="true">

                        <div class="mb-4">
                            <label for="username" class="block text-gray-700 font-medium mb-2">اسم المستخدم</label>
                            <div class="relative">
                                <input type="text" id="username" name="username" x-model="username" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" required>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="password" class="block text-gray-700 font-medium mb-2">كلمة المرور</label>
                            <div class="relative">
                                <input type="password" id="password" name="password" x-model="password" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" required>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="remember" name="remember" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                <label for="remember" class="mr-2 block text-gray-700">تذكرني</label>
                            </div>
                            <a href="#" class="text-primary hover:text-blue-700 text-sm">نسيت كلمة المرور؟</a>
                        </div>

                        <button type="submit" class="w-full bg-primary hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-sign-in-alt ml-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>

                    <div class="mt-4">
                        <button @click="step = 1" class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للخطوة السابقة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-6 text-center text-gray-500 text-sm">
                <p>جميع الحقوق محفوظة &copy; {{ date('Y') }} استقدامي</p>
                <p class="mt-1">
                    <a href="{{ route('super_admin.login') }}" class="text-primary hover:text-blue-700">
                        <i class="fas fa-user-shield ml-1"></i>
                        تسجيل دخول المسؤول الأعلى
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('loginForm', () => ({
                step: 1,
                serialNumber: '',
                username: '',
                password: '',
                dbVerified: false,
                loading: false,
                companyName: '',
                dbInfo: null,
                errorMessage: null,

                formatSerialNumber() {
                    // تنسيق الرقم التسلسلي (إضافة شرطات كل 4 أحرف)
                    let value = this.serialNumber.replace(/[^A-Z0-9]/gi, '').toUpperCase();
                    let formattedValue = '';

                    for (let i = 0; i < value.length; i++) {
                        if (i > 0 && i % 4 === 0 && formattedValue.length < 19) {
                            formattedValue += '-';
                        }
                        if (formattedValue.length < 19) {
                            formattedValue += value[i];
                        }
                    }

                    this.serialNumber = formattedValue;
                },

                verifyCompany() {
                    if (this.loading || this.serialNumber.length < 16) {
                        return;
                    }

                    this.loading = true;
                    this.dbInfo = null;
                    this.errorMessage = null;

                    // الحصول على CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

                    // إرسال طلب للتحقق من الشركة
                    fetch('/accounts/api/check-serial-number/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify({
                            serial_number: this.serialNumber
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        this.loading = false;

                        if (data.success) {
                            this.dbVerified = true;
                            this.companyName = data.company.name;
                            this.dbInfo = data.database;

                            // الانتقال إلى الخطوة التالية بعد ثانية واحدة
                            setTimeout(() => {
                                this.step = 2;
                            }, 1000);
                        } else {
                            this.errorMessage = data.message || 'فشل في التحقق من الشركة';
                        }
                    })
                    .catch(error => {
                        this.loading = false;
                        console.error('Error:', error);
                        this.errorMessage = 'حدث خطأ أثناء التحقق من الشركة. يرجى المحاولة مرة أخرى.';
                    });
                }
            }));
        });

        function formatSerialNumber() {
            // تنسيق الرقم التسلسلي (إضافة شرطات كل 4 أحرف)
            let value = this.serialNumber.replace(/[^A-Z0-9]/gi, '').toUpperCase();
            let formattedValue = '';

            for (let i = 0; i < value.length; i++) {
                if (i > 0 && i % 4 === 0 && formattedValue.length < 19) {
                    formattedValue += '-';
                }
                if (formattedValue.length < 19) {
                    formattedValue += value[i];
                }
            }

            this.serialNumber = formattedValue;
        }

        function verifyCompany() {
            if (this.loading || this.serialNumber.length < 16) {
                return;
            }

            this.loading = true;
            this.dbInfo = null;
            this.errorMessage = null;

            console.log('التحقق من الرقم التسلسلي:', this.serialNumber);

            // إرسال طلب للتحقق من الشركة
            fetch('/accounts/api/check-serial-number/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    serial_number: this.serialNumber
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                this.loading = false;

                if (data.success) {
                    this.dbVerified = true;
                    this.companyName = data.company.name;
                    this.dbInfo = data.database;

                    // الانتقال إلى الخطوة التالية بعد ثانية واحدة
                    setTimeout(() => {
                        this.step = 2;
                    }, 1000);
                } else {
                    this.errorMessage = data.message || 'فشل في التحقق من الشركة';
                }
            })
            .catch(error => {
                this.loading = false;
                console.error('Error:', error);
                this.errorMessage = 'حدث خطأ أثناء التحقق من الشركة. يرجى المحاولة مرة أخرى.';
            });
        }
    </script>
</body>
</html>
