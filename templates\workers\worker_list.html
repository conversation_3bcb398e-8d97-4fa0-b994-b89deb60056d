{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}قائمة العمال{% endblock %}

{% block page_title %}قائمة العمال{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--text-secondary); text-decoration: none;">العمال</a></li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- بطاقة قائمة العمال -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>قائمة العمال</h5>
            <div>
                <a href="{% url 'workers:import_workers' %}" class="btn btn-light me-2">
                    <i class="fas fa-file-import me-1"></i> استيراد العمال من ملف إكسل
                </a>
                <a href="{% url 'workers:worker_create' %}" class="btn btn-light">
                    <i class="fas fa-plus me-1"></i> إضافة عامل جديد
                </a>
            </div>
        </div>
        
        <!-- تنبيه بالميزات الجديدة -->
        <div class="alert alert-info m-3">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-info-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading">ميزات جديدة!</h5>
                    <p class="mb-0">يمكنك الآن استيراد العمال من ملف إكسل بالنقر على زر "استيراد العمال من ملف إكسل".</p>
                    <p class="mb-0">كما يمكنك استخدام ميزة مسح جواز السفر عند إضافة عامل جديد أو تعديل بيانات عامل موجود.</p>
                </div>
            </div>
        </div>
        
        <div class="card-body">
            <!-- فلاتر البحث -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <form method="get" class="d-flex">
                        <input type="text" name="search" class="form-control me-2" placeholder="بحث..." value="{{ request.GET.search }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                    <div class="btn-group">
                        <a href="{% url 'workers:worker_list' %}" class="btn btn-outline-primary {% if not request.GET.status %}active{% endif %}">الكل</a>
                        <a href="{% url 'workers:worker_list' %}?status=active" class="btn btn-outline-primary {% if request.GET.status == 'active' %}active{% endif %}">نشط</a>
                        <a href="{% url 'workers:worker_list' %}?status=inactive" class="btn btn-outline-primary {% if request.GET.status == 'inactive' %}active{% endif %}">غير نشط</a>
                        <a href="{% url 'workers:worker_list' %}?status=on_leave" class="btn btn-outline-primary {% if request.GET.status == 'on_leave' %}active{% endif %}">في إجازة</a>
                        <a href="{% url 'workers:worker_list' %}?status=terminated" class="btn btn-outline-primary {% if request.GET.status == 'terminated' %}active{% endif %}">منتهي الخدمة</a>
                    </div>
                </div>
            </div>

            <!-- جدول العمال -->
            {% if workers %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>رقم جواز السفر</th>
                                <th>رقم التأشيرة</th>
                                <th>الجنسية</th>
                                <th>تاريخ انتهاء جواز السفر</th>
                                <th>تاريخ انتهاء التأشيرة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in workers %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <a href="{% url 'workers:worker_detail' worker.id %}" class="text-decoration-none">
                                            {{ worker.first_name }} {{ worker.last_name }}
                                        </a>
                                    </td>
                                    <td>{{ worker.passport_number }}</td>
                                    <td>{{ worker.visa_number }}</td>
                                    <td>{{ worker.get_nationality_display }}</td>
                                    <td>
                                        {{ worker.passport_expiry|date:"Y-m-d" }}
                                        {% if worker.passport_expiry and worker.passport_expiry < today %}
                                            <span class="badge bg-danger">منتهي</span>
                                        {% elif worker.passport_expiry and worker.passport_expiry < expiry_warning %}
                                            <span class="badge bg-warning">قريب من الانتهاء</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ worker.visa_expiry|date:"Y-m-d" }}
                                        {% if worker.visa_expiry and worker.visa_expiry < today %}
                                            <span class="badge bg-danger">منتهية</span>
                                        {% elif worker.visa_expiry and worker.visa_expiry < expiry_warning %}
                                            <span class="badge bg-warning">قريبة من الانتهاء</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if worker.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif worker.status == 'inactive' %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% elif worker.status == 'on_leave' %}
                                            <span class="badge bg-warning">في إجازة</span>
                                        {% elif worker.status == 'terminated' %}
                                            <span class="badge bg-dark">منتهي الخدمة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'workers:worker_detail' worker.id %}" class="btn btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'workers:worker_update' worker.id %}" class="btn btn-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'workers:worker_delete' worker.id %}" class="btn btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <!-- رسالة عند عدم وجود عمال -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا يوجد عمال متاحين.
                    {% if request.GET.search or request.GET.status %}
                        <a href="{% url 'workers:worker_list' %}" class="alert-link">عرض جميع العمال</a>
                    {% else %}
                        <a href="{% url 'workers:worker_create' %}" class="alert-link">إضافة عامل جديد</a>
                    {% endif %}
                </div>
            {% endif %}

            <!-- شرح أزرار الإجراءات -->
            <div class="alert alert-info mt-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading">أزرار الإجراءات</h6>
                        <p class="mb-0">يمكنك استخدام الأزرار التالية للقيام بالإجراءات المختلفة على العمال:</p>
                        <div class="mt-2">
                            <span class="btn btn-info btn-sm me-2"><i class="fas fa-eye"></i> عرض</span>
                            <span class="btn btn-primary btn-sm me-2"><i class="fas fa-edit"></i> تعديل</span>
                            <span class="btn btn-danger btn-sm"><i class="fas fa-trash"></i> حذف</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات الرئيسية -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                <a href="{% url 'workers:worker_create' %}" class="btn btn-lg btn-success">
                    <i class="fas fa-plus me-1"></i> إضافة عامل جديد
                </a>
                <a href="{% url 'workers:import_workers' %}" class="btn btn-lg btn-primary">
                    <i class="fas fa-file-import me-1"></i> استيراد العمال من ملف إكسل
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // يمكن إضافة أي كود JavaScript خاص بصفحة العمال هنا
    $(document).ready(function() {
        // تفعيل الجدول كـ DataTable
        $('.table').DataTable({
            "paging": false,
            "info": false,
            "searching": false,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
            }
        });
    });
</script>
{% endblock %}
