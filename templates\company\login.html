<!DOCTYPE html>
{% load static %}
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول الشركات | منصة استقدامي السحابية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#143D8D',
                        'secondary-blue': '#407BFF',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #143D8D;
            background-image: url('{% static "images/company-login-bg.jpg" %}?v={{ now|date:'U' }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }

        .backdrop-blur {
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .input-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 16px; /* زيادة المسافة من اليمين */
            color: #407BFF;
            z-index: 10;
            pointer-events: none; /* لمنع التداخل مع النص */
        }

        .password-toggle {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 16px; /* زيادة المسافة من اليسار */
            color: #6B7280;
            cursor: pointer;
            z-index: 10;
        }

        .input-with-icon {
            padding-right: 48px; /* زيادة المسافة */
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in-out;
        }

        /* حركات الشعار */
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .animate-pulse-slow {
            animation: pulse 4s ease-in-out infinite;
        }

        /* تحسين حقول الإدخال */
        .input-field {
            padding-right: 48px !important; /* زيادة المسافة من اليمين للأيقونة */
            padding-left: 16px !important;
            width: 100%;
            text-indent: 0 !important; /* منع إزاحة النص */
            text-align: right !important; /* محاذاة النص إلى اليمين */
        }

        /* حقل كلمة المرور مع أيقونة الإظهار/الإخفاء */
        .password-field {
            padding-right: 48px !important; /* زيادة المسافة من اليمين للأيقونة */
            padding-left: 48px !important; /* زيادة المسافة للأيقونة على اليسار */
            width: 100%;
            text-indent: 0 !important; /* منع إزاحة النص */
            text-align: right !important; /* محاذاة النص إلى اليمين */
        }

        /* تأثيرات حركية */
        .hover-effect {
            transition: all 0.3s ease;
        }

        .hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="bg-gray-100 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen p-6">
        <div class="w-full max-w-4xl shadow-xl rounded-xl overflow-hidden flex bg-white">

            <!-- قسم تعريف المنصة -->
            <div class="hidden md:flex w-1/2 bg-[#143D8D] text-white flex-col justify-between items-stretch rounded-tr-lg rounded-br-lg bg-cover bg-center"
                 style="background-image: url('{% static 'images/bg-pattern.svg' %}?v={{ now|date:'U' }}'); background-size: cover;">

                <!-- القسم العلوي: الشعار والعنوان والمزايا -->
                <div class="p-6 text-center flex-1 flex flex-col items-center justify-start w-full">
                    <!-- الشعار -->
                    <div class="flex justify-center items-center mb-4 mt-6">
                        <img src="{% static 'images/logo.webp' %}?v={{ now|date:'U' }}" alt="شعار المنصة"
                             class="w-28 h-28 object-contain drop-shadow-xl animate-float transition duration-700 ease-in-out hover:scale-105"
                             onerror="this.src='{% static 'images/logo-placeholder.png' %}?v={{ now|date:'U' }}'; this.onerror=null;">
                    </div>

                    <!-- العنوان والمزايا -->
                    <div class="space-y-4 w-full">
                        <h1 class="text-2xl font-bold">منصة استقدامي السحابية</h1>
                        <p class="text-sm text-blue-100">دخول الشركات</p>

                        <!-- قائمة الميزات -->
                        <ul class="space-y-3 text-sm text-blue-200">
                            <li class="flex items-center justify-center gap-2">
                                <i class="fas fa-globe text-blue-200"></i>
                                نظام إدارة مركزي وسهل
                            </li>
                            <li class="flex items-center justify-center gap-2">
                                <i class="fas fa-lock text-blue-200"></i>
                                أمان وحماية البيانات
                            </li>
                            <li class="flex items-center justify-center gap-2">
                                <i class="fas fa-chart-line text-blue-200"></i>
                                متابعة لحظية للأعمال
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- القسم السفلي: مميزات النظام -->
                <div class="px-6 py-4 text-sm text-blue-100 border-t border-white/10 w-full">
                    <p class="mb-2 font-semibold text-white">مميزات النظام:</p>
                    <ul class="list-disc list-inside space-y-1">
                        <li>إدارة كاملة للعمال والعقود والخدمات</li>
                        <li>تقارير آنية وتفصيلية</li>
                        <li>تحكم مرن بالصلاحيات والمستخدمين</li>
                        <li>نظام متعدد الشركات وقواعد البيانات</li>
                        <li>واجهة عربية بالكامل</li>
                    </ul>

                    <!-- معلومات النسخة والدعم الفني -->
                    <div class="text-xs text-blue-100 mt-4 flex justify-between items-center border-t border-white/5 pt-3 pb-2">
                        <span><strong>v2.5.0</strong> النسخة</span>
                        <span class="flex items-center gap-1">
                            <i class="fas fa-headset"></i> دعم فني متواصل
                        </span>
                    </div>
                </div>
            </div>

            <!-- قسم تسجيل الدخول -->
            <div class="w-full md:w-1/2 bg-white p-8 md:p-10 flex flex-col justify-center">
                <div class="flex flex-col items-center mb-8 md:hidden">
                    <div class="flex justify-center items-center mb-4">
                        <img src="{% static 'images/logo.webp' %}?v={{ now|date:'U' }}" alt="شعار المنصة"
                             class="w-28 h-28 object-contain drop-shadow-xl animate-float transition duration-700 ease-in-out hover:scale-105"
                             onerror="this.src='{% static 'images/logo-placeholder.png' %}?v={{ now|date:'U' }}'; this.onerror=null;">
                    </div>
                    <h1 class="text-xl font-bold text-primary-blue text-center leading-relaxed">منصة استقدامي السحابية</h1>
                    <p class="text-xs text-gray-500 mb-2">دخول الشركات</p>
                    <div class="text-xs text-gray-400 text-center">
                        <p><strong class="text-blue-500">v2.5.0</strong> النسخة</p>
                        <p class="flex items-center justify-center gap-1 mt-1">
                            <i class="fas fa-headset text-blue-400"></i> دعم فني متواصل
                        </p>
                    </div>
                </div>

                <h2 class="text-2xl font-bold text-primary-blue mb-2">مرحبًا بعودتك!</h2>
                <p class="text-sm text-gray-500 mb-6">يرجى تسجيل دخول الشركة للمتابعة.</p>

                {% if messages or error_message %}
                    {% if error_message %}
                        <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-4 flex items-center shadow-md">
                            <i class="fas fa-exclamation-circle text-red-500 ml-3 text-lg"></i>
                            <div>{{ error_message }}</div>
                            <button class="mr-auto text-red-500 hover:text-red-700" onclick="this.parentElement.remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    {% endif %}

                    {% for message in messages %}
                        {% if message.tags == 'success' %}
                            <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 rounded-lg mb-4 flex items-center shadow-md">
                                <i class="fas fa-check-circle text-green-500 ml-3 text-lg"></i>
                                <div>{{ message }}</div>
                                <button class="mr-auto text-green-500 hover:text-green-700" onclick="this.parentElement.remove()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% elif message.tags == 'error' %}
                            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-4 flex items-center shadow-md">
                                <i class="fas fa-exclamation-circle text-red-500 ml-3 text-lg"></i>
                                <div>{{ message }}</div>
                                <button class="mr-auto text-red-500 hover:text-red-700" onclick="this.parentElement.remove()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% else %}
                            <div class="bg-blue-100 border-r-4 border-blue-500 text-blue-700 p-4 rounded-lg mb-4 flex items-center shadow-md">
                                <i class="fas fa-info-circle text-blue-500 ml-3 text-lg"></i>
                                <div>{{ message }}</div>
                                <button class="mr-auto text-blue-500 hover:text-blue-700" onclick="this.parentElement.remove()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}

                <form action="{% url 'company:login' %}" method="POST" class="space-y-6" id="loginForm">
                    {% csrf_token %}
                    <input type="hidden" name="db_verified" id="db_verified" value="false">

                    <div>
                        <label class="block mb-2 font-semibold text-gray-700">اسم المستخدم</label>
                        <div class="relative">
                            <input type="text" name="username" class="input-field border border-gray-300 p-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary-blue focus:border-transparent shadow-sm hover-effect" placeholder="اسم المستخدم" required>
                            <span class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <i class="fas fa-user text-secondary-blue"></i>
                            </span>
                        </div>
                    </div>

                    <div>
                        <label class="block mb-2 font-semibold text-gray-700">كلمة المرور</label>
                        <div class="relative">
                            <input type="password" id="password-field" name="password" class="password-field border border-gray-300 p-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary-blue focus:border-transparent shadow-sm hover-effect" placeholder="كلمة المرور" required>
                            <span class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <i class="fas fa-lock text-secondary-blue"></i>
                            </span>
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 cursor-pointer" id="password-toggle">
                                <i class="fas fa-eye text-gray-500 hover:text-secondary-blue transition-colors"></i>
                            </span>
                        </div>
                    </div>

                    <div>
                        <label class="block mb-2 font-semibold text-gray-700">الرقم التسلسلي</label>
                        <div class="flex items-center gap-2">
                            <!-- زر التحقق قبل حقل الإدخال -->
                            <button type="button" id="checkSerialBtn" class="bg-green-600 hover:bg-green-700 text-white px-3 py-3 rounded-lg transition-all duration-300 flex items-center justify-center shadow-md hover:shadow-lg transform hover:-translate-y-px" title="التحقق من الرقم التسلسلي">
                                <i class="fas fa-check-circle"></i>
                            </button>

                            <!-- حقل الإدخال -->
                            <div class="relative w-full">
                                <input type="text" id="serial_number" name="serial_number" class="input-field border border-gray-300 p-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary-blue focus:border-transparent shadow-sm hover-effect" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19" required>
                                <span class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <i class="fas fa-key text-secondary-blue"></i>
                                </span>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">أدخل الرقم التسلسلي المكون من 16 حرف</p>

                        <!-- مؤشر التحميل -->
                        <div id="companyLoadingDisplay" class="hidden mt-3 rounded-lg bg-blue-50 px-4 py-3 text-sm flex items-center shadow-sm border border-blue-100">
                            <i class="fas fa-spinner fa-spin text-blue-600 ml-3"></i>
                            <span class="font-semibold text-blue-700">جاري التحقق من الرقم التسلسلي...</span>
                        </div>

                        <!-- عرض اسم الشركة -->
                        <div id="companyNameDisplay" class="hidden mt-3 rounded-lg bg-green-50 px-4 py-3 text-sm flex items-center shadow-sm border border-green-100 animate-fadeIn">
                            <i class="fas fa-building text-green-600 ml-3 text-lg"></i>
                            <div class="flex items-center">
                                <span class="text-green-600 ml-1">اسم الشركة:</span>
                                <span id="companyNameText" class="font-semibold text-green-700 mr-1"></span>
                            </div>
                        </div>

                        <!-- رسالة الخطأ -->
                        <div id="companyErrorDisplay" class="hidden mt-3 rounded-lg bg-red-50 px-4 py-3 text-sm flex items-center shadow-sm border border-red-100 animate-fadeIn">
                            <i class="fas fa-exclamation-circle text-red-600 ml-3 text-lg"></i>
                            <span id="companyErrorText" class="font-semibold text-red-700"></span>
                        </div>
                    </div>

                    <button type="submit" id="loginButton" class="w-full bg-secondary-blue hover:bg-primary-blue text-white font-semibold py-3 px-4 rounded-lg transition duration-300 shadow-md hover:shadow-lg mt-6 transform hover:-translate-y-0.5">
                        <i class="fas fa-sign-in-alt ml-2"></i> تسجيل الدخول
                    </button>
                </form>

                <div class="mt-8 text-center">
                    <button type="button" id="show-help-modal" class="text-sm text-secondary-blue hover:text-primary-blue transition duration-300 flex items-center justify-center mx-auto px-3 py-1.5 rounded-full hover:bg-blue-50">
                        <i class="fas fa-question-circle ml-2"></i> هل تواجه مشكلة في الدخول؟
                    </button>
                </div>

                <!-- Modal for help -->
                <div id="help-modal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 hidden">
                    <div class="bg-white p-6 rounded-xl shadow-2xl max-w-md w-full border border-gray-200 transform transition-all duration-300">
                        <div class="flex justify-between items-center mb-5 border-b border-gray-100 pb-4">
                            <h3 class="text-lg font-bold text-primary-blue flex items-center">
                                <i class="fas fa-question-circle text-secondary-blue ml-3 text-xl"></i>
                                مساعدة في تسجيل الدخول
                            </h3>
                            <button id="close-help-modal" class="text-gray-500 hover:text-red-500 transition duration-300 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                                <i class="fas fa-times text-lg"></i>
                            </button>
                        </div>

                        <div class="space-y-4">
                            <div class="bg-blue-50 p-5 rounded-xl border border-blue-100 shadow-sm">
                                <h4 class="font-bold mb-3 text-primary-blue flex items-center">
                                    <i class="fas fa-info-circle ml-2 text-secondary-blue"></i>
                                    معلومات تسجيل الدخول
                                </h4>
                                <p class="text-sm mb-3 text-gray-700">للدخول إلى النظام، تحتاج إلى:</p>
                                <ul class="text-sm space-y-3 pr-3">
                                    <li class="flex items-center">
                                        <i class="fas fa-user-circle text-secondary-blue ml-3 text-lg"></i>
                                        <span class="text-gray-700">اسم المستخدم الخاص بك</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-lock text-secondary-blue ml-3 text-lg"></i>
                                        <span class="text-gray-700">كلمة المرور</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-key text-secondary-blue ml-3 text-lg"></i>
                                        <span class="text-gray-700">الرقم التسلسلي للشركة (16 رقم)</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="bg-yellow-50 p-5 rounded-xl border border-yellow-100 shadow-sm">
                                <h4 class="font-bold mb-3 text-yellow-700 flex items-center">
                                    <i class="fas fa-exclamation-triangle ml-2 text-yellow-600"></i>
                                    في حال واجهت مشكلة
                                </h4>
                                <ul class="text-sm space-y-3 pr-3">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 ml-3 mt-0.5 text-lg"></i>
                                        <span class="text-gray-700">تأكد من إدخال اسم المستخدم وكلمة المرور بشكل صحيح</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 ml-3 mt-0.5 text-lg"></i>
                                        <span class="text-gray-700">تأكد من إدخال الرقم التسلسلي بشكل صحيح</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-headset text-green-500 ml-3 mt-0.5 text-lg"></i>
                                        <span class="text-gray-700">تواصل مع المسؤول الأعلى للنظام للحصول على المساعدة</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="text-center mt-6">
                            <button id="close-help-modal-btn" class="bg-secondary-blue hover:bg-primary-blue text-white font-semibold py-2.5 px-8 rounded-lg transition duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                                <i class="fas fa-check ml-2"></i> فهمت
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التأكد من إخفاء رسائل الخطأ عند تحميل الصفحة
            document.getElementById('companyErrorDisplay').classList.add('hidden');
            document.getElementById('companyNameDisplay').classList.add('hidden');
            document.getElementById('companyLoadingDisplay').classList.add('hidden');

            // Validación del formulario de inicio de sesión
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const dbVerifiedInput = document.getElementById('db_verified');

            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    // Verificar si la base de datos ha sido verificada
                    if (dbVerifiedInput.value !== 'true') {
                        e.preventDefault();

                        // Mostrar mensaje de error
                        showError('يجب التحقق من الرقم التسلسلي وقاعدة البيانات أولاً');

                        // Hacer scroll hasta el mensaje de error
                        document.getElementById('companyErrorDisplay').scrollIntoView({ behavior: 'smooth' });

                        return false;
                    }
                });
            }

            // Help modal functionality
            const showHelpModal = document.getElementById('show-help-modal');
            const closeHelpModal = document.getElementById('close-help-modal');
            const closeHelpModalBtn = document.getElementById('close-help-modal-btn');
            const helpModal = document.getElementById('help-modal');

            if (showHelpModal && closeHelpModal && helpModal) {
                // Show modal
                showHelpModal.addEventListener('click', function() {
                    helpModal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden'); // Prevent scrolling
                });

                // Close modal with X button
                closeHelpModal.addEventListener('click', function() {
                    helpModal.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                });

                // Close modal with "فهمت" button
                if (closeHelpModalBtn) {
                    closeHelpModalBtn.addEventListener('click', function() {
                        helpModal.classList.add('hidden');
                        document.body.classList.remove('overflow-hidden');
                    });
                }

                // Close modal when clicking outside
                helpModal.addEventListener('click', function(e) {
                    if (e.target === helpModal) {
                        helpModal.classList.add('hidden');
                        document.body.classList.remove('overflow-hidden');
                    }
                });

                // Close modal with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !helpModal.classList.contains('hidden')) {
                        helpModal.classList.add('hidden');
                        document.body.classList.remove('overflow-hidden');
                    }
                });
            }

            // Format serial number input
            const serialInput = document.querySelector('#serial_number');
            const checkSerialBtn = document.querySelector('#checkSerialBtn');

            if (serialInput) {
                serialInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/[^0-9A-Za-z]/g, '');
                    let formattedValue = '';

                    for (let i = 0; i < value.length && i < 16; i++) {
                        if (i > 0 && i % 4 === 0) {
                            formattedValue += '-';
                        }
                        formattedValue += value[i];
                    }

                    e.target.value = formattedValue;

                    // إخفاء عناصر العرض عند تغيير الإدخال
                    document.getElementById('companyNameDisplay').classList.add('hidden');
                    document.getElementById('companyErrorDisplay').classList.add('hidden');
                });

                // التحقق عند الضغط على Enter
                serialInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault(); // منع إرسال النموذج
                        checkCompanyName(serialInput.value);
                    }
                });
            }

            // التحقق عند النقر على زر التحقق
            if (checkSerialBtn) {
                checkSerialBtn.addEventListener('click', function() {
                    checkCompanyName(serialInput.value);
                });
            }

            // إظهار/إخفاء كلمة المرور
            const passwordField = document.getElementById('password-field');
            const passwordToggle = document.getElementById('password-toggle');

            if (passwordField && passwordToggle) {
                passwordToggle.addEventListener('click', function() {
                    // تبديل نوع الحقل بين password و text
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // تبديل الأيقونة بين العين المفتوحة والمغلقة
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.classList.toggle('fa-eye');
                        icon.classList.toggle('fa-eye-slash');
                    }
                });
            }

            // وظيفة التحقق من صحة قاعدة البيانات
            function verifyDatabase(serial) {
                // إظهار مؤشر التحميل
                document.getElementById('companyLoadingDisplay').classList.remove('hidden');
                document.getElementById('companyLoadingDisplay').innerHTML = `
                    <i class="fas fa-spinner fa-spin text-blue-600 ml-3"></i>
                    <span class="font-semibold text-blue-700">جاري التحقق من صحة قاعدة البيانات...</span>
                `;

                // استخدام API الداخلي للتحقق من صحة قاعدة البيانات
                fetch('/accounts/api/verify-database/', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Cache-Control': 'no-cache',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        serial_number: serial
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`فشل الاتصال بالخادم: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // إخفاء مؤشر التحميل
                    document.getElementById('companyLoadingDisplay').classList.add('hidden');

                    if (data.success) {
                        // تم التحقق من صحة قاعدة البيانات بنجاح
                        document.getElementById('companyNameDisplay').innerHTML = `
                            <i class="fas fa-database text-green-600 ml-3 text-lg"></i>
                            <div class="flex flex-col">
                                <span class="font-semibold text-green-700">${data.data.company_name}</span>
                                <span class="text-xs text-green-600">تم التحقق من صحة قاعدة البيانات (${data.data.tables_count} جدول)</span>
                            </div>
                        `;
                        document.getElementById('companyNameDisplay').classList.remove('hidden');

                        // تعيين قيمة حقل التحقق من قاعدة البيانات
                        document.getElementById('db_verified').value = 'true';

                        // تغيير لون زر تسجيل الدخول
                        const loginButton = document.getElementById('loginButton');
                        if (loginButton) {
                            loginButton.classList.remove('bg-secondary-blue', 'hover:bg-primary-blue');
                            loginButton.classList.add('bg-green-600', 'hover:bg-green-700');
                        }
                    } else {
                        // فشل التحقق من صحة قاعدة البيانات
                        showError(data.message || 'فشل التحقق من صحة قاعدة البيانات');
                    }
                })
                .catch(error => {
                    // إخفاء مؤشر التحميل وعرض رسالة الخطأ
                    document.getElementById('companyLoadingDisplay').classList.add('hidden');
                    showError('فشل التحقق من صحة قاعدة البيانات: ' + error.message);
                });
            }

            // وظيفة التحقق من اسم الشركة
            function checkCompanyName(serial) {
                // الحصول على زر التحقق مرة واحدة
                const checkSerialBtn = document.querySelector('#checkSerialBtn');

                // وظيفة مساعدة لعرض رسالة خطأ
                function showError(message, showRefreshButton = false) {
                    const errorDisplay = document.getElementById('companyErrorDisplay');
                    const errorText = document.getElementById('companyErrorText');

                    // إزالة أي أزرار إضافية من رسالة الخطأ السابقة
                    while (errorDisplay.childNodes.length > 2) {
                        errorDisplay.removeChild(errorDisplay.lastChild);
                    }

                    // تعيين نص الخطأ
                    errorText.textContent = message;

                    // تم إزالة زر تحديث الصفحة حسب المتطلبات

                    // إظهار رسالة الخطأ وإخفاء العناصر الأخرى
                    errorDisplay.classList.remove('hidden');
                    document.getElementById('companyNameDisplay').classList.add('hidden');
                    document.getElementById('companyLoadingDisplay').classList.add('hidden');
                }

                // وظيفة مساعدة لتغيير حالة زر التحقق
                function setButtonState(isLoading, isError = false, isSuccess = false) {
                    if (!checkSerialBtn) return;

                    // إعادة تعيين حالة الزر
                    checkSerialBtn.disabled = isLoading;
                    checkSerialBtn.classList.toggle('opacity-50', isLoading);

                    // إزالة جميع الفئات السابقة
                    checkSerialBtn.classList.remove(
                        'bg-green-600', 'hover:bg-green-700',
                        'bg-red-600', 'hover:bg-red-700',
                        'bg-blue-600', 'hover:bg-blue-700'
                    );

                    if (isError) {
                        // تعيين حالة الخطأ
                        checkSerialBtn.classList.add('bg-red-600', 'hover:bg-red-700');
                        checkSerialBtn.innerHTML = '<i class="fas fa-times"></i>';

                        // إعادة الزر إلى حالته الأصلية بعد 2 ثانية
                        setTimeout(() => {
                            checkSerialBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
                            checkSerialBtn.classList.add('bg-green-600', 'hover:bg-green-700');
                            checkSerialBtn.innerHTML = '<i class="fas fa-check-circle"></i>';
                        }, 2000);
                    } else if (isSuccess) {
                        // تعيين حالة النجاح
                        checkSerialBtn.classList.add('bg-green-600', 'hover:bg-green-700');
                        checkSerialBtn.innerHTML = '<i class="fas fa-check"></i>';
                    } else if (isLoading) {
                        // تعيين حالة التحميل
                        checkSerialBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                        checkSerialBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    } else {
                        // تعيين الحالة الافتراضية
                        checkSerialBtn.classList.add('bg-green-600', 'hover:bg-green-700');
                        checkSerialBtn.innerHTML = '<i class="fas fa-check-circle"></i>';
                    }
                }

                // التحقق من أن الرقم التسلسلي غير فارغ
                if (!serial || serial.trim() === '') {
                    showError('الرجاء إدخال الرقم التسلسلي');
                    return;
                }

                // تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
                const cleanSerial = serial.replace(/-/g, '').replace(/\s/g, '');

                // التحقق من طول الرقم التسلسلي
                if (cleanSerial.length !== 16) {
                    showError('الرقم التسلسلي يجب أن يتكون من 16 حرف');
                    return;
                }

                // التحقق من صحة الرقم التسلسلي (يجب أن يحتوي على أحرف وأرقام فقط)
                if (!/^[a-zA-Z0-9]+$/.test(cleanSerial)) {
                    showError('الرقم التسلسلي يجب أن يحتوي على أحرف وأرقام فقط');
                    return;
                }

                // إظهار مؤشر التحميل
                document.getElementById('companyLoadingDisplay').classList.remove('hidden');
                document.getElementById('companyNameDisplay').classList.add('hidden');
                document.getElementById('companyErrorDisplay').classList.add('hidden');

                // تعطيل زر التحقق أثناء التحميل وتغيير حالته
                setButtonState(true);

                // إعادة تنسيق الرقم التسلسلي (إضافة الشرطات)
                const formattedSerial = cleanSerial.match(/.{1,4}/g).join('-');

                console.log('التحقق من الرقم التسلسلي:', formattedSerial);

                // تحديث نص التحميل
                document.getElementById('companyLoadingDisplay').innerHTML = `
                    <i class="fas fa-spinner fa-spin text-blue-600 ml-3"></i>
                    <span class="font-semibold text-blue-700">جاري التحقق من الرقم التسلسلي...</span>
                `;

                // التحقق المباشر من الرقم التسلسلي بدون تأخير
                try {
                    console.log('بدء عملية التحقق من الرقم التسلسلي:', formattedSerial);

                    // لا نستخدم بيانات وهمية، نعتمد فقط على البيانات الحقيقية من قاعدة البيانات

                    // استخدام API الداخلي للتحقق من الرقم التسلسلي والتأكد من صحة قاعدة البيانات
                    const url = `/accounts/api/check-serial-number/`;
                    console.log('URL الطلب:', url);

                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Cache-Control': 'no-cache',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: JSON.stringify({
                            serial_number: formattedSerial
                        })
                    })
                    .then(response => {
                        console.log('استجابة الخادم:', response.status, response.statusText);
                        if (!response.ok) {
                            throw new Error(`فشل الاتصال بالخادم: ${response.status} ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // إخفاء مؤشر التحميل
                        document.getElementById('companyLoadingDisplay').classList.add('hidden');

                        console.log('بيانات الاستجابة:', data);

                        if (data.success) {
                            // عرض اسم الشركة
                            document.getElementById('companyNameText').textContent = data.data.company_name;
                            document.getElementById('companyNameDisplay').classList.remove('hidden');
                            document.getElementById('companyErrorDisplay').classList.add('hidden');

                            // تغيير لون زر التحقق إلى اللون الأخضر
                            setButtonState(false, false, true);

                            // تحديث حقل الإدخال بالرقم المنسق
                            document.getElementById('serial_number').value = formattedSerial;

                            // التحقق من صحة قاعدة البيانات
                            verifyDatabase(formattedSerial);
                        } else {
                            // عرض رسالة الخطأ
                            showError(data.message || 'لم يتم العثور على شركة بهذا الرقم التسلسلي');

                            // تغيير لون زر التحقق إلى اللون الأحمر
                            setButtonState(false, true);
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في عملية الجلب:', error);
                        // إخفاء مؤشر التحميل وعرض رسالة الخطأ

                        // التحقق مما إذا كان الخطأ متعلقًا بـ CSRF أو مشكلة اتصال
                        if (error.message && (
                            error.message.includes('CSRF') ||
                            error.message.includes('403') ||
                            error.message.includes('Forbidden')
                        )) {
                            showError('خطأ في التحقق من CSRF. يرجى تحديث الصفحة والمحاولة مرة أخرى.', true);
                        } else if (error.message && error.message.includes('فشل الاتصال بالخادم')) {
                            showError('فشل الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.', true);
                        } else {
                            showError('حدث خطأ أثناء التحقق من الرقم التسلسلي. يرجى المحاولة مرة أخرى.');
                        }

                        // تغيير لون زر التحقق إلى اللون الأحمر
                        setButtonState(false, true);
                    })
                    .finally(() => {
                        // إعادة تفعيل زر التحقق
                        if (checkSerialBtn) {
                            checkSerialBtn.disabled = false;
                            checkSerialBtn.classList.remove('opacity-50');
                        }
                    });
                } catch (error) {
                    console.error('خطأ غير متوقع في عملية الجلب:', error);

                    // تسجيل معلومات إضافية للتصحيح
                    console.error('معلومات الخطأ:', {
                        message: error.message,
                        stack: error.stack,
                        name: error.name,
                        serialNumber: formattedSerial
                    });

                    // عرض رسالة خطأ مع زر تحديث
                    showError('حدث خطأ غير متوقع أثناء التحقق من الرقم التسلسلي. يرجى تحديث الصفحة والمحاولة مرة أخرى.', true);
                    setButtonState(false, true);

                    // محاولة إعادة تحميل الصفحة تلقائيًا بعد 5 ثوانٍ
                    setTimeout(() => {
                        // التحقق مما إذا كان الخطأ لا يزال معروضًا
                        if (!document.getElementById('companyErrorDisplay').classList.contains('hidden')) {
                            console.log('إعادة تحميل الصفحة تلقائيًا بعد خطأ غير متوقع');
                            window.location.reload();
                        }
                    }, 5000);
                }
            }
        });
    </script>
</body>
</html>
