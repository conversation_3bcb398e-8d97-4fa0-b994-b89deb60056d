{% extends 'company/base.html' %}
{% load static %}

{% block title %}لوحة تحكم الشركة{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard-effects.css' %}">
{% endblock %}

{% block content %}
<div class="p-6">
    <h2 class="text-2xl font-bold text-[#143D8D] border-b-2 border-[#407BFF] pb-2 mb-6 inline-block">الملخص العام</h2>

    <!-- الإحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- بطاقة: عدد العمال -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#2196F3]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عدد العمال</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">إجمالي العمال المسجلين</p>
                    <div class="mt-2 text-[#2196F3] dark:text-pink-300 font-bold text-xl" data-stat="workers_count">{{ stats.workers_count }}</div>
                </div>
                <div class="text-4xl text-[#2196F3] dark:text-pink-300">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: عدد العملاء -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#4CAF50]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عدد العملاء</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">إجمالي العملاء المسجلين</p>
                    <div class="mt-2 text-[#4CAF50] dark:text-amber-300 font-bold text-xl" data-stat="clients_count">{{ stats.clients_count }}</div>
                </div>
                <div class="text-4xl text-[#4CAF50] dark:text-amber-300">
                    <i class="fas fa-user-tie"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: العقود النشطة -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#00C853]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">العقود النشطة</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العقود السارية حالياً</p>
                    <div class="mt-2 text-[#00C853] dark:text-violet-300 font-bold text-xl" data-stat="active_contracts">{{ stats.active_contracts }}</div>
                </div>
                <div class="text-4xl text-[#00C853] dark:text-violet-300">
                    <i class="fas fa-file-contract"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: العقود المنتهية -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#FF0000]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">العقود المنتهية</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العقود المنتهية صلاحيتها</p>
                    <div class="mt-2 text-[#FF0000] dark:text-red-300 font-bold text-xl" data-stat="expired_contracts">{{ stats.expired_contracts }}</div>
                </div>
                <div class="text-4xl text-[#FF0000] dark:text-red-300">
                    <i class="fas fa-file-contract"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: الخدمات اليومية -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#03A9F4]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">الخدمات اليومية</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">خدمات التنظيف اليومية</p>
                    <div class="mt-2 text-[#03A9F4] dark:text-blue-300 font-bold text-xl" data-stat="daily_services">{{ stats.daily_services }}</div>
                </div>
                <div class="text-4xl text-[#03A9F4] dark:text-blue-300">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: الخدمات الشهرية -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#9C27B0]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">الخدمات الشهرية</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">خدمات التنظيف الشهرية</p>
                    <div class="mt-2 text-[#9C27B0] dark:text-purple-300 font-bold text-xl" data-stat="monthly_services">{{ stats.monthly_services }}</div>
                </div>
                <div class="text-4xl text-[#9C27B0] dark:text-purple-300">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: عدد المستخدمين -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#3F51B5]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عدد المستخدمين</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">مستخدمي النظام</p>
                    <div class="mt-2 text-[#3F51B5] dark:text-teal-300 font-bold text-xl" data-stat="users_count">{{ stats.users_count|default:"3" }}</div>
                </div>
                <div class="text-4xl text-[#3F51B5] dark:text-teal-300">
                    <i class="fas fa-users-cog"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة: العقود المخصصة -->
        <div class="bg-white dark:bg-slate-800 shadow-md p-4 rounded hover:shadow-lg transition-all duration-300 border-r-4 border-[#FFC107]">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">العقود المخصصة</h3>
                    <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">عقود الخدمات المخصصة</p>
                    <div class="mt-2 text-[#FFC107] dark:text-orange-300 font-bold text-xl" data-stat="custom_contracts">{{ stats.custom_contracts|default:"12" }}</div>
                </div>
                <div class="text-4xl text-[#FFC107] dark:text-orange-300">
                    <i class="fas fa-file-signature"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- خط فاصل -->
    <hr class="my-8 border-gray-300">

    <!-- التنبيهات -->
    <div id="alerts_section" class="mb-8 {% if stats.expiring_contracts == 0 and stats.expiring_residencies == 0 %}hidden{% endif %}">
        <h2 class="text-2xl font-bold text-[#143D8D] border-b-2 border-[#407BFF] pb-2 mb-6 inline-block">التنبيهات</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- تنبيه العقود التي ستنتهي قريبًا -->
            <div class="bg-yellow-50 dark:bg-slate-800 border-r-4 border-yellow-400 dark:border-amber-300 text-yellow-800 dark:text-amber-300 p-4 rounded-lg shadow hover:shadow-md transition-all duration-300 flex justify-between items-center {% if stats.expiring_contracts == 0 %}hidden{% endif %}" role="alert">
                <div class="flex items-center">
                    <div class="flex-shrink-0 text-2xl ml-3 text-yellow-500 dark:text-amber-300">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <p class="font-bold mb-1 dark:text-white">تنبيه العقود</p>
                        <p class="dark:text-slate-300">هناك <span data-alert-count="contract_alerts_count">{{ stats.expiring_contracts }}</span> عقود ستنتهي خلال 7 أيام.</p>
                    </div>
                </div>
                <button type="button" class="bg-white dark:bg-slate-700 text-sm text-[#143D8D] dark:text-white hover:bg-[#143D8D] dark:hover:bg-slate-600 hover:text-white px-3 py-1 rounded shadow transition-colors duration-300 font-semibold" onclick="toggleContractAlerts()">عرض التفاصيل</button>
            </div>

            <!-- تنبيه الإقامات التي ستنتهي قريبًا -->
            <div class="bg-orange-50 dark:bg-slate-800 border-r-4 border-orange-400 dark:border-orange-300 text-orange-800 dark:text-orange-300 p-4 rounded-lg shadow hover:shadow-md transition-all duration-300 flex justify-between items-center {% if stats.expiring_residencies == 0 %}hidden{% endif %}" role="alert">
                <div class="flex items-center">
                    <div class="flex-shrink-0 text-2xl ml-3 text-orange-500 dark:text-orange-300">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div>
                        <p class="font-bold mb-1 dark:text-white">تنبيه الإقامات</p>
                        <p class="dark:text-slate-300">هناك <span data-alert-count="residency_alerts_count">{{ stats.expiring_residencies }}</span> إقامات ستنتهي خلال 30 يوم.</p>
                    </div>
                </div>
                <button type="button" class="bg-white dark:bg-slate-700 text-sm text-[#143D8D] dark:text-white hover:bg-[#143D8D] dark:hover:bg-slate-600 hover:text-white px-3 py-1 rounded shadow transition-colors duration-300 font-semibold" onclick="toggleResidencyAlerts()">عرض التفاصيل</button>
            </div>
        </div>

        <!-- تفاصيل تنبيهات العقود -->
        <div id="contract_alerts_details" class="mt-4 bg-white dark:bg-slate-800 rounded-lg shadow-md p-4 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">تفاصيل العقود التي ستنتهي قريباً</h3>
                <button type="button" class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white" onclick="toggleContractAlerts()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="contract_alerts_list" class="max-h-64 overflow-y-auto">
                <!-- سيتم ملء هذا القسم ديناميكياً بواسطة JavaScript -->
                <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                    جاري تحميل البيانات...
                </div>
            </div>
            <div class="mt-4 text-center">
                <a href="{% url 'company:contracts' %}" class="inline-block bg-[#143D8D] text-white px-4 py-2 rounded hover:bg-[#407BFF] transition-colors duration-300">
                    عرض جميع العقود
                </a>
            </div>
        </div>

        <!-- تفاصيل تنبيهات الإقامات -->
        <div id="residency_alerts_details" class="mt-4 bg-white dark:bg-slate-800 rounded-lg shadow-md p-4 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">تفاصيل الإقامات التي ستنتهي قريباً</h3>
                <button type="button" class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white" onclick="toggleResidencyAlerts()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="residency_alerts_list" class="max-h-64 overflow-y-auto">
                <!-- سيتم ملء هذا القسم ديناميكياً بواسطة JavaScript -->
                <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                    جاري تحميل البيانات...
                </div>
            </div>
            <div class="mt-4 text-center">
                <a href="{% url 'company:workers' %}" class="inline-block bg-[#143D8D] text-white px-4 py-2 rounded hover:bg-[#407BFF] transition-colors duration-300">
                    عرض جميع العمال
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- الأنشطة الأخيرة -->
        <div class="lg:col-span-2">
            <h2 class="text-2xl font-bold text-[#143D8D] border-b-2 border-[#407BFF] pb-2 mb-6 inline-block">الأنشطة الأخيرة</h2>
            <div class="bg-white shadow-md rounded-lg p-6">
                <ul id="recent_activities_list" class="border-r-2 border-gray-200 pr-6 space-y-6">
                    {% for activity in recent_activities|slice:":4" %}
                    <li class="relative activity-item">
                        <span class="absolute -right-8 top-0 w-4 h-4 bg-[#407BFF] rounded-full border-4 border-white"></span>
                        <div class="flex justify-between items-center w-full border-b border-gray-100 pb-2">
                            <div class="font-bold text-gray-800 dark:text-white">{{ activity.description }}</div>
                            <div class="text-gray-400 text-xs text-left w-40 text-left">{{ activity.date|date:"Y-m-d H:i" }}</div>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ activity.user }}</div>
                    </li>
                    {% empty %}
                    <li class="text-center text-gray-500 py-4">
                        <i class="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
                        <p>لا توجد أنشطة حديثة</p>
                    </li>
                    {% endfor %}
                </ul>

                <!-- رابط لعرض المزيد من الأنشطة -->
                {% if recent_activities|length > 4 %}
                <div class="text-center mt-4">
                    <a href="{% url 'company:activities' %}" class="text-[#143D8D] hover:text-[#407BFF] text-sm font-semibold">
                        عرض كافة الأنشطة <i class="fas fa-arrow-left ml-1"></i>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الروابط السريعة -->
        <div>
            <h2 class="text-2xl font-bold text-[#143D8D] border-b-2 border-[#407BFF] pb-2 mb-6 inline-block">الروابط السريعة</h2>
            <div class="grid grid-cols-1 gap-4">
                <a href="{% url 'company:workers' %}" class="bg-white dark:bg-slate-800 hover:shadow-lg transition-all duration-300 p-4 rounded-lg flex items-center space-x-3 rtl:space-x-reverse border border-gray-100 dark:border-slate-700 group">
                    <div class="text-[#143D8D] dark:text-pink-300 bg-blue-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-slate-700 transition-colors">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-[#143D8D] dark:text-white font-bold text-sm">إدارة العمال</h4>
                        <p class="text-xs text-gray-500 dark:text-slate-400">عرض وإضافة وتعديل بيانات العمال</p>
                    </div>
                </a>

                <a href="{% url 'company:clients' %}" class="bg-white dark:bg-slate-800 hover:shadow-lg transition-all duration-300 p-4 rounded-lg flex items-center space-x-3 rtl:space-x-reverse border border-gray-100 dark:border-slate-700 group">
                    <div class="text-green-600 dark:text-amber-300 bg-green-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-slate-700 transition-colors">
                        <i class="fas fa-user-tie text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-[#143D8D] dark:text-white font-bold text-sm">إدارة العملاء</h4>
                        <p class="text-xs text-gray-500 dark:text-slate-400">عرض وإضافة وتعديل بيانات العملاء</p>
                    </div>
                </a>

                <a href="{% url 'company:contracts' %}" class="bg-white dark:bg-slate-800 hover:shadow-lg transition-all duration-300 p-4 rounded-lg flex items-center space-x-3 rtl:space-x-reverse border border-gray-100 dark:border-slate-700 group">
                    <div class="text-purple-600 dark:text-violet-300 bg-purple-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-slate-700 transition-colors">
                        <i class="fas fa-file-contract text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-[#143D8D] dark:text-white font-bold text-sm">إدارة العقود</h4>
                        <p class="text-xs text-gray-500 dark:text-slate-400">عرض وإضافة وتعديل العقود</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/realtime-dashboard.js' %}"></script>
<script>
    // وظائف إظهار وإخفاء تفاصيل التنبيهات
    function toggleContractAlerts() {
        const detailsElement = document.getElementById('contract_alerts_details');
        detailsElement.classList.toggle('hidden');

        // إذا تم إظهار التفاصيل، قم بتحديث البيانات
        if (!detailsElement.classList.contains('hidden')) {
            updateDashboardAlerts();
        }
    }

    function toggleResidencyAlerts() {
        const detailsElement = document.getElementById('residency_alerts_details');
        detailsElement.classList.toggle('hidden');

        // إذا تم إظهار التفاصيل، قم بتحديث البيانات
        if (!detailsElement.classList.contains('hidden')) {
            updateDashboardAlerts();
        }
    }
</script>
{% endblock %}