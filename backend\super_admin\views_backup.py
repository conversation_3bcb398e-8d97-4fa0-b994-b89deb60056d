"""
وظائف النسخ الاحتياطي واستعادة قواعد البيانات
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, FileResponse
from django.utils import timezone
import os
import json
from .models import Company, SystemLog, DatabaseBackup, BackupSchedule
from .backup_utils import restore_company_database, delete_backup, clean_old_backups
# التحقق من صلاحيات المسؤول الأعلى
def is_super_admin(user):
    """التحقق مما إذا كان المستخدم مسؤولاً أعلى (منشئ النظام)"""
    return user.is_superuser

# عرض صفحة النسخ الاحتياطي
@login_required
def system_backup_schedule(request):
    """صفحة جدولة النسخ الاحتياطي للنظام"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على جدولة النسخ الاحتياطي للنظام
    schedule = BackupSchedule.objects.filter(company=None).first()

    context = {
        'schedule': schedule,
        'company': None,
    }

    return render(request, 'super_admin/backup/schedule.html', context)

# عرض تفاصيل النسخة الاحتياطية
@login_required
def backup_details(request, filename):
    """عرض تفاصيل النسخة الاحتياطية"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # التحقق من وجود الملف
    backup_dir = os.path.join(os.getcwd(), 'backups')
    backup_file = os.path.join(backup_dir, filename)

    if not os.path.exists(backup_file):
        messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
        return redirect('super_admin:system_backup')

    # الحصول على معلومات الملف
    file_stats = os.stat(backup_file)
    try:
        created_at = timezone.datetime.fromtimestamp(file_stats.st_birthtime).strftime('%Y-%m-%d %H:%M:%S')
    except AttributeError:
        # st_birthtime غير متوفر في بعض أنظمة التشغيل
        created_at = timezone.datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
    size = file_stats.st_size
    size_formatted = f"{size / (1024 * 1024):.2f} MB"

    # محاولة قراءة جزء من محتوى الملف (أول 50 سطر فقط)
    backup_content = None
    try:
        with open(backup_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()[:50]
            backup_content = ''.join(lines)
    except:
        backup_content = "لا يمكن عرض محتوى الملف"

    # البحث عن معلومات إضافية في ملف JSON (إذا وجد)
    info_file = os.path.join(backup_dir, f"{os.path.splitext(filename)[0]}_info.json")
    backup_info = {}
    if os.path.exists(info_file):
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
        except:
            pass

    # إنشاء كائن النسخة الاحتياطية للعرض
    backup = {
        'filename': filename,
        'created_at': backup_info.get('created_at', created_at),
        'size': size,
        'size_formatted': size_formatted,
        'type': backup_info.get('backup_type', 'system'),
        'description': backup_info.get('description', ''),
        'created_by': backup_info.get('created_by', ''),
    }

    # إذا كانت النسخة الاحتياطية لشركة محددة
    if 'company_id' in backup_info:
        try:
            company = Company.objects.get(id=backup_info['company_id'])
            backup['company'] = company
        except:
            pass

    context = {
        'backup': backup,
        'backup_content': backup_content,
    }

    return render(request, 'super_admin/backup/details.html', context)

# عرض صفحة استعادة النسخة الاحتياطية
@login_required
def restore_backup_view(request, filename):
    """عرض صفحة استعادة النسخة الاحتياطية"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # التحقق من وجود الملف
    backup_dir = os.path.join(os.getcwd(), 'backups')
    backup_file = os.path.join(backup_dir, filename)

    if not os.path.exists(backup_file):
        messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
        return redirect('super_admin:system_backup')

    # الحصول على معلومات الملف
    file_stats = os.stat(backup_file)
    try:
        created_at = timezone.datetime.fromtimestamp(file_stats.st_birthtime).strftime('%Y-%m-%d %H:%M:%S')
    except AttributeError:
        # st_birthtime غير متوفر في بعض أنظمة التشغيل
        created_at = timezone.datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
    size = file_stats.st_size
    size_formatted = f"{size / (1024 * 1024):.2f} MB"

    # البحث عن معلومات إضافية في ملف JSON (إذا وجد)
    info_file = os.path.join(backup_dir, f"{os.path.splitext(filename)[0]}_info.json")
    backup_info = {}
    if os.path.exists(info_file):
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
        except:
            pass

    # إنشاء كائن النسخة الاحتياطية للعرض
    backup = {
        'filename': filename,
        'created_at': backup_info.get('created_at', created_at),
        'size': size,
        'size_formatted': size_formatted,
        'type': backup_info.get('backup_type', 'system'),
    }

    context = {
        'backup': backup,
    }

    return render(request, 'super_admin/backup/restore.html', context)

# استعادة نسخة احتياطية
@login_required
def restore_backup(request):
    """استعادة نسخة احتياطية لقواعد البيانات"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    if request.method == 'POST':
        try:
            # التحقق من وجود طلب AJAX
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                backup_id = request.POST.get('backup_id')

                if not backup_id:
                    return JsonResponse({'success': False, 'message': 'لم يتم تحديد النسخة الاحتياطية'})

                # استدعاء دالة استعادة النسخة الاحتياطية
                success, message = restore_company_database(backup_id, request.user)

                return JsonResponse({'success': success, 'message': message})

            # إذا لم يكن طلب AJAX، إعادة التوجيه إلى صفحة النسخ الاحتياطي
            return redirect('super_admin:system_backup')
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}'})

    # إذا لم يكن طلب POST، إعادة التوجيه إلى صفحة النسخ الاحتياطي
    return redirect('super_admin:system_backup')

# تنزيل نسخة احتياطية
@login_required
def download_system_backup(request, filename):
    """تنزيل ملف نسخة احتياطية للنظام"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    try:
        # الحصول على النسخة الاحتياطية
        backup = get_object_or_404(DatabaseBackup, id=filename)

        # التحقق من وجود الملف
        if not os.path.exists(backup.file_path):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('super_admin:system_backup')

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='download',
            company=backup.company,
            description=f'تم تنزيل النسخة الاحتياطية: {backup.file_name}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إرجاع الملف للتنزيل
        response = FileResponse(open(backup.file_path, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{backup.file_name}"'
        return response
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء تنزيل النسخة الاحتياطية: {str(e)}')
        return redirect('super_admin:system_backup')

# حذف نسخة احتياطية
@login_required
def delete_system_backup(request, filename):
    """حذف ملف نسخة احتياطية للنظام"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    try:
        # الحصول على النسخة الاحتياطية
        backup = get_object_or_404(DatabaseBackup, id=filename)

        # استدعاء دالة حذف النسخة الاحتياطية
        success, message = delete_backup(backup.id, request.user)

        if success:
            messages.success(request, message)
        else:
            messages.error(request, message)
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}')

    return redirect('super_admin:system_backup')

# تنظيف النسخ الاحتياطية القديمة
@login_required
def clean_old_system_backups(request):
    """حذف النسخ الاحتياطية القديمة للنظام"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    try:
        # استدعاء دالة تنظيف النسخ الاحتياطية القديمة
        company_id = request.GET.get('company_id')
        retention_days = int(request.GET.get('retention_days', 30))

        count = clean_old_backups(company_id, retention_days)

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='delete',
            description=f'تم حذف {count} من النسخ الاحتياطية القديمة',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم حذف {count} من النسخ الاحتياطية القديمة بنجاح')
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء تنظيف النسخ الاحتياطية القديمة: {str(e)}')

    return redirect('super_admin:system_backup')

# تنزيل نسخة احتياطية لشركة محددة
@login_required
def download_backup(request, company_id, filename):
    """تنزيل ملف نسخة احتياطية للشركة"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    try:
        # الحصول على الشركة
        company = get_object_or_404(Company, id=company_id)

        # الحصول على النسخة الاحتياطية
        backup = get_object_or_404(DatabaseBackup, company=company, file_name=filename)

        # التحقق من وجود الملف
        if not os.path.exists(backup.file_path):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('super_admin:company_detail', company_id=company_id)

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='download',
            company=company,
            description=f'تم تنزيل النسخة الاحتياطية: {filename}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إرجاع الملف للتنزيل
        response = FileResponse(open(backup.file_path, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء تنزيل النسخة الاحتياطية: {str(e)}')
        return redirect('super_admin:company_detail', company_id=company_id)

# رفع نسخة احتياطية
@login_required
def upload_backup(request, company_id):
    """رفع ملف نسخة احتياطية"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على الشركة
    company = get_object_or_404(Company, id=company_id)

    if request.method == 'POST':
        try:
            # التحقق من وجود ملف
            if 'backup_file' not in request.FILES:
                messages.error(request, 'لم يتم تحديد ملف النسخة الاحتياطية')
                return redirect('super_admin:company_detail', company_id=company_id)

            # الحصول على الملف
            backup_file = request.FILES['backup_file']

            # التحقق من امتداد الملف
            if not backup_file.name.endswith('.sqlite3'):
                messages.error(request, 'يجب أن يكون ملف النسخة الاحتياطية بامتداد .sqlite3')
                return redirect('super_admin:company_detail', company_id=company_id)

            # إنشاء مجلد النسخ الاحتياطية للشركة
            from .backup_utils import create_company_backup_directory
            backup_dir = create_company_backup_directory(company.database_name)

            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{company.database_name}_uploaded_{timestamp}.sqlite3"
            backup_path = os.path.join(backup_dir, backup_filename)

            # حفظ الملف
            with open(backup_path, 'wb+') as destination:
                for chunk in backup_file.chunks():
                    destination.write(chunk)

            # إنشاء سجل للنسخة الاحتياطية
            file_size = os.path.getsize(backup_path)
            DatabaseBackup.objects.create(
                company=company,
                file_name=backup_filename,
                file_path=backup_path,
                file_size=file_size,
                backup_type='manual',
                description=f'تم رفع النسخة الاحتياطية بواسطة {request.user.username}',
                created_by=request.user
            )

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='upload',
                company=company,
                description=f'تم رفع نسخة احتياطية: {backup_filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم رفع النسخة الاحتياطية بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء رفع النسخة الاحتياطية: {str(e)}')

    return redirect('super_admin:company_detail', company_id=company_id)
