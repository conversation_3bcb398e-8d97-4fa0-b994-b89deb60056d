from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.http import JsonResponse, HttpResponse, FileResponse
from django.contrib.auth.models import User
from django.conf import settings
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
import os
import string
import random
import subprocess
import datetime
import json

# استيراد إعدادات البريد الإلكتروني
try:
    from ..email_settings import *
except ImportError:
    # إعدادات البريد الإلكتروني الافتراضية
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
    DEFAULT_FROM_EMAIL = '<EMAIL>'
from .models import Company, SuperAdminUser, SystemLog, SystemSettings, BackupSchedule, FailedLoginAttempt, DatabaseBackup, BlockedSerial, AdminLoginAttempt
from .forms import CompanyForm, SuperAdminLoginForm, BlockedSerialForm, TwoFactorForm
from .database_utils import (
    extract_database_schema, reset_database, clone_database_structure,
    create_empty_database, get_database_tables_count, get_database_size, format_size
)
# from .views_database import database_management, view_database_schema

# التحقق من صلاحيات المسؤول الأعلى
def is_super_admin(user):
    """التحقق مما إذا كان المستخدم مسؤولاً أعلى (منشئ النظام)"""
    return user.is_superuser and user.username == 'admin' and hasattr(user, 'super_admin_profile')

# صفحة تسجيل الدخول للمسؤول الأعلى
@csrf_exempt
def super_admin_login(request):
    """صفحة تسجيل الدخول للمسؤول الأعلى"""
    import time
    from datetime import datetime, timedelta

    # التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
    if request.user.is_authenticated and is_super_admin(request.user):
        return redirect('super_admin:dashboard')

    # الحصول على عنوان IP للمستخدم
    ip_address = request.META.get('REMOTE_ADDR', '0.0.0.0')

    # التحقق من وجود محاولات فاشلة سابقة من نفس عنوان IP
    failed_attempts = AdminLoginAttempt.objects.filter(
        ip_address=ip_address,
        is_successful=False,
        created_at__gte=timezone.now() - timedelta(minutes=15)
    ).count()

    # إذا كان هناك أكثر من 5 محاولات فاشلة خلال الـ 15 دقيقة الماضية، قم بتأخير الاستجابة
    if failed_attempts >= 5:
        # تأخير الاستجابة بمقدار ثانية واحدة لكل محاولة فاشلة (بحد أقصى 10 ثوانٍ)
        delay_seconds = min(failed_attempts, 10)
        time.sleep(delay_seconds)

    # الحصول على اسم المستخدم من الجلسة أو النموذج
    username = request.session.get('super_admin_username', '') or request.POST.get('username', '')

    # تهيئة النموذج
    if request.method == 'POST':
        form = SuperAdminLoginForm(request.POST)
    else:
        form = SuperAdminLoginForm(initial={'username': username})

    # معالجة طلب إرسال رمز جديد
    if request.method == 'POST' and 'resend_code' in request.POST:
        if username:
            try:
                user = User.objects.get(username=username, is_superuser=True)
                from .email_verification import send_verification_code
                success, code = send_verification_code(user, user.email)
                if success:
                    # تسجيل العملية
                    SystemLog.objects.create(
                        user=None,
                        action='update',
                        description=f'تم طلب إرسال رمز الدخول الآمن لحساب {username}',
                        ip_address=ip_address
                    )
                    messages.success(request, f'تم إرسال رمز جديد إلى البريد الإلكتروني: {user.email}')
                else:
                    messages.error(request, 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود أو ليس مسؤولًا أعلى.')
        else:
            messages.error(request, 'يرجى إدخال اسم المستخدم أولاً.')

        # إعادة عرض صفحة تسجيل الدخول
        return render(request, 'super_admin/login.html', {
            'form': form,
            'current_year': timezone.now().year,
            'failed_attempts': failed_attempts,
            'username': username
        })

    # معالجة طلب تسجيل الدخول
    if request.method == 'POST' and 'login' in request.POST:
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            secure_code = request.POST.get('secure_code', '').strip()

            # إضافة تأخير بسيط لمنع هجمات القوة الغاشمة
            time.sleep(0.5)

            # محاولة العثور على المستخدم أولاً للتحقق من قفل الحساب وتقييد IP
            try:
                user_obj = User.objects.get(username=username)

                # التحقق من وجود ملف تعريف المسؤول الأعلى
                if hasattr(user_obj, 'super_admin_profile'):
                    super_admin_profile = user_obj.super_admin_profile

                    # التحقق مما إذا كان الحساب مقفلاً
                    if super_admin_profile.is_account_locked():
                        # حساب الوقت المتبقي للقفل
                        remaining_time = (super_admin_profile.account_locked_until - timezone.now()).total_seconds() / 60
                        messages.error(request, f'تم قفل الحساب مؤقتًا. يرجى المحاولة مرة أخرى بعد {int(remaining_time)} دقيقة.')
                        return render(request, 'super_admin/login.html', {
                            'form': form,
                            'current_year': timezone.now().year,
                            'failed_attempts': failed_attempts
                        })

                    # التحقق من تقييد عنوان IP
                    if super_admin_profile.ip_restriction_enabled and not super_admin_profile.is_ip_allowed(ip_address):
                        messages.error(request, 'غير مسموح بالوصول من عنوان IP الخاص بك.')
                        return render(request, 'super_admin/login.html', {
                            'form': form,
                            'current_year': timezone.now().year,
                            'failed_attempts': failed_attempts
                        })
            except User.DoesNotExist:
                pass

            # محاولة المصادقة
            user = authenticate(request, username=username, password=password)

            if user is not None and user.is_superuser:
                # التحقق من وجود ملف تعريف المسؤول الأعلى
                super_admin_profile, created = SuperAdminUser.objects.get_or_create(user=user)

                # حفظ اسم المستخدم في الجلسة
                request.session['super_admin_username'] = username

                # إذا كان اسم المستخدم هو MUNTADER_WISSAM، تأكد من أنه مسؤول أعلى
                if user.username == 'MUNTADER_WISSAM':
                    user.is_superuser = True
                    user.is_staff = True
                    user.save()

                # الحصول على طريقة المصادقة المختارة
                auth_method = request.POST.get('auth_method', 'email')

                # التحقق من رمز الدخول الآمن
                if not secure_code:
                    # إرسال رمز تحقق جديد إذا لم يدخل المستخدم رمزًا (فقط لطريقة البريد الإلكتروني)
                    if auth_method == 'email':
                        from .email_verification import send_verification_code
                        success, code = send_verification_code(user, user.email)
                        if success:
                            messages.success(request, f'تم إرسال رمز الدخول الآمن إلى البريد الإلكتروني: {user.email}')
                        else:
                            messages.error(request, 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.')
                    else:
                        # إذا كان المستخدم يستخدم تطبيق Authenticator، نطلب منه إدخال الرمز
                        messages.info(request, 'يرجى إدخال الرمز من تطبيق Authenticator')
                else:
                    # التحقق من الرمز المدخل حسب طريقة المصادقة
                    verification_success = False

                    if auth_method == 'email':
                        # التحقق من رمز البريد الإلكتروني
                        from .email_verification import verify_code
                        verification_success = verify_code(user.id, secure_code)
                    else:
                        # التحقق من رمز تطبيق Authenticator
                        if super_admin_profile.two_factor_secret:
                            verification_success = super_admin_profile.verify_totp_code(secure_code)
                        else:
                            messages.error(request, 'لم يتم إعداد تطبيق Authenticator بعد. يرجى استخدام رمز البريد الإلكتروني أو إعداد التطبيق أولاً.')

                    if verification_success:
                        # إعادة تعيين عدد محاولات تسجيل الدخول الفاشلة
                        super_admin_profile.reset_failed_login_attempts()

                        # تحديث آخر عنوان IP للتسجيل
                        super_admin_profile.last_login_ip = ip_address
                        super_admin_profile.save()

                        # تسجيل محاولة تسجيل الدخول الناجحة
                        AdminLoginAttempt.objects.create(
                            username=username,
                            ip_address=ip_address,
                            user_agent=request.META.get('HTTP_USER_AGENT', ''),
                            is_successful=True
                        )

                        # تسجيل الدخول
                        login(request, user)

                        # تسجيل عملية تسجيل الدخول
                        auth_method_desc = "البريد الإلكتروني" if auth_method == "email" else "تطبيق Authenticator"
                        SystemLog.objects.create(
                            user=user,
                            action='login',
                            description=f'تم تسجيل الدخول باستخدام المصادقة الثنائية عبر {auth_method_desc}',
                            ip_address=ip_address
                        )

                        messages.success(request, 'تم تسجيل الدخول بنجاح')
                        return redirect('super_admin:dashboard')
                    else:
                        # رمز التحقق غير صحيح
                        if auth_method == 'email':
                            messages.error(request, 'رمز الدخول الآمن غير صحيح أو انتهت صلاحيته. يرجى التأكد من الرمز أو طلب رمز جديد.')
                        else:
                            messages.error(request, 'رمز تطبيق Authenticator غير صحيح. يرجى التأكد من الرمز الحالي في التطبيق.')
            else:
                # تسجيل محاولة تسجيل الدخول الفاشلة
                AdminLoginAttempt.objects.create(
                    username=username,
                    ip_address=ip_address,
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    is_successful=False
                )

                # تسجيل محاولة تسجيل الدخول الفاشلة في سجل النظام
                SystemLog.objects.create(
                    user=None,
                    action='login_failed',
                    description=f'محاولة تسجيل دخول فاشلة باستخدام اسم المستخدم: {username}',
                    ip_address=ip_address
                )

                # تحديث عدد محاولات تسجيل الدخول الفاشلة للمستخدم إذا كان موجودًا
                try:
                    user_obj = User.objects.get(username=username)
                    if hasattr(user_obj, 'super_admin_profile'):
                        user_obj.super_admin_profile.record_failed_login()
                except User.DoesNotExist:
                    pass

                # إذا كان هناك أكثر من 3 محاولات فاشلة، عرض رسالة تحذير
                if failed_attempts >= 3:
                    messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة. سيتم تأخير المحاولات المتكررة لأسباب أمنية.')
                else:
                    messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')

    # إضافة السنة الحالية والوقت الحالي إلى سياق القالب
    context = {
        'form': form,
        'current_year': timezone.now().year,
        'failed_attempts': failed_attempts,
        'username': username,
        'now': timezone.now()
    }

    return render(request, 'super_admin/login.html', context)

# صفحة التحقق الثنائي
def super_admin_2fa(request):
    """صفحة التحقق الثنائي للمسؤول الأعلى"""
    import pyotp

    if not hasattr(request.user, 'super_admin_profile'):
        logout(request)
        return redirect('super_admin:super_admin_login')

    super_admin_profile = request.user.super_admin_profile

    # التحقق من وجود سر للتحقق الثنائي
    if not super_admin_profile.two_factor_secret:
        # إنشاء سر جديد للتحقق الثنائي
        secret = pyotp.random_base32()
        super_admin_profile.two_factor_secret = secret
        super_admin_profile.two_factor_enabled = True
        super_admin_profile.save()

        # إعادة التوجيه إلى صفحة إعداد التحقق الثنائي
        return redirect('setup_2fa')

    # إرسال رمز المصادقة الثنائية عبر البريد الإلكتروني إذا تم طلبه
    if request.method == 'POST' and 'send_email_code' in request.POST:
        success, code = super_admin_profile.send_2fa_code_email()
        if success:
            messages.success(request, f'تم إرسال رمز المصادقة الثنائية إلى بريدك الإلكتروني: {request.user.email}')
        else:
            messages.error(request, 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.')

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description='تم طلب إرسال رمز المصادقة الثنائية عبر البريد الإلكتروني',
            ip_address=request.META.get('REMOTE_ADDR')
        )

    form = TwoFactorForm(request.POST or None)

    if request.method == 'POST' and 'verify_code' in request.POST:
        if form.is_valid():
            code = form.cleaned_data['code']

            # التحقق من الرمز باستخدام وظيفة التحقق من البريد الإلكتروني
            from .email_verification import verify_code as verify_email_code

            if code and verify_email_code(request.user.id, code):
                # تحديث آخر تسجيل دخول
                super_admin_profile.last_login_ip = request.META.get('REMOTE_ADDR')
                super_admin_profile.save()

                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='login',
                    description='تم تسجيل الدخول باستخدام المصادقة الثنائية',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

                messages.success(request, 'تم التحقق بنجاح')
                return redirect('super_admin:dashboard')
            else:
                messages.error(request, 'الرمز غير صحيح أو انتهت صلاحيته')

                # تسجيل محاولة فاشلة
                SystemLog.objects.create(
                    user=request.user,
                    action='login',
                    description='محاولة فاشلة للمصادقة الثنائية',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

    return render(request, 'super_admin/2fa.html', {'form': form})

# إعادة إرسال رمز المصادقة الثنائية
def resend_2fa_code(request):
    """إعادة إرسال رمز المصادقة الثنائية عبر البريد الإلكتروني"""
    if request.method == 'POST':
        username = request.POST.get('username')

        if not username:
            messages.error(request, 'يرجى إدخال اسم المستخدم.')
            return redirect('super_admin:super_admin_login')

        try:
            # استخدام select_for_update لتجنب مشكلة قفل قاعدة البيانات
            from django.db import transaction

            with transaction.atomic():
                user = User.objects.select_for_update().get(username=username)
                if hasattr(user, 'super_admin_profile'):
                    # استخدام وظيفة إرسال رمز التحقق عبر البريد الإلكتروني
                    from .email_verification import send_verification_code

                    # إرسال رمز التحقق
                    success, code = send_verification_code(user, user.email)

                    if success:
                        # حفظ اسم المستخدم في الجلسة
                        request.session['super_admin_username'] = username

                        messages.success(request, f'تم إرسال رمز الدخول الآمن إلى البريد الإلكتروني: {user.email}')

                        # تسجيل العملية
                        try:
                            SystemLog.objects.create(
                                user=None,
                                action='update',
                                description=f'تم طلب إرسال رمز الدخول الآمن لحساب {username}',
                                ip_address=request.META.get('REMOTE_ADDR')
                            )
                        except Exception as e:
                            print(f"خطأ في تسجيل العملية: {e}")
                    else:
                        messages.error(request, 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى لاحقًا.')
                else:
                    messages.error(request, 'المستخدم غير موجود أو ليس مسؤولًا أعلى.')
        except User.DoesNotExist:
            messages.error(request, 'المستخدم غير موجود.')
        except Exception as e:
            # التقاط أي أخطاء أخرى
            print(f"خطأ في إعادة إرسال رمز التحقق: {e}")
            messages.error(request, 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى لاحقًا.')

    return redirect('super_admin:super_admin_login')

# صفحة إعداد التحقق الثنائي
def setup_2fa(request):
    """صفحة إعداد التحقق الثنائي للمسؤول الأعلى"""
    from .email_verification import send_verification_code, verify_code

    if not hasattr(request.user, 'super_admin_profile'):
        logout(request)
        return redirect('super_admin:super_admin_login')

    super_admin_profile = request.user.super_admin_profile

    # إرسال رمز التحقق عبر البريد الإلكتروني عند الوصول إلى الصفحة
    if request.method == 'GET':
        success, code = send_verification_code(request.user, request.user.email)
        if success:
            messages.success(request, f'تم إرسال رمز التحقق إلى بريدك الإلكتروني: {request.user.email}')
        else:
            messages.error(request, 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.')

    if request.method == 'POST':
        code = request.POST.get('code')

        # التحقق من الرمز
        if code and verify_code(request.user.id, code):
            # تفعيل التحقق الثنائي
            super_admin_profile.two_factor_enabled = True
            super_admin_profile.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم تفعيل المصادقة الثنائية',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم تفعيل المصادقة الثنائية بنجاح')
            return redirect('super_admin:dashboard')
        else:
            messages.error(request, 'الرمز غير صحيح أو انتهت صلاحيته، يرجى المحاولة مرة أخرى')
            # إعادة إرسال رمز جديد
            success, code = send_verification_code(request.user, request.user.email)
            if success:
                messages.info(request, 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني')

    context = {
        'email': request.user.email
    }

    return render(request, 'super_admin/setup_2fa.html', context)

# صفحة إعداد المصادقة الثنائية باستخدام تطبيق Authenticator
def setup_authenticator(request):
    """صفحة إعداد المصادقة الثنائية باستخدام تطبيق Authenticator"""
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول للوصول إلى صفحة إعداد المصادقة الثنائية')
        return redirect('super_admin:super_admin_login')

    # التحقق من صلاحيات المسؤول الأعلى
    if not request.user.is_superuser:
        messages.error(request, 'ليس لديك صلاحيات كافية للوصول إلى صفحة إعداد المصادقة الثنائية')
        return redirect('super_admin:super_admin_login')

    # إنشاء ملف تعريف المسؤول الأعلى إذا لم يكن موجودًا
    if not hasattr(request.user, 'super_admin_profile'):
        try:
            from .models import SuperAdminUser
            SuperAdminUser.objects.create(user=request.user)
            messages.success(request, 'تم إنشاء ملف تعريف المسؤول الأعلى بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء ملف تعريف المسؤول الأعلى: {str(e)}')
            return redirect('super_admin:super_admin_login')

    super_admin_profile = request.user.super_admin_profile

    # إنشاء سر TOTP جديد إذا لم يكن موجودًا
    if not super_admin_profile.two_factor_secret:
        secret = super_admin_profile.generate_totp_secret()
    else:
        secret = super_admin_profile.two_factor_secret

    # إنشاء رمز QR
    qr_code = super_admin_profile.generate_qr_code_image()

    if request.method == 'POST' and 'verify_code' in request.POST:
        code = request.POST.get('code')

        # التحقق من الرمز
        if code and super_admin_profile.verify_totp_code(code):
            # تفعيل التحقق الثنائي
            super_admin_profile.two_factor_enabled = True
            super_admin_profile.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم تفعيل المصادقة الثنائية باستخدام تطبيق Authenticator',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم تفعيل المصادقة الثنائية باستخدام تطبيق Authenticator بنجاح')
            return redirect('super_admin:dashboard')
        else:
            messages.error(request, 'الرمز غير صحيح، يرجى التأكد من الرمز الحالي في تطبيق Authenticator')

    context = {
        'secret': secret,
        'qr_code': qr_code
    }

    return render(request, 'super_admin/setup_authenticator.html', context)

# لوحة تحكم المسؤول الأعلى
def super_admin_dashboard(request):
    """لوحة تحكم المسؤول الأعلى"""
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول للوصول إلى لوحة التحكم')
        return redirect('super_admin:login')

    # التحقق من صلاحيات المسؤول الأعلى
    if not request.user.is_superuser:
        messages.error(request, 'ليس لديك صلاحيات كافية للوصول إلى لوحة التحكم')
        return redirect('super_admin:login')

    # إحصائيات النظام
    stats = {
        'companies_count': Company.objects.count(),
        'active_companies': Company.objects.filter(status='active').count(),
        'inactive_companies': Company.objects.filter(status__in=['inactive', 'suspended']).count(),
        'recent_logs': SystemLog.objects.all().order_by('-created_at')[:10],
        'blocked_serials_count': BlockedSerial.objects.count(),
        'permanent_blocked_serials': BlockedSerial.objects.filter(is_permanent=True).count(),
        'temporary_blocked_serials': BlockedSerial.objects.filter(is_permanent=False, blocked_until__gte=timezone.now().date()).count(),
        'expired_blocked_serials': BlockedSerial.objects.filter(is_permanent=False, blocked_until__lt=timezone.now().date()).count(),
        'last_login': SystemLog.objects.filter(action='login').order_by('-created_at').first(),
        'last_backup': SystemSettings.get_settings().last_backup_date,
    }

    # قائمة الشركات مع ترقيم الصفحات
    per_page = request.GET.get('per_page', 10)
    try:
        per_page = int(per_page)
    except ValueError:
        per_page = 10

    # الحد الأقصى لعدد العناصر في الصفحة
    if per_page > 100:
        per_page = 100

    # ترتيب الشركات
    companies_list = Company.objects.all().order_by('-created_at')

    # إنشاء كائن الترقيم
    paginator = Paginator(companies_list, per_page)
    page = request.GET.get('page', 1)

    try:
        companies = paginator.page(page)
    except PageNotAnInteger:
        # إذا كان رقم الصفحة ليس عددًا صحيحًا، عرض الصفحة الأولى
        companies = paginator.page(1)
    except EmptyPage:
        # إذا كان رقم الصفحة أكبر من عدد الصفحات، عرض الصفحة الأخيرة
        companies = paginator.page(paginator.num_pages)

    # إحصائيات إضافية
    # عدد الشركات حسب الحالة
    company_status_stats = {
        'active': Company.objects.filter(status='active').count(),
        'inactive': Company.objects.filter(status='inactive').count(),
        'suspended': Company.objects.filter(status='suspended').count(),
        'trial': Company.objects.filter(status='trial').count(),
    }

    # الشركات التي تنتهي صلاحيتها قريبًا (خلال 30 يوم)
    expiring_soon = Company.objects.filter(
        expiry_date__isnull=False,
        expiry_date__gte=timezone.now().date(),
        expiry_date__lte=timezone.now().date() + timezone.timedelta(days=30)
    ).order_by('expiry_date')

    context = {
        'stats': stats,
        'companies': companies,
        'company_status_stats': company_status_stats,
        'expiring_soon': expiring_soon,
        'current_date': timezone.now(),
    }

    return render(request, 'super_admin/dashboard.html', context)

# إنشاء شركة جديدة
def create_company(request):
    """إنشاء شركة جديدة مع قاعدة بيانات خاصة بها"""
    # توليد رقم تسلسلي مقترح بالتنسيق الصحيح
    company_instance = Company()
    suggested_serial = company_instance.generate_serial_number()

    # التحقق من وجود طلب AJAX لتوليد رقم تسلسلي جديد
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and 'generate_serial' in request.GET:
        new_serial = company_instance.generate_serial_number()
        return JsonResponse({'serial_number': new_serial})

    # التحقق من وجود طلب AJAX لاختبار الاتصال بقاعدة البيانات
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'test_connection' in request.POST:
        db_name = request.POST.get('database_name', '')
        db_user = request.POST.get('database_user', '')
        db_password = request.POST.get('database_password', '')
        db_host = request.POST.get('database_host', 'localhost')

        # التحقق من وجود قاعدة بيانات بنفس الاسم
        existing_db = Company.objects.filter(database_name=db_name).exists()
        if existing_db:
            return JsonResponse({'success': False, 'message': 'قاعدة البيانات موجودة بالفعل. الرجاء اختيار اسم آخر.'})

        # محاكاة اختبار الاتصال (في بيئة حقيقية، يمكن استخدام اتصال فعلي)
        try:
            # هنا يمكن إضافة كود لاختبار الاتصال بقاعدة البيانات
            # في هذا المثال، نفترض أن الاتصال ناجح إذا كانت جميع الحقول غير فارغة
            if db_name and db_user and db_password:
                return JsonResponse({'success': True, 'message': 'تم الاتصال بقاعدة البيانات بنجاح'})
            else:
                return JsonResponse({'success': False, 'message': 'فشل الاتصال: الرجاء التأكد من صحة البيانات المدخلة'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'فشل الاتصال: {str(e)}'})

    if request.method == 'POST' and 'create_company' in request.POST:
        form = CompanyForm(request.POST)
        if form.is_valid():
            company = form.save(commit=False)

            # استخدام الرقم التسلسلي المقدم أو توليد رقم جديد
            serial_number = request.POST.get('serial_number_preview')
            if not serial_number:
                serial_number = company.generate_serial_number()
            else:
                # تنظيف وتنسيق الرقم التسلسلي المقدم
                clean_serial = serial_number.replace('-', '').upper()
                if len(clean_serial) == 16:
                    # تنسيق الرقم التسلسلي بالشكل الصحيح
                    serial_number = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])
                else:
                    # إذا كان الرقم التسلسلي غير صالح، توليد رقم جديد
                    serial_number = company.generate_serial_number()

            # التحقق من عدم وجود رقم تسلسلي مطابق
            if Company.objects.filter(serial_number=serial_number).exists():
                messages.error(request, 'الرقم التسلسلي موجود بالفعل. تم توليد رقم جديد.')
                serial_number = company.generate_serial_number()

            # التحقق من عدم وجود الرقم في قائمة الأرقام المحظورة
            if BlockedSerial.objects.filter(serial_number=serial_number).exists():
                messages.error(request, 'الرقم التسلسلي محظور. تم توليد رقم جديد.')
                serial_number = company.generate_serial_number()

            company.serial_number = serial_number

            # التحقق من وجود قاعدة بيانات بنفس الاسم
            if Company.objects.filter(database_name=company.database_name).exists():
                messages.error(request, 'قاعدة البيانات موجودة بالفعل. الرجاء اختيار اسم آخر.')
                return render(request, 'super_admin/create_company.html', {'form': form, 'suggested_serial': serial_number})

            # حفظ الشركة
            company.save()

            # إنشاء وتهيئة قاعدة البيانات
            try:
                # استدعاء وظيفة تهيئة قاعدة البيانات
                db_init_result = company.initialize_company_database()

                if not db_init_result['success']:
                    # في حالة فشل التهيئة، احذف الشركة
                    company.delete()
                    messages.error(request, f'فشل في تهيئة قاعدة البيانات: {db_init_result["message"]}')
                    return render(request, 'super_admin/create_company.html', {'form': form, 'suggested_serial': serial_number})

                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='create',
                    company=company,
                    description=f'تم إنشاء شركة جديدة: {company.name} وتهيئة قاعدة البيانات بنجاح',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

                messages.success(request, f'تم إنشاء الشركة {company.name} وتهيئة قاعدة البيانات بنجاح')
                return redirect('super_admin:dashboard')
            except Exception as e:
                # في حالة حدوث خطأ، احذف الشركة
                company.delete()
                messages.error(request, f'حدث خطأ أثناء إنشاء قاعدة البيانات: {str(e)}')
    else:
        form = CompanyForm(initial={'serial_number_preview': suggested_serial})

    return render(request, 'super_admin/create_company.html', {'form': form, 'suggested_serial': suggested_serial})

# تفاصيل الشركة
def company_detail(request, company_id):
    """عرض تفاصيل الشركة"""
    company = get_object_or_404(Company, id=company_id)

    # التحقق من وجود طلب AJAX لاختبار الاتصال بقاعدة البيانات
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'test_connection' in request.POST:
        db_name = request.POST.get('database_name', '')
        db_user = request.POST.get('database_user', '')
        db_password = request.POST.get('database_password', '')
        db_host = request.POST.get('database_host', 'localhost')

        # محاكاة اختبار الاتصال (في بيئة حقيقية، يمكن استخدام اتصال فعلي)
        try:
            # هنا يمكن إضافة كود لاختبار الاتصال بقاعدة البيانات
            # في هذا المثال، نفترض أن الاتصال ناجح إذا كانت جميع الحقول غير فارغة
            if db_name and db_user and db_password:
                return JsonResponse({'success': True, 'message': 'تم الاتصال بقاعدة البيانات بنجاح'})
            else:
                return JsonResponse({'success': False, 'message': 'فشل الاتصال: الرجاء التأكد من صحة البيانات المدخلة'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'فشل الاتصال: {str(e)}'})

    # التحقق من وجود طلب AJAX لتحديث بيانات الشركة
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'update_company' in request.POST:
        try:
            # الحصول على البيانات الجديدة
            name = request.POST.get('name', '')
            expiry_date = request.POST.get('expiry_date', None)
            status = request.POST.get('status', '')
            database_name = request.POST.get('database_name', '')
            database_user = request.POST.get('database_user', '')
            database_password = request.POST.get('database_password', '')
            database_host = request.POST.get('database_host', '')

            # حفظ البيانات القديمة للسجل
            old_data = {
                'name': company.name,
                'expiry_date': company.expiry_date.strftime('%Y-%m-%d') if company.expiry_date else None,
                'status': company.status,
                'database_name': company.database_name,
                'database_user': company.database_user,
                'database_password': '********',  # لا نخزن كلمة المرور الفعلية في السجل
                'database_host': company.database_host
            }

            # تحديث بيانات الشركة
            company.name = name
            if expiry_date:
                company.expiry_date = expiry_date
            company.status = status

            # تحديث بيانات قاعدة البيانات إذا تم تغييرها
            db_changed = False
            if database_name and database_name != company.database_name:
                # التحقق من عدم وجود قاعدة بيانات أخرى بنفس الاسم
                if Company.objects.filter(database_name=database_name).exclude(id=company.id).exists():
                    return JsonResponse({'success': False, 'message': 'قاعدة البيانات موجودة بالفعل. الرجاء اختيار اسم آخر.'})
                company.database_name = database_name
                db_changed = True

            if database_user and database_user != company.database_user:
                company.database_user = database_user
                db_changed = True

            if database_password and database_password != '********':  # تم تغيير كلمة المرور
                company.database_password = database_password
                db_changed = True

            if database_host and database_host != company.database_host:
                company.database_host = database_host
                db_changed = True

            # حفظ التغييرات
            company.save()

            # تسجيل العملية
            changes = []
            if company.name != old_data['name']:
                changes.append(f"الاسم من '{old_data['name']}' إلى '{company.name}'")

            if str(company.expiry_date) != str(old_data['expiry_date']):
                changes.append(f"تاريخ الانتهاء من '{old_data['expiry_date']}' إلى '{company.expiry_date}'")

            if company.status != old_data['status']:
                changes.append(f"الحالة من '{old_data['status']}' إلى '{company.status}'")

            if db_changed:
                changes.append("تم تحديث بيانات الاتصال بقاعدة البيانات")

            description = f"تم تحديث بيانات الشركة {company.name}: " + ", ".join(changes)

            SystemLog.objects.create(
                user=request.user,
                action='update',
                company=company,
                description=description,
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({'success': True, 'message': 'تم تحديث بيانات الشركة بنجاح'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء تحديث البيانات: {str(e)}'})

    # التحقق من وجود طلب AJAX لإنشاء نسخة احتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'create_backup' in request.POST:
        try:
            # إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
            backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            # إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'{company.database_name}_{timestamp}.sql'
            backup_file = os.path.join(backup_dir, backup_filename)

            # إنشاء ملف JSON للمعلومات الإضافية
            info_filename = f'{company.database_name}_{timestamp}_info.json'
            info_file = os.path.join(backup_dir, info_filename)

            # في بيئة حقيقية، يمكن استخدام أمر مثل pg_dump أو mysqldump
            # هنا نقوم بمحاكاة عملية النسخ الاحتياطي

            # محاكاة أمر النسخ الاحتياطي (في بيئة حقيقية، استخدم الأمر المناسب)
            # مثال: mysqldump -u {company.database_user} -p{company.database_password} -h {company.database_host} {company.database_name} > {backup_file}

            # إنشاء ملف SQL فارغ للتوضيح
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(f'-- نسخة احتياطية لقاعدة بيانات {company.database_name}\n')
                f.write(f'-- تاريخ النسخ: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                f.write(f'-- الشركة: {company.name}\n\n')
                f.write('-- محتوى قاعدة البيانات هنا\n')

            # حفظ معلومات إضافية في ملف JSON
            backup_info = {
                'company_id': company.id,
                'company_name': company.name,
                'database_name': company.database_name,
                'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'created_by': request.user.username,
                'file_size': os.path.getsize(backup_file),
                'backup_type': 'manual',
                'description': request.POST.get('backup_description', 'نسخة احتياطية يدوية'),
                'sql_file': backup_filename
            }

            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=4)

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='backup',
                company=company,
                description=f'تم إنشاء نسخة احتياطية لقاعدة بيانات {company.database_name}: {backup_filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_info': {
                    'filename': backup_filename,
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'size': os.path.getsize(backup_file),
                    'description': backup_info['description']
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}'})

    # التحقق من وجود طلب AJAX لاستعادة نسخة احتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'restore_backup' in request.POST:
        try:
            backup_filename = request.POST.get('backup_filename', '')
            if not backup_filename:
                return JsonResponse({'success': False, 'message': 'لم يتم تحديد ملف النسخة الاحتياطية'})

            backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
            backup_file = os.path.join(backup_dir, backup_filename)

            if not os.path.exists(backup_file):
                return JsonResponse({'success': False, 'message': 'ملف النسخة الاحتياطية غير موجود'})

            # في بيئة حقيقية، يمكن استخدام أمر مثل psql أو mysql
            # هنا نقوم بمحاكاة عملية الاستعادة

            # محاكاة أمر الاستعادة (في بيئة حقيقية، استخدم الأمر المناسب)
            # مثال: mysql -u {company.database_user} -p{company.database_password} -h {company.database_host} {company.database_name} < {backup_file}

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='restore',
                company=company,
                description=f'تم استعادة قاعدة بيانات {company.database_name} من النسخة الاحتياطية: {backup_filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({'success': True, 'message': 'تم استعادة قاعدة البيانات بنجاح'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء استعادة قاعدة البيانات: {str(e)}'})

    # التحقق من وجود طلب AJAX لحذف نسخة احتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'delete_backup' in request.POST:
        try:
            backup_filename = request.POST.get('backup_filename', '')
            if not backup_filename:
                return JsonResponse({'success': False, 'message': 'لم يتم تحديد ملف النسخة الاحتياطية'})

            backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
            backup_file = os.path.join(backup_dir, backup_filename)

            # التحقق من وجود ملف المعلومات المرتبط
            info_filename = backup_filename.replace('.sql', '_info.json')
            info_file = os.path.join(backup_dir, info_filename)

            if not os.path.exists(backup_file):
                return JsonResponse({'success': False, 'message': 'ملف النسخة الاحتياطية غير موجود'})

            # حذف الملفات
            os.remove(backup_file)
            if os.path.exists(info_file):
                os.remove(info_file)

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='delete',
                company=company,
                description=f'تم حذف النسخة الاحتياطية: {backup_filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({'success': True, 'message': 'تم حذف النسخة الاحتياطية بنجاح'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}'})

    # التحقق من وجود طلب AJAX للحصول على قائمة النسخ الاحتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and 'get_backups' in request.GET:
        try:
            backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            backups = []
            for filename in os.listdir(backup_dir):
                if filename.endswith('_info.json'):
                    info_file = os.path.join(backup_dir, filename)
                    with open(info_file, 'r', encoding='utf-8') as f:
                        backup_info = json.load(f)

                    sql_file = os.path.join(backup_dir, backup_info.get('sql_file', ''))
                    if os.path.exists(sql_file):
                        backups.append({
                            'filename': backup_info.get('sql_file', ''),
                            'created_at': backup_info.get('created_at', ''),
                            'size': os.path.getsize(sql_file),
                            'description': backup_info.get('description', ''),
                            'created_by': backup_info.get('created_by', ''),
                            'backup_type': backup_info.get('backup_type', 'manual')
                        })

            # ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)

            return JsonResponse({'success': True, 'backups': backups})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء جلب قائمة النسخ الاحتياطية: {str(e)}'})

    # سجلات الشركة
    logs = SystemLog.objects.filter(company=company).order_by('-created_at')[:20]

    context = {
        'company': company,
        'logs': logs,
        'now': timezone.now(),
    }

    return render(request, 'super_admin/company_detail.html', context)

# تفعيل/تعطيل شركة
def toggle_company_status(request, company_id):
    """تفعيل أو تعطيل شركة"""
    company = get_object_or_404(Company, id=company_id)

    if company.status == 'active':
        company.status = 'inactive'
        action = 'deactivate'
        message = f'تم تعطيل الشركة {company.name} بنجاح'
    else:
        company.status = 'active'
        action = 'activate'
        message = f'تم تفعيل الشركة {company.name} بنجاح'

    company.save()

    # تسجيل العملية
    SystemLog.objects.create(
        user=request.user,
        action=action,
        company=company,
        description=message,
        ip_address=request.META.get('REMOTE_ADDR')
    )

    messages.success(request, message)
    return redirect('super_admin:company_detail', company_id=company.id)

# حذف شركة
def delete_company(request, company_id):
    """حذف شركة وقاعدة بياناتها"""
    company = get_object_or_404(Company, id=company_id)

    if request.method == 'POST':
        company_name = company.name

        # هنا يمكن إضافة الكود لحذف قاعدة البيانات
        # في بيئة حقيقية، يمكن استخدام أوامر SQL أو API لحذف قاعدة البيانات

        # تسجيل العملية قبل الحذف
        SystemLog.objects.create(
            user=request.user,
            action='delete',
            description=f'تم حذف الشركة: {company_name}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # حذف الشركة
        company.delete()

        messages.success(request, f'تم حذف الشركة {company_name} بنجاح')
        return redirect('super_admin:dashboard')

    return render(request, 'super_admin/delete_company.html', {'company': company})

# إعدادات النظام العامة
def system_settings(request):
    """إعدادات النظام العامة"""
    # الحصول على إعدادات النظام الحالية
    settings = SystemSettings.get_settings()

    if request.method == 'POST':
        # تحديث الإعدادات العامة
        if 'save_general' in request.POST:
            settings.app_name = request.POST.get('app_name', settings.app_name)
            settings.default_language = request.POST.get('default_language', settings.default_language)
            settings.enable_registration = 'enable_registration' in request.POST
            settings.maintenance_mode = 'maintenance_mode' in request.POST
            settings.updated_by = request.user
            settings.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم تحديث الإعدادات العامة',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم حفظ الإعدادات العامة بنجاح')

    # الحصول على جدولة النسخ الاحتياطي للنظام
    system_backup_schedule = BackupSchedule.objects.filter(company=None).first()

    context = {
        'settings': settings,
        'system_backup_schedule': system_backup_schedule,
    }

    return render(request, 'super_admin/system_settings_new.html', context)

# إعدادات النظام العامة (القالب الجديد)
def system_settings_new(request):
    """إعدادات النظام العامة (القالب الجديد مع التبويبات)"""
    # الحصول على إعدادات النظام الحالية
    settings = SystemSettings.get_settings()

    if request.method == 'POST':
        # تحديث الإعدادات العامة
        if 'save_general' in request.POST:
            settings.app_name = request.POST.get('app_name', settings.app_name)
            settings.default_language = request.POST.get('default_language', settings.default_language)
            settings.enable_registration = 'enable_registration' in request.POST
            settings.maintenance_mode = 'maintenance_mode' in request.POST
            settings.updated_by = request.user
            settings.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم تحديث الإعدادات العامة',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم حفظ الإعدادات العامة بنجاح')

        # تحديث إعدادات الأمان
        elif 'save_security' in request.POST:
            settings.two_factor_for_admins = 'two_factor_for_admins' in request.POST
            settings.two_factor_type = request.POST.get('two_factor_type', 'otp')
            settings.session_timeout = int(request.POST.get('session_timeout', 60))
            settings.updated_by = request.user
            settings.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم تحديث إعدادات الأمان',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم حفظ إعدادات الأمان بنجاح')

        # تحديث جدولة النسخ الاحتياطي
        elif 'save_backup_schedule' in request.POST:
            # الحصول على البيانات الجديدة
            is_active = 'backup_active' in request.POST
            frequency = request.POST.get('backup_frequency', 'weekly')
            time_str = request.POST.get('backup_time', '00:00')
            day_of_week = int(request.POST.get('day_of_week', 0))  # الافتراضي: الاثنين
            day_of_month = int(request.POST.get('day_of_month', 1))  # الافتراضي: اليوم الأول من الشهر
            retention_period = int(request.POST.get('backup_retention', 30))

            # تحويل الوقت إلى كائن time
            hour, minute = map(int, time_str.split(':'))
            backup_time = datetime.time(hour=hour, minute=minute)

            # إنشاء أو تحديث جدولة النسخ الاحتياطي للنظام
            system_backup_schedule, created = BackupSchedule.objects.get_or_create(
                company=None,  # جدولة النسخ الاحتياطي للنظام بأكمله
                defaults={
                    'is_active': is_active,
                    'frequency': frequency,
                    'time': backup_time,
                    'day_of_week': day_of_week,
                    'day_of_month': day_of_month,
                    'retention_period': retention_period,
                    'created_by': request.user,
                    'updated_by': request.user
                }
            )

            if not created:
                system_backup_schedule.is_active = is_active
                system_backup_schedule.frequency = frequency
                system_backup_schedule.time = backup_time
                system_backup_schedule.day_of_week = day_of_week
                system_backup_schedule.day_of_month = day_of_month
                system_backup_schedule.retention_period = retention_period
                system_backup_schedule.updated_by = request.user

                # إعادة حساب موعد النسخة الاحتياطية التالية إذا كانت الجدولة مفعلة
                if is_active:
                    system_backup_schedule.next_backup = system_backup_schedule.calculate_next_backup()

                system_backup_schedule.save()

            # تسجيل العملية
            action = 'تفعيل' if is_active else 'تعطيل'
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description=f'تم {action} جدولة النسخ الاحتياطي التلقائي للنظام',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم حفظ إعدادات جدولة النسخ الاحتياطي بنجاح')

    # الحصول على جدولة النسخ الاحتياطي للنظام
    system_backup_schedule = BackupSchedule.objects.filter(company=None).first()

    # الحصول على قائمة النسخ الاحتياطية السابقة
    backup_dir = os.path.join(os.getcwd(), 'backups')
    backups = []

    if os.path.exists(backup_dir):
        for filename in os.listdir(backup_dir):
            if filename.endswith('.sql'):
                file_path = os.path.join(backup_dir, filename)
                backups.append({
                    'filename': filename,
                    'created_at': datetime.datetime.fromtimestamp(os.path.getctime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                    'size': os.path.getsize(file_path),
                    'size_formatted': f"{os.path.getsize(file_path) / (1024 * 1024):.2f} MB"
                })

    # ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأحدث أولاً)
    backups.sort(key=lambda x: x['created_at'], reverse=True)

    context = {
        'settings': settings,
        'system_backup_schedule': system_backup_schedule,
        'backups': backups[:10],  # عرض آخر 10 نسخ احتياطية فقط
    }

    return render(request, 'super_admin/system_settings_new.html', context)

# إنشاء نسخة احتياطية
@login_required
@user_passes_test(is_super_admin)
def create_backup(request):
    """إنشاء نسخة احتياطية لقواعد البيانات"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    if request.method == 'POST':
        try:
            # التحقق من وجود طلب AJAX
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                company_id = request.POST.get('company_id')
                description = request.POST.get('description', '')

                if not company_id:
                    return JsonResponse({'success': False, 'message': 'لم يتم تحديد الشركة'})

                # استدعاء دالة إنشاء النسخة الاحتياطية
                from .backup_utils import backup_company_database
                backup, error = backup_company_database(company_id, 'manual', description, request.user)

                if backup:
                    return JsonResponse({
                        'success': True,
                        'message': f'تم إنشاء النسخة الاحتياطية بنجاح: {backup.file_name}',
                        'backup_id': backup.id,
                        'file_name': backup.file_name,
                        'company_name': backup.company.name,
                        'created_at': backup.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'file_size': backup.get_size_formatted()
                    })
                else:
                    return JsonResponse({'success': False, 'message': f'فشل إنشاء النسخة الاحتياطية: {error}'})

            # إذا لم يكن طلب AJAX، إنشاء نسخة احتياطية للنظام بالكامل
            # إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
            from .backup_utils import create_backup_directory
            backup_dir = create_backup_directory()

            # تحديث تاريخ آخر نسخة احتياطية
            settings = SystemSettings.get_settings()
            settings.last_backup_date = timezone.now()
            settings.updated_by = request.user
            settings.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='create',
                description=f'تم إنشاء نسخة احتياطية: {os.path.basename(backup_file)}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم إنشاء النسخة الاحتياطية بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}')

    return redirect('super_admin:system_settings_new')

# استعادة نسخة احتياطية
def restore_backup(request):
    """استعادة نسخة احتياطية لقواعد البيانات"""
    if request.method == 'POST':
        try:
            # في بيئة حقيقية، يمكن استخدام أمر مثل psql أو mysql
            # هنا نقوم فقط بمحاكاة عملية الاستعادة

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description='تم استعادة نسخة احتياطية',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم استعادة النسخة الاحتياطية بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}')

    return redirect('super_admin:system_settings_new')

# تنزيل نسخة احتياطية للنظام
@login_required
def download_system_backup(request, filename):
    """تنزيل ملف نسخة احتياطية للنظام"""
    try:
        # التحقق من مسار الملف
        backup_dir = os.path.join(os.getcwd(), 'backups')
        backup_file = os.path.join(backup_dir, filename)

        # التحقق من وجود الملف
        if not os.path.exists(backup_file):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('super_admin:system_settings_new')

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='download',
            description=f'تم تنزيل النسخة الاحتياطية للنظام: {filename}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إرسال الملف للتنزيل
        response = FileResponse(open(backup_file, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء تنزيل النسخة الاحتياطية: {str(e)}')
        return redirect('super_admin:system_settings_new')

# تنزيل نسخة احتياطية للشركة
@login_required
def download_backup(request, company_id, filename):
    """تنزيل ملف نسخة احتياطية للشركة"""
    try:
        # التحقق من وجود الشركة
        company = get_object_or_404(Company, id=company_id)

        # التحقق من مسار الملف
        backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
        backup_file = os.path.join(backup_dir, filename)

        # التحقق من وجود الملف
        if not os.path.exists(backup_file):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('super_admin:company_detail', company_id=company_id)

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='download',
            company=company,
            description=f'تم تنزيل النسخة الاحتياطية: {filename}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إرسال الملف للتنزيل
        response = FileResponse(open(backup_file, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء تنزيل النسخة الاحتياطية: {str(e)}')
        return redirect('super_admin:company_detail', company_id=company_id)

# حذف نسخة احتياطية للنظام
@login_required
def delete_system_backup(request, filename):
    """حذف ملف نسخة احتياطية للنظام"""
    if request.method == 'POST':
        try:
            # التحقق من مسار الملف
            backup_dir = os.path.join(os.getcwd(), 'backups')
            backup_file = os.path.join(backup_dir, filename)

            # التحقق من وجود الملف
            if not os.path.exists(backup_file):
                messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
                return redirect('super_admin:system_settings_new')

            # حذف الملف
            os.remove(backup_file)

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='delete',
                description=f'تم حذف النسخة الاحتياطية للنظام: {filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم حذف النسخة الاحتياطية بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}')

    return redirect('super_admin:system_settings_new')

# رفع نسخة احتياطية
@login_required
def upload_backup(request, company_id):
    """رفع ملف نسخة احتياطية"""
    try:
        # التحقق من وجود الشركة
        company = get_object_or_404(Company, id=company_id)

        if request.method == 'POST' and request.FILES.get('backup_file'):
            # الحصول على الملف المرفوع
            uploaded_file = request.FILES['backup_file']

            # التحقق من امتداد الملف
            if not uploaded_file.name.endswith('.sql'):
                return JsonResponse({'success': False, 'message': 'يجب أن يكون الملف بامتداد .sql'})

            # إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
            backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            # إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'{company.database_name}_uploaded_{timestamp}.sql'
            backup_file = os.path.join(backup_dir, backup_filename)

            # حفظ الملف المرفوع
            with open(backup_file, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)

            # إنشاء ملف JSON للمعلومات الإضافية
            info_filename = f'{company.database_name}_uploaded_{timestamp}_info.json'
            info_file = os.path.join(backup_dir, info_filename)

            # حفظ معلومات إضافية في ملف JSON
            backup_info = {
                'company_id': company.id,
                'company_name': company.name,
                'database_name': company.database_name,
                'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'created_by': request.user.username,
                'file_size': os.path.getsize(backup_file),
                'backup_type': 'uploaded',
                'description': request.POST.get('backup_description', 'نسخة احتياطية مرفوعة'),
                'original_filename': uploaded_file.name,
                'sql_file': backup_filename
            }

            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=4)

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='upload',
                company=company,
                description=f'تم رفع نسخة احتياطية: {backup_filename}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({
                'success': True,
                'message': 'تم رفع النسخة الاحتياطية بنجاح',
                'backup_info': {
                    'filename': backup_filename,
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'size': os.path.getsize(backup_file),
                    'description': backup_info['description']
                }
            })

        return JsonResponse({'success': False, 'message': 'لم يتم تحديد ملف للرفع'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء رفع النسخة الاحتياطية: {str(e)}'})

# تسجيل الخروج
def super_admin_logout(request):
    """تسجيل الخروج من لوحة المسؤول الأعلى"""
    # تسجيل عملية تسجيل الخروج
    SystemLog.objects.create(
        user=request.user,
        action='logout',
        description='تم تسجيل الخروج من لوحة المسؤول الأعلى',
        ip_address=request.META.get('REMOTE_ADDR')
    )

    # تسجيل الخروج
    logout(request)

    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('super_admin:login')

# عرض الأرقام التسلسلية المحظورة
@login_required
def blocked_serials(request):
    """عرض الأرقام التسلسلية المحظورة"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على قائمة الأرقام التسلسلية المحظورة
    blocked_serials = FailedLoginAttempt.get_blocked_serials()

    # التحقق من وجود طلب AJAX لإلغاء حظر رقم تسلسلي
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'unblock_serial' in request.POST:
        serial_number = request.POST.get('serial_number', '')
        if not serial_number:
            return JsonResponse({'success': False, 'message': 'لم يتم تحديد الرقم التسلسلي'})

        # إلغاء حظر الرقم التسلسلي
        updated = FailedLoginAttempt.unblock_serial(serial_number)

        if updated:
            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description=f'تم إلغاء حظر الرقم التسلسلي: {serial_number}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({'success': True, 'message': f'تم إلغاء حظر الرقم التسلسلي {serial_number} بنجاح'})
        else:
            return JsonResponse({'success': False, 'message': 'لم يتم العثور على محاولات محظورة لهذا الرقم التسلسلي'})

    # التحقق من وجود طلب AJAX لإلغاء حظر جميع الأرقام التسلسلية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'unblock_all' in request.POST:
        try:
            # الحصول على جميع الأرقام التسلسلية المحظورة
            blocked_serials_list = FailedLoginAttempt.objects.filter(is_blocked=True).values_list('serial_number', flat=True).distinct()

            # عدد الأرقام التسلسلية المحظورة
            blocked_count = len(blocked_serials_list)

            if blocked_count == 0:
                return JsonResponse({'success': False, 'message': 'لا توجد أرقام تسلسلية محظورة'})

            # إلغاء حظر جميع الأرقام التسلسلية
            updated = FailedLoginAttempt.objects.filter(is_blocked=True).update(is_blocked=False)

            if updated:
                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='update',
                    description=f'تم إلغاء حظر جميع الأرقام التسلسلية المحظورة (عدد {blocked_count})',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

                return JsonResponse({'success': True, 'message': f'تم إلغاء حظر {blocked_count} رقم تسلسلي بنجاح'})
            else:
                return JsonResponse({'success': False, 'message': 'لم يتم إلغاء حظر أي رقم تسلسلي'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء إلغاء الحظر: {str(e)}'})

    context = {
        'blocked_serials': blocked_serials,
        'current_date': timezone.now(),
    }

    return render(request, 'super_admin/blocked_serials.html', context)

# إدارة جدولة النسخ الاحتياطي
def manage_backup_schedule(request, company_id):
    """إدارة جدولة النسخ الاحتياطي للشركة"""
    company = get_object_or_404(Company, id=company_id)

    # الحصول على جدولة النسخ الاحتياطي الحالية أو إنشاء واحدة جديدة
    backup_schedule, created = BackupSchedule.objects.get_or_create(
        company=company,
        defaults={
            'created_by': request.user,
            'updated_by': request.user
        }
    )

    # التحقق من وجود طلب AJAX لتحديث جدولة النسخ الاحتياطي
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST':
        try:
            # الحصول على البيانات الجديدة
            is_active = request.POST.get('is_active') == 'true'
            frequency = request.POST.get('frequency', 'weekly')
            time_str = request.POST.get('time', '00:00')
            day_of_week = int(request.POST.get('day_of_week', 0))
            day_of_month = int(request.POST.get('day_of_month', 1))
            retention_period = int(request.POST.get('retention_period', 30))

            # تحويل الوقت إلى كائن time
            hour, minute = map(int, time_str.split(':'))
            backup_time = datetime.time(hour=hour, minute=minute)

            # تحديث جدولة النسخ الاحتياطي
            backup_schedule.is_active = is_active
            backup_schedule.frequency = frequency
            backup_schedule.time = backup_time
            backup_schedule.day_of_week = day_of_week
            backup_schedule.day_of_month = day_of_month
            backup_schedule.retention_period = retention_period
            backup_schedule.updated_by = request.user

            # إعادة حساب موعد النسخة الاحتياطية التالية إذا كانت الجدولة مفعلة
            if is_active:
                backup_schedule.next_backup = backup_schedule.calculate_next_backup()

            # حفظ التغييرات
            backup_schedule.save()

            # تسجيل العملية
            action = 'تفعيل' if is_active else 'تعطيل'
            SystemLog.objects.create(
                user=request.user,
                action='update',
                company=company,
                description=f'تم {action} جدولة النسخ الاحتياطي التلقائي للشركة {company.name}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return JsonResponse({
                'success': True,
                'message': 'تم تحديث جدولة النسخ الاحتياطي بنجاح',
                'next_backup': backup_schedule.next_backup.strftime('%Y-%m-%d %H:%M:%S') if backup_schedule.next_backup else None
            })
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء تحديث جدولة النسخ الاحتياطي: {str(e)}'})

    context = {
        'company': company,
        'backup_schedule': backup_schedule,
        'now': timezone.now(),
    }

    return render(request, 'super_admin/backup_schedule.html', context)

# صفحة النسخ الاحتياطي
@login_required
def system_backup(request):
    """صفحة النسخ الاحتياطي للنظام"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على قائمة الشركات
    companies = Company.objects.all().order_by('name')

    # الحصول على قائمة النسخ الاحتياطية
    backups = DatabaseBackup.objects.all().order_by('-created_at')

    # تصفية النسخ الاحتياطية حسب الشركة
    company_id = request.GET.get('company_id')
    if company_id:
        backups = backups.filter(company_id=company_id)

    # تصفية النسخ الاحتياطية حسب النوع
    backup_type = request.GET.get('backup_type')
    if backup_type:
        backups = backups.filter(backup_type=backup_type)

    # تصفية النسخ الاحتياطية حسب التاريخ
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date and end_date:
        backups = backups.filter(created_at__range=[start_date, end_date])

    # التحقق من وجود طلب AJAX لإنشاء نسخة احتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'create_backup' in request.POST:
        try:
            company_id = request.POST.get('company_id')
            description = request.POST.get('description', '')

            if not company_id:
                return JsonResponse({'success': False, 'message': 'لم يتم تحديد الشركة'})

            # استدعاء دالة إنشاء النسخة الاحتياطية
            from .backup_utils import backup_company_database
            backup, error = backup_company_database(company_id, 'manual', description, request.user)

            if backup:
                return JsonResponse({
                    'success': True,
                    'message': f'تم إنشاء النسخة الاحتياطية بنجاح: {backup.file_name}',
                    'backup_id': backup.id,
                    'file_name': backup.file_name,
                    'company_name': backup.company.name,
                    'created_at': backup.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'file_size': backup.get_size_formatted()
                })
            else:
                return JsonResponse({'success': False, 'message': f'فشل إنشاء النسخة الاحتياطية: {error}'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}'})

    # التحقق من وجود طلب AJAX لحذف نسخة احتياطية
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and request.method == 'POST' and 'delete_backup' in request.POST:
        try:
            backup_id = request.POST.get('backup_id')

            if not backup_id:
                return JsonResponse({'success': False, 'message': 'لم يتم تحديد النسخة الاحتياطية'})

            # استدعاء دالة حذف النسخة الاحتياطية
            from .backup_utils import delete_backup
            success, message = delete_backup(backup_id, request.user)

            return JsonResponse({'success': success, 'message': message})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}'})

    context = {
        'backups': backups,
        'companies': companies,
        'backup_types': dict(DatabaseBackup.BACKUP_TYPE_CHOICES),
        'settings': SystemSettings.objects.first(),
        'current_date': timezone.now(),
    }

    return render(request, 'super_admin/system_backup.html', context)

# صفحة سجل النظام
@login_required
def system_logs(request):
    """صفحة سجل النظام"""
    # الحصول على سجلات النظام
    logs = SystemLog.objects.all().order_by('-created_at')

    # تصفية السجلات حسب المعايير
    action = request.GET.get('action')
    if action:
        logs = logs.filter(action=action)

    user_id = request.GET.get('user_id')
    if user_id:
        logs = logs.filter(user_id=user_id)

    company_id = request.GET.get('company_id')
    if company_id:
        logs = logs.filter(company_id=company_id)

    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date and end_date:
        logs = logs.filter(created_at__range=[start_date, end_date])

    # الحصول على قائمة المستخدمين والشركات للتصفية
    users = User.objects.filter(id__in=logs.values_list('user_id', flat=True)).distinct()
    companies = Company.objects.filter(id__in=logs.values_list('company_id', flat=True)).distinct()

    context = {
        'logs': logs,
        'users': users,
        'companies': companies,
        'action_choices': SystemLog.ACTION_CHOICES,
    }

    return render(request, 'super_admin/system_logs.html', context)

# صفحة الملف الشخصي
@login_required
def user_profile(request):
    """صفحة الملف الشخصي للمستخدم"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على ملف تعريف المسؤول الأعلى
    super_admin_profile, created = SuperAdminUser.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # تحديث بيانات الملف الشخصي
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        phone = request.POST.get('phone')

        # تحديث بيانات المستخدم
        user = request.user
        user.first_name = first_name
        user.last_name = last_name
        user.email = email
        user.save()

        # تحديث بيانات ملف التعريف
        super_admin_profile.phone = phone
        super_admin_profile.save()

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description='تم تحديث الملف الشخصي',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, 'تم تحديث الملف الشخصي بنجاح')
        return redirect('super_admin:user_profile')

    # سجلات المستخدم
    logs = SystemLog.objects.filter(user=request.user).order_by('-created_at')[:10]

    context = {
        'profile': super_admin_profile,
        'logs': logs,
    }

    return render(request, 'super_admin/user_profile.html', context)

# تغيير كلمة المرور
@login_required
def change_password(request):
    """تغيير كلمة المرور للمسؤول الأعلى"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        # التحقق من صحة كلمة المرور الحالية
        if not request.user.check_password(current_password):
            messages.error(request, 'كلمة المرور الحالية غير صحيحة')
            return redirect('super_admin:user_profile')

        # التحقق من تطابق كلمة المرور الجديدة مع التأكيد
        if new_password != confirm_password:
            messages.error(request, 'كلمة المرور الجديدة وتأكيدها غير متطابقين')
            return redirect('super_admin:user_profile')

        # التحقق من طول كلمة المرور الجديدة
        if len(new_password) < 8:
            messages.error(request, 'يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل')
            return redirect('super_admin:user_profile')

        # تغيير كلمة المرور
        user = request.user
        user.set_password(new_password)
        user.save()

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description='تم تغيير كلمة المرور',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إعادة تسجيل الدخول للمستخدم بعد تغيير كلمة المرور
        from django.contrib.auth import update_session_auth_hash
        update_session_auth_hash(request, user)

        messages.success(request, 'تم تغيير كلمة المرور بنجاح')
        return redirect('super_admin:user_profile')

    # في حالة الوصول المباشر إلى هذا المسار، إعادة التوجيه إلى صفحة الملف الشخصي
    return redirect('super_admin:user_profile')

# صفحة الأرقام التسلسلية المحظورة
@login_required
@user_passes_test(is_super_admin)
def blocked_serials(request):
    """صفحة إدارة الأرقام التسلسلية المحظورة"""
    # تسجيل زيارة الصفحة
    SystemLog.objects.create(
        user=request.user,
        action='view',
        description='زيارة صفحة الأرقام التسلسلية المحظورة',
        ip_address=request.META.get('REMOTE_ADDR')
    )

    # الحصول على قائمة الأرقام التسلسلية المحظورة
    blocked_serials_list = BlockedSerial.objects.all().order_by('-created_at')

    # تصفية حسب الحالة
    filter_type = request.GET.get('filter', '')
    if filter_type == 'permanent':
        blocked_serials_list = blocked_serials_list.filter(is_permanent=True)
    elif filter_type == 'temporary':
        blocked_serials_list = blocked_serials_list.filter(is_permanent=False, blocked_until__gte=timezone.now().date())
    elif filter_type == 'expired':
        blocked_serials_list = blocked_serials_list.filter(is_permanent=False, blocked_until__lt=timezone.now().date())

    # إحصائيات
    permanent_count = BlockedSerial.objects.filter(is_permanent=True).count()
    temporary_count = BlockedSerial.objects.filter(is_permanent=False, blocked_until__gte=timezone.now().date()).count()
    expired_count = BlockedSerial.objects.filter(is_permanent=False, blocked_until__lt=timezone.now().date()).count()

    # نموذج إضافة رقم تسلسلي محظور
    form = BlockedSerialForm()

    context = {
        'blocked_serials': blocked_serials_list,
        'form': form,
        'filter': filter_type,
        'permanent_count': permanent_count,
        'temporary_count': temporary_count,
        'expired_count': expired_count,
        'current_date': timezone.now(),
    }

    return render(request, 'super_admin/blocked_serials.html', context)

# إضافة رقم تسلسلي محظور
@login_required
@user_passes_test(is_super_admin)
def add_blocked_serial(request):
    """إضافة رقم تسلسلي محظور جديد"""
    if request.method == 'POST':
        form = BlockedSerialForm(request.POST)
        if form.is_valid():
            # إنشاء كائن BlockedSerial جديد
            blocked_serial = BlockedSerial(
                serial_number=form.cleaned_data['serial_number'],
                reason=form.cleaned_data['reason'],
                is_permanent=form.cleaned_data['is_permanent'],
                created_by=request.user
            )

            # إذا كان الحظر مؤقتًا، تعيين تاريخ انتهاء الحظر
            if not form.cleaned_data['is_permanent']:
                blocked_serial.blocked_until = form.cleaned_data['blocked_until']

            # حفظ الرقم التسلسلي المحظور
            blocked_serial.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='create',
                description=f'تم إضافة رقم تسلسلي محظور جديد: {blocked_serial.serial_number}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم إضافة الرقم التسلسلي {blocked_serial.serial_number} إلى قائمة الأرقام المحظورة بنجاح')
            return redirect('super_admin:blocked_serials')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = BlockedSerialForm()

    # الحصول على قائمة الأرقام التسلسلية المحظورة
    blocked_serials_list = BlockedSerial.objects.all().order_by('-created_at')

    # إحصائيات
    permanent_count = BlockedSerial.objects.filter(is_permanent=True).count()
    temporary_count = BlockedSerial.objects.filter(is_permanent=False, blocked_until__gte=timezone.now().date()).count()
    expired_count = BlockedSerial.objects.filter(is_permanent=False, blocked_until__lt=timezone.now().date()).count()

    context = {
        'blocked_serials': blocked_serials_list,
        'form': form,
        'permanent_count': permanent_count,
        'temporary_count': temporary_count,
        'expired_count': expired_count,
        'current_date': timezone.now(),
    }

    return render(request, 'super_admin/blocked_serials.html', context)

# تعديل رقم تسلسلي محظور
@login_required
@user_passes_test(is_super_admin)
def edit_blocked_serial(request, serial_id):
    """تعديل رقم تسلسلي محظور"""
    blocked_serial = get_object_or_404(BlockedSerial, id=serial_id)

    if request.method == 'POST':
        form = BlockedSerialForm(request.POST, instance=blocked_serial)
        if form.is_valid():
            # تحديث بيانات الرقم التسلسلي المحظور
            blocked_serial.reason = form.cleaned_data['reason']
            blocked_serial.is_permanent = form.cleaned_data['is_permanent']

            # إذا كان الحظر مؤقتًا، تحديث تاريخ انتهاء الحظر
            if not form.cleaned_data['is_permanent']:
                blocked_serial.blocked_until = form.cleaned_data['blocked_until']
            else:
                blocked_serial.blocked_until = None

            # حفظ التغييرات
            blocked_serial.save()

            # تسجيل العملية
            SystemLog.objects.create(
                user=request.user,
                action='update',
                description=f'تم تعديل الرقم التسلسلي المحظور: {blocked_serial.serial_number}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم تحديث الرقم التسلسلي {blocked_serial.serial_number} بنجاح')
            return redirect('super_admin:blocked_serials')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = BlockedSerialForm(instance=blocked_serial)

    context = {
        'form': form,
        'blocked_serial': blocked_serial,
    }

    return render(request, 'super_admin/edit_blocked_serial.html', context)

# حذف رقم تسلسلي محظور
@login_required
@user_passes_test(is_super_admin)
def delete_blocked_serial(request, serial_id):
    """حذف رقم تسلسلي محظور"""
    blocked_serial = get_object_or_404(BlockedSerial, id=serial_id)

    # حفظ الرقم التسلسلي للسجل
    serial_number = blocked_serial.serial_number

    # حذف الرقم التسلسلي المحظور
    blocked_serial.delete()

    # تسجيل العملية
    SystemLog.objects.create(
        user=request.user,
        action='delete',
        description=f'تم حذف الرقم التسلسلي المحظور: {serial_number}',
        ip_address=request.META.get('REMOTE_ADDR')
    )

    messages.success(request, f'تم حذف الرقم التسلسلي {serial_number} من قائمة الأرقام المحظورة بنجاح')
    return redirect('super_admin:blocked_serials')

# إعدادات البريد الإلكتروني
@login_required
@user_passes_test(is_super_admin)
def email_settings(request):
    """إدارة إعدادات البريد الإلكتروني للنظام"""
    from django.conf import settings as django_settings
    import os

    # الحصول على مسار ملف الإعدادات
    settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'backend', 'labor_management', 'settings.py')

    # قراءة ملف الإعدادات
    with open(settings_path, 'r', encoding='utf-8') as f:
        settings_content = f.read()

    # استخراج قيم الإعدادات الحالية
    current_email = django_settings.EMAIL_HOST_USER

    if request.method == 'POST':
        # الحصول على القيم الجديدة
        new_email = request.POST.get('email', '')
        new_password = request.POST.get('password', '')

        # التحقق من صحة البيانات
        if not new_email or '@' not in new_email:
            messages.error(request, 'يرجى إدخال بريد إلكتروني صحيح')
            return redirect('super_admin:email_settings')

        # تحديث بريد المسؤول الأعلى في قاعدة البيانات
        user = request.user
        user.email = new_email
        user.save()

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description='تم تحديث إعدادات البريد الإلكتروني',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إنشاء ملف تكوين بيئي للإعدادات الحساسة
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')

        # التحقق من وجود الملف
        if os.path.exists(env_path):
            # قراءة الملف الحالي
            with open(env_path, 'r', encoding='utf-8') as f:
                env_content = f.read()

            # تحديث القيم
            if 'EMAIL_HOST_USER' in env_content:
                env_content = env_content.replace(f"EMAIL_HOST_USER={current_email}", f"EMAIL_HOST_USER={new_email}")
            else:
                env_content += f"\nEMAIL_HOST_USER={new_email}"

            if new_password and 'EMAIL_HOST_PASSWORD' in env_content:
                # استخراج كلمة المرور الحالية
                import re
                current_password_match = re.search(r'EMAIL_HOST_PASSWORD=([^\n]+)', env_content)
                if current_password_match:
                    current_password = current_password_match.group(1)
                    env_content = env_content.replace(f"EMAIL_HOST_PASSWORD={current_password}", f"EMAIL_HOST_PASSWORD={new_password}")
            elif new_password:
                env_content += f"\nEMAIL_HOST_PASSWORD={new_password}"
        else:
            # إنشاء ملف جديد
            env_content = f"EMAIL_HOST_USER={new_email}"
            if new_password:
                env_content += f"\nEMAIL_HOST_PASSWORD={new_password}"

        # حفظ الملف
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_content)

        messages.success(request, 'تم تحديث إعدادات البريد الإلكتروني بنجاح. يرجى إعادة تشغيل الخادم لتطبيق التغييرات.')
        return redirect('super_admin:email_settings')

    context = {
        'current_email': current_email,
    }

    return render(request, 'super_admin/email_settings.html', context)

# إدارة تقييدات عناوين IP
@login_required
@user_passes_test(is_super_admin)
def ip_restrictions(request):
    """إدارة تقييدات عناوين IP للمسؤول الأعلى"""
    # التحقق من وجود ملف تعريف المسؤول الأعلى
    if not hasattr(request.user, 'super_admin_profile'):
        messages.error(request, 'لا يمكنك الوصول إلى هذه الصفحة')
        return redirect('super_admin:dashboard')

    super_admin_profile = request.user.super_admin_profile

    if request.method == 'POST':
        # تحديث إعدادات تقييد IP
        ip_restriction_enabled = request.POST.get('ip_restriction_enabled') == 'on'
        allowed_ips = request.POST.get('allowed_ips', '')

        # حفظ الإعدادات
        super_admin_profile.ip_restriction_enabled = ip_restriction_enabled
        super_admin_profile.allowed_ips = allowed_ips
        super_admin_profile.save()

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description='تم تحديث إعدادات تقييد عناوين IP',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, 'تم تحديث إعدادات تقييد عناوين IP بنجاح')

        # التحقق من أن عنوان IP الحالي مسموح به
        current_ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
        if ip_restriction_enabled and not super_admin_profile.is_ip_allowed(current_ip):
            messages.warning(request, f'تنبيه: عنوان IP الحالي الخاص بك ({current_ip}) غير مدرج في قائمة العناوين المسموح بها. قد تواجه مشكلة في تسجيل الدخول في المرة القادمة.')

        return redirect('super_admin:ip_restrictions')

    # الحصول على عنوان IP الحالي للمستخدم
    current_ip = request.META.get('REMOTE_ADDR', '0.0.0.0')

    context = {
        'super_admin_profile': super_admin_profile,
        'current_ip': current_ip,
        'is_current_ip_allowed': super_admin_profile.is_ip_allowed(current_ip),
    }

    return render(request, 'super_admin/ip_restrictions.html', context)
