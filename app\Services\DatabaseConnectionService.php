<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Models\Company;
use Exception;
use Illuminate\Support\Facades\Log;

class DatabaseConnectionService
{
    /**
     * اسم اتصال قاعدة البيانات الخاصة بالشركة
     */
    const TENANT_CONNECTION = 'tenant';

    /**
     * اختبار الاتصال بقاعدة بيانات شركة
     *
     * @param Company $company
     * @return array
     */
    public function testConnection(Company $company)
    {
        try {
            // تكوين اتصال قاعدة البيانات
            $this->configureConnection($company);

            // محاولة الاتصال بقاعدة البيانات
            DB::connection(self::TENANT_CONNECTION)->getPdo();

            // الحصول على عدد الجداول في قاعدة البيانات
            $tablesCount = $this->getTablesCount($company);

            // الحصول على نوع قاعدة البيانات
            $dbType = DB::connection(self::TENANT_CONNECTION)->getDriverName();

            return [
                'success' => true,
                'message' => 'تم الاتصال بقاعدة البيانات بنجاح',
                'tables_count' => $tablesCount,
                'db_type' => $dbType
            ];
        } catch (Exception $e) {
            Log::error('فشل الاتصال بقاعدة البيانات: ' . $e->getMessage(), [
                'company_id' => $company->id,
                'database_name' => $company->database_name
            ]);

            return [
                'success' => false,
                'message' => 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage(),
                'tables_count' => 0,
                'db_type' => null
            ];
        }
    }

    /**
     * الحصول على عدد الجداول في قاعدة البيانات
     *
     * @param Company $company
     * @return int
     */
    public function getTablesCount(Company $company)
    {
        try {
            // تكوين اتصال قاعدة البيانات إذا لم يكن موجوداً
            $this->configureConnection($company);

            // الحصول على عدد الجداول حسب نوع قاعدة البيانات
            $connection = DB::connection(self::TENANT_CONNECTION);
            $driver = $connection->getDriverName();

            if ($driver === 'mysql') {
                $query = "SELECT COUNT(*) as tables_count FROM information_schema.tables WHERE table_schema = ?";
                $result = $connection->select($query, [$company->database_name]);
                return $result[0]->tables_count;
            } elseif ($driver === 'sqlite') {
                $query = "SELECT COUNT(*) as tables_count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'";
                $result = $connection->select($query);
                return $result[0]->tables_count;
            } elseif ($driver === 'pgsql') {
                $query = "SELECT COUNT(*) as tables_count FROM information_schema.tables WHERE table_schema = 'public'";
                $result = $connection->select($query);
                return $result[0]->tables_count;
            }

            return 0;
        } catch (Exception $e) {
            Log::error('فشل في الحصول على عدد الجداول: ' . $e->getMessage(), [
                'company_id' => $company->id,
                'database_name' => $company->database_name
            ]);

            return 0;
        }
    }

    /**
     * الحصول على حجم قاعدة البيانات
     *
     * @param Company $company
     * @return string
     */
    public function getDatabaseSize(Company $company)
    {
        try {
            // تكوين اتصال قاعدة البيانات إذا لم يكن موجوداً
            $this->configureConnection($company);

            // الحصول على حجم قاعدة البيانات حسب نوع قاعدة البيانات
            $connection = DB::connection(self::TENANT_CONNECTION);
            $driver = $connection->getDriverName();

            if ($driver === 'mysql') {
                $query = "SELECT SUM(data_length + index_length) as size FROM information_schema.tables WHERE table_schema = ?";
                $result = $connection->select($query, [$company->database_name]);
                $sizeInBytes = $result[0]->size ?? 0;
                return $this->formatSize($sizeInBytes);
            } elseif ($driver === 'sqlite') {
                // للأسف، SQLite لا يوفر طريقة مباشرة للحصول على حجم قاعدة البيانات من داخل SQL
                // يمكن استخدام حجم الملف بدلاً من ذلك
                $dbPath = database_path($company->database_name . '.sqlite');
                if (file_exists($dbPath)) {
                    $sizeInBytes = filesize($dbPath);
                    return $this->formatSize($sizeInBytes);
                }
                return '0 B';
            } elseif ($driver === 'pgsql') {
                $query = "SELECT pg_database_size(current_database()) as size";
                $result = $connection->select($query);
                $sizeInBytes = $result[0]->size ?? 0;
                return $this->formatSize($sizeInBytes);
            }

            return '0 B';
        } catch (Exception $e) {
            Log::error('فشل في الحصول على حجم قاعدة البيانات: ' . $e->getMessage(), [
                'company_id' => $company->id,
                'database_name' => $company->database_name
            ]);

            return '0 B';
        }
    }

    /**
     * تنسيق حجم الملف
     *
     * @param int $bytes
     * @return string
     */
    private function formatSize($bytes)
    {
        if ($bytes <= 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes, 1024));
        return round($bytes / pow(1024, $i), 2) . ' ' . $units[$i];
    }

    /**
     * تكوين اتصال قاعدة البيانات
     *
     * @param Company $company
     * @return bool
     */
    public function configureConnection(Company $company)
    {
        try {
            // التحقق من وجود الشركة
            if (!$company) {
                Log::error("لم يتم تحديد الشركة");
                return false;
            }

            // التحقق من حالة الشركة
            if ($company->status !== 'active') {
                Log::error("الشركة {$company->name} غير نشطة");
                return false;
            }

            // تحديد نوع قاعدة البيانات (SQLite أو MySQL)
            $isMysql = $company->database_host && !in_array($company->database_host, ['localhost', '127.0.0.1']);

            if ($isMysql) {
                // تكوين اتصال MySQL
                Config::set('database.connections.' . self::TENANT_CONNECTION, [
                    'driver' => 'mysql',
                    'host' => $company->database_host,
                    'database' => $company->database_name,
                    'username' => $company->database_user,
                    'password' => $company->database_password,
                    'port' => '3306',
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                    'strict' => true,
                    'engine' => null,
                ]);
            } else {
                // تكوين اتصال SQLite
                $dbPath = database_path("company_dbs/{$company->database_name}.sqlite3");

                // التحقق من وجود ملف قاعدة البيانات
                if (!file_exists($dbPath)) {
                    Log::error("ملف قاعدة البيانات غير موجود: {$dbPath}");
                    return false;
                }

                Config::set('database.connections.' . self::TENANT_CONNECTION, [
                    'driver' => 'sqlite',
                    'database' => $dbPath,
                    'prefix' => '',
                    'foreign_key_constraints' => true,
                ]);
            }

            // تنظيف الاتصال السابق وإعادة الاتصال
            DB::purge(self::TENANT_CONNECTION);

            // اختبار الاتصال
            try {
                DB::connection(self::TENANT_CONNECTION)->getPdo();

                // التحقق من صحة الاتصال بتنفيذ استعلام بسيط
                $result = DB::connection(self::TENANT_CONNECTION)
                            ->select('SELECT 1 as test');

                if (isset($result[0]->test) && $result[0]->test == 1) {
                    Log::info("تم التحقق من صحة اتصال قاعدة البيانات: {$company->database_name}");
                } else {
                    Log::error("فشل اختبار اتصال قاعدة البيانات: {$company->database_name}");
                    return false;
                }
            } catch (Exception $e) {
                Log::error("خطأ في اختبار اتصال قاعدة البيانات: {$e->getMessage()}");
                return false;
            }

            Log::info("تم إعداد الاتصال بقاعدة بيانات الشركة {$company->name} بنجاح");
            return true;
        } catch (Exception $e) {
            Log::error("فشل في إعداد اتصال قاعدة بيانات الشركة {$company->name}: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * إعداد اتصال قاعدة بيانات الشركة (واجهة جديدة)
     *
     * @param Company $company
     * @return bool
     */
    public function setupConnection(Company $company)
    {
        return $this->configureConnection($company);
    }

    /**
     * تبديل الاتصال إلى قاعدة بيانات شركة
     *
     * @param Company $company
     * @return bool
     */
    public function switchToCompanyDatabase(Company $company)
    {
        try {
            // تكوين اتصال قاعدة البيانات
            $setupSuccess = $this->configureConnection($company);

            if (!$setupSuccess) {
                Log::error('فشل في تكوين اتصال قاعدة البيانات', [
                    'company_id' => $company->id,
                    'database_name' => $company->database_name
                ]);
                return false;
            }

            // محاولة الاتصال بقاعدة البيانات
            DB::connection(self::TENANT_CONNECTION)->getPdo();

            // تعيين الاتصال الافتراضي إلى قاعدة بيانات الشركة
            DB::setDefaultConnection(self::TENANT_CONNECTION);

            return true;
        } catch (Exception $e) {
            Log::error('فشل في تبديل الاتصال إلى قاعدة بيانات الشركة: ' . $e->getMessage(), [
                'company_id' => $company->id,
                'database_name' => $company->database_name
            ]);

            return false;
        }
    }

    /**
     * تهيئة قاعدة بيانات الشركة
     *
     * @param Company $company
     * @return bool|array
     */
    public function initializeDatabase(Company $company)
    {
        try {
            // تحديد نوع قاعدة البيانات
            $isMysql = $company->database_host && !in_array($company->database_host, ['localhost', '127.0.0.1']);

            if ($isMysql) {
                // تهيئة قاعدة بيانات MySQL
                return $this->initializeMysqlDatabase($company);
            } else {
                // تهيئة قاعدة بيانات SQLite
                return $this->initializeSqliteDatabase($company);
            }
        } catch (Exception $e) {
            Log::error("فشل في تهيئة قاعدة بيانات الشركة {$company->name}: {$e->getMessage()}");
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * تهيئة قاعدة بيانات MySQL للشركة
     *
     * @param Company $company
     * @return array
     */
    private function initializeMysqlDatabase(Company $company)
    {
        // تنفيذ عمليات تهيئة قاعدة بيانات MySQL
        // هذه الوظيفة تحتاج إلى تنفيذ حسب متطلبات المشروع
        return [
            'success' => true,
            'message' => 'تم تهيئة قاعدة بيانات MySQL بنجاح'
        ];
    }

    /**
     * تهيئة قاعدة بيانات SQLite للشركة
     *
     * @param Company $company
     * @return array
     */
    private function initializeSqliteDatabase(Company $company)
    {
        $dbPath = database_path("company_dbs/{$company->database_name}.sqlite3");

        // التحقق من وجود ملف قاعدة البيانات
        if (!file_exists($dbPath)) {
            // إنشاء المجلد إذا لم يكن موجودًا
            if (!file_exists(dirname($dbPath))) {
                mkdir(dirname($dbPath), 0755, true);
            }

            // إنشاء ملف قاعدة بيانات فارغ
            try {
                $pdo = new PDO("sqlite:{$dbPath}");
                $pdo = null;
            } catch (Exception $e) {
                Log::error("فشل في إنشاء ملف قاعدة البيانات: {$e->getMessage()}");
                return [
                    'success' => false,
                    'message' => "فشل في إنشاء ملف قاعدة البيانات: {$e->getMessage()}"
                ];
            }
        }

        // تنفيذ عمليات تهيئة قاعدة بيانات SQLite
        // هذه الوظيفة تحتاج إلى تنفيذ حسب متطلبات المشروع
        return [
            'success' => true,
            'message' => 'تم تهيئة قاعدة بيانات SQLite بنجاح'
        ];
    }

    /**
     * العودة إلى قاعدة البيانات الرئيسية
     *
     * @return void
     */
    public function switchToMainDatabase()
    {
        DB::setDefaultConnection('mysql');
    }
}
