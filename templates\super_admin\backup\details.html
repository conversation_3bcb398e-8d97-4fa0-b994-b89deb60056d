{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}تفاصيل النسخة الاحتياطية | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    .details-card {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .details-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-icon {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-left: 1rem;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: #1f2937;
    }

    .action-button {
        transition: all 0.3s ease;
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }

    .action-button:hover {
        transform: translateY(-1px);
    }

    .action-button i {
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-blue-800 flex items-center">
            <i class="fas fa-file-archive text-blue-600 ml-3 text-2xl"></i>
            تفاصيل النسخة الاحتياطية
        </h2>
        <a href="{% url 'super_admin:system_backup' %}" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-colors shadow-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة للنسخ الاحتياطي
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md" role="alert">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
                {% endif %}
                <span class="block sm:inline">{{ message }}</span>
            </div>
        {% endfor %}
    {% endif %}

    <div class="details-card bg-white p-6 rounded-xl border border-gray-200 shadow-md">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-info-circle text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">معلومات النسخة الاحتياطية</h3>
                <p class="text-gray-500 text-sm mt-1">تفاصيل النسخة الاحتياطية المحددة</p>
            </div>
        </div>

        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg mb-6">
            <div class="flex items-center">
                <div class="bg-blue-500 text-white p-3 rounded-full ml-4 shadow-sm">
                    <i class="fas fa-file-archive text-lg"></i>
                </div>
                <div>
                    <h4 class="text-lg font-bold text-blue-800">{{ backup.filename }}</h4>
                    <p class="text-blue-600 text-sm">{{ backup.description|default:"لا يوجد وصف" }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
            <div class="info-item">
                <div class="info-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">تاريخ الإنشاء</div>
                    <div class="info-value">{{ backup.created_at }}</div>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon bg-green-100 text-green-600">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">حجم الملف</div>
                    <div class="info-value">{{ backup.size_formatted }}</div>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-database"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">نوع النسخة</div>
                    <div class="info-value">{{ backup.type|default:"نسخة كاملة" }}</div>
                </div>
            </div>
            {% if backup.company %}
            <div class="info-item">
                <div class="info-icon bg-indigo-100 text-indigo-600">
                    <i class="fas fa-building"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">الشركة</div>
                    <div class="info-value">{{ backup.company.name }}</div>
                </div>
            </div>
            {% endif %}
            <div class="info-item">
                <div class="info-icon bg-yellow-100 text-yellow-600">
                    <i class="fas fa-user"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">تم الإنشاء بواسطة</div>
                    <div class="info-value">{{ backup.created_by|default:"غير محدد" }}</div>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon bg-red-100 text-red-600">
                    <i class="fas fa-code"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">نوع الملف</div>
                    <div class="info-value">SQL</div>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
            <h4 class="font-bold text-gray-800 mb-3 flex items-center">
                <div class="bg-gray-200 text-gray-600 p-2 rounded-full ml-2">
                    <i class="fas fa-table"></i>
                </div>
                محتويات النسخة الاحتياطية
            </h4>
            <div class="bg-white p-4 rounded-lg border border-gray-200 max-h-60 overflow-y-auto text-sm font-mono text-gray-700 dir-ltr">
                {% if backup_content %}
                    <pre>{{ backup_content }}</pre>
                {% else %}
                    <p class="text-gray-500 text-center py-4">لا يمكن عرض محتويات النسخة الاحتياطية</p>
                {% endif %}
            </div>
        </div>

        <div class="flex justify-between items-center pt-4 border-t border-gray-200">
            <div class="flex space-x-2 space-x-reverse">
                <a href="{% url 'super_admin:download_system_backup' filename=backup.filename %}" class="action-button bg-blue-100 text-blue-700 hover:bg-blue-200">
                    <i class="fas fa-download"></i> تنزيل
                </a>
                <a href="{% url 'super_admin:restore_backup_view' filename=backup.filename %}" class="action-button bg-green-100 text-green-700 hover:bg-green-200">
                    <i class="fas fa-undo-alt"></i> استعادة
                </a>
            </div>
            <button type="button" id="delete-backup-btn" class="action-button bg-red-100 text-red-700 hover:bg-red-200">
                <i class="fas fa-trash-alt"></i> حذف
            </button>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div id="delete-backup-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4">
            <h3 class="text-lg font-bold">تأكيد حذف النسخة الاحتياطية</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center mb-4 bg-red-50 p-4 rounded-lg border border-red-200">
                <div class="bg-red-100 text-red-600 p-2 rounded-full ml-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="text-red-700">هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <p class="text-gray-700 mb-4">اسم الملف: <span class="font-bold">{{ backup.filename }}</span></p>
            <form method="post" action="{% url 'super_admin:delete_system_backup' filename=backup.filename %}">
                {% csrf_token %}
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" id="cancel-delete" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">إلغاء</button>
                    <button type="submit" class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-red-700 transition-colors">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteBackupBtn = document.getElementById('delete-backup-btn');
        const deleteBackupModal = document.getElementById('delete-backup-modal');
        const cancelDeleteBtn = document.getElementById('cancel-delete');

        // عرض نافذة تأكيد الحذف
        deleteBackupBtn.addEventListener('click', function() {
            deleteBackupModal.classList.remove('hidden');
        });

        // إغلاق نافذة تأكيد الحذف
        cancelDeleteBtn.addEventListener('click', function() {
            deleteBackupModal.classList.add('hidden');
        });
    });
</script>
{% endblock %}
