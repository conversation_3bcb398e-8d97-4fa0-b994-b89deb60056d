# Generated by Django 5.2 on 2025-04-15 20:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_number', models.CharField(max_length=50, verbose_name='رقم المعاملة')),
                ('document_type', models.CharField(choices=[('incoming', 'وارد'), ('outgoing', 'صادر')], max_length=20, verbose_name='نوع المعاملة')),
                ('entity', models.Char<PERSON>ield(max_length=100, verbose_name='الجهة')),
                ('subject', models.Char<PERSON><PERSON>(max_length=200, verbose_name='الموضوع')),
                ('details', models.TextField(blank=True, null=True, verbose_name='تفاصيل المعاملة')),
                ('document_date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('status', models.CharField(choices=[('completed', 'مكتملة'), ('pending', 'قيد الإجراء'), ('rejected', 'مرفوضة')], default='pending', max_length=20, verbose_name='حالة المعاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة',
                'verbose_name_plural': 'معاملات',
                'ordering': ['-document_date'],
            },
        ),
        migrations.CreateModel(
            name='DocumentAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='documents/attachments/%Y/%m/', verbose_name='الملف')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='وصف الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='documents.document', verbose_name='المعاملة')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مرفق معاملة',
                'verbose_name_plural': 'مرفقات المعاملات',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
