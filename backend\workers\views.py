from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.http import Http404, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import json
import pandas as pd
import re
import os
from datetime import datetime
from .models import Worker, WorkerDocument, RecruitmentCompany
from .forms import WorkerForm, WorkerImportForm, WorkerDocumentForm
from backend.dashboard.models import Activity
from backend.system_settings.models import SystemAction
# استيراد وظائف معالجة جوازات السفر عند الحاجة

# وظيفة مساعدة للحصول على إحصائيات العمال
def get_worker_statistics():
    """
    وظيفة مساعدة للحصول على إحصائيات العمال حسب النوع والحالة
    تستخدم في لوحة التحكم وأي مكان آخر يحتاج إلى عرض الإحصائيات
    """
    # عدد العمال حسب النوع - فقط العمال النشطين
    contract_workers_count = Worker.objects.filter(status='active', worker_type='contract').count()
    custom_workers_count = Worker.objects.filter(status='active', worker_type='custom').count()
    monthly_workers_count = Worker.objects.filter(status='active', worker_type='monthly').count()

    # إجمالي العمال النشطين
    total_active_workers = contract_workers_count + custom_workers_count + monthly_workers_count

    # إجمالي العمال بغض النظر عن الحالة
    total_workers = Worker.objects.count()

    # العمال حسب الحالة
    active_workers = Worker.objects.filter(status='active').count()
    inactive_workers = Worker.objects.filter(status='inactive').count()
    on_leave_workers = Worker.objects.filter(status='on_leave').count()
    terminated_workers = Worker.objects.filter(status='terminated').count()

    return {
        'contract_workers_count': contract_workers_count,
        'custom_workers_count': custom_workers_count,
        'monthly_workers_count': monthly_workers_count,
        'total_active_workers': total_active_workers,
        'total_workers': total_workers,
        'active_workers': active_workers,
        'inactive_workers': inactive_workers,
        'on_leave_workers': on_leave_workers,
        'terminated_workers': terminated_workers,
    }

# إشارات (signals) لتحديث الإحصائيات عند تغيير بيانات العمال
@receiver(post_save, sender=Worker)
def worker_saved(sender, instance, created, **kwargs):
    """
    تنفيذ عند إنشاء أو تحديث عامل
    - تحديث الإحصائيات
    - تسجيل النشاط
    """
    # تسجيل النشاط
    if created:
        activity_type = 'worker_added'
        description = f'تم إضافة العامل {instance.first_name} {instance.last_name}'
    else:
        activity_type = 'worker_updated'
        description = f'تم تحديث بيانات العامل {instance.first_name} {instance.last_name}'

    # إنشاء سجل نشاط جديد
    Activity.objects.create(
        description=description,
        activity_type=activity_type,
        date_created=timezone.now(),
        related_worker=instance
    )

@receiver(post_delete, sender=Worker)
def worker_deleted(sender, instance, **kwargs):
    """
    تنفيذ عند حذف عامل
    - تحديث الإحصائيات
    - تسجيل النشاط
    """
    # تسجيل النشاط
    Activity.objects.create(
        description=f'تم حذف العامل {instance.first_name} {instance.last_name}',
        activity_type='worker_deleted',
        date_created=timezone.now()
    )

# API لإحصائيات العمال
@login_required
def worker_statistics_api(request):
    """
    API للحصول على إحصائيات العمال في الوقت الفعلي
    تستخدم للتحديث المستمر للإحصائيات في واجهة المستخدم
    """
    # الحصول على الإحصائيات باستخدام الوظيفة المساعدة
    stats = get_worker_statistics()

    # إرجاع الإحصائيات كـ JSON
    return JsonResponse(stats)

# Create your views here.

# صفحة معالجة صور جوازات السفر
@login_required
@csrf_exempt
def process_passport_image_view(request):
    """
    صفحة معالجة صور جوازات السفر مع أدوات المساعدة
    """
    if request.method == 'POST':
        try:
            # استلام بيانات الصورة
            data = json.loads(request.body)
            image_data = data.get('image')

            if not image_data:
                return JsonResponse({
                    'success': False,
                    'error': 'لم يتم توفير بيانات الصورة'
                }, status=400)

            # استخدام وظيفة معالجة الصورة المحسنة
            from backend.scanner.passport_ocr_enhanced import process_passport_image
            result = process_passport_image(image_data)

            # إرجاع النتيجة
            return JsonResponse(result)

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ أثناء معالجة الصورة: {str(e)}'
            }, status=500)

    # عرض صفحة معالجة الصورة
    return render(request, 'workers/process_passport_image.html')

# صفحات رفع والتقاط صور جوازات السفر
@login_required
def upload_passport(request):
    """صفحة رفع صورة جواز سفر"""
    return render(request, 'workers/upload_passport.html')

@login_required
def capture_passport(request):
    """صفحة التقاط صورة جواز سفر"""
    return render(request, 'workers/capture_passport.html')

@login_required
def workers_dashboard(request):
    """View function for the workers dashboard page."""
    # Get worker statistics using the helper function
    stats = get_worker_statistics()

    # Get recent workers - show the most recently added workers first
    recent_workers = Worker.objects.all().order_by('-date_joined')[:5]

    # Get recent activities
    recent_activities = Activity.objects.all().order_by('-date_created')[:5]

    # Combine statistics, recent workers and activities in the context
    context = {
        'recent_workers': recent_workers,
        'recent_activities': recent_activities,
        **stats  # Unpack all statistics into the context
    }

    return render(request, 'pages/workers/dashboard.html', context)

@login_required
def contract_workers(request):
    """View function for contract workers list."""
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    from django.db.models import Q

    # Get search parameters
    search_query = request.GET.get('search', '')
    nationality_filter = request.GET.get('nationality', '')
    status_filter = request.GET.get('status', '')

    # فلترة حسب نوع العامل - عمال العقود الدائمية
    workers_list = Worker.objects.filter(worker_type='contract')

    # Apply filters
    if search_query:
        workers_list = workers_list.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(passport_number__icontains=search_query)
        )

    if nationality_filter:
        workers_list = workers_list.filter(nationality=nationality_filter)

    if status_filter:
        workers_list = workers_list.filter(status=status_filter)

    # Order the results
    workers_list = workers_list.order_by('first_name', 'last_name')

    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(workers_list, 10)  # Show 10 workers per page

    try:
        workers = paginator.page(page)
    except PageNotAnInteger:
        workers = paginator.page(1)
    except EmptyPage:
        workers = paginator.page(paginator.num_pages)

    # Get choices for filters
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'workers': workers,
        'worker_type': 'contract',
        'title': 'عمال العقود الدائمية',
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'search_query': search_query,
        'nationality_filter': nationality_filter,
        'status_filter': status_filter,
    }

    return render(request, 'pages/workers/list.html', context)

@login_required
def custom_service_workers(request):
    """View function for custom service workers list."""
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    from django.db.models import Q

    # Get search parameters
    search_query = request.GET.get('search', '')
    nationality_filter = request.GET.get('nationality', '')
    status_filter = request.GET.get('status', '')

    # فلترة حسب نوع العامل - عمال الخدمات المخصصة
    workers_list = Worker.objects.filter(worker_type='custom')

    # Apply filters
    if search_query:
        workers_list = workers_list.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(passport_number__icontains=search_query)
        )

    if nationality_filter:
        workers_list = workers_list.filter(nationality=nationality_filter)

    if status_filter:
        workers_list = workers_list.filter(status=status_filter)

    # Order the results
    workers_list = workers_list.order_by('first_name', 'last_name')

    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(workers_list, 10)  # Show 10 workers per page

    try:
        workers = paginator.page(page)
    except PageNotAnInteger:
        workers = paginator.page(1)
    except EmptyPage:
        workers = paginator.page(paginator.num_pages)

    # Get choices for filters
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'workers': workers,
        'worker_type': 'custom',
        'title': 'عمال الخدمات المخصصة',
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'search_query': search_query,
        'nationality_filter': nationality_filter,
        'status_filter': status_filter,
    }

    return render(request, 'pages/workers/list.html', context)

@login_required
def monthly_workers(request):
    """View function for monthly contract workers list."""
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    from django.db.models import Q

    # Get search parameters
    search_query = request.GET.get('search', '')
    nationality_filter = request.GET.get('nationality', '')
    status_filter = request.GET.get('status', '')

    # فلترة حسب نوع العامل - عمال العقود الشهرية
    workers_list = Worker.objects.filter(worker_type='monthly')

    # Apply filters
    if search_query:
        workers_list = workers_list.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(passport_number__icontains=search_query)
        )

    if nationality_filter:
        workers_list = workers_list.filter(nationality=nationality_filter)

    if status_filter:
        workers_list = workers_list.filter(status=status_filter)

    # Order the results
    workers_list = workers_list.order_by('first_name', 'last_name')

    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(workers_list, 10)  # Show 10 workers per page

    try:
        workers = paginator.page(page)
    except PageNotAnInteger:
        workers = paginator.page(1)
    except EmptyPage:
        workers = paginator.page(paginator.num_pages)

    # Get choices for filters
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'workers': workers,
        'worker_type': 'monthly',
        'title': 'عمال العقود الشهرية',
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'search_query': search_query,
        'nationality_filter': nationality_filter,
        'status_filter': status_filter,
    }

    return render(request, 'pages/workers/list.html', context)

@login_required
def helper_tools(request):
    """صفحة الأدوات المساعدة"""
    return render(request, 'workers/helper_tools.html')

def worker_list(request):
    """View function for the worker list page."""
    # Obtener todos los trabajadores ordenados por nombre
    workers = Worker.objects.all().order_by('first_name', 'last_name')

    # Pasar los trabajadores a la plantilla
    context = {
        'workers': workers,
    }

    return render(request, 'workers/worker_list.html', context)

@login_required
def modern_worker_list(request):
    """View function for the modern worker list page with Tailwind CSS."""
    # Get all workers ordered by name
    workers = Worker.objects.all().order_by('first_name', 'last_name')

    # Get nationalities for filter
    nationalities = []

    # Prepare context
    context = {
        'workers': workers,
        'nationalities': nationalities,
    }

    return render(request, 'workers/modern_worker_list.html', context)

@login_required
def new_worker_list(request):
    """View function for the new worker list page with Tailwind CSS design."""
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    from django.db.models import Q

    # Get search parameters
    search_query = request.GET.get('search', '')
    nationality_filter = request.GET.get('nationality', '')
    status_filter = request.GET.get('status', '')
    worker_type = request.GET.get('type', '')  # Get worker type from URL parameter

    # Start with all workers
    workers_list = Worker.objects.all()

    # Apply worker type filter
    if worker_type:
        if worker_type == 'contract':
            workers_list = workers_list.filter(worker_type='contract')
            title = 'عمال العقود الدائمية'
        elif worker_type == 'custom':
            workers_list = workers_list.filter(worker_type='custom')
            title = 'عمال الخدمات المخصصة'
        elif worker_type == 'monthly':
            workers_list = workers_list.filter(worker_type='monthly')
            title = 'عمال العقود الشهرية'
        else:
            title = 'قائمة العمال'
    else:
        title = 'قائمة العمال'

    # Apply other filters
    if search_query:
        workers_list = workers_list.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(passport_number__icontains=search_query)
        )

    if nationality_filter:
        workers_list = workers_list.filter(nationality=nationality_filter)

    if status_filter:
        workers_list = workers_list.filter(status=status_filter)

    # Order the results
    workers_list = workers_list.order_by('first_name', 'last_name')

    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(workers_list, 10)  # Show 10 workers per page

    try:
        workers = paginator.page(page)
    except PageNotAnInteger:
        workers = paginator.page(1)
    except EmptyPage:
        workers = paginator.page(paginator.num_pages)

    # Get choices for filters
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    # Prepare context
    context = {
        'workers': workers,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'search_query': search_query,
        'nationality_filter': nationality_filter,
        'status_filter': status_filter,
        'worker_type': worker_type,
        'title': title
    }

    return render(request, 'pages/workers/list.html', context)

@login_required
def new_worker_detail(request, worker_id):
    """View function for viewing a worker's details with the new design."""
    try:
        worker = get_object_or_404(Worker, id=worker_id)
    except Http404:
        messages.error(request, 'العامل غير موجود أو تم حذفه بالفعل')
        return redirect('workers:worker_list')

    # الحصول على المستندات المرتبطة بالعامل من نموذج Document
    from backend.documents.models import Document
    worker_documents = Document.objects.filter(
        entity__icontains=f'Worker: {worker.first_name} {worker.last_name}'
    )

    # تحضير متغيرات لفحص تواريخ انتهاء المستندات
    from django.utils import timezone
    today = timezone.now().date()
    expiry_warning = today + timezone.timedelta(days=30)  # تحذير قبل 30 يوم من انتهاء الصلاحية

    context = {
        'worker': worker,
        'worker_documents': worker_documents,
        'today': today,
        'expiry_warning': expiry_warning,
        'title': f'تفاصيل العامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/worker_detail_tailwind.html', context)

@login_required
def create_permanent_worker(request):
    """View function for creating a new permanent contract worker."""
    # Get recruitment companies for the form
    recruitment_companies = RecruitmentCompany.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        # Process form data manually
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        gender = request.POST.get('gender', 'M')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth', None)
        phone_number = request.POST.get('phone_number', '')
        job_title = request.POST.get('job_title', '')
        passport_number = request.POST.get('passport_number', '')
        passport_expiry = request.POST.get('passport_expiry', None)
        passport_issue_date = request.POST.get('passport_issue_date', None)
        entry_date = request.POST.get('entry_date', None)
        visa_number = request.POST.get('visa_number', '')
        visa_expiry = request.POST.get('visa_expiry', None)
        date_joined = request.POST.get('date_joined', None)
        company_beneficiary = request.POST.get('company_beneficiary', '')
        contract_type = request.POST.get('contract_type', 'full_time')
        salary = request.POST.get('salary', 0)

        # التحقق من عدم وجود رقم جواز سفر مكرر
        if passport_number and Worker.objects.filter(passport_number=passport_number).exists():
            messages.error(request, f'رقم جواز السفر {passport_number} موجود بالفعل. يرجى استخدام رقم آخر.')
            return redirect('workers:create_permanent_worker')

        # Create worker object
        worker = Worker(
            first_name=first_name,
            last_name=last_name,
            gender=gender,
            nationality=nationality,
            phone_number=phone_number,
            job_title=job_title,
            passport_number=passport_number,
            status='active',
            worker_type='contract'  # Set worker type to permanent contract
        )

        # Set date fields if provided
        if date_of_birth:
            worker.date_of_birth = date_of_birth
        if passport_expiry:
            worker.passport_expiry = passport_expiry

        # تعيين تاريخ إصدار جواز السفر حتى لو كان فارغاً
        worker.passport_issue_date = passport_issue_date if passport_issue_date else None

        if entry_date:
            worker.entry_date = entry_date
        if visa_number:
            worker.visa_number = visa_number
        if visa_expiry:
            worker.visa_expiry = visa_expiry
        if date_joined:
            worker.date_joined = date_joined

        # Set additional fields
        worker.company_beneficiary = company_beneficiary
        worker.contract_type = contract_type

        # تعيين الراتب حتى لو كان فارغاً
        try:
            worker.salary = float(salary) if salary else 0
        except (ValueError, TypeError):
            worker.salary = 0

        # Save worker to get an ID
        worker.save()

        # Handle file uploads
        if 'photo' in request.FILES:
            worker.photo = request.FILES['photo']
        if 'passport_image' in request.FILES:
            worker.passport_image = request.FILES['passport_image']
        if 'visa_image' in request.FILES:
            worker.visa_image = request.FILES['visa_image']

        # Save worker again with files
        worker.save()

        # Save worker data for undo action
        worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': 'contract',
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='create',
            module='workers',
            record_id=worker.id,
            description=f'تم إضافة العامل: {worker.first_name} {worker.last_name}',
            previous_data=None,
            current_data=json.dumps(worker_data),
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Register activity
        Activity.objects.create(
            description=f'تم إضافة العامل {worker.first_name} {worker.last_name}',
            activity_type='worker_added',
            date_created=timezone.now(),
            related_worker=worker
        )

        messages.success(request, f'تم إضافة العامل {worker.first_name} {worker.last_name} بنجاح')

        # Redirect to contract workers list
        return redirect('workers:contract_workers')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'recruitment_companies': recruitment_companies,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'title': 'إضافة عامل عقد دائم'
    }

    return render(request, 'workers/forms/permanent_worker_form.html', context)

@login_required
def create_custom_worker(request):
    """View function for creating a new custom service worker."""
    # Get recruitment companies for the form
    recruitment_companies = RecruitmentCompany.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        # Process form data manually
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        gender = request.POST.get('gender', 'M')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth', None)
        phone_number = request.POST.get('phone_number', '')
        job_title = request.POST.get('job_title', '')
        passport_number = request.POST.get('passport_number', '')
        passport_expiry = request.POST.get('passport_expiry', None)
        passport_issue_date = request.POST.get('passport_issue_date', None)
        entry_date = request.POST.get('entry_date', None)
        visa_number = request.POST.get('visa_number', '')
        visa_expiry = request.POST.get('visa_expiry', None)
        service_description = request.POST.get('service_description', '')
        service_date = request.POST.get('service_date', None)
        service_duration = request.POST.get('service_duration', 1)
        service_cost = request.POST.get('service_cost', 0)

        # التحقق من عدم وجود رقم جواز سفر مكرر
        if passport_number and Worker.objects.filter(passport_number=passport_number).exists():
            messages.error(request, f'رقم جواز السفر {passport_number} موجود بالفعل. يرجى استخدام رقم آخر.')
            return redirect('workers:create_custom_worker')

        # Create worker object
        worker = Worker(
            first_name=first_name,
            last_name=last_name,
            gender=gender,
            nationality=nationality,
            phone_number=phone_number,
            job_title=job_title,
            passport_number=passport_number,
            status='active',
            worker_type='custom',  # Set worker type to custom service
            notes=service_description  # Store service description in notes field
        )

        # Set date fields if provided
        if date_of_birth:
            worker.date_of_birth = date_of_birth
        if passport_expiry:
            worker.passport_expiry = passport_expiry

        # تعيين تاريخ إصدار جواز السفر حتى لو كان فارغاً
        worker.passport_issue_date = passport_issue_date if passport_issue_date else None

        if entry_date:
            worker.entry_date = entry_date
        if visa_number:
            worker.visa_number = visa_number
        if visa_expiry:
            worker.visa_expiry = visa_expiry
        if service_date:
            worker.date_joined = service_date  # Use date_joined for service date

        # Set additional fields (these would need to be added to the Worker model)
        # For now, we'll store them in the notes field as JSON
        service_info = {
            'service_description': service_description,
            'service_date': service_date,
            'service_duration': service_duration,
            'service_cost': service_cost
        }
        worker.notes = json.dumps(service_info)

        # تعيين تكلفة الخدمة كراتب حتى لو كانت فارغة
        try:
            worker.salary = float(service_cost) if service_cost else 0
        except (ValueError, TypeError):
            worker.salary = 0

        # Save worker to get an ID
        worker.save()

        # Handle file uploads
        if 'photo' in request.FILES:
            worker.photo = request.FILES['photo']
        if 'passport_image' in request.FILES:
            worker.passport_image = request.FILES['passport_image']
        if 'visa_image' in request.FILES:
            worker.visa_image = request.FILES['visa_image']

        # Save worker again with files
        worker.save()

        # Save worker data for undo action
        worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': 'custom',
            'service_info': service_info,
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='create',
            module='workers',
            record_id=worker.id,
            description=f'تم إضافة العامل: {worker.first_name} {worker.last_name}',
            previous_data=None,
            current_data=json.dumps(worker_data),
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Register activity
        Activity.objects.create(
            description=f'تم إضافة العامل {worker.first_name} {worker.last_name}',
            activity_type='worker_added',
            date_created=timezone.now(),
            related_worker=worker
        )

        messages.success(request, f'تم إضافة العامل {worker.first_name} {worker.last_name} بنجاح')

        # Redirect to custom service workers list
        return redirect('workers:custom_service_workers')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'recruitment_companies': recruitment_companies,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'title': 'إضافة عامل خدمة مخصصة'
    }

    return render(request, 'workers/forms/custom_worker_form.html', context)

@login_required
def create_monthly_worker(request):
    """View function for creating a new monthly contract worker."""
    # Get recruitment companies for the form
    recruitment_companies = RecruitmentCompany.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        # Process form data manually
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        gender = request.POST.get('gender', 'M')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth', None)
        phone_number = request.POST.get('phone_number', '')
        job_title = request.POST.get('job_title', '')
        passport_number = request.POST.get('passport_number', '')
        passport_expiry = request.POST.get('passport_expiry', None)
        passport_issue_date = request.POST.get('passport_issue_date', None)
        entry_date = request.POST.get('entry_date', None)
        visa_number = request.POST.get('visa_number', '')
        visa_expiry = request.POST.get('visa_expiry', None)
        date_joined = request.POST.get('date_joined', None)
        contract_end_date = request.POST.get('contract_end_date', None)
        renewal_period = request.POST.get('renewal_period', 1)
        monthly_salary = request.POST.get('monthly_salary', 0)

        # التحقق من عدم وجود رقم جواز سفر مكرر
        if passport_number and Worker.objects.filter(passport_number=passport_number).exists():
            messages.error(request, f'رقم جواز السفر {passport_number} موجود بالفعل. يرجى استخدام رقم آخر.')
            return redirect('workers:create_monthly_worker')

        # Create worker object
        worker = Worker(
            first_name=first_name,
            last_name=last_name,
            gender=gender,
            nationality=nationality,
            phone_number=phone_number,
            job_title=job_title,
            passport_number=passport_number,
            status='active',
            worker_type='monthly'  # Set worker type to monthly contract
        )

        # Set date fields if provided
        if date_of_birth:
            worker.date_of_birth = date_of_birth
        if passport_expiry:
            worker.passport_expiry = passport_expiry

        # تعيين تاريخ إصدار جواز السفر حتى لو كان فارغاً
        worker.passport_issue_date = passport_issue_date if passport_issue_date else None

        if entry_date:
            worker.entry_date = entry_date
        if visa_number:
            worker.visa_number = visa_number
        if visa_expiry:
            worker.visa_expiry = visa_expiry
        if date_joined:
            worker.date_joined = date_joined

        # Set additional fields (these would need to be added to the Worker model)
        # For now, we'll store them in the notes field as JSON
        contract_info = {
            'contract_end_date': contract_end_date,
            'renewal_period': renewal_period
        }
        worker.notes = json.dumps(contract_info)

        # تعيين الراتب الشهري حتى لو كان فارغاً
        try:
            worker.salary = float(monthly_salary) if monthly_salary else 0
        except (ValueError, TypeError):
            worker.salary = 0

        # Save worker to get an ID
        worker.save()

        # Handle file uploads
        if 'photo' in request.FILES:
            worker.photo = request.FILES['photo']
        if 'passport_image' in request.FILES:
            worker.passport_image = request.FILES['passport_image']
        if 'visa_image' in request.FILES:
            worker.visa_image = request.FILES['visa_image']

        # Save worker again with files
        worker.save()

        # Save worker data for undo action
        worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': 'monthly',
            'contract_info': contract_info,
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='create',
            module='workers',
            record_id=worker.id,
            description=f'تم إضافة العامل: {worker.first_name} {worker.last_name}',
            previous_data=None,
            current_data=json.dumps(worker_data),
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Register activity
        Activity.objects.create(
            description=f'تم إضافة العامل {worker.first_name} {worker.last_name}',
            activity_type='worker_added',
            date_created=timezone.now(),
            related_worker=worker
        )

        messages.success(request, f'تم إضافة العامل {worker.first_name} {worker.last_name} بنجاح')

        # Redirect to monthly workers list
        return redirect('workers:monthly_workers')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'recruitment_companies': recruitment_companies,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'title': 'إضافة عامل عقد شهري'
    }

    return render(request, 'workers/forms/monthly_worker_form.html', context)

@login_required
def worker_create(request):
    """View function for creating a new worker."""
    # Get recruitment companies for the form
    recruitment_companies = RecruitmentCompany.objects.filter(is_active=True).order_by('name')

    # Get worker type from query parameter or default to 'contract'
    worker_type = request.GET.get('type', 'contract')

    if request.method == 'POST':
        # Process form data manually
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        gender = request.POST.get('gender', 'M')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth', None)
        phone_number = request.POST.get('phone_number', '')
        job_title = request.POST.get('job_title', '')
        passport_number = request.POST.get('passport_number', '')
        passport_expiry = request.POST.get('passport_expiry', None)
        passport_issue_date = request.POST.get('passport_issue_date', None)
        entry_date = request.POST.get('entry_date', None)
        visa_number = request.POST.get('visa_number', '')
        visa_expiry = request.POST.get('visa_expiry', None)
        recruitment_company_id = request.POST.get('recruitment_company', None)
        date_joined = request.POST.get('date_joined', None)
        status = request.POST.get('status', 'active')
        salary = request.POST.get('salary', 0)
        notes = request.POST.get('notes', '')
        worker_type = request.POST.get('worker_type', 'contract')

        # التحقق من اختيار نوع العامل
        if not worker_type or worker_type == '':
            messages.error(request, 'يرجى اختيار نوع العامل قبل الحفظ')
            return redirect('workers:worker_create')

        # التحقق من عدم وجود رقم جواز سفر مكرر
        if passport_number and Worker.objects.filter(passport_number=passport_number).exists():
            messages.error(request, f'رقم جواز السفر {passport_number} موجود بالفعل. يرجى استخدام رقم آخر.')
            return redirect('workers:worker_create')

        # Create worker object
        worker = Worker(
            first_name=first_name,
            last_name=last_name,
            gender=gender,
            nationality=nationality,
            phone_number=phone_number,
            job_title=job_title,
            passport_number=passport_number,
            status=status,
            notes=notes,
            worker_type=worker_type  # تعيين نوع العامل
        )

        # Set date fields if provided
        if date_of_birth:
            worker.date_of_birth = date_of_birth
        if passport_expiry:
            worker.passport_expiry = passport_expiry

        # تعيين تاريخ إصدار جواز السفر حتى لو كان فارغاً
        worker.passport_issue_date = passport_issue_date if passport_issue_date else None

        if entry_date:
            worker.entry_date = entry_date
        if visa_number:
            worker.visa_number = visa_number
        if visa_expiry:
            worker.visa_expiry = visa_expiry
        if date_joined:
            worker.date_joined = date_joined

        # Set recruitment company if provided
        if recruitment_company_id and recruitment_company_id != '':
            try:
                recruitment_company = RecruitmentCompany.objects.get(id=recruitment_company_id)
                worker.recruitment_company = recruitment_company
            except RecruitmentCompany.DoesNotExist:
                pass

        # تعيين الراتب حتى لو كان فارغاً
        try:
            worker.salary = float(salary) if salary else 0
        except (ValueError, TypeError):
            worker.salary = 0

        # Save worker to get an ID
        worker.save()

        # Handle file uploads
        if 'photo' in request.FILES:
            worker.photo = request.FILES['photo']
        if 'passport_image' in request.FILES:
            worker.passport_image = request.FILES['passport_image']
        if 'visa_image' in request.FILES:
            worker.visa_image = request.FILES['visa_image']

        # Save worker again with files
        worker.save()

        # Save worker data for undo action
        worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': worker_type,
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='create',
            module='workers',
            record_id=worker.id,
            description=f'تم إضافة العامل: {worker.first_name} {worker.last_name}',
            previous_data=None,
            current_data=json.dumps(worker_data),
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Register activity
        Activity.objects.create(
            description=f'تم إضافة العامل {worker.first_name} {worker.last_name}',
            activity_type='worker_added',
            date_created=timezone.now(),
            related_worker=worker
        )

        messages.success(request, f'تم إضافة العامل {worker.first_name} {worker.last_name} بنجاح')

        # Redirect based on worker type
        if worker_type == 'contract':
            return redirect('workers:contract_workers')
        elif worker_type == 'custom':
            return redirect('workers:custom_service_workers')
        elif worker_type == 'monthly':
            return redirect('workers:monthly_workers')
        else:
            return redirect('workers:worker_list')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'recruitment_companies': recruitment_companies,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'worker_type': worker_type,
        'title': 'إضافة عامل جديد'
    }

    return render(request, 'workers/worker_form_tailwind.html', context)

def worker_detail(request, worker_id):
    """View function for viewing a worker's details."""
    try:
        worker = get_object_or_404(Worker, id=worker_id)
    except Http404:
        messages.error(request, 'العامل غير موجود أو تم حذفه بالفعل')
        return redirect('workers:worker_list')

    # تحضير متغيرات لفحص تواريخ انتهاء المستندات
    from django.utils import timezone
    today = timezone.now().date()
    expiry_warning = today + timezone.timedelta(days=30)  # تحذير قبل 30 يوم من انتهاء الصلاحية

    context = {
        'worker': worker,
        'today': today,
        'expiry_warning': expiry_warning,
        'title': f'تفاصيل العامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/worker_detail_tailwind.html', context)

@login_required
def worker_update(request, worker_id):
    """View function for updating a worker's information."""
    try:
        worker = get_object_or_404(Worker, id=worker_id)
    except Http404:
        messages.error(request, 'العامل غير موجود أو تم حذفه بالفعل')
        return redirect('workers:worker_list')

    # Determine worker type (for redirect after save)
    worker_type = request.GET.get('type', 'contract')

    # Save previous worker data for undo action
    previous_worker_data = {
        'first_name': worker.first_name,
        'last_name': worker.last_name,
        'gender': worker.gender,
        'nationality': worker.nationality,
        'passport_number': worker.passport_number,
        'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
        'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
        'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
        'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
        'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
        'is_active': worker.is_active,
        'status': worker.status,
        'date_joined': str(worker.date_joined) if worker.date_joined else None,
        'worker_type': worker_type,
        'salary': float(worker.salary) if worker.salary else 0,
    }

    if request.method == 'POST':
        # Process form data manually
        first_name = request.POST.get('first_name', worker.first_name)
        last_name = request.POST.get('last_name', worker.last_name)
        gender = request.POST.get('gender', worker.gender)
        nationality = request.POST.get('nationality', worker.nationality)
        date_of_birth = request.POST.get('date_of_birth', None)
        passport_number = request.POST.get('passport_number', worker.passport_number)
        passport_expiry = request.POST.get('passport_expiry', None)
        passport_issue_date = request.POST.get('passport_issue_date', None)
        entry_date = request.POST.get('entry_date', None)
        visa_number = request.POST.get('visa_number', getattr(worker, 'visa_number', None))
        visa_expiry = request.POST.get('visa_expiry', None)
        recruitment_company_id = request.POST.get('recruitment_company', None)
        date_joined = request.POST.get('date_joined', None)
        status = request.POST.get('status', worker.status)
        salary = request.POST.get('salary', worker.salary)
        notes = request.POST.get('notes', worker.notes)
        worker_type = request.POST.get('worker_type', worker_type)

        # التحقق من اختيار نوع العامل
        if not worker_type or worker_type == '':
            messages.error(request, 'يرجى اختيار نوع العامل قبل الحفظ')
            return redirect('workers:worker_update', worker_id=worker_id)

        # التحقق من عدم وجود رقم جواز سفر مكرر (مع استثناء العامل الحالي)
        if passport_number and Worker.objects.filter(passport_number=passport_number).exclude(id=worker_id).exists():
            messages.error(request, f'رقم جواز السفر {passport_number} موجود بالفعل لعامل آخر. يرجى استخدام رقم آخر.')
            return redirect('workers:worker_update', worker_id=worker_id)

        # Update worker object
        worker.first_name = first_name
        worker.last_name = last_name
        worker.gender = gender
        worker.nationality = nationality
        worker.passport_number = passport_number
        worker.status = status
        worker.notes = notes
        worker.worker_type = worker_type  # تحديث نوع العامل

        # تحديث الراتب
        try:
            worker.salary = float(salary)
        except (ValueError, TypeError):
            # إذا كان الراتب غير صالح، نحتفظ بالقيمة الحالية
            pass

        # تحديث رقم التأشيرة إذا كان موجوداً
        if hasattr(worker, 'visa_number'):
            worker.visa_number = visa_number

        # Set date fields if provided
        if date_of_birth:
            worker.date_of_birth = date_of_birth
        if passport_expiry:
            worker.passport_expiry = passport_expiry

        # تعيين تاريخ إصدار جواز السفر حتى لو كان فارغاً
        worker.passport_issue_date = passport_issue_date if passport_issue_date else None

        if entry_date:
            worker.entry_date = entry_date
        if visa_expiry:
            worker.visa_expiry = visa_expiry
        if date_joined:
            worker.date_joined = date_joined

        # Set recruitment company if provided
        if recruitment_company_id and recruitment_company_id != '':
            try:
                recruitment_company = RecruitmentCompany.objects.get(id=recruitment_company_id)
                worker.recruitment_company = recruitment_company
            except RecruitmentCompany.DoesNotExist:
                pass
        elif recruitment_company_id == '':
            worker.recruitment_company = None

        # تم تعيين الراتب سابقاً، لا حاجة لتكراره هنا

        # Handle file uploads
        if 'photo' in request.FILES:
            # Delete old file if exists
            if worker.photo:
                try:
                    worker.photo.delete()
                except:
                    pass
            worker.photo = request.FILES['photo']

        if 'passport_image' in request.FILES:
            # Delete old file if exists
            if worker.passport_image:
                try:
                    worker.passport_image.delete()
                except:
                    pass
            worker.passport_image = request.FILES['passport_image']

        if 'visa_image' in request.FILES:
            # Delete old file if exists
            if worker.visa_image:
                try:
                    worker.visa_image.delete()
                except:
                    pass
            worker.visa_image = request.FILES['visa_image']

        # Save worker with updates
        worker.save()

        # Save current worker data for undo action
        current_worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': worker_type,
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='update',
            module='workers',
            record_id=worker.id,
            description=f'تم تحديث بيانات العامل: {worker.first_name} {worker.last_name}',
            previous_data=json.dumps(previous_worker_data),
            current_data=json.dumps(current_worker_data),
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Register activity
        Activity.objects.create(
            description=f'تم تحديث بيانات العامل {worker.first_name} {worker.last_name}',
            activity_type='worker_updated',
            date_created=timezone.now(),
            related_worker=worker
        )

        messages.success(request, f'تم تحديث بيانات العامل {worker.first_name} {worker.last_name} بنجاح')

        # Redirect based on worker type
        if worker_type == 'contract':
            return redirect('workers:contract_workers')
        elif worker_type == 'custom':
            return redirect('workers:custom_service_workers')
        elif worker_type == 'monthly':
            return redirect('workers:monthly_workers')
        else:
            return redirect('workers:worker_list')

    # Get recruitment companies for the form
    recruitment_companies = RecruitmentCompany.objects.filter(is_active=True).order_by('name')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'worker': worker,
        'recruitment_companies': recruitment_companies,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'worker_type': worker_type,
        'title': f'تعديل بيانات العامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/worker_form_tailwind.html', context)

@login_required
def worker_delete(request, worker_id):
    """View function for deleting a worker."""
    try:
        worker = get_object_or_404(Worker, id=worker_id)
    except Http404:
        messages.error(request, 'العامل غير موجود أو تم حذفه بالفعل')
        return redirect('workers:worker_list')

    # Determine worker type (for redirect after delete)
    worker_type = request.GET.get('type', 'contract')

    if request.method == 'POST':
        worker_name = f'{worker.first_name} {worker.last_name}'

        # Save worker data before deleting for undo action
        worker_data = {
            'first_name': worker.first_name,
            'last_name': worker.last_name,
            'gender': worker.gender,
            'nationality': worker.nationality,
            'passport_number': worker.passport_number,
            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
            'passport_issue_date': str(worker.passport_issue_date) if hasattr(worker, 'passport_issue_date') and worker.passport_issue_date else None,
            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
            'is_active': worker.is_active,
            'status': worker.status,
            'date_joined': str(worker.date_joined) if worker.date_joined else None,
            'worker_type': worker_type,
            'salary': float(worker.salary) if worker.salary else 0,
        }

        # Create SystemAction record for undo functionality
        SystemAction.objects.create(
            user=request.user,
            action_type='delete',
            module='workers',
            record_id=worker.id,
            description=f'تم حذف العامل: {worker_name}',
            previous_data=json.dumps(worker_data),
            current_data=None,
            status='completed',
            can_undo=True,
            ip_address=request.META.get('REMOTE_ADDR'),
        )

        # Delete worker files
        if worker.photo:
            try:
                worker.photo.delete()
            except:
                pass
        if worker.passport_image:
            try:
                worker.passport_image.delete()
            except:
                pass
        if worker.visa_image:
            try:
                worker.visa_image.delete()
            except:
                pass

        # Delete worker
        worker.delete()

        # Register activity
        Activity.objects.create(
            description=f'تم حذف العامل {worker_name}',
            activity_type='worker_deleted',
            date_created=timezone.now()
        )

        messages.success(request, f'تم حذف العامل {worker_name} بنجاح')

        # Redirect based on worker type
        if worker_type == 'contract':
            return redirect('workers:contract_workers')
        elif worker_type == 'custom':
            return redirect('workers:custom_service_workers')
        elif worker_type == 'monthly':
            return redirect('workers:monthly_workers')
        else:
            return redirect('workers:worker_list')

    # Get choices for dropdowns
    nationality_choices = Worker.NATIONALITY_CHOICES
    status_choices = Worker.STATUS_CHOICES

    context = {
        'worker': worker,
        'worker_type': worker_type,
        'nationality_choices': nationality_choices,
        'status_choices': status_choices,
        'title': f'حذف العامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/worker_delete_confirm.html', context)

@login_required
def import_workers(request):
    """View function for importing workers from Excel file."""
    if request.method == 'POST':
        form = WorkerImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['excel_file']

            # Read the Excel file
            try:
                # Load the Excel file into a pandas DataFrame
                df = pd.read_excel(excel_file, engine='openpyxl')

                # Check if the DataFrame is empty
                if df.empty:
                    messages.error(request, 'ملف الإكسل فارغ')
                    return redirect('workers:import_workers')

                # Check required columns
                required_columns = ['first_name', 'last_name', 'passport_number']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    messages.error(request, f'الأعمدة التالية مفقودة: {", ".join(missing_columns)}')
                    return redirect('workers:import_workers')

                # Process each row
                success_count = 0
                error_count = 0
                error_messages = []

                for index, row in df.iterrows():
                    try:
                        # Validate required fields
                        if pd.isna(row['first_name']) or pd.isna(row['last_name']) or pd.isna(row['passport_number']):
                            error_count += 1
                            error_messages.append(f'الصف {index + 2}: بعض الحقول المطلوبة فارغة')
                            continue

                        # Check if worker with same passport number already exists
                        if Worker.objects.filter(passport_number=row['passport_number']).exists():
                            error_count += 1
                            error_messages.append(f'العامل برقم جواز سفر {row["passport_number"]} موجود بالفعل')
                            continue

                        # Create worker object
                        worker = Worker(
                            first_name=row['first_name'],
                            last_name=row['last_name'],
                            passport_number=row['passport_number']
                        )

                        # Add optional fields if they exist in the Excel file
                        if 'gender' in df.columns and pd.notna(row['gender']):
                            # Validate gender
                            if row['gender'] in ['M', 'F']:
                                worker.gender = row['gender']
                            else:
                                worker.gender = 'M'  # Default to male if invalid

                        if 'nationality' in df.columns and pd.notna(row['nationality']):
                            # Check if nationality is valid
                            valid_nationalities = [choice[0] for choice in Worker._meta.get_field('nationality').choices]
                            if row['nationality'] in valid_nationalities:
                                worker.nationality = row['nationality']

                        if 'passport_expiry' in df.columns and pd.notna(row['passport_expiry']):
                            try:
                                # Convert to date if it's not already
                                if isinstance(row['passport_expiry'], str):
                                    worker.passport_expiry = datetime.strptime(row['passport_expiry'], '%Y-%m-%d').date()
                                else:
                                    worker.passport_expiry = row['passport_expiry']
                            except Exception:
                                # If date conversion fails, leave it as None
                                pass

                        if 'visa_expiry' in df.columns and pd.notna(row['visa_expiry']):
                            try:
                                # Convert to date if it's not already
                                if isinstance(row['visa_expiry'], str):
                                    worker.visa_expiry = datetime.strptime(row['visa_expiry'], '%Y-%m-%d').date()
                                else:
                                    worker.visa_expiry = row['visa_expiry']
                            except Exception:
                                # If date conversion fails, leave it as None
                                pass

                        if 'date_of_birth' in df.columns and pd.notna(row['date_of_birth']):
                            try:
                                # Convert to date if it's not already
                                if isinstance(row['date_of_birth'], str):
                                    worker.date_of_birth = datetime.strptime(row['date_of_birth'], '%Y-%m-%d').date()
                                else:
                                    worker.date_of_birth = row['date_of_birth']
                            except Exception:
                                # If date conversion fails, leave it as None
                                pass





                        if 'is_active' in df.columns and pd.notna(row['is_active']):
                            # Convert various formats to boolean
                            if isinstance(row['is_active'], bool):
                                worker.is_active = row['is_active']
                            elif isinstance(row['is_active'], str):
                                worker.is_active = row['is_active'].lower() in ['true', 'yes', 'y', '1', 'نعم']
                            elif isinstance(row['is_active'], (int, float)):
                                worker.is_active = bool(row['is_active'])

                        if 'status' in df.columns and pd.notna(row['status']):
                            # Check if status is valid
                            valid_statuses = [choice[0] for choice in Worker._meta.get_field('status').choices]
                            if row['status'] in valid_statuses:
                                worker.status = row['status']
                            else:
                                worker.status = 'active'  # Default to active if invalid

                        if 'date_joined' in df.columns and pd.notna(row['date_joined']):
                            try:
                                # Convert to date if it's not already
                                if isinstance(row['date_joined'], str):
                                    worker.date_joined = datetime.strptime(row['date_joined'], '%Y-%m-%d').date()
                                else:
                                    worker.date_joined = row['date_joined']
                            except Exception:
                                # If date conversion fails, use current date
                                worker.date_joined = timezone.now().date()
                        else:
                            # Default to current date if not provided
                            worker.date_joined = timezone.now().date()

                        # Save the worker
                        worker.save()

                        # Save worker data for undo action
                        worker_data = {
                            'first_name': worker.first_name,
                            'last_name': worker.last_name,
                            'gender': worker.gender,
                            'nationality': worker.nationality,
                            'passport_number': worker.passport_number,
                            'passport_expiry': str(worker.passport_expiry) if worker.passport_expiry else None,
                            'visa_number': worker.visa_number if hasattr(worker, 'visa_number') else None,
                            'visa_expiry': str(worker.visa_expiry) if worker.visa_expiry else None,
                            'date_of_birth': str(worker.date_of_birth) if worker.date_of_birth else None,
                            'phone_number': worker.phone_number if hasattr(worker, 'phone_number') else None,
                            'address': worker.address if hasattr(worker, 'address') else None,
                            'skills': worker.skills if hasattr(worker, 'skills') else None,
                            'salary': worker.salary if hasattr(worker, 'salary') else 0,
                            'is_active': worker.is_active,
                            'status': worker.status,
                            'date_joined': str(worker.date_joined) if worker.date_joined else None,
                        }

                        # Create SystemAction record for undo functionality
                        SystemAction.objects.create(
                            user=request.user,
                            action_type='create',
                            module='workers',
                            record_id=worker.id,
                            description=f'تم إضافة العامل: {worker.first_name} {worker.last_name} عن طريق الاستيراد',
                            previous_data=None,
                            current_data=json.dumps(worker_data),
                            status='completed',
                            can_undo=True,
                            ip_address=request.META.get('REMOTE_ADDR'),
                        )

                        # Register activity
                        Activity.objects.create(
                            description=f'تم إضافة العامل {worker.first_name} {worker.last_name} عن طريق الاستيراد',
                            activity_type='worker_imported',
                            date_created=timezone.now(),
                            related_worker=worker
                        )

                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        error_messages.append(f'خطأ في الصف {index + 2}: {str(e)}')

                # Show summary message
                if success_count > 0:
                    messages.success(request, f'تم استيراد {success_count} عامل بنجاح')

                if error_count > 0:
                    messages.warning(request, f'فشل استيراد {error_count} عامل')
                    for error in error_messages[:10]:  # Show only first 10 errors to avoid overwhelming the user
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(request, f'و {len(error_messages) - 10} أخطاء أخرى...')

                return redirect('workers:worker_list')

            except Exception as e:
                messages.error(request, f'خطأ في معالجة ملف الإكسل: {str(e)}')
                return redirect('workers:import_workers')
    else:
        form = WorkerImportForm()

    # Check if template file exists
    template_path = os.path.join('static', 'templates', 'worker_import_template.xlsx')
    template_exists = os.path.exists(template_path)

    context = {
        'form': form,
        'title': 'استيراد العمال من ملف إكسل',
        'template_exists': template_exists
    }

    return render(request, 'workers/import_workers.html', context)

# scan_passport function removed


# Helper functions for passport data extraction
def extract_passport_number(text):
    """Extract passport number from text."""
    # Common passport number patterns
    patterns = [
        r'[A-Z][0-9]{8}',  # Format: A12345678
        r'[A-Z][0-9]{7}',  # Format: A1234567
        r'[A-Z]{2}[0-9]{7}',  # Format: *********
        r'PASSPORT NO\.?\s*[A-Z0-9]+',  # Format: PASSPORT NO. *********
        r'PASSPORT\s*[A-Z0-9]+',  # Format: PASSPORT *********
        r'NO\.?\s*[A-Z0-9]+',  # Format: NO. *********
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0]
            if 'PASSPORT NO' in match.upper():
                match = re.sub(r'PASSPORT NO\.?\s*', '', match, flags=re.IGNORECASE)
            elif 'PASSPORT' in match.upper():
                match = re.sub(r'PASSPORT\s*', '', match, flags=re.IGNORECASE)
            elif 'NO' in match.upper():
                match = re.sub(r'NO\.?\s*', '', match, flags=re.IGNORECASE)
            return match.strip()

    return ''

def extract_name(text):
    """Extract name from text."""
    # Common name patterns
    patterns = [
        r'NAME\s*[\w\s]+\n',  # Format: NAME John Doe
        r'SURNAME[\s\w]+\nGIVEN NAMES[\s\w]+',  # Format: SURNAME Doe GIVEN NAMES John
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0]
            if 'NAME' in match.upper():
                match = re.sub(r'NAME\s*', '', match, flags=re.IGNORECASE)
            elif 'SURNAME' in match.upper() and 'GIVEN NAMES' in match.upper():
                surname = re.search(r'SURNAME\s*([\w\s]+)\n', match, re.IGNORECASE)
                given_names = re.search(r'GIVEN NAMES\s*([\w\s]+)', match, re.IGNORECASE)
                if surname and given_names:
                    return f"{given_names.group(1).strip()} {surname.group(1).strip()}"
            return match.strip()

    return ''

def extract_nationality(text):
    """Extract nationality from text."""
    # Common nationality patterns
    patterns = [
        r'NATIONALITY\s*[\w\s]+\n',  # Format: NATIONALITY Egyptian
        r'NATIONALITE\s*[\w\s]+\n',  # Format: NATIONALITE Egyptian
    ]

    nationality_map = {
        'EGYPTIAN': 'EG',
        'EGYPT': 'EG',
        'INDIAN': 'IN',
        'INDIA': 'IN',
        'PAKISTANI': 'PK',
        'PAKISTAN': 'PK',
        'BANGLADESHI': 'BD',
        'BANGLADESH': 'BD',
        'FILIPINO': 'PH',
        'PHILIPPINES': 'PH',
        'NEPALESE': 'NP',
        'NEPAL': 'NP',
        'SRI LANKAN': 'LK',
        'SRI LANKA': 'LK',
        'ETHIOPIAN': 'ET',
        'ETHIOPIA': 'ET',
        'KENYAN': 'KE',
        'KENYA': 'KE',
        'INDONESIAN': 'ID',
        'INDONESIA': 'ID',
    }

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0]
            if 'NATIONALITY' in match.upper():
                match = re.sub(r'NATIONALITY\s*', '', match, flags=re.IGNORECASE)
            elif 'NATIONALITE' in match.upper():
                match = re.sub(r'NATIONALITE\s*', '', match, flags=re.IGNORECASE)

            # Convert to country code
            match = match.strip().upper()
            for key, value in nationality_map.items():
                if key in match:
                    return value

            return 'EG'  # Default to Egyptian if not found

    return 'EG'  # Default to Egyptian

def extract_date_of_birth(text):
    """Extract date of birth from text."""
    # Common date of birth patterns
    patterns = [
        r'DATE OF BIRTH\s*[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}',  # Format: DATE OF BIRTH 01 JAN 1990
        r'DATE OF BIRTH\s*[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}',  # Format: DATE OF BIRTH 01/01/1990
        r'DATE OF BIRTH\s*[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}',  # Format: DATE OF BIRTH 01-01-1990
        r'DOB\s*[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}',  # Format: DOB 01 JAN 1990
        r'DOB\s*[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}',  # Format: DOB 01/01/1990
        r'DOB\s*[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}',  # Format: DOB 01-01-1990
    ]

    month_map = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04', 'MAY': '05', 'JUN': '06',
        'JUL': '07', 'AUG': '08', 'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
    }

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0]
            if 'DATE OF BIRTH' in match.upper():
                match = re.sub(r'DATE OF BIRTH\s*', '', match, flags=re.IGNORECASE)
            elif 'DOB' in match.upper():
                match = re.sub(r'DOB\s*', '', match, flags=re.IGNORECASE)

            # Parse the date
            if re.match(r'[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}', match, re.IGNORECASE):
                # Format: 01 JAN 1990
                day, month, year = re.findall(r'([0-9]{1,2})\s*([A-Z]{3})\s*([0-9]{4})', match, re.IGNORECASE)[0]
                month = month_map.get(month.upper(), '01')
                return f"{year}-{month}-{day.zfill(2)}"
            elif re.match(r'[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}', match):
                # Format: 01/01/1990
                day, month, year = re.findall(r'([0-9]{1,2})/([0-9]{1,2})/([0-9]{4})', match)[0]
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            elif re.match(r'[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}', match):
                # Format: 01-01-1990
                day, month, year = re.findall(r'([0-9]{1,2})-([0-9]{1,2})-([0-9]{4})', match)[0]
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

    return ''  # Default to empty string

def extract_expiry_date(text):
    """Extract passport expiry date from text."""
    # Common expiry date patterns
    patterns = [
        r'EXPIRY DATE\s*[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}',  # Format: EXPIRY DATE 01 JAN 2025
        r'EXPIRY DATE\s*[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}',  # Format: EXPIRY DATE 01/01/2025
        r'EXPIRY DATE\s*[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}',  # Format: EXPIRY DATE 01-01-2025
        r'DATE OF EXPIRY\s*[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}',  # Format: DATE OF EXPIRY 01 JAN 2025
        r'DATE OF EXPIRY\s*[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}',  # Format: DATE OF EXPIRY 01/01/2025
        r'DATE OF EXPIRY\s*[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}',  # Format: DATE OF EXPIRY 01-01-2025
    ]

    month_map = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04', 'MAY': '05', 'JUN': '06',
        'JUL': '07', 'AUG': '08', 'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
    }

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0]
            if 'EXPIRY DATE' in match.upper():
                match = re.sub(r'EXPIRY DATE\s*', '', match, flags=re.IGNORECASE)
            elif 'DATE OF EXPIRY' in match.upper():
                match = re.sub(r'DATE OF EXPIRY\s*', '', match, flags=re.IGNORECASE)

            # Parse the date
            if re.match(r'[0-9]{1,2}\s*[A-Z]{3}\s*[0-9]{4}', match, re.IGNORECASE):
                # Format: 01 JAN 2025
                day, month, year = re.findall(r'([0-9]{1,2})\s*([A-Z]{3})\s*([0-9]{4})', match, re.IGNORECASE)[0]
                month = month_map.get(month.upper(), '01')
                return f"{year}-{month}-{day.zfill(2)}"
            elif re.match(r'[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}', match):
                # Format: 01/01/2025
                day, month, year = re.findall(r'([0-9]{1,2})/([0-9]{1,2})/([0-9]{4})', match)[0]
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            elif re.match(r'[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}', match):
                # Format: 01-01-2025
                day, month, year = re.findall(r'([0-9]{1,2})-([0-9]{1,2})-([0-9]{4})', match)[0]
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

    return ''  # Default to empty string

def extract_gender(text):
    """Extract gender from text."""
    # Common gender patterns
    patterns = [
        r'SEX\s*[MF]\b',  # Format: SEX M
        r'GENDER\s*[MF]\b',  # Format: GENDER M
        r'SEX\s*MALE',  # Format: SEX MALE
        r'SEX\s*FEMALE',  # Format: SEX FEMALE
        r'GENDER\s*MALE',  # Format: GENDER MALE
        r'GENDER\s*FEMALE',  # Format: GENDER FEMALE
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Clean up the match
            match = matches[0].upper()
            if 'MALE' in match or 'M' in match.split()[-1]:
                return 'M'
            elif 'FEMALE' in match or 'F' in match.split()[-1]:
                return 'F'

    return 'M'  # Default to male


@login_required
def helper_tools(request):
    """View function for helper tools page."""
    context = {
        'title': 'أدوات المساعدة'
    }
    return render(request, 'workers/helper_tools.html', context)


@login_required
def worker_documents(request, worker_id):
    """View function for managing worker documents."""
    worker = get_object_or_404(Worker, id=worker_id)
    documents = WorkerDocument.objects.filter(worker=worker).order_by('-upload_date')

    context = {
        'worker': worker,
        'documents': documents,
        'title': f'مستندات العامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/worker_documents.html', context)


@login_required
def add_worker_document(request, worker_id):
    """View function for adding a document to a worker."""
    worker = get_object_or_404(Worker, id=worker_id)

    if request.method == 'POST':
        form = WorkerDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.worker = worker
            document.save()

            # Log activity
            Activity.objects.create(
                user=request.user,
                action='create',
                object_id=document.id,
                object_type='WorkerDocument',
                description=f'تمت إضافة مستند {document.get_document_type_display()} للعامل {worker.first_name} {worker.last_name}'
            )

            # Create system action for undo
            SystemAction.objects.create(
                user=request.user,
                action_type='create',
                module='workers',
                record_id=document.id,
                description=f'تمت إضافة مستند {document.get_document_type_display()} للعامل {worker.first_name} {worker.last_name}',
                can_undo=True
            )

            messages.success(request, 'تمت إضافة المستند بنجاح')
            return redirect('workers:worker_documents', worker_id=worker.id)
    else:
        form = WorkerDocumentForm()

    context = {
        'worker': worker,
        'form': form,
        'title': f'إضافة مستند للعامل: {worker.first_name} {worker.last_name}'
    }

    return render(request, 'workers/add_worker_document.html', context)


@login_required
def delete_worker_document(request, document_id):
    """View function for deleting a worker document."""
    document = get_object_or_404(WorkerDocument, id=document_id)
    worker = document.worker

    if request.method != 'POST':
        # إذا كانت الطريقة ليست POST، قم بإعادة توجيه المستخدم إلى الصفحة السابقة
        referer = request.META.get('HTTP_REFERER')
        if referer:
            return redirect(referer)
        else:
            return redirect('workers:worker_detail', worker_id=worker.id)

    document_type = document.get_document_type_display()
    document.delete()

    # Log activity
    Activity.objects.create(
        user=request.user,
        action='delete',
        object_id=document_id,
        object_type='WorkerDocument',
        description=f'تم حذف مستند {document_type} للعامل {worker.first_name} {worker.last_name}'
    )

    # Create system action for undo
    SystemAction.objects.create(
        user=request.user,
        action_type='delete',
        module='workers',
        record_id=document_id,
        description=f'تم حذف مستند {document_type} للعامل {worker.first_name} {worker.last_name}',
        can_undo=True
    )

    messages.success(request, 'تم حذف المستند بنجاح')

    # العودة إلى الصفحة السابقة إذا كانت متوفرة
    referer = request.META.get('HTTP_REFERER')
    if referer:
        return redirect(referer)
    else:
        return redirect('workers:worker_detail', worker_id=worker.id)


@login_required
def worker_procedures(request, worker_id):
    """View function for the worker procedures page."""
    worker = get_object_or_404(Worker, id=worker_id)
    return render(request, 'workers/worker_procedures.html', {
        'worker': worker,
        'page_title': f'إجراءات العامل | {worker.first_name} {worker.last_name}'
    })
