from rest_framework import serializers
from procedures.models import BloodTest, SponsorshipTransfer, Operation, WorkPermit, ResidenceID, BranchTransfer
from django.utils.translation import gettext_lazy as _


class BloodTestSerializer(serializers.ModelSerializer):
    """مسلسل بيانات فحص الدم"""
    
    worker_name = serializers.SerializerMethodField()
    result_display = serializers.SerializerMethodField()
    
    class Meta:
        model = BloodTest
        fields = [
            'id', 'worker', 'worker_name', 'test_date', 'hospital', 'doctor',
            'result', 'result_display', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name', 'result_display']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None
    
    def get_result_display(self, obj):
        return obj.get_result_display()


class SponsorshipTransferSerializer(serializers.ModelSerializer):
    """مسلسل بيانات نقل الكفالة"""
    
    worker_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = SponsorshipTransfer
        fields = [
            'id', 'worker', 'worker_name', 'application_date', 'approval_date',
            'previous_sponsor', 'new_sponsor', 'status', 'status_display',
            'reference_number', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name', 'status_display']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None
    
    def get_status_display(self, obj):
        return obj.get_status_display()


class OperationSerializer(serializers.ModelSerializer):
    """مسلسل بيانات العملية الجراحية"""
    
    worker_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Operation
        fields = [
            'id', 'worker', 'worker_name', 'operation_date', 'hospital',
            'doctor', 'operation_type', 'cost', 'currency', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None


class WorkPermitSerializer(serializers.ModelSerializer):
    """مسلسل بيانات تصريح العمل"""
    
    worker_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkPermit
        fields = [
            'id', 'worker', 'worker_name', 'issue_date', 'expiry_date',
            'permit_number', 'issuing_authority', 'job_title', 'status',
            'status_display', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name', 'status_display']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None
    
    def get_status_display(self, obj):
        return obj.get_status_display()


class ResidenceIDSerializer(serializers.ModelSerializer):
    """مسلسل بيانات بطاقة الإقامة"""
    
    worker_name = serializers.SerializerMethodField()
    
    class Meta:
        model = ResidenceID
        fields = [
            'id', 'worker', 'worker_name', 'issue_date', 'expiry_date',
            'id_number', 'issuing_authority', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None


class BranchTransferSerializer(serializers.ModelSerializer):
    """مسلسل بيانات نقل الفرع"""
    
    worker_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = BranchTransfer
        fields = [
            'id', 'worker', 'worker_name', 'transfer_date', 'from_branch',
            'to_branch', 'reason', 'status', 'status_display', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'worker_name', 'status_display']
    
    def get_worker_name(self, obj):
        if obj.worker:
            return f"{obj.worker.first_name} {obj.worker.last_name}"
        return None
    
    def get_status_display(self, obj):
        return obj.get_status_display()
