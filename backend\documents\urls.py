from django.urls import path
from . import views

app_name = 'documents'

urlpatterns = [
    # path('', views.documents_dashboard, name='documents_dashboard'),  # معطل مؤقتاً
    path('list/', views.document_list, name='document_list'),
    path('incoming-outgoing/', views.incoming_outgoing, name='incoming_outgoing'),
    path('upload/', views.document_upload, name='document_upload'),  # مسار جديد لرفع المستندات
    path('<int:document_id>/', views.document_detail, name='document_detail'),
    path('<int:document_id>/update/', views.document_update, name='document_update'),
    path('<int:document_id>/delete/', views.document_delete, name='document_delete'),
    path('<int:document_id>/attachments/add/', views.add_document_attachment, name='add_document_attachment'),
    path('<int:document_id>/attachments/<int:attachment_id>/delete/', views.delete_document_attachment, name='delete_document_attachment'),
]
