from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import FinancialAccount, Transaction, Budget, Invoice


@login_required
def finance_dashboard(request):
    """لوحة تحكم المالية"""
    # إحصائيات عامة
    total_accounts = FinancialAccount.objects.filter(is_active=True).count()
    total_balance = FinancialAccount.objects.filter(is_active=True).aggregate(
        total=Sum('balance'))['total'] or 0
    
    # المعاملات الحديثة
    recent_transactions = Transaction.objects.all().order_by('-created_at')[:10]
    
    # إحصائيات الشهر الحالي
    current_month = timezone.now().replace(day=1)
    monthly_income = Transaction.objects.filter(
        transaction_type='income',
        transaction_date__gte=current_month
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    monthly_expenses = Transaction.objects.filter(
        transaction_type='expense',
        transaction_date__gte=current_month
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    # الفواتير المعلقة
    pending_invoices = Invoice.objects.filter(
        status__in=['sent', 'overdue']
    ).count()
    
    context = {
        'total_accounts': total_accounts,
        'total_balance': total_balance,
        'monthly_income': monthly_income,
        'monthly_expenses': monthly_expenses,
        'net_income': monthly_income - monthly_expenses,
        'pending_invoices': pending_invoices,
        'recent_transactions': recent_transactions,
    }
    
    return render(request, 'finance/dashboard.html', context)


@login_required
def account_list(request):
    """قائمة الحسابات المالية"""
    accounts = FinancialAccount.objects.all().order_by('-created_at')
    return render(request, 'finance/account_list.html', {'accounts': accounts})


@login_required
def transaction_list(request):
    """قائمة المعاملات المالية"""
    transactions = Transaction.objects.all().order_by('-transaction_date', '-created_at')
    
    # فلترة حسب النوع
    transaction_type = request.GET.get('type')
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)
    
    # فلترة حسب الفئة
    category = request.GET.get('category')
    if category:
        transactions = transactions.filter(category=category)
    
    # فلترة حسب التاريخ
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        transactions = transactions.filter(transaction_date__gte=date_from)
    if date_to:
        transactions = transactions.filter(transaction_date__lte=date_to)
    
    context = {
        'transactions': transactions,
        'transaction_types': Transaction.TRANSACTION_TYPE_CHOICES,
        'categories': Transaction.CATEGORY_CHOICES,
    }
    
    return render(request, 'finance/transaction_list.html', context)


@login_required
def budget_list(request):
    """قائمة الميزانيات"""
    budgets = Budget.objects.filter(is_active=True).order_by('-created_at')
    return render(request, 'finance/budget_list.html', {'budgets': budgets})


@login_required
def invoice_list(request):
    """قائمة الفواتير"""
    invoices = Invoice.objects.all().order_by('-issue_date')
    
    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status:
        invoices = invoices.filter(status=status)
    
    context = {
        'invoices': invoices,
        'statuses': Invoice.STATUS_CHOICES,
    }
    
    return render(request, 'finance/invoice_list.html', context)


@login_required
def financial_reports(request):
    """التقارير المالية"""
    # تقرير الدخل والمصروفات
    current_year = timezone.now().year
    
    # البيانات الشهرية للسنة الحالية
    monthly_data = []
    for month in range(1, 13):
        month_start = datetime(current_year, month, 1)
        if month == 12:
            month_end = datetime(current_year + 1, 1, 1) - timedelta(days=1)
        else:
            month_end = datetime(current_year, month + 1, 1) - timedelta(days=1)
        
        income = Transaction.objects.filter(
            transaction_type='income',
            transaction_date__range=[month_start, month_end]
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        expenses = Transaction.objects.filter(
            transaction_type='expense',
            transaction_date__range=[month_start, month_end]
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        monthly_data.append({
            'month': month,
            'month_name': month_start.strftime('%B'),
            'income': income,
            'expenses': expenses,
            'net': income - expenses
        })
    
    context = {
        'monthly_data': monthly_data,
        'current_year': current_year,
    }
    
    return render(request, 'finance/reports.html', context)
