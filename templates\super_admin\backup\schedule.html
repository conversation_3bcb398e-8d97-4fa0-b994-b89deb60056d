{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}جدولة النسخ الاحتياطي | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    .schedule-card {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .schedule-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-input {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-input:focus {
        border-color: #3b82f6;
        outline: 0;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }

    .form-select {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
        background-position: left 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-left: 2.5rem;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .form-select:focus {
        border-color: #3b82f6;
        outline: 0;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }

    .form-checkbox {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.25rem;
        border: 1px solid #d1d5db;
        transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-checkbox:checked {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }

    .form-checkbox:focus {
        outline: 0;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }

    .schedule-info {
        background-color: #f3f4f6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .schedule-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .schedule-info-item:last-child {
        margin-bottom: 0;
    }

    .schedule-info-icon {
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-left: 0.75rem;
    }

    .schedule-info-content {
        flex: 1;
    }

    .schedule-info-label {
        font-size: 0.75rem;
        color: #6b7280;
        margin-bottom: 0.125rem;
    }

    .schedule-info-value {
        font-weight: 600;
        color: #1f2937;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-blue-800 flex items-center">
            <i class="fas fa-calendar-alt text-blue-600 ml-3 text-2xl"></i>
            جدولة النسخ الاحتياطي
        </h2>
        <a href="{% url 'super_admin:system_backup' %}" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-colors shadow-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة للنسخ الاحتياطي
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md" role="alert">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
                {% endif %}
                <span class="block sm:inline">{{ message }}</span>
            </div>
        {% endfor %}
    {% endif %}

    <div class="schedule-card bg-white p-6 rounded-xl border border-gray-200 shadow-md">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-indigo-400 to-indigo-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-calendar-alt text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">جدولة النسخ الاحتياطي التلقائي</h3>
                <p class="text-gray-500 text-sm mt-1">إعداد جدولة تلقائية للنسخ الاحتياطي</p>
            </div>
        </div>

        {% if schedule %}
        <div class="bg-gradient-to-r from-indigo-50 to-indigo-100 p-5 rounded-lg mb-6 shadow-sm border border-indigo-200">
            <h4 class="font-bold text-indigo-800 mb-4 flex items-center">
                <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                    <i class="fas fa-info-circle"></i>
                </div>
                معلومات الجدولة الحالية
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">التكرار</div>
                        <div class="schedule-info-value">
                            {% if schedule.frequency == 'daily' %}
                                يومي
                            {% elif schedule.frequency == 'weekly' %}
                                أسبوعي
                            {% elif schedule.frequency == 'monthly' %}
                                شهري
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">وقت النسخ الاحتياطي</div>
                        <div class="schedule-info-value">{{ schedule.time|time:"H:i" }}</div>
                    </div>
                </div>
                {% if schedule.frequency == 'weekly' %}
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">يوم الأسبوع</div>
                        <div class="schedule-info-value">
                            {% if schedule.day_of_week == 0 %}الاثنين
                            {% elif schedule.day_of_week == 1 %}الثلاثاء
                            {% elif schedule.day_of_week == 2 %}الأربعاء
                            {% elif schedule.day_of_week == 3 %}الخميس
                            {% elif schedule.day_of_week == 4 %}الجمعة
                            {% elif schedule.day_of_week == 5 %}السبت
                            {% elif schedule.day_of_week == 6 %}الأحد
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% if schedule.frequency == 'monthly' %}
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">يوم الشهر</div>
                        <div class="schedule-info-value">{{ schedule.day_of_month }}</div>
                    </div>
                </div>
                {% endif %}
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">فترة الاحتفاظ (بالأيام)</div>
                        <div class="schedule-info-value">{{ schedule.retention_period }}</div>
                    </div>
                </div>
                <div class="schedule-info-item">
                    <div class="schedule-info-icon bg-indigo-100 text-indigo-600">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">النسخة الاحتياطية التالية</div>
                        <div class="schedule-info-value">{{ schedule.next_backup|date:"j F Y - H:i" }}</div>
                    </div>
                </div>
                <div class="schedule-info-item">
                    <div class="schedule-info-icon {% if schedule.is_active %}bg-green-100 text-green-600{% else %}bg-red-100 text-red-600{% endif %}">
                        <i class="fas {% if schedule.is_active %}fa-toggle-on{% else %}fa-toggle-off{% endif %}"></i>
                    </div>
                    <div class="schedule-info-content">
                        <div class="schedule-info-label">الحالة</div>
                        <div class="schedule-info-value">
                            {% if schedule.is_active %}
                                <span class="text-green-600">مفعل</span>
                            {% else %}
                                <span class="text-red-600">معطل</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <form method="post" action="{% url 'super_admin:manage_backup_schedule' company_id=company.id %}">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="backup_frequency" class="form-label flex items-center">
                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        التكرار
                    </label>
                    <select name="backup_frequency" id="backup_frequency" class="form-select">
                        <option value="daily" {% if schedule and schedule.frequency == 'daily' %}selected{% endif %}>يومي</option>
                        <option value="weekly" {% if schedule and schedule.frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                        <option value="monthly" {% if schedule and schedule.frequency == 'monthly' %}selected{% endif %}>شهري</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">حدد عدد مرات إنشاء النسخ الاحتياطية</p>
                </div>

                <div class="form-group">
                    <label for="backup_time" class="form-label flex items-center">
                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                            <i class="fas fa-clock"></i>
                        </div>
                        وقت النسخ الاحتياطي
                    </label>
                    <input type="time" name="backup_time" id="backup_time" class="form-input" value="{% if schedule %}{{ schedule.time|time:'H:i' }}{% else %}00:00{% endif %}">
                    <p class="text-xs text-gray-500 mt-1">الوقت الذي سيتم فيه إنشاء النسخة الاحتياطية</p>
                </div>
            </div>

            <div id="weekly_options" class="mt-6 bg-indigo-50 p-5 rounded-lg border border-indigo-100 shadow-sm {% if not schedule or schedule.frequency != 'weekly' %}hidden{% endif %}">
                <div class="flex items-center mb-4">
                    <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <h5 class="font-medium text-indigo-800">إعدادات الجدولة الأسبوعية</h5>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-indigo-100">
                    <label for="day_of_week" class="form-label flex items-center">
                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        يوم الأسبوع
                    </label>
                    <select name="day_of_week" id="day_of_week" class="form-select">
                        <option value="0" {% if schedule and schedule.day_of_week == 0 %}selected{% endif %}>الاثنين</option>
                        <option value="1" {% if schedule and schedule.day_of_week == 1 %}selected{% endif %}>الثلاثاء</option>
                        <option value="2" {% if schedule and schedule.day_of_week == 2 %}selected{% endif %}>الأربعاء</option>
                        <option value="3" {% if schedule and schedule.day_of_week == 3 %}selected{% endif %}>الخميس</option>
                        <option value="4" {% if schedule and schedule.day_of_week == 4 %}selected{% endif %}>الجمعة</option>
                        <option value="5" {% if schedule and schedule.day_of_week == 5 %}selected{% endif %}>السبت</option>
                        <option value="6" {% if schedule and schedule.day_of_week == 6 %}selected{% endif %}>الأحد</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">اليوم الذي سيتم فيه إنشاء النسخة الاحتياطية كل أسبوع</p>
                </div>
            </div>

            <div id="monthly_options" class="mt-6 bg-indigo-50 p-5 rounded-lg border border-indigo-100 shadow-sm {% if not schedule or schedule.frequency != 'monthly' %}hidden{% endif %}">
                <div class="flex items-center mb-4">
                    <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5 class="font-medium text-indigo-800">إعدادات الجدولة الشهرية</h5>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-indigo-100">
                    <label for="day_of_month" class="form-label flex items-center">
                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        يوم الشهر
                    </label>
                    <select name="day_of_month" id="day_of_month" class="form-select">
                        {% for i in "123456789"|add:"0123456789"|add:"0123456789"|add:"012" %}
                            <option value="{{ forloop.counter }}" {% if schedule and schedule.day_of_month == forloop.counter %}selected{% endif %}>{{ forloop.counter }}</option>
                        {% endfor %}
                    </select>
                    <p class="text-xs text-gray-500 mt-1">اليوم الذي سيتم فيه إنشاء النسخة الاحتياطية كل شهر</p>
                </div>
            </div>

            <div class="form-group mt-6">
                <label for="backup_retention" class="form-label flex items-center">
                    <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-2">
                        <i class="fas fa-history"></i>
                    </div>
                    فترة الاحتفاظ (بالأيام)
                </label>
                <input type="number" name="backup_retention" id="backup_retention" min="1" max="365" class="form-input" value="{% if schedule %}{{ schedule.retention_period }}{% else %}30{% endif %}">
                <p class="text-xs text-gray-500 mt-1">عدد الأيام التي سيتم الاحتفاظ بالنسخ الاحتياطية خلالها</p>
            </div>

            <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-100 flex items-center">
                <div class="flex items-center">
                    <input type="checkbox" name="backup_active" id="backup_active" class="form-checkbox" {% if schedule and schedule.is_active %}checked{% endif %}>
                </div>
                <div class="mr-3">
                    <label for="backup_active" class="block text-sm font-medium text-gray-700">
                        تفعيل النسخ الاحتياطي التلقائي
                    </label>
                    <p class="text-xs text-gray-500 mt-1">تفعيل أو تعطيل جدولة النسخ الاحتياطي التلقائي</p>
                </div>
                <div class="mr-auto">
                    <div class="bg-green-100 text-green-600 p-2 rounded-full">
                        <i class="fas fa-toggle-on"></i>
                    </div>
                </div>
            </div>

            <div class="pt-6 border-t border-gray-200 flex justify-end mt-6">
                <button type="submit" class="flex justify-center py-3 px-8 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                    <i class="fas fa-calendar-check ml-2"></i> حفظ جدولة النسخ الاحتياطي
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const frequencySelect = document.getElementById('backup_frequency');
        const weeklyOptions = document.getElementById('weekly_options');
        const monthlyOptions = document.getElementById('monthly_options');

        // عرض/إخفاء الخيارات حسب التكرار المحدد
        function updateOptions() {
            const frequency = frequencySelect.value;
            
            if (frequency === 'weekly') {
                weeklyOptions.classList.remove('hidden');
                monthlyOptions.classList.add('hidden');
            } else if (frequency === 'monthly') {
                weeklyOptions.classList.add('hidden');
                monthlyOptions.classList.remove('hidden');
            } else {
                weeklyOptions.classList.add('hidden');
                monthlyOptions.classList.add('hidden');
            }
        }

        // تحديث الخيارات عند تغيير التكرار
        frequencySelect.addEventListener('change', updateOptions);

        // تحديث الخيارات عند تحميل الصفحة
        updateOptions();
    });
</script>
{% endblock %}
