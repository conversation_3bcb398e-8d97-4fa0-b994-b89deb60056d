import React from 'react';
import PropTypes from 'prop-types';

/**
 * مكون شارة متقدم يدعم أنماط مختلفة وألوان متعددة
 */
const Badge = ({
  children,
  color = 'primary',
  variant = 'default',
  size = 'md',
  rounded = true,
  className = '',
  ...rest
}) => {
  // تحديد الأنماط الأساسية للشارة
  const baseClasses = 'inline-flex items-center justify-center font-medium';
  
  // تحديد أنماط الحجم
  const sizeClasses = {
    sm: 'px-1.5 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm',
  };
  
  // تحديد أنماط الاستدارة
  const roundedClasses = rounded ? 'rounded-full' : 'rounded-md';
  
  // تحديد أنماط اللون والمظهر للنمط الافتراضي
  const defaultVariantClasses = {
    primary: 'bg-primary-light text-primary-dark',
    secondary: 'bg-secondary-light text-secondary-dark',
    success: 'bg-success-light text-success-dark',
    danger: 'bg-danger-light text-white',
    warning: 'bg-warning-light text-warning-dark',
    info: 'bg-info-light text-info-dark',
    neutral: 'bg-neutral-light text-neutral-dark',
    'blue-345785': 'bg-blue-345785/20 text-blue-345785',
    'blue-4b93da': 'bg-blue-4b93da/20 text-blue-4b93da',
    'blue-90c1dc': 'bg-blue-90c1dc/20 text-blue-90c1dc',
    'gray-294252': 'bg-gray-294252/20 text-gray-294252',
    'gray-3773b5': 'bg-gray-3773b5/20 text-gray-3773b5',
    'accent-42759b': 'bg-accent-42759b/20 text-accent-42759b',
    'accent-d34169': 'bg-accent-d34169/20 text-accent-d34169',
    'accent-c38d61': 'bg-accent-c38d61/20 text-accent-c38d61',
    'accent-8faee1': 'bg-accent-8faee1/20 text-accent-8faee1',
  };
  
  // تحديد أنماط اللون والمظهر للنمط الصلب
  const solidVariantClasses = {
    primary: 'bg-primary text-white',
    secondary: 'bg-secondary text-white',
    success: 'bg-success text-white',
    danger: 'bg-danger text-white',
    warning: 'bg-warning text-white',
    info: 'bg-info text-white',
    neutral: 'bg-neutral text-white',
    'blue-345785': 'bg-blue-345785 text-white',
    'blue-4b93da': 'bg-blue-4b93da text-white',
    'blue-90c1dc': 'bg-blue-90c1dc text-white',
    'gray-294252': 'bg-gray-294252 text-white',
    'gray-3773b5': 'bg-gray-3773b5 text-white',
    'accent-42759b': 'bg-accent-42759b text-white',
    'accent-d34169': 'bg-accent-d34169 text-white',
    'accent-c38d61': 'bg-accent-c38d61 text-white',
    'accent-8faee1': 'bg-accent-8faee1 text-white',
  };
  
  // تحديد أنماط اللون والمظهر للنمط المخطط
  const outlineVariantClasses = {
    primary: 'bg-transparent border border-primary text-primary',
    secondary: 'bg-transparent border border-secondary text-secondary',
    success: 'bg-transparent border border-success text-success',
    danger: 'bg-transparent border border-danger text-danger',
    warning: 'bg-transparent border border-warning text-warning',
    info: 'bg-transparent border border-info text-info',
    neutral: 'bg-transparent border border-neutral text-neutral-dark',
    'blue-345785': 'bg-transparent border border-blue-345785 text-blue-345785',
    'blue-4b93da': 'bg-transparent border border-blue-4b93da text-blue-4b93da',
    'blue-90c1dc': 'bg-transparent border border-blue-90c1dc text-blue-90c1dc',
    'gray-294252': 'bg-transparent border border-gray-294252 text-gray-294252',
    'gray-3773b5': 'bg-transparent border border-gray-3773b5 text-gray-3773b5',
    'accent-42759b': 'bg-transparent border border-accent-42759b text-accent-42759b',
    'accent-d34169': 'bg-transparent border border-accent-d34169 text-accent-d34169',
    'accent-c38d61': 'bg-transparent border border-accent-c38d61 text-accent-c38d61',
    'accent-8faee1': 'bg-transparent border border-accent-8faee1 text-accent-8faee1',
  };
  
  // اختيار نمط اللون المناسب بناءً على النمط المحدد
  let colorClasses;
  switch (variant) {
    case 'solid':
      colorClasses = solidVariantClasses[color] || solidVariantClasses.primary;
      break;
    case 'outline':
      colorClasses = outlineVariantClasses[color] || outlineVariantClasses.primary;
      break;
    default:
      colorClasses = defaultVariantClasses[color] || defaultVariantClasses.primary;
      break;
  }
  
  // تجميع الأنماط
  const classes = `${baseClasses} ${sizeClasses[size]} ${roundedClasses} ${colorClasses} ${className}`;
  
  return (
    <span className={classes} {...rest}>
      {children}
    </span>
  );
};

Badge.propTypes = {
  children: PropTypes.node.isRequired,
  color: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'solid', 'outline']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  rounded: PropTypes.bool,
  className: PropTypes.string,
};

export default Badge;
