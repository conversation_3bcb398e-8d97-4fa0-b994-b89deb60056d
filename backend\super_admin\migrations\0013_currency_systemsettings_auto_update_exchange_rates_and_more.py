# Generated by Django 5.2 on 2025-05-28 19:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0012_subscriptionplan_companysubscription_maintenancemode_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(choices=[('IQD', 'الدينار العراقي'), ('USD', 'الدولار الأمريكي')], max_length=3, unique=True, verbose_name='رمز العملة')),
                ('name', models.CharField(max_length=50, verbose_name='اسم العملة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز العملة المختصر')),
                ('symbol_position', models.CharField(choices=[('before', 'قبل الرقم'), ('after', 'بعد الرقم')], default='after', max_length=10, verbose_name='موضع رمز العملة')),
                ('decimal_places', models.IntegerField(default=0, verbose_name='عدد الخانات العشرية')),
                ('thousands_separator', models.CharField(default=',', max_length=1, verbose_name='فاصل الآلاف')),
                ('decimal_separator', models.CharField(default='.', max_length=1, verbose_name='فاصل العشرية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('exchange_rate_to_usd', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف مقابل الدولار')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عملة',
                'verbose_name_plural': 'العملات',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='auto_update_exchange_rates',
            field=models.BooleanField(default=False, verbose_name='تحديث أسعار الصرف تلقائياً'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='default_currency',
            field=models.CharField(choices=[('IQD', 'الدينار العراقي'), ('USD', 'الدولار الأمريكي')], default='IQD', max_length=3, verbose_name='العملة الافتراضية'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='show_currency_converter',
            field=models.BooleanField(default=True, verbose_name='إظهار محول العملات'),
        ),
    ]
