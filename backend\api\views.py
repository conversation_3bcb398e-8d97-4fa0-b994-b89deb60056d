from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
import logging
from backend.super_admin.models import Company
from backend.super_admin.database_connector import test_company_db_connection, setup_company_db_connection

# إعداد التسجيل
logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_root(request, format=None):
    """
    الصفحة الرئيسية لواجهة برمجة التطبيقات (API) لنظام إدارة العمالة.
    توفر روابط لجميع نقاط النهاية الرئيسية.
    """
    return Response({
        'auth': {
            'login': reverse('token_obtain_pair', request=request, format=format),
            'refresh': reverse('token_refresh', request=request, format=format),
            'user': reverse('user-detail', request=request, format=format),
        },
        'workers': reverse('worker-list', request=request, format=format),
        'contracts': reverse('contract-list', request=request, format=format),
        'services': reverse('service-list', request=request, format=format),
        'documents': reverse('document-list', request=request, format=format),
        'procedures': reverse('procedure-list', request=request, format=format),
        'reports': reverse('report-list', request=request, format=format),
        'settings': reverse('setting-list', request=request, format=format),
        'docs': {
            'swagger': reverse('schema-swagger-ui', request=request, format=format),
            'redoc': reverse('schema-redoc', request=request, format=format),
        }
    })

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def verify_database(request):
    """
    التحقق من صحة قاعدة بيانات الشركة باستخدام الرقم التسلسلي
    """
    try:
        data = json.loads(request.body)
        serial_number = data.get('serial_number')

        logger.info(f"طلب التحقق من قاعدة البيانات للرقم التسلسلي: {serial_number}")

        if not serial_number:
            logger.warning("لم يتم تقديم الرقم التسلسلي")
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي مطلوب'
            }, status=400)

        # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        clean_serial = serial_number.replace('-', '').replace(' ', '')

        # التحقق من طول الرقم التسلسلي
        if len(clean_serial) != 16:
            logger.warning(f"الرقم التسلسلي غير صالح (الطول = {len(clean_serial)}): {serial_number}")
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
            }, status=400)

        # تنظيف الرقم التسلسلي وإعادة تنسيقه
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])
        logger.debug(f"الرقم التسلسلي المنسق: {formatted_serial}")

        # البحث عن الشركة بالرقم التسلسلي المنسق أو غير المنسق
        company = Company.objects.filter(serial_number=clean_serial).first()
        if not company:
            company = Company.objects.filter(serial_number=formatted_serial).first()

        if not company:
            logger.warning(f"لم يتم العثور على شركة بالرقم التسلسلي: {serial_number}")
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة'
            }, status=404)

        logger.info(f"تم العثور على الشركة: {company.name} (ID: {company.id})")

        # التحقق من حالة الشركة
        if company.status != 'active':
            logger.warning(f"الشركة غير نشطة: {company.name} (الحالة: {company.status})")
            return JsonResponse({
                'success': False,
                'message': 'الشركة غير نشطة'
            }, status=403)

        # اختبار الاتصال بقاعدة البيانات
        logger.info(f"اختبار الاتصال بقاعدة بيانات الشركة: {company.name}")
        connection_test = test_company_db_connection(company)

        if not connection_test['success']:
            logger.warning(f"فشل الاتصال بقاعدة البيانات: {connection_test['message']}")

            # محاولة إعادة تهيئة قاعدة البيانات
            try:
                logger.info(f"محاولة إعادة تهيئة قاعدة البيانات: {company.database_name}")
                from backend.super_admin.database_initializer import initialize_company_database
                init_result = initialize_company_database(company)
                logger.info(f"نتيجة إعادة تهيئة قاعدة البيانات: {init_result}")

                # إعادة اختبار الاتصال
                connection_test = test_company_db_connection(company)

                if not connection_test['success']:
                    logger.error(f"فشل الاتصال بقاعدة البيانات بعد إعادة التهيئة: {connection_test['message']}")
                    return JsonResponse({
                        'success': False,
                        'message': f'تعذر الاتصال بقاعدة بيانات الشركة. يرجى التواصل مع الدعم الفني.'
                    }, status=500)
            except Exception as e:
                logger.exception(f"استثناء أثناء تهيئة قاعدة البيانات: {str(e)}")
                return JsonResponse({
                    'success': False,
                    'message': f'حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى التواصل مع الدعم الفني.'
                }, status=500)

        # إعداد اتصال قاعدة البيانات في Django
        logger.info(f"إعداد اتصال قاعدة البيانات في Django: {company.database_name}")
        setup_success = setup_company_db_connection(company)

        if not setup_success:
            logger.error(f"فشل في إعداد اتصال قاعدة البيانات في Django: {company.database_name}")
            return JsonResponse({
                'success': False,
                'message': 'فشل في إعداد اتصال قاعدة البيانات'
            }, status=500)

        logger.info(f"تم التحقق من قاعدة البيانات بنجاح: {company.name}")
        return JsonResponse({
            'success': True,
            'message': 'تم التحقق من قاعدة البيانات بنجاح',
            'company': {
                'id': company.id,
                'name': company.name,
                'serial_number': company.serial_number,
                'database_name': company.database_name,
                'status': company.status
            },
            'database': {
                'type': connection_test.get('db_type', 'unknown'),
                'tables_count': connection_test.get('tables_count', 0)
            }
        })

    except json.JSONDecodeError:
        logger.warning("بيانات JSON غير صالحة")
        return JsonResponse({
            'success': False,
            'message': 'بيانات JSON غير صالحة'
        }, status=400)

    except Exception as e:
        logger.exception(f"استثناء غير متوقع: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء التحقق من قاعدة البيانات: {str(e)}'
        }, status=500)
