from django import forms
from .models import Document, DocumentAttachment

class DocumentForm(forms.ModelForm):
    """Form for creating and updating documents."""

    class Meta:
        model = Document
        fields = ['document_number', 'document_type', 'entity', 'subject', 'details', 'document_date', 'status']
        widgets = {
            'document_date': forms.DateInput(attrs={'type': 'date'}),
            'details': forms.Textarea(attrs={'rows': 4}),
        }


class DocumentAttachmentForm(forms.ModelForm):
    """Form for adding attachments to documents."""

    class Meta:
        model = DocumentAttachment
        fields = ['file', 'attachment_type', 'description', 'expiry_date']
        widgets = {
            'description': forms.TextInput(attrs={'placeholder': 'وصف اختياري للملف', 'class': 'form-control'}),
            'attachment_type': forms.Select(attrs={'class': 'form-control'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }


class DocumentFilterForm(forms.Form):
    """Form for filtering documents."""

    document_number = forms.CharField(required=False, label='رقم المعاملة')
    document_type = forms.ChoiceField(
        choices=[('', 'اختر نوع المعاملة')] + list(Document.DOCUMENT_TYPE_CHOICES),
        required=False,
        label='نوع المعاملة'
    )
    entity = forms.CharField(required=False, label='الجهة')
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date'}),
        label='تاريخ المعاملة (من)'
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date'}),
        label='تاريخ المعاملة (إلى)'
    )
    status = forms.ChoiceField(
        choices=[('', 'اختر حالة المعاملة')] + list(Document.STATUS_CHOICES),
        required=False,
        label='حالة المعاملة'
    )
