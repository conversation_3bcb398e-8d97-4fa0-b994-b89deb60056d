{% extends 'layouts/dashboard.html' %}
{% load static %}

{% block title %}رفع مستند للعامل | منصة استقدامي{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden mb-6">
        <div class="p-4 border-b border-gray-200 dark:border-slate-700 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800 dark:text-slate-200">
                <i class="fas fa-file-upload text-blue-500 dark:text-blue-400 mr-2"></i>
                رفع مستند للعامل: {{ worker.first_name }} {{ worker.last_name }}
            </h2>
            <a href="{% url 'workers:worker_detail' worker_id=worker.id %}" class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                <i class="fas fa-arrow-right ml-1"></i>
                العودة لتفاصيل العامل
            </a>
        </div>

        <div class="p-4 bg-blue-50 dark:bg-slate-700 border-b border-blue-100 dark:border-slate-600">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <i class="fas fa-info-circle text-blue-500 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-blue-800 dark:text-blue-300">معلومات رفع المستندات</h3>
                    <p class="mt-1 text-blue-700 dark:text-blue-200">
                        يمكنك رفع مستندات للعامل مثل جواز السفر، الستيكر (ملصق التأشيرة)، الإقامة، فحص دم، وغيرها.
                        تأكد من أن المستندات واضحة وبصيغة PDF أو صور (JPG، PNG).
                    </p>
                    <div class="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                            <i class="fas fa-lightbulb ml-1"></i>
                            <strong>نصيحة:</strong> يمكنك دمج صورة جواز السفر مع صورة الستيكر في ملف PDF واحد واختيار نوع المرفق "جواز سفر مع الستيكر".
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-6">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}

                {% if form.errors %}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-400">يوجد أخطاء في النموذج:</h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                {{ form.errors }}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="col-span-1 md:col-span-2">
                        <label for="{{ form.file.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            الملف <span class="text-red-500">*</span>
                        </label>

                        <!-- زر اختيار الملف منفصل -->
                        <div class="mb-4">
                            <div class="flex items-center">
                                <label for="{{ form.file.id_for_label }}" class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800 cursor-pointer transition-all duration-200 hover:scale-105">
                                    <i class="fas fa-file-upload mr-2 text-lg"></i>
                                    اختر ملف
                                    {{ form.file }}
                                </label>

                                <div id="selected-file-container" class="mr-4 flex-1 hidden">
                                    <div class="flex items-center p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                                        <div id="file-icon" class="ml-2 text-lg"></div>
                                        <div class="flex-1">
                                            <span id="selected-file-name" class="text-sm font-medium text-green-700 dark:text-green-400">لم يتم اختيار ملف</span>
                                        </div>
                                        <button type="button" id="remove-file" class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <div id="no-file-selected" class="mr-4 text-sm text-gray-600 dark:text-slate-400">
                                    <i class="fas fa-info-circle ml-1 text-blue-500"></i>
                                    لم يتم اختيار ملف بعد
                                </div>
                            </div>
                        </div>

                        <!-- منطقة السحب والإفلات منفصلة -->
                        <div id="drop-zone" class="mt-1 flex flex-col items-center justify-center px-6 py-8 border-2 border-gray-300 dark:border-slate-600 border-dashed rounded-lg transition-all duration-300 hover:bg-gray-50 dark:hover:bg-slate-700/50">
                            <div class="text-center">
                                <div class="mb-4 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full inline-flex items-center justify-center">
                                    <i class="fas fa-cloud-upload-alt text-blue-500 dark:text-blue-400 text-3xl"></i>
                                </div>
                                <p class="text-base font-medium text-gray-700 dark:text-slate-300 mb-2">
                                    اسحب وأفلت الملف هنا
                                </p>
                                <p class="text-sm text-gray-500 dark:text-slate-400 mb-3">
                                    أو انقر على زر "اختر ملف" أعلاه
                                </p>
                                <div class="flex flex-wrap justify-center gap-2 mt-2">
                                    <span class="px-2 py-1 bg-gray-100 dark:bg-slate-700 rounded-md text-xs text-gray-600 dark:text-slate-300">
                                        <i class="fas fa-file-pdf text-red-500 ml-1"></i> PDF
                                    </span>
                                    <span class="px-2 py-1 bg-gray-100 dark:bg-slate-700 rounded-md text-xs text-gray-600 dark:text-slate-300">
                                        <i class="fas fa-file-image text-blue-500 ml-1"></i> PNG
                                    </span>
                                    <span class="px-2 py-1 bg-gray-100 dark:bg-slate-700 rounded-md text-xs text-gray-600 dark:text-slate-300">
                                        <i class="fas fa-file-image text-green-500 ml-1"></i> JPG
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-slate-400 mt-3">
                                    الحد الأقصى لحجم الملف: 10MB
                                </p>
                            </div>
                        </div>
                        {% if form.file.errors %}
                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ form.file.errors }}</p>
                        {% endif %}
                    </div>

                    <div class="col-span-1">
                        <label for="{{ form.attachment_type.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            نوع المرفق <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <select id="{{ form.attachment_type.id_for_label }}" name="{{ form.attachment_type.html_name }}" class="block w-full rounded-md border-gray-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                {% for value, text in form.fields.attachment_type.choices %}
                                <option value="{{ value }}">{{ text }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% if form.attachment_type.errors %}
                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ form.attachment_type.errors }}</p>
                        {% endif %}
                    </div>

                    <div class="col-span-1">
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            وصف المستند
                        </label>
                        <div class="mt-1">
                            <input type="text" id="{{ form.description.id_for_label }}" name="{{ form.description.html_name }}" placeholder="وصف اختياري للملف" class="block w-full rounded-md border-gray-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        {% if form.description.errors %}
                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ form.description.errors }}</p>
                        {% endif %}
                    </div>

                    <div class="col-span-1">
                        <label for="{{ form.expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                            تاريخ انتهاء الصلاحية <span class="text-gray-500">(اختياري)</span>
                        </label>
                        <div class="mt-1">
                            <input type="date" id="{{ form.expiry_date.id_for_label }}" name="{{ form.expiry_date.html_name }}" class="block w-full rounded-md border-gray-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        {% if form.expiry_date.errors %}
                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ form.expiry_date.errors }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500 dark:text-slate-400">أدخل تاريخ انتهاء صلاحية المستند إذا كان ينطبق (مثل: الإقامة، جواز السفر)</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <a href="{% url 'workers:worker_detail' worker_id=worker.id %}" class="px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-slate-300 bg-white dark:bg-slate-700 hover:bg-gray-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800">
                        إلغاء
                    </a>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800">
                        <i class="fas fa-upload ml-1"></i>
                        رفع المستند
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم عرض المرفقات الحالية -->
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden mt-6">
        <div class="p-4 border-b border-gray-200 dark:border-slate-700">
            <h2 class="text-xl font-bold text-gray-800 dark:text-slate-200">
                <i class="fas fa-file-alt text-blue-500 dark:text-blue-400 mr-2"></i>
                مستندات العامل الحالية
            </h2>
        </div>

        {% if worker_documents and worker_documents.exists %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                <thead class="bg-gray-50 dark:bg-slate-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">نوع المستند</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">الوصف</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">تاريخ الرفع</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">تاريخ انتهاء الصلاحية</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                    {% for document in worker_documents %}
                        {% for attachment in document.attachments.all %}
                        <tr class="hover:bg-gray-50 dark:hover:bg-slate-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if attachment.attachment_type == 'passport' %}
                                    <i class="fas fa-passport text-blue-500 mr-2"></i>
                                    {% elif attachment.attachment_type == 'visa_sticker' %}
                                    <i class="fas fa-id-card text-green-500 mr-2"></i>
                                    {% elif attachment.attachment_type == 'passport_with_visa' %}
                                    <div class="flex mr-2">
                                        <i class="fas fa-passport text-blue-500"></i>
                                        <i class="fas fa-plus text-gray-400 text-xs mx-1"></i>
                                        <i class="fas fa-id-card text-green-500"></i>
                                    </div>
                                    {% elif attachment.attachment_type == 'iqama' %}
                                    <i class="fas fa-id-card text-green-500 mr-2"></i>
                                    {% elif attachment.attachment_type == 'contract' %}
                                    <i class="fas fa-file-contract text-purple-500 mr-2"></i>
                                    {% elif attachment.attachment_type == 'medical' %}
                                    <i class="fas fa-notes-medical text-red-500 mr-2"></i>
                                    {% elif attachment.attachment_type == 'certificate' %}
                                    <i class="fas fa-certificate text-yellow-500 mr-2"></i>
                                    {% else %}
                                    <i class="fas fa-file text-gray-500 mr-2"></i>
                                    {% endif %}
                                    <span class="text-sm text-gray-900 dark:text-slate-200">{{ attachment.get_attachment_type_display }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-slate-200">
                                {{ attachment.description|default:"--" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-slate-200">
                                {{ attachment.uploaded_at|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-slate-200">
                                {% if attachment.expiry_date %}
                                    {% if attachment.expiry_date < today %}
                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        منتهي: {{ attachment.expiry_date|date:"Y-m-d" }}
                                    </span>
                                    {% elif attachment.expiry_date < expiry_warning %}
                                    <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        ينتهي قريباً: {{ attachment.expiry_date|date:"Y-m-d" }}
                                    </span>
                                    {% else %}
                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        {{ attachment.expiry_date|date:"Y-m-d" }}
                                    </span>
                                    {% endif %}
                                {% else %}
                                    --
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-slate-200">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{{ attachment.file.url }}" target="_blank" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" title="عرض الملف">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ attachment.file.url }}" download class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" title="تنزيل الملف">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button type="button"
                                        data-delete-url="{% url 'documents:delete_document_attachment' document.id attachment.id %}"
                                        onclick="window.deleteItem(this.getAttribute('data-delete-url'));"
                                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 bg-transparent border-0 p-0"
                                        title="حذف الملف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- عرض رسالة عدم وجود مستندات بنفس طريقة عرض العقود الفارغة -->
        <div class="text-center py-12 bg-blue-50 dark:bg-slate-800/50 rounded-lg m-6">
            <div class="text-blue-300 dark:text-blue-400 mb-4">
                <i class="fas fa-file-alt text-6xl"></i>
            </div>
            <p class="text-blue-800 dark:text-blue-300 mb-6 text-lg">لا توجد مستندات مرفقة لهذا العامل</p>
            <a href="#" onclick="document.getElementById('{{ form.file.id_for_label }}').click(); return false;" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2.5 rounded-lg text-sm hover:from-blue-700 hover:to-blue-800 transition-colors duration-300 shadow-md dark:shadow-slate-900/50 flex items-center justify-center mx-auto w-48">
                <i class="fas fa-upload ml-2"></i> إضافة مستند
            </a>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('{{ form.file.id_for_label }}');
        const dropZone = document.getElementById('drop-zone');
        const fileNameDisplay = document.getElementById('selected-file-name');
        const selectedFileContainer = document.getElementById('selected-file-container');
        const noFileSelected = document.getElementById('no-file-selected');
        const fileIconContainer = document.getElementById('file-icon');
        const removeFileButton = document.getElementById('remove-file');

        // إخفاء حقل الملف الأصلي
        fileInput.style.display = 'none';

        // إضافة تأثيرات السحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
            dropZone.classList.remove('border-gray-300', 'dark:border-slate-600');

            // إضافة تأثير النبض
            dropZone.classList.add('animate-pulse');
        }

        function unhighlight() {
            dropZone.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'animate-pulse');
            dropZone.classList.add('border-gray-300', 'dark:border-slate-600');
        }

        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files && files.length > 0) {
                fileInput.files = files;
                updateFileDisplay(files[0]);
            }
        }

        // تحديث اسم الملف عند اختياره عبر زر الاختيار
        fileInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                updateFileDisplay(this.files[0]);
            } else {
                resetFileDisplay();
            }
        });

        // زر إزالة الملف
        removeFileButton.addEventListener('click', function() {
            // إعادة تعيين حقل الملف
            fileInput.value = '';
            resetFileDisplay();
        });

        // دالة تحديث عرض الملف
        function updateFileDisplay(file) {
            // إظهار حاوية الملف المحدد وإخفاء رسالة عدم اختيار ملف
            selectedFileContainer.classList.remove('hidden');
            noFileSelected.classList.add('hidden');

            // تحديث اسم الملف
            fileNameDisplay.textContent = file.name;

            // تحديد أيقونة الملف بناءً على نوعه
            let iconHTML = '';
            const fileName = file.name.toLowerCase();

            if (fileName.endsWith('.pdf')) {
                iconHTML = '<i class="fas fa-file-pdf text-red-500"></i>';
            } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
                iconHTML = '<i class="fas fa-file-image text-blue-500"></i>';
            } else if (fileName.endsWith('.png')) {
                iconHTML = '<i class="fas fa-file-image text-green-500"></i>';
            } else {
                iconHTML = '<i class="fas fa-file text-gray-500"></i>';
            }

            fileIconContainer.innerHTML = iconHTML;

            // تغيير مظهر منطقة السحب والإفلات
            dropZone.classList.add('opacity-50');
        }

        // دالة إعادة تعيين عرض الملف
        function resetFileDisplay() {
            // إخفاء حاوية الملف المحدد وإظهار رسالة عدم اختيار ملف
            selectedFileContainer.classList.add('hidden');
            noFileSelected.classList.remove('hidden');

            // إعادة تعيين أيقونة الملف واسمه
            fileIconContainer.innerHTML = '';
            fileNameDisplay.textContent = '';

            // إعادة مظهر منطقة السحب والإفلات
            dropZone.classList.remove('opacity-50');
        }
    });
</script>
{% endblock %}
