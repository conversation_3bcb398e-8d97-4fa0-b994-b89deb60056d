from django.core.management.base import BaseCommand
from backend.super_admin.models import Company
from django.db import transaction

class Command(BaseCommand):
    help = 'حذف جميع الشركات من النظام'

    def handle(self, *args, **options):
        try:
            # عدد الشركات قبل الحذف
            companies_count = Company.objects.count()
            self.stdout.write(self.style.SUCCESS(f"عدد الشركات قبل الحذف: {companies_count}"))
            
            # حذف جميع الشركات
            with transaction.atomic():
                deleted_count = Company.objects.all().delete()
                
            self.stdout.write(self.style.SUCCESS(f"تم حذف الشركات بنجاح. عدد العناصر المحذوفة: {deleted_count}"))
            
            # التأكد من عدم وجود شركات بعد الحذف
            remaining_count = Company.objects.count()
            self.stdout.write(self.style.SUCCESS(f"عدد الشركات المتبقية: {remaining_count}"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء حذف الشركات: {str(e)}"))
