{% extends 'layouts/company_modern.html' %}
{% load static %}

{% block title %}إدارة العمال - منصة استقدامي{% endblock %}

{% block content %}
<div class="container mx-auto px-6 py-8 space-y-8">

    <!-- عنوان الصفحة -->
    <section class="card-modern p-8 animate-fadeInUp">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6 space-x-reverse">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg animate-float">
                    <i class="fas fa-hard-hat text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">👷‍♂️ إدارة العمال</h1>
                    <p class="text-gray-600 dark:text-gray-400 text-lg">إدارة شاملة لجميع أنواع العمال في الشركة</p>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <div class="dropdown dropdown-end">
                    <label tabindex="0" class="btn bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-none hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة عامل جديد
                        <i class="fas fa-chevron-down mr-2"></i>
                    </label>
                    <ul tabindex="0" class="dropdown-content menu p-2 shadow-xl bg-white dark:bg-gray-800 rounded-box w-64 mt-2">
                        <li><a href="{% url 'workers:create_permanent_worker' %}" class="text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20">
                            <i class="fas fa-briefcase text-blue-500"></i>
                            عامل عقد دائم
                        </a></li>
                        <li><a href="{% url 'workers:create_monthly_worker' %}" class="text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20">
                            <i class="fas fa-calendar-alt text-purple-500"></i>
                            عامل عقد شهري
                        </a></li>
                        <li><a href="{% url 'workers:create_custom_worker' %}" class="text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20">
                            <i class="fas fa-cogs text-orange-500"></i>
                            عامل خدمة مخصصة
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- إحصائيات العمال -->
    <section>
        <div class="text-center mb-8 animate-fadeInUp">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">📊 إحصائيات العمال</h2>
            <p class="text-gray-600 dark:text-gray-400">نظرة شاملة على أنواع العمال وحالاتهم</p>
        </div>

        <!-- إحصائيات عامة -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="card-modern p-6 hover-lift animate-scaleIn">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">إجمالي العمال</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ total_workers|default:0 }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.1s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">العمال النشطون</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ active_workers|default:0 }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.2s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-pause-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">غير نشط</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ inactive_workers|default:0 }}">0</div>
                    </div>
                </div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.3s;">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mr-4">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">في إجازة</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-white" data-counter="{{ on_leave_workers|default:0 }}">0</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات حسب نوع العامل -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.4s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-briefcase text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">عمال العقود الدائمة</div>
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" data-counter="{{ contract_workers|default:0 }}">0</div>
                        </div>
                    </div>
                    <a href="{% url 'workers:contract_workers' %}" class="text-blue-500 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">عقود طويلة المدى</div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.5s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-calendar-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">عمال العقود الشهرية</div>
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400" data-counter="{{ monthly_workers|default:0 }}">0</div>
                        </div>
                    </div>
                    <a href="{% url 'workers:monthly_workers' %}" class="text-purple-500 hover:text-purple-600 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">عقود قابلة للتجديد</div>
            </div>

            <div class="card-modern p-6 hover-lift animate-scaleIn" style="animation-delay: 0.6s;">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-cogs text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">عمال الخدمات المخصصة</div>
                            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400" data-counter="{{ custom_workers|default:0 }}">0</div>
                        </div>
                    </div>
                    <a href="{% url 'workers:custom_workers' %}" class="text-orange-500 hover:text-orange-600 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">خدمات متخصصة</div>
            </div>
        </div>
    </section>

    <!-- قسم البحث والفلاتر -->
    <section class="card-modern p-6 animate-fadeInUp">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div class="flex items-center">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-list text-blue-600 dark:text-blue-400 mr-2"></i>
                    قائمة العمال
                </h2>
            </div>

            <form method="GET" class="flex flex-col sm:flex-row items-center gap-4">
                <!-- البحث -->
                <div class="relative">
                    <input type="text" name="search" value="{{ search_query }}"
                           placeholder="البحث بالاسم، رقم الجواز، أو الجنسية..."
                           class="pl-10 pr-4 py-2.5 w-64 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>

                <!-- فلتر نوع العامل -->
                <select name="worker_type" class="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">جميع الأنواع</option>
                    <option value="contract" {% if worker_type_filter == 'contract' %}selected{% endif %}>عقد دائم</option>
                    <option value="monthly" {% if worker_type_filter == 'monthly' %}selected{% endif %}>عقد شهري</option>
                    <option value="custom" {% if worker_type_filter == 'custom' %}selected{% endif %}>خدمة مخصصة</option>
                </select>

                <!-- فلتر الحالة -->
                <select name="status" class="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    <option value="on_leave" {% if status_filter == 'on_leave' %}selected{% endif %}>في إجازة</option>
                    <option value="terminated" {% if status_filter == 'terminated' %}selected{% endif %}>منتهي الخدمة</option>
                </select>

                <!-- فلتر الجنسية -->
                <select name="nationality" class="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">جميع الجنسيات</option>
                    {% for nationality in nationalities %}
                        <option value="{{ nationality }}" {% if nationality_filter == nationality %}selected{% endif %}>{{ nationality }}</option>
                    {% endfor %}
                </select>

                <!-- أزرار التحكم -->
                <div class="flex items-center gap-2">
                    <button type="submit" class="px-4 py-2.5 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-search mr-1"></i>
                        بحث
                    </button>
                    <a href="{% url 'workers:workers_list' %}" class="px-4 py-2.5 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                        <i class="fas fa-times mr-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </section>

    <!-- جدول العمال -->
    <section class="card-modern animate-fadeInUp">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                        عرض {{ workers.start_index|default:0 }} - {{ workers.end_index|default:0 }} من {{ workers.paginator.count|default:0 }} عامل
                    </span>
                    {% if search_query or worker_type_filter or status_filter or nationality_filter %}
                        <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs">
                            <i class="fas fa-filter mr-1"></i>
                            مفلتر
                        </span>
                    {% endif %}
                </div>

                <div class="flex items-center gap-2">
                    <button onclick="exportWorkers()" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm">
                        <i class="fas fa-download mr-1"></i>
                        تصدير
                    </button>
                    <button onclick="importWorkers()" class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm">
                        <i class="fas fa-upload mr-1"></i>
                        استيراد
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الاسم</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">نوع العامل</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الجنسية</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">المهنة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">رقم الجواز</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الحالة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">تاريخ الإضافة</th>
                        <th class="px-6 py-4 text-right text-sm font-semibold">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for worker in workers %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                {% if worker.photo %}
                                    <img src="{{ worker.photo.url }}" alt="{{ worker.first_name }}" class="w-10 h-10 rounded-full object-cover mr-3">
                                {% else %}
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                {% endif %}
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-white">{{ worker.first_name }} {{ worker.last_name }}</div>
                                    {% if worker.phone_number %}
                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ worker.phone_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if worker.worker_type == 'contract' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                    <i class="fas fa-briefcase text-xs mr-1"></i>
                                    عقد دائم
                                </span>
                            {% elif worker.worker_type == 'monthly' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                                    <i class="fas fa-calendar-alt text-xs mr-1"></i>
                                    عقد شهري
                                </span>
                            {% elif worker.worker_type == 'custom' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                                    <i class="fas fa-cogs text-xs mr-1"></i>
                                    خدمة مخصصة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                                    <i class="fas fa-question text-xs mr-1"></i>
                                    غير محدد
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.get_nationality_display|default:worker.nationality }}</td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.job_title|default:"غير محدد" }}</td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300 font-mono">{{ worker.passport_number }}</td>
                        <td class="px-6 py-4">
                            {% if worker.status == 'active' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    نشط
                                </span>
                            {% elif worker.status == 'on_leave' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                    <i class="fas fa-clock text-xs mr-1"></i>
                                    في إجازة
                                </span>
                            {% elif worker.status == 'terminated' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                    <i class="fas fa-times-circle text-xs mr-1"></i>
                                    منتهي الخدمة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                                    <i class="fas fa-pause-circle text-xs mr-1"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-gray-700 dark:text-gray-300">{{ worker.date_joined|date:"Y/m/d"|default:worker.created_at|date:"Y/m/d" }}</td>
                        <td class="px-6 py-4">
                            <div class="flex items-center gap-2">
                                <a href="{% url 'workers:worker_detail' worker.id %}" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'workers:worker_update' worker.id %}" class="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteWorker({{ worker.id }}, '{{ worker.first_name }} {{ worker.last_name }}')" class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <div class="dropdown dropdown-end">
                                    <label tabindex="0" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </label>
                                    <ul tabindex="0" class="dropdown-content menu p-2 shadow-xl bg-white dark:bg-gray-800 rounded-box w-52 mt-2">
                                        <li><a href="{% url 'workers:worker_documents' worker.id %}" class="text-sm">
                                            <i class="fas fa-file-alt text-blue-500"></i>
                                            المستندات
                                        </a></li>
                                        <li><a onclick="transferWorker({{ worker.id }})" class="text-sm">
                                            <i class="fas fa-exchange-alt text-green-500"></i>
                                            نقل العامل
                                        </a></li>
                                        <li><a onclick="printWorkerCard({{ worker.id }})" class="text-sm">
                                            <i class="fas fa-print text-purple-500"></i>
                                            طباعة البطاقة
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-16 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mb-6">
                                    <i class="fas fa-hard-hat text-3xl text-gray-400 dark:text-gray-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">لا توجد عمال مسجلين</h3>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mb-6 max-w-md">
                                    {% if search_query or worker_type_filter or status_filter or nationality_filter %}
                                        لم يتم العثور على عمال تطابق معايير البحث المحددة. جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
                                    {% else %}
                                        ابدأ ببناء فريق العمل الخاص بك عن طريق إضافة العمال الجدد إلى النظام.
                                    {% endif %}
                                </p>
                                <div class="flex flex-col sm:flex-row gap-3">
                                    {% if search_query or worker_type_filter or status_filter or nationality_filter %}
                                        <a href="{% url 'workers:workers_list' %}" class="px-6 py-2.5 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm">
                                            <i class="fas fa-times mr-2"></i>
                                            إعادة تعيين الفلاتر
                                        </a>
                                    {% endif %}
                                    <div class="dropdown dropdown-top">
                                        <label tabindex="0" class="btn bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-none hover:shadow-lg transition-all duration-300">
                                            <i class="fas fa-plus mr-2"></i>
                                            إضافة عامل جديد
                                        </label>
                                        <ul tabindex="0" class="dropdown-content menu p-2 shadow-xl bg-white dark:bg-gray-800 rounded-box w-64 mb-2">
                                            <li><a href="{% url 'workers:create_permanent_worker' %}" class="text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20">
                                                <i class="fas fa-briefcase text-blue-500"></i>
                                                عامل عقد دائم
                                            </a></li>
                                            <li><a href="{% url 'workers:create_monthly_worker' %}" class="text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                                <i class="fas fa-calendar-alt text-purple-500"></i>
                                                عامل عقد شهري
                                            </a></li>
                                            <li><a href="{% url 'workers:create_custom_worker' %}" class="text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20">
                                                <i class="fas fa-cogs text-orange-500"></i>
                                                عامل خدمة مخصصة
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if workers.has_other_pages %}
        <div class="p-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    عرض {{ workers.start_index }} - {{ workers.end_index }} من {{ workers.paginator.count }} عامل
                </div>

                <div class="flex items-center gap-2">
                    {% if workers.has_previous %}
                        <a href="?page={{ workers.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if worker_type_filter %}&worker_type={{ worker_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if nationality_filter %}&nationality={{ nationality_filter }}{% endif %}"
                           class="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-chevron-right mr-1"></i>
                            السابق
                        </a>
                    {% endif %}

                    <div class="flex items-center gap-1">
                        {% for i in workers.paginator.page_range %}
                            {% if workers.number == i %}
                                <span class="px-3 py-2 bg-blue-500 text-white rounded-lg font-medium">{{ i }}</span>
                            {% elif i == workers.paginator.page_range.0 or i == workers.paginator.page_range.1 or i == workers.number|add:"-1" or i == workers.number|add:"1" or i == workers.paginator.num_pages|add:"-1" or i == workers.paginator.num_pages %}
                                <a href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if worker_type_filter %}&worker_type={{ worker_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if nationality_filter %}&nationality={{ nationality_filter }}{% endif %}"
                                   class="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">{{ i }}</a>
                            {% elif i == workers.number|add:"-2" or i == workers.number|add:"2" %}
                                <span class="px-2 py-2 text-gray-400">...</span>
                            {% endif %}
                        {% endfor %}
                    </div>

                    {% if workers.has_next %}
                        <a href="?page={{ workers.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if worker_type_filter %}&worker_type={{ worker_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if nationality_filter %}&nationality={{ nationality_filter }}{% endif %}"
                           class="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            التالي
                            <i class="fas fa-chevron-left mr-1"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </section>
</div>

<script>
// وظائف إدارة العمال المحدثة
function deleteWorker(workerId, workerName) {
    Swal.fire({
        icon: 'warning',
        title: 'حذف العامل',
        html: `هل أنت متأكد من حذف العامل <strong>${workerName}</strong>؟<br><small class="text-gray-500">هذا الإجراء لا يمكن التراجع عنه</small>`,
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // هنا يمكن إضافة طلب AJAX لحذف العامل
            fetch(`/workers/delete/${workerId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف بنجاح',
                        text: `تم حذف العامل ${workerName} بنجاح`,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في الحذف',
                        text: data.message || 'حدث خطأ أثناء حذف العامل',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ في الاتصال بالخادم',
                    confirmButtonColor: '#ef4444'
                });
            });
        }
    });
}

function transferWorker(workerId) {
    Swal.fire({
        icon: 'question',
        title: 'نقل العامل',
        text: 'هل تريد نقل هذا العامل إلى عميل آخر؟',
        showCancelButton: true,
        confirmButtonText: 'نعم، انقل',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#10b981'
    }).then((result) => {
        if (result.isConfirmed) {
            // توجيه إلى صفحة نقل العامل
            window.location.href = `/workers/transfer/${workerId}/`;
        }
    });
}

function printWorkerCard(workerId) {
    Swal.fire({
        icon: 'info',
        title: 'طباعة بطاقة العامل',
        text: 'سيتم فتح نافذة جديدة لطباعة بطاقة العامل',
        showCancelButton: true,
        confirmButtonText: 'طباعة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#8b5cf6'
    }).then((result) => {
        if (result.isConfirmed) {
            // فتح نافذة طباعة
            window.open(`/workers/print-card/${workerId}/`, '_blank');
        }
    });
}

function exportWorkers() {
    Swal.fire({
        icon: 'question',
        title: 'تصدير بيانات العمال',
        text: 'اختر تنسيق التصدير',
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: 'Excel',
        denyButtonText: 'PDF',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#10b981',
        denyButtonColor: '#3b82f6'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/workers/export/excel/';
        } else if (result.isDenied) {
            window.location.href = '/workers/export/pdf/';
        }
    });
}

function importWorkers() {
    Swal.fire({
        icon: 'info',
        title: 'استيراد العمال',
        text: 'سيتم توجيهك إلى صفحة استيراد العمال',
        showCancelButton: true,
        confirmButtonText: 'متابعة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#f97316'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/workers/import/';
        }
    });
}

// تأثير العداد للأرقام
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-counter'));
        if (target > 0) {
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current).toLocaleString('ar-SA');
            }, 16);
        }
    });

    // إضافة تأثيرات التحريك
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fadeInUp');
            }
        });
    }, observerOptions);

    // مراقبة العناصر للتحريك
    document.querySelectorAll('.card-modern').forEach(card => {
        observer.observe(card);
    });

    // تحسين تجربة المستخدم للفلاتر
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // يمكن إضافة بحث فوري هنا
            }, 500);
        });
    }

    // إضافة تأثيرات hover للبطاقات
    document.querySelectorAll('.hover-lift').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// إضافة CSS للتحريك
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes scaleIn {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    .animate-fadeInUp {
        animation: fadeInUp 0.6s ease-out forwards;
    }

    .animate-scaleIn {
        animation: scaleIn 0.5s ease-out forwards;
    }

    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .card-modern {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .dark .card-modern {
        background: rgb(31 41 55);
        border-color: rgba(255, 255, 255, 0.1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
