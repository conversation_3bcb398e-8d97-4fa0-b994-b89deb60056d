import api from './api';

// خدمات المصادقة
const authService = {
  // تسجيل الدخول
  login: async (username, password, serialNumber) => {
    const response = await api.post('/auth/token/', {
      username,
      password,
      serial_number: serialNumber,
    });
    return response.data;
  },
  
  // تسجيل الخروج
  logout: async (refreshToken) => {
    const response = await api.post('/auth/logout/', {
      refresh: refreshToken,
    });
    return response.data;
  },
  
  // تحديث رمز الوصول
  refreshToken: async (refreshToken) => {
    const response = await api.post('/auth/token/refresh/', {
      refresh: refreshToken,
    });
    return response.data;
  },
  
  // الحصول على معلومات المستخدم الحالي
  getCurrentUser: async () => {
    const response = await api.get('/auth/users/me/');
    return response.data;
  },
  
  // تغيير كلمة المرور
  changePassword: async (oldPassword, newPassword) => {
    const response = await api.post('/auth/users/change-password/', {
      old_password: oldPassword,
      new_password: newPassword,
      confirm_password: newPassword,
    });
    return response.data;
  },
};

export default authService;
