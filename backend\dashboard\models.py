from django.db import models
from django.utils import timezone
from backend.workers.models import Worker

class Alert(models.Model):
    ALERT_TYPE_CHOICES = (
        ('passport_expiry', 'انتهاء جواز سفر'),
        ('visa_expiry', 'انتهاء تأشيرة'),
        ('payment_due', 'استحقاق دفعة'),
        ('procedure_due', 'استحقاق إجراء'),
        ('document_expiry', 'انتهاء مستند'),
        ('other', 'أخرى'),
    )

    PRIORITY_CHOICES = (
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    )

    title = models.CharField(max_length=200, verbose_name='العنوان')
    description = models.TextField(verbose_name='الوصف')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES, verbose_name='نوع التنبيه')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name='الأولوية')
    date_created = models.DateField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    due_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')
    is_read = models.BooleanField(default=False, verbose_name='مقروء')
    is_resolved = models.BooleanField(default=False, verbose_name='تم حله')
    related_worker = models.ForeignKey(Worker, on_delete=models.CASCADE, blank=True, null=True, related_name='alerts', verbose_name='العامل المرتبط')

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'تنبيه'
        verbose_name_plural = 'التنبيهات'
        ordering = ['-priority', '-date_created']

class Activity(models.Model):
    ACTIVITY_TYPE_CHOICES = (
        ('worker_added', 'إضافة عامل'),
        ('worker_updated', 'تحديث عامل'),
        ('payment_added', 'إضافة دفعة'),
        ('client_added', 'إضافة عميل'),
        ('client_updated', 'تحديث عميل'),
        ('procedure_added', 'إضافة إجراء'),
        ('procedure_updated', 'تحديث إجراء'),
        ('other', 'أخرى'),
    )

    description = models.TextField(verbose_name='الوصف')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPE_CHOICES, verbose_name='نوع النشاط')
    date_created = models.DateTimeField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    related_worker = models.ForeignKey(Worker, on_delete=models.CASCADE, blank=True, null=True, related_name='activities', verbose_name='العامل المرتبط')

    def __str__(self):
        return f"{self.get_activity_type_display()} - {self.date_created.strftime('%Y-%m-%d %H:%M')}"

    class Meta:
        verbose_name = 'نشاط'
        verbose_name_plural = 'النشاطات'
        ordering = ['-date_created']
