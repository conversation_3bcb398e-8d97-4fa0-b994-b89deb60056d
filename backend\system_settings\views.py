from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.models import User, Group, Permission
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import timedelta
import json
from .models import SystemSetting, UserProfile, UserPermission, WorkerSetting, NotificationSetting, AuditLog, Currency, SystemAction
from .forms import SystemSettingForm, UserProfileForm, UserPermissionForm, WorkerSettingForm, NotificationSettingForm, CurrencyForm
from django.shortcuts import get_object_or_404
from backend.workers.models import RecruitmentCompany, Worker
from backend.workers.forms import RecruitmentCompanyForm
from backend.contracts.models import Contract
from backend.services.models import BookingSchedule

# Helper function to check if user is admin
def is_admin(user):
    return user.is_superuser or UserProfile.objects.filter(user=user, role='admin').exists()

@login_required
@user_passes_test(is_admin)
def admin_settings(request):
    """Comprehensive settings page for system administrators only"""
    # Get system statistics
    stats = {
        'users_count': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'workers_count': Worker.objects.count(),
        'contracts_count': Contract.objects.count(),
        'services_count': BookingSchedule.objects.count(),
        'last_login': AuditLog.objects.filter(action='login').order_by('-created_at').first(),
        'recent_actions': AuditLog.objects.all().order_by('-created_at')[:10],
    }

    # Get system settings grouped by category
    settings = SystemSetting.objects.all().order_by('category', 'key')
    settings_by_category = {}
    for setting in settings:
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = []
        settings_by_category[setting.category].append(setting)

    # Get user roles statistics
    user_roles = {
        'admins': UserProfile.objects.filter(role='admin').count(),
        'managers': UserProfile.objects.filter(role='manager').count(),
        'supervisors': UserProfile.objects.filter(role='supervisor').count(),
        'staff': UserProfile.objects.filter(role='staff').count(),
    }

    # Get worker settings
    worker_settings = WorkerSetting.objects.all().order_by('setting_key')

    # Get currencies
    currencies = Currency.objects.all().order_by('-is_default', 'name')
    default_currency = Currency.objects.filter(is_default=True).first()

    # Get recruitment companies
    recruitment_companies = RecruitmentCompany.objects.all().order_by('name')

    # Get notification settings
    notification_types = [
        'worker_expiry',
        'contract_expiry',
        'service_scheduled',
        'payment_due',
        'system_updates'
    ]

    # Get recent audit logs
    audit_logs = AuditLog.objects.all().order_by('-created_at')[:20]

    # Get system actions that can be undone
    system_actions = SystemAction.objects.filter(can_undo=True).exclude(status='undone').order_by('-created_at')[:10]

    context = {
        'stats': stats,
        'settings_by_category': settings_by_category,
        'user_roles': user_roles,
        'worker_settings': worker_settings,
        'currencies': currencies,
        'default_currency': default_currency,
        'recruitment_companies': recruitment_companies,
        'notification_types': notification_types,
        'audit_logs': audit_logs,
        'system_actions': system_actions,
        'users': User.objects.all().order_by('username')[:20],
    }

    return render(request, 'system_settings/admin_settings.html', context)

@login_required
def settings_home(request):
    """View for the main settings page"""
    return render(request, 'system_settings/settings_home.html')

@login_required
def system_settings(request):
    """View for managing system settings"""
    settings = SystemSetting.objects.all().order_by('category', 'key')

    # Group settings by category
    settings_by_category = {}
    for setting in settings:
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = []
        settings_by_category[setting.category].append(setting)

    context = {
        'settings_by_category': settings_by_category,
    }

    return render(request, 'system_settings/system_settings.html', context)

@login_required
def edit_system_setting(request, setting_id):
    """View for editing a system setting"""
    setting = get_object_or_404(SystemSetting, id=setting_id)

    if request.method == 'POST':
        form = SystemSettingForm(request.POST, instance=setting)
        if form.is_valid():
            form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='update',
                module='system_settings',
                record_id=setting.id,
                description=f'تم تحديث إعداد النظام: {setting.key}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم تحديث الإعداد {setting.key} بنجاح')
            return redirect('system_settings')
    else:
        form = SystemSettingForm(instance=setting)

    context = {
        'form': form,
        'setting': setting,
    }

    return render(request, 'system_settings/edit_system_setting.html', context)

@login_required
def user_management(request):
    """View for managing users"""
    search_query = request.GET.get('search', '')

    if search_query:
        users = User.objects.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        ).order_by('username')
    else:
        users = User.objects.all().order_by('username')

    # Pagination
    paginator = Paginator(users, 10)  # Show 10 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }

    return render(request, 'system_settings/user_management.html', context)

@login_required
def user_detail(request, user_id):
    """View for viewing user details"""
    user = get_object_or_404(User, id=user_id)

    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=user)

    # Get user permissions
    permissions = UserPermission.objects.filter(user=user)

    # Get audit logs for this user
    audit_logs = AuditLog.objects.filter(user=user).order_by('-created_at')[:10]

    context = {
        'user_obj': user,
        'profile': profile,
        'permissions': permissions,
        'audit_logs': audit_logs,
    }

    return render(request, 'system_settings/user_detail.html', context)

@login_required
def edit_user(request, user_id):
    """View for editing a user"""
    user = get_object_or_404(User, id=user_id)
    profile, created = UserProfile.objects.get_or_create(user=user)

    if request.method == 'POST':
        # Handle user form
        user.username = request.POST.get('username')
        user.first_name = request.POST.get('first_name')
        user.last_name = request.POST.get('last_name')
        user.email = request.POST.get('email')
        user.is_active = 'is_active' in request.POST
        user.is_staff = 'is_staff' in request.POST

        # Set password if provided
        password = request.POST.get('password')
        if password:
            user.set_password(password)

        user.save()

        # Handle profile form
        profile_form = UserProfileForm(request.POST, request.FILES, instance=profile)
        if profile_form.is_valid():
            profile_form.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            module='user_management',
            record_id=user.id,
            description=f'تم تحديث بيانات المستخدم: {user.username}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم تحديث بيانات المستخدم {user.username} بنجاح')
        return redirect('user_detail', user_id=user.id)
    else:
        profile_form = UserProfileForm(instance=profile)

    context = {
        'user_obj': user,
        'profile_form': profile_form,
    }

    return render(request, 'system_settings/edit_user.html', context)

@login_required
def create_user(request):
    """View for creating a new user"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        is_staff = 'is_staff' in request.POST

        # Create user
        user = User.objects.create_user(
            username=username,
            password=password,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_staff=is_staff
        )

        # Create profile
        profile_form = UserProfileForm(request.POST, request.FILES)
        if profile_form.is_valid():
            profile = profile_form.save(commit=False)
            profile.user = user
            profile.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            module='user_management',
            record_id=user.id,
            description=f'تم إنشاء مستخدم جديد: {user.username}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم إنشاء المستخدم {user.username} بنجاح')
        return redirect('user_detail', user_id=user.id)
    else:
        profile_form = UserProfileForm()

    context = {
        'profile_form': profile_form,
    }

    return render(request, 'system_settings/create_user.html', context)

@login_required
def user_permissions(request, user_id):
    """View for managing user permissions"""
    user = get_object_or_404(User, id=user_id)

    # Define available modules
    modules = [
        'dashboard',
        'workers',
        'contracts',
        'services',
        'reports',
        'settings'
    ]

    # Get existing permissions
    permissions = {}
    for perm in UserPermission.objects.filter(user=user):
        permissions[perm.module] = perm

    if request.method == 'POST':
        # Process permissions
        for module in modules:
            perm, created = UserPermission.objects.get_or_create(user=user, module=module)

            perm.can_view = f'view_{module}' in request.POST
            perm.can_add = f'add_{module}' in request.POST
            perm.can_edit = f'edit_{module}' in request.POST
            perm.can_delete = f'delete_{module}' in request.POST

            perm.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            module='user_permissions',
            record_id=user.id,
            description=f'تم تحديث صلاحيات المستخدم: {user.username}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم تحديث صلاحيات المستخدم {user.username} بنجاح')
        return redirect('user_detail', user_id=user.id)

    context = {
        'user_obj': user,
        'modules': modules,
        'permissions': permissions,
    }

    return render(request, 'system_settings/user_permissions.html', context)

@login_required
def worker_settings(request):
    """View for managing worker settings"""
    settings = WorkerSetting.objects.all().order_by('setting_key')

    # Obtener valores específicos de configuración
    passport_expiry_warning_days = '60'  # Valor predeterminado
    visa_expiry_warning_days = '30'  # Valor predeterminado
    nationalities = []

    # Filtrar para no mostrar available_skills en la interfaz
    filtered_settings = [setting for setting in settings if setting.setting_key != 'available_skills']

    # Buscar valores en la base de datos
    for setting in settings:
        if setting.setting_key == 'passport_expiry_warning_days':
            passport_expiry_warning_days = setting.setting_value
        elif setting.setting_key == 'visa_expiry_warning_days':
            visa_expiry_warning_days = setting.setting_value
        elif setting.setting_key == 'available_nationalities':
            nationalities = setting.setting_value.split(',')

    context = {
        'settings': filtered_settings,
        'passport_expiry_warning_days': passport_expiry_warning_days,
        'visa_expiry_warning_days': visa_expiry_warning_days,
        'nationalities': nationalities,
    }

    return render(request, 'system_settings/worker_settings_enhanced.html', context)

@login_required
def edit_worker_setting(request, setting_id):
    """View for editing a worker setting"""
    setting = get_object_or_404(WorkerSetting, id=setting_id)

    if request.method == 'POST':
        form = WorkerSettingForm(request.POST, instance=setting)
        if form.is_valid():
            form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='update',
                module='worker_settings',
                record_id=setting.id,
                description=f'تم تحديث إعداد العمال: {setting.setting_key}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم تحديث إعداد العمال {setting.setting_key} بنجاح')
            return redirect('worker_settings')
    else:
        form = WorkerSettingForm(instance=setting)

    context = {
        'form': form,
        'setting': setting,
    }

    return render(request, 'system_settings/edit_worker_setting.html', context)

@login_required
def create_worker_setting(request):
    """View for creating a new worker setting"""
    if request.method == 'POST':
        form = WorkerSettingForm(request.POST)
        if form.is_valid():
            setting = form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                module='worker_settings',
                record_id=setting.id,
                description=f'تم إنشاء إعداد عمال جديد: {setting.setting_key}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم إنشاء إعداد العمال {setting.setting_key} بنجاح')
            return redirect('worker_settings')
    else:
        form = WorkerSettingForm()

    context = {
        'form': form,
    }

    return render(request, 'system_settings/create_worker_setting.html', context)


@login_required
@csrf_exempt
def update_worker_setting(request):
    """View for updating a worker setting via AJAX"""
    if request.method == 'POST':
        setting_key = request.POST.get('key')
        setting_value = request.POST.get('value')

        if not setting_key or not setting_value:
            return JsonResponse({'status': 'error', 'message': 'المفتاح أو القيمة غير موجودة'}, status=400)

        # Buscar o crear la configuración
        setting, created = WorkerSetting.objects.get_or_create(
            setting_key=setting_key,
            defaults={
                'setting_value': setting_value,
                'description': f'إعداد {setting_key}'
            }
        )

        if not created:
            # Actualizar la configuración existente
            setting.setting_value = setting_value
            setting.save()

        # Registrar la acción
        AuditLog.objects.create(
            user=request.user,
            action='update' if not created else 'create',
            module='worker_settings',
            record_id=setting.id,
            description=f'تم تحديث إعداد العمال: {setting.setting_key}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        return JsonResponse({'status': 'success', 'message': f'تم تحديث الإعداد {setting_key} بنجاح'})

    return JsonResponse({'status': 'error', 'message': 'طريقة غير مسموح بها'}, status=405)


@login_required
@csrf_exempt
def delete_worker_setting(request, setting_id):
    """View for deleting a worker setting via AJAX"""
    if request.method == 'POST':
        try:
            setting = get_object_or_404(WorkerSetting, id=setting_id)

            # حفظ بيانات الإعداد قبل الحذف للتراجع عنها لاحقاً إذا لزم الأمر
            setting_data = {
                'setting_key': setting.setting_key,
                'setting_value': setting.setting_value,
                'description': setting.description,
                'is_active': setting.is_active
            }

            # إنشاء إجراء نظام يمكن التراجع عنه
            SystemAction.objects.create(
                user=request.user,
                action_type='delete',
                module='worker_settings',
                record_id=setting.id,
                description=f'تم حذف إعداد العمال: {setting.setting_key}',
                previous_data=json.dumps(setting_data),
                can_undo=True,
                ip_address=request.META.get('REMOTE_ADDR')
            )

            # تسجيل الإجراء في سجل التدقيق
            AuditLog.objects.create(
                user=request.user,
                action='delete',
                module='worker_settings',
                record_id=setting.id,
                description=f'تم حذف إعداد العمال: {setting.setting_key}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            # حذف الإعداد
            setting.delete()

            return JsonResponse({'status': 'success', 'message': f'تم حذف الإعداد {setting_data["setting_key"]} بنجاح'})

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'حدث خطأ أثناء حذف الإعداد: {str(e)}'})

    return JsonResponse({'status': 'error', 'message': 'طريقة غير مسموح بها'}, status=405)

@login_required
def notification_settings(request):
    """View for managing notification settings"""
    user_settings = NotificationSetting.objects.filter(user=request.user)

    # Define notification types
    notification_types = [
        'worker_expiry',
        'contract_expiry',
        'service_scheduled',
        'payment_due',
        'system_updates'
    ]

    # Ensure all notification types exist for the user
    for notification_type in notification_types:
        NotificationSetting.objects.get_or_create(
            user=request.user,
            notification_type=notification_type,
            defaults={
                'email_enabled': True,
                'sms_enabled': False,
                'in_app_enabled': True
            }
        )

    # Refresh the query
    user_settings = NotificationSetting.objects.filter(user=request.user)

    if request.method == 'POST':
        # Update notification settings
        for setting in user_settings:
            setting.email_enabled = f'email_{setting.notification_type}' in request.POST
            setting.sms_enabled = f'sms_{setting.notification_type}' in request.POST
            setting.in_app_enabled = f'in_app_{setting.notification_type}' in request.POST
            setting.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            module='notification_settings',
            description='تم تحديث إعدادات الإشعارات',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, 'تم تحديث إعدادات الإشعارات بنجاح')
        return redirect('notification_settings')

    context = {
        'user_settings': user_settings,
    }

    return render(request, 'system_settings/notification_settings.html', context)

@login_required
def audit_logs(request):
    """View for viewing audit logs"""
    logs = AuditLog.objects.all().order_by('-created_at')

    # Filter by user if specified
    user_id = request.GET.get('user_id')
    if user_id:
        logs = logs.filter(user_id=user_id)

    # Filter by module if specified
    module = request.GET.get('module')
    if module:
        logs = logs.filter(module=module)

    # Filter by action if specified
    action = request.GET.get('action')
    if action:
        logs = logs.filter(action=action)

    # Filter by date range if specified
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date and end_date:
        logs = logs.filter(created_at__range=[start_date, end_date])

    # Pagination
    paginator = Paginator(logs, 20)  # Show 20 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'users': User.objects.all(),
        'modules': AuditLog.objects.values_list('module', flat=True).distinct(),
        'actions': [choice[0] for choice in AuditLog.ACTION_CHOICES],
    }

    return render(request, 'system_settings/audit_logs.html', context)

@login_required
def system_actions(request):
    """View for viewing system actions that can be undone"""
    # Only show actions that can be undone and are not already undone
    actions = SystemAction.objects.filter(can_undo=True).exclude(status='undone').order_by('-created_at')

    # Filter by user if specified
    user_id = request.GET.get('user_id')
    if user_id:
        actions = actions.filter(user_id=user_id)

    # Filter by module if specified
    module = request.GET.get('module')
    if module:
        actions = actions.filter(module=module)

    # Filter by action type if specified
    action_type = request.GET.get('action_type')
    if action_type:
        actions = actions.filter(action_type=action_type)

    # Filter by date range if specified
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date and end_date:
        actions = actions.filter(created_at__range=[start_date, end_date])

    # Pagination
    paginator = Paginator(actions, 20)  # Show 20 actions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get distinct modules and map them to their display names
    modules_list = []
    for module in SystemAction.objects.values_list('module', flat=True).distinct():
        for code, name in SystemAction.MODULE_CHOICES:
            if code == module:
                modules_list.append((module, name))
                break
        else:
            modules_list.append((module, module))

    context = {
        'page_obj': page_obj,
        'users': User.objects.all(),
        'modules': modules_list,
        'action_types': SystemAction.ACTION_TYPES,
    }

    return render(request, 'system_settings/system_actions.html', context)


@login_required
def undo_action(request, action_id):
    """View for undoing a system action"""
    # Check if user has admin permissions
    if not request.user.is_superuser and not UserProfile.objects.filter(user=request.user, role='admin').exists():
        messages.error(request, 'ليس لديك صلاحية للقيام بهذا الإجراء')
        return redirect('system_actions')

    action = get_object_or_404(SystemAction, id=action_id)

    # Check if action can be undone
    if not action.can_undo or action.status == 'undone':
        messages.error(request, 'لا يمكن التراجع عن هذا الإجراء')
        return redirect('system_actions')

    if request.method == 'POST':
        try:
            # Perform the undo operation based on action type
            if action.action_type == 'create':
                # For create actions, we delete the created object
                if action.module == 'user_management':
                    user = User.objects.get(id=action.record_id)
                    user.delete()
                elif action.module == 'worker_settings':
                    setting = WorkerSetting.objects.get(id=action.record_id)
                    setting.delete()
                elif action.module == 'currencies':
                    currency = Currency.objects.get(id=action.record_id)
                    currency.delete()
                elif action.module == 'workers':
                    from workers.models import Worker
                    worker = Worker.objects.get(id=action.record_id)
                    worker.delete()
                elif action.module == 'contracts':
                    from contracts.models import Contract
                    contract = Contract.objects.get(id=action.record_id)
                    contract.delete()
                # Add more modules as needed

            elif action.action_type == 'update':
                # For update actions, we restore the previous data
                if action.module == 'workers':
                    from workers.models import Worker
                    worker = Worker.objects.get(id=action.record_id)
                    previous_data = json.loads(action.previous_data) if action.previous_data else {}
                    if previous_data:
                        if 'first_name' in previous_data:
                            worker.first_name = previous_data['first_name']
                        if 'last_name' in previous_data:
                            worker.last_name = previous_data['last_name']
                        if 'nationality' in previous_data:
                            worker.nationality = previous_data['nationality']
                        if 'passport_number' in previous_data:
                            worker.passport_number = previous_data['passport_number']
                        if 'passport_expiry' in previous_data and previous_data['passport_expiry']:
                            worker.passport_expiry = timezone.datetime.fromisoformat(previous_data['passport_expiry'])
                        if 'visa_number' in previous_data:
                            worker.visa_number = previous_data['visa_number']
                        if 'visa_expiry' in previous_data and previous_data['visa_expiry']:
                            worker.visa_expiry = timezone.datetime.fromisoformat(previous_data['visa_expiry'])
                        if 'date_of_birth' in previous_data and previous_data['date_of_birth']:
                            worker.date_of_birth = timezone.datetime.fromisoformat(previous_data['date_of_birth'])
                        if 'phone_number' in previous_data:
                            worker.phone_number = previous_data['phone_number']
                        if 'address' in previous_data:
                            worker.address = previous_data['address']
                        if 'skills' in previous_data:
                            worker.skills = previous_data['skills']
                        if 'is_active' in previous_data:
                            worker.is_active = previous_data['is_active']
                        if 'status' in previous_data:
                            worker.status = previous_data['status']
                        if 'date_joined' in previous_data and previous_data['date_joined']:
                            worker.date_joined = timezone.datetime.fromisoformat(previous_data['date_joined'])
                        worker.save()
                elif action.module == 'contracts':
                    from contracts.models import Contract
                    contract = Contract.objects.get(id=action.record_id)
                    previous_data = json.loads(action.previous_data) if action.previous_data else {}
                    if previous_data:
                        if 'contract_number' in previous_data:
                            contract.contract_number = previous_data['contract_number']
                        if 'client_name' in previous_data:
                            contract.client_name = previous_data['client_name']
                        if 'start_date' in previous_data and previous_data['start_date']:
                            contract.start_date = timezone.datetime.fromisoformat(previous_data['start_date'])
                        if 'end_date' in previous_data and previous_data['end_date']:
                            contract.end_date = timezone.datetime.fromisoformat(previous_data['end_date'])
                        if 'contract_value' in previous_data:
                            contract.contract_value = previous_data['contract_value']
                        if 'payment_terms' in previous_data:
                            contract.payment_terms = previous_data['payment_terms']
                        if 'status' in previous_data:
                            contract.status = previous_data['status']
                        if 'description' in previous_data:
                            contract.description = previous_data['description']
                        contract.save()

                        # Update workers
                        if 'workers' in previous_data:
                            from workers.models import Worker
                            # Clear current workers
                            contract.workers.clear()
                            # Add workers from previous data
                            for worker_id in previous_data['workers']:
                                try:
                                    worker = Worker.objects.get(id=worker_id)
                                    contract.workers.add(worker)
                                except Worker.DoesNotExist:
                                    pass
                elif action.module == 'user_management':
                    user = User.objects.get(id=action.record_id)
                    previous_data = json.loads(action.previous_data) if action.previous_data else {}
                    if previous_data:
                        if 'username' in previous_data:
                            user.username = previous_data['username']
                        if 'email' in previous_data:
                            user.email = previous_data['email']
                        if 'first_name' in previous_data:
                            user.first_name = previous_data['first_name']
                        if 'last_name' in previous_data:
                            user.last_name = previous_data['last_name']
                        if 'is_active' in previous_data:
                            user.is_active = previous_data['is_active']
                        if 'is_staff' in previous_data:
                            user.is_staff = previous_data['is_staff']
                        user.save()

                        # Also update profile if exists
                        if 'profile' in previous_data:
                            profile_data = previous_data['profile']
                            profile, _ = UserProfile.objects.get_or_create(user=user)
                            if 'role' in profile_data:
                                profile.role = profile_data['role']
                            if 'phone' in profile_data:
                                profile.phone = profile_data['phone']
                            if 'address' in profile_data:
                                profile.address = profile_data['address']
                            if 'is_active' in profile_data:
                                profile.is_active = profile_data['is_active']
                            profile.save()
                elif action.module == 'worker_settings':
                    setting = WorkerSetting.objects.get(id=action.record_id)
                    previous_data = json.loads(action.previous_data) if action.previous_data else {}
                    if previous_data:
                        if 'setting_key' in previous_data:
                            setting.setting_key = previous_data['setting_key']
                        if 'setting_value' in previous_data:
                            setting.setting_value = previous_data['setting_value']
                        if 'description' in previous_data:
                            setting.description = previous_data['description']
                        if 'is_active' in previous_data:
                            setting.is_active = previous_data['is_active']
                        setting.save()
                elif action.module == 'currencies':
                    currency = Currency.objects.get(id=action.record_id)
                    previous_data = json.loads(action.previous_data) if action.previous_data else {}
                    if previous_data:
                        if 'code' in previous_data:
                            currency.code = previous_data['code']
                        if 'name' in previous_data:
                            currency.name = previous_data['name']
                        if 'symbol' in previous_data:
                            currency.symbol = previous_data['symbol']
                        if 'exchange_rate' in previous_data:
                            currency.exchange_rate = previous_data['exchange_rate']
                        if 'is_default' in previous_data:
                            currency.is_default = previous_data['is_default']
                        if 'is_active' in previous_data:
                            currency.is_active = previous_data['is_active']
                        currency.save()
                # Add more modules as needed

            elif action.action_type == 'delete':
                # For delete actions, we recreate the object with the saved data
                if action.module == 'user_management' and action.previous_data:
                    user_data = json.loads(action.previous_data)
                    user = User.objects.create(
                        username=user_data.get('username', ''),
                        email=user_data.get('email', ''),
                        first_name=user_data.get('first_name', ''),
                        last_name=user_data.get('last_name', ''),
                        is_active=user_data.get('is_active', True),
                        is_staff=user_data.get('is_staff', False)
                    )

                    # Also recreate profile if exists
                    if 'profile' in user_data:
                        profile_data = user_data['profile']
                        profile = UserProfile.objects.create(
                            user=user,
                            role=profile_data.get('role', 'staff'),
                            phone=profile_data.get('phone', ''),
                            address=profile_data.get('address', ''),
                            is_active=profile_data.get('is_active', True)
                        )
                elif action.module == 'worker_settings' and action.previous_data:
                    try:
                        # Parse the previous data
                        setting_data = json.loads(action.previous_data)
                        setting_key = setting_data.get('setting_key', '')

                        # Log the data we're working with for debugging
                        print(f"Attempting to restore worker setting: {setting_key}")
                        print(f"Setting data: {setting_data}")

                        # Check if a setting with this key already exists
                        existing_setting = WorkerSetting.objects.filter(setting_key=setting_key).first()

                        if existing_setting:
                            # Update existing setting
                            existing_setting.setting_value = setting_data.get('setting_value', '')
                            existing_setting.description = setting_data.get('description', '')
                            existing_setting.is_active = setting_data.get('is_active', True)
                            existing_setting.save()

                            # Log restoration of existing setting
                            print(f"Updated existing setting: {existing_setting.setting_key}")
                        else:
                            # Create new setting with explicit values
                            new_setting = WorkerSetting(
                                setting_key=setting_key,
                                setting_value=setting_data.get('setting_value', ''),
                                description=setting_data.get('description', ''),
                                is_active=setting_data.get('is_active', True)
                            )
                            new_setting.save()
                            print(f"Created new setting: {new_setting.setting_key} with value: {new_setting.setting_value}")

                            # Double-check that the setting was created
                            check_setting = WorkerSetting.objects.filter(setting_key=setting_key).first()
                            if check_setting:
                                print(f"Verified setting exists: {check_setting.setting_key} with value: {check_setting.setting_value}")
                            else:
                                print(f"WARNING: Failed to create setting: {setting_key}")
                    except Exception as e:
                        print(f"Error restoring worker setting: {str(e)}")
                        raise
                elif action.module == 'currencies' and action.previous_data:
                    currency_data = json.loads(action.previous_data)
                    Currency.objects.create(
                        code=currency_data.get('code', ''),
                        name=currency_data.get('name', ''),
                        symbol=currency_data.get('symbol', ''),
                        exchange_rate=currency_data.get('exchange_rate', 1.0),
                        is_default=currency_data.get('is_default', False),
                        is_active=currency_data.get('is_active', True)
                    )
                elif action.module == 'workers' and action.previous_data:
                    from workers.models import Worker
                    worker_data = json.loads(action.previous_data)
                    Worker.objects.create(
                        first_name=worker_data.get('first_name', ''),
                        last_name=worker_data.get('last_name', ''),
                        nationality=worker_data.get('nationality', ''),
                        passport_number=worker_data.get('passport_number', ''),
                        passport_expiry=worker_data.get('passport_expiry') if worker_data.get('passport_expiry') else None,
                        visa_number=worker_data.get('visa_number', ''),
                        visa_expiry=worker_data.get('visa_expiry') if worker_data.get('visa_expiry') else None,
                        date_of_birth=worker_data.get('date_of_birth') if worker_data.get('date_of_birth') else None,
                        phone_number=worker_data.get('phone_number', ''),
                        address=worker_data.get('address', ''),
                        skills=worker_data.get('skills', ''),
                        is_active=worker_data.get('is_active', True),
                        status=worker_data.get('status', 'active'),
                        date_joined=worker_data.get('date_joined') if worker_data.get('date_joined') else timezone.now()
                    )
                elif action.module == 'contracts' and action.previous_data:
                    from contracts.models import Contract
                    contract_data = json.loads(action.previous_data)
                    contract = Contract.objects.create(
                        contract_number=contract_data.get('contract_number', ''),
                        client_name=contract_data.get('client_name', ''),
                        start_date=timezone.datetime.fromisoformat(contract_data.get('start_date')) if contract_data.get('start_date') else None,
                        end_date=timezone.datetime.fromisoformat(contract_data.get('end_date')) if contract_data.get('end_date') else None,
                        contract_value=contract_data.get('contract_value', '0'),
                        payment_terms=contract_data.get('payment_terms', ''),
                        status=contract_data.get('status', 'active'),
                        description=contract_data.get('description', ''),
                        date_created=timezone.now()
                    )

                    # Add workers to contract
                    if 'workers' in contract_data and contract_data['workers']:
                        from workers.models import Worker
                        for worker_id in contract_data['workers']:
                            try:
                                worker = Worker.objects.get(id=worker_id)
                                contract.workers.add(worker)
                            except Worker.DoesNotExist:
                                pass
                # Add more modules as needed

            # Update action status
            action.status = 'undone'
            action.undone_at = timezone.now()
            action.undone_by = request.user
            action.save()

            # Log the undo action with more details
            action_type_display = dict(SystemAction.ACTION_TYPES).get(action.action_type, action.action_type)
            module_display = action.get_module_display()

            # Create detailed audit log
            AuditLog.objects.create(
                user=request.user,
                action='other',
                module=action.module,
                record_id=action.record_id,
                description=f'تم التراجع عن إجراء {action_type_display} في {module_display}: {action.description}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            # Create success message with more details
            success_message = f'تم التراجع عن الإجراء بنجاح: {action.description}'
            messages.success(request, success_message)
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء التراجع عن الإجراء: {str(e)}')

        return redirect('system_actions')

    # Prepare data for template
    action_details = False
    previous_data = {}
    current_data = {}

    # Parse JSON data if available
    if action.previous_data:
        try:
            previous_data_raw = json.loads(action.previous_data)
            if isinstance(previous_data_raw, dict):
                previous_data = previous_data_raw
                action_details = True
        except json.JSONDecodeError:
            pass

    if action.current_data:
        try:
            current_data_raw = json.loads(action.current_data)
            if isinstance(current_data_raw, dict):
                current_data = current_data_raw
                action_details = True
        except json.JSONDecodeError:
            pass

    # Translate field names to Arabic for better display
    field_translations = {
        # Common fields
        'first_name': 'الاسم الأول',
        'last_name': 'الاسم الأخير',
        'username': 'اسم المستخدم',
        'email': 'البريد الإلكتروني',
        'is_active': 'نشط',
        'date_joined': 'تاريخ الانضمام',
        'phone_number': 'رقم الهاتف',
        'address': 'العنوان',
        'status': 'الحالة',
        'description': 'الوصف',

        # Worker fields
        'nationality': 'الجنسية',
        'passport_number': 'رقم جواز السفر',
        'passport_expiry': 'تاريخ انتهاء جواز السفر',
        'visa_number': 'رقم التأشيرة',
        'visa_expiry': 'تاريخ انتهاء التأشيرة',
        'date_of_birth': 'تاريخ الميلاد',
        'skills': 'المهارات',

        # Contract fields
        'contract_number': 'رقم العقد',
        'client_name': 'اسم العميل',
        'start_date': 'تاريخ البدء',
        'end_date': 'تاريخ الانتهاء',
        'contract_value': 'قيمة العقد',
        'payment_terms': 'شروط الدفع',
        'workers': 'العمال',

        # Currency fields
        'code': 'الرمز',
        'name': 'الاسم',
        'symbol': 'الرمز',
        'exchange_rate': 'سعر الصرف',
        'is_default': 'افتراضي',

        # Settings fields
        'setting_key': 'مفتاح الإعداد',
        'setting_value': 'قيمة الإعداد',
    }

    # Translate field names
    translated_previous_data = {}
    translated_current_data = {}

    for key, value in previous_data.items():
        translated_key = field_translations.get(key, key)
        translated_previous_data[translated_key] = value

    for key, value in current_data.items():
        translated_key = field_translations.get(key, key)
        translated_current_data[translated_key] = value

    context = {
        'action': action,
        'action_details': action_details,
        'previous_data': translated_previous_data,
        'current_data': translated_current_data
    }

    return render(request, 'system_settings/undo_action.html', context)


@login_required
def all_notifications(request):
    """View for viewing all notifications"""
    # En un sistema real, estas notificaciones vendrían de una base de datos
    # Aquí creamos algunas notificaciones de ejemplo
    notifications = [
        {
            'id': 1,
            'title': 'تنبيه انتهاء جواز سفر',
            'message': 'جواز سفر العامل أحمد محمد سينتهي خلال 30 يوم.',
            'date': timezone.now() - timedelta(days=3),
            'type': 'warning',
            'is_read': False
        },
        {
            'id': 2,
            'title': 'تنبيه انتهاء عقد',
            'message': 'عقد شركة الفيصل للخدمات سينتهي خلال 15 يوم.',
            'date': timezone.now() - timedelta(days=5),
            'type': 'warning',
            'is_read': True
        },
        {
            'id': 3,
            'title': 'خدمة تنظيف مجدولة',
            'message': 'تم جدولة خدمة تنظيف لشركة النور غداً الساعة 10 صباحاً.',
            'date': timezone.now() - timedelta(days=7),
            'type': 'info',
            'is_read': False
        },
        {
            'id': 4,
            'title': 'تم إضافة عامل جديد',
            'message': 'تم إضافة العامل محمد علي إلى النظام.',
            'date': timezone.now() - timedelta(days=10),
            'type': 'success',
            'is_read': True
        },
        {
            'id': 5,
            'title': 'تحديث بيانات عقد',
            'message': 'تم تحديث بيانات عقد شركة الأمل للخدمات.',
            'date': timezone.now() - timedelta(days=12),
            'type': 'info',
            'is_read': True
        },
        {
            'id': 6,
            'title': 'تنبيه انتهاء تأشيرة',
            'message': 'تأشيرة العامل محمود أحمد ستنتهي خلال 20 يوم.',
            'date': timezone.now() - timedelta(days=15),
            'type': 'warning',
            'is_read': False
        },
        {
            'id': 7,
            'title': 'تم إلغاء خدمة تنظيف',
            'message': 'تم إلغاء خدمة التنظيف المجدولة لشركة السلام.',
            'date': timezone.now() - timedelta(days=18),
            'type': 'danger',
            'is_read': True
        },
        {
            'id': 8,
            'title': 'تم تجديد عقد',
            'message': 'تم تجديد عقد شركة النور للخدمات لمدة سنة إضافية.',
            'date': timezone.now() - timedelta(days=20),
            'type': 'success',
            'is_read': True
        },
    ]

    # Filtrar por tipo si se especifica
    notification_type = request.GET.get('type')
    if notification_type:
        notifications = [n for n in notifications if n['type'] == notification_type]

    # Filtrar por estado de lectura si se especifica
    is_read = request.GET.get('is_read')
    if is_read is not None:
        is_read_bool = is_read.lower() == 'true'
        notifications = [n for n in notifications if n['is_read'] == is_read_bool]

    # Ordenar por fecha (más reciente primero)
    notifications.sort(key=lambda x: x['date'], reverse=True)

    # Paginación
    paginator = Paginator(notifications, 10)  # 10 notificaciones por página
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'notification_types': ['warning', 'info', 'success', 'danger'],
        'unread_count': len([n for n in notifications if not n['is_read']]),
    }

    return render(request, 'system_settings/all_notifications.html', context)

@login_required
def user_profile(request):
    """View for viewing and editing user's own profile"""
    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Handle user form
        user = request.user
        user.first_name = request.POST.get('first_name')
        user.last_name = request.POST.get('last_name')
        user.email = request.POST.get('email')

        # Set password if provided
        password = request.POST.get('password')
        if password:
            user.set_password(password)

        user.save()

        # Handle profile form
        profile_form = UserProfileForm(request.POST, request.FILES, instance=profile)
        if profile_form.is_valid():
            profile_form.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            module='user_profile',
            record_id=user.id,
            description=f'تم تحديث الملف الشخصي',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, 'تم تحديث الملف الشخصي بنجاح')
        return redirect('user_profile')
    else:
        profile_form = UserProfileForm(instance=profile)

    # Get recent audit logs for this user
    audit_logs = AuditLog.objects.filter(user=request.user).order_by('-created_at')[:5]

    context = {
        'profile_form': profile_form,
        'profile': profile,
        'audit_logs': audit_logs,
    }

    return render(request, 'system_settings/user_profile.html', context)

@login_required
def logout_view(request):
    """View for logging out"""
    # Log the action
    AuditLog.objects.create(
        user=request.user,
        action='logout',
        module='authentication',
        description='تم تسجيل الخروج من النظام',
        ip_address=request.META.get('REMOTE_ADDR')
    )

    # Logout the user
    logout(request)

    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('login')

@login_required
def currencies(request):
    """View for managing currencies"""
    currencies_list = Currency.objects.all().order_by('-is_default', 'code')

    context = {
        'currencies': currencies_list,
    }

    return render(request, 'system_settings/currencies.html', context)

@login_required
def create_currency(request):
    """View for creating a new currency"""
    if request.method == 'POST':
        form = CurrencyForm(request.POST)
        if form.is_valid():
            currency = form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                module='currencies',
                record_id=currency.id,
                description=f'تم إنشاء عملة جديدة: {currency.name} ({currency.code})',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم إنشاء العملة {currency.name} بنجاح')
            return redirect('currencies')
    else:
        form = CurrencyForm()

    context = {
        'form': form,
        'title': 'إضافة عملة جديدة',
    }

    return render(request, 'system_settings/currency_form.html', context)

@login_required
def edit_currency(request, currency_id):
    """View for editing a currency"""
    currency = get_object_or_404(Currency, id=currency_id)

    if request.method == 'POST':
        form = CurrencyForm(request.POST, instance=currency)
        if form.is_valid():
            currency = form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='update',
                module='currencies',
                record_id=currency.id,
                description=f'تم تحديث العملة: {currency.name} ({currency.code})',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'تم تحديث العملة {currency.name} بنجاح')
            return redirect('currencies')
    else:
        form = CurrencyForm(instance=currency)

    context = {
        'form': form,
        'currency': currency,
        'title': f'تعديل العملة: {currency.name}',
    }

    return render(request, 'system_settings/currency_form.html', context)

@login_required
def delete_currency(request, currency_id):
    """View for deleting a currency"""
    currency = get_object_or_404(Currency, id=currency_id)

    # Don't allow deleting the default currency
    if currency.is_default:
        messages.error(request, 'لا يمكن حذف العملة الافتراضية')
        return redirect('currencies')

    if request.method == 'POST':
        # Log the action before deleting
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            module='currencies',
            record_id=currency.id,
            description=f'تم حذف العملة: {currency.name} ({currency.code})',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        currency.delete()
        messages.success(request, f'تم حذف العملة {currency.name} بنجاح')
        return redirect('currencies')

    context = {
        'currency': currency,
    }

    return render(request, 'system_settings/delete_currency.html', context)

@login_required
def set_default_currency(request, currency_id):
    """View for setting a currency as default"""
    currency = get_object_or_404(Currency, id=currency_id)

    # Already default
    if currency.is_default:
        messages.info(request, f'العملة {currency.name} هي بالفعل العملة الافتراضية')
        return redirect('currencies')

    # Set as default (this will unset any other default currency due to the save method)
    currency.is_default = True
    currency.save()

    # Log the action
    AuditLog.objects.create(
        user=request.user,
        action='update',
        module='currencies',
        record_id=currency.id,
        description=f'تم تعيين العملة {currency.name} ({currency.code}) كعملة افتراضية',
        ip_address=request.META.get('REMOTE_ADDR')
    )

    messages.success(request, f'تم تعيين العملة {currency.name} كعملة افتراضية بنجاح')
    return redirect('currencies')


@login_required
def recruitment_companies(request):
    """View for managing recruitment companies"""
    companies = RecruitmentCompany.objects.all().order_by('name')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        companies = companies.filter(
            Q(name__icontains=search_query) |
            Q(country__icontains=search_query) |
            Q(contact_person__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(companies, 10)  # Show 10 companies per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'title': 'إدارة شركات الاستقدام'
    }

    return render(request, 'system_settings/recruitment_companies.html', context)


@login_required
def create_recruitment_company(request):
    """View for creating a new recruitment company"""
    if request.method == 'POST':
        form = RecruitmentCompanyForm(request.POST)
        if form.is_valid():
            company = form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                module='recruitment_companies',
                record_id=company.id,
                description=f'تم إضافة شركة استقدام: {company.name}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم إضافة شركة الاستقدام بنجاح')
            return redirect('recruitment_companies')
    else:
        form = RecruitmentCompanyForm()

    context = {
        'form': form,
        'title': 'إضافة شركة استقدام جديدة'
    }

    return render(request, 'system_settings/create_recruitment_company.html', context)


@login_required
def edit_recruitment_company(request, company_id):
    """View for editing a recruitment company"""
    company = get_object_or_404(RecruitmentCompany, id=company_id)

    if request.method == 'POST':
        form = RecruitmentCompanyForm(request.POST, instance=company)
        if form.is_valid():
            # Save the form
            company = form.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='update',
                module='recruitment_companies',
                record_id=company.id,
                description=f'تم تعديل شركة الاستقدام: {company.name}',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, 'تم تعديل شركة الاستقدام بنجاح')
            return redirect('recruitment_companies')
    else:
        form = RecruitmentCompanyForm(instance=company)

    context = {
        'form': form,
        'company': company,
        'title': f'تعديل شركة الاستقدام: {company.name}'
    }

    return render(request, 'system_settings/edit_recruitment_company.html', context)


@login_required
def delete_recruitment_company(request, company_id):
    """View for deleting a recruitment company"""
    company = get_object_or_404(RecruitmentCompany, id=company_id)

    # Check if the company has workers
    if company.workers.exists():
        messages.error(request, 'لا يمكن حذف شركة الاستقدام لأنها مرتبطة بعمال')
        return redirect('recruitment_companies')

    if request.method == 'POST':
        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            module='recruitment_companies',
            record_id=company.id,
            description=f'تم حذف شركة الاستقدام: {company.name}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # Delete the company
        company.delete()

        messages.success(request, 'تم حذف شركة الاستقدام بنجاح')
        return redirect('recruitment_companies')

    context = {
        'company': company,
        'title': 'حذف شركة الاستقدام'
    }

    return render(request, 'system_settings/delete_recruitment_company.html', context)
