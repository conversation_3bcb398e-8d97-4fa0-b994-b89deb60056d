<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المصادقة الثنائية - المسؤول الأعلى</title>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- SweetAlert2 -->
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .security-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
        }
        
        .verification-card {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(239, 68, 68, 0.2);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }
        
        .code-input {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(239, 68, 68, 0.3);
            transition: all 0.3s ease;
            font-size: 24px;
            text-align: center;
            font-weight: bold;
        }
        
        .code-input:focus {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            background: rgba(30, 41, 59, 0.9);
        }
        
        .verify-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transition: all 0.3s ease;
        }
        
        .verify-btn:hover {
            background: linear-gradient(135deg, #b91c1c, #991b1b);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(220, 38, 38, 0.4);
        }
        
        .countdown {
            color: #fbbf24;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="security-pattern">

    <!-- Security Warning Banner -->
    <div class="bg-red-600 text-white text-center py-3 text-sm font-bold">
        <i class="fas fa-shield-alt ml-2"></i>
        مصادقة ثنائية آمنة - تحقق من بريدك الإلكتروني
        <i class="fas fa-envelope ml-2"></i>
    </div>

    <div class="min-h-screen flex items-center justify-center p-6">
        <div class="verification-card rounded-2xl p-8 w-full max-w-md">
            
            <!-- Header -->
            <div class="text-center mb-8">
                {% load static %}
                <div class="relative mb-6">
                    <div class="pulse-animation absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-lock text-6xl text-red-400 opacity-20"></i>
                    </div>
                    <img src="{% static 'images/logo.webp' %}" alt="استقدامي" class="h-16 w-auto mx-auto relative z-10">
                </div>
                
                <h1 class="text-2xl font-bold text-white mb-2">
                    <i class="fas fa-key text-red-400 ml-2"></i>
                    المصادقة الثنائية
                </h1>
                <p class="text-gray-300 text-sm">أدخل رمز التحقق المرسل إلى بريدك الإلكتروني</p>
                
                <div class="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-3 mt-4">
                    <p class="text-blue-200 text-xs">
                        <i class="fas fa-info-circle ml-2"></i>
                        تم إرسال رمز التحقق إلى: <EMAIL>
                    </p>
                </div>
            </div>

            <!-- Verification Form -->
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Verification Code -->
                <div>
                    <label class="block text-gray-300 text-sm font-medium mb-3 text-center">
                        <i class="fas fa-shield-alt ml-2"></i>
                        رمز التحقق (6 أرقام)
                    </label>
                    <div class="flex justify-center space-x-2 space-x-reverse">
                        <input type="text" 
                               name="verification_code" 
                               id="verification_code"
                               maxlength="6"
                               required
                               class="code-input w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:outline-none"
                               placeholder="000000"
                               autocomplete="off">
                    </div>
                </div>

                <!-- Countdown Timer -->
                <div class="text-center">
                    <p class="text-gray-400 text-sm">
                        انتهاء صلاحية الرمز خلال: 
                        <span class="countdown" id="countdown">01:00</span>
                    </p>
                </div>

                <!-- Verify Button -->
                <button type="submit" 
                        class="verify-btn w-full py-3 px-6 rounded-lg text-white font-bold text-lg">
                    <i class="fas fa-check-circle ml-2"></i>
                    تحقق من الرمز
                </button>

                <!-- Resend Code -->
                <div class="text-center">
                    <button type="button" 
                            id="resend-btn"
                            onclick="resendCode()"
                            class="text-blue-400 hover:text-blue-300 text-sm underline disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        <i class="fas fa-redo ml-2"></i>
                        إعادة إرسال الرمز
                    </button>
                </div>

                <!-- Messages -->
                {% if messages %}
                    <div class="mt-4">
                        {% for message in messages %}
                            <div class="bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-900 bg-opacity-50 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-600 text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% else %}blue{% endif %}-200 px-4 py-3 rounded-lg text-sm">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} ml-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </form>

            <!-- Back to Login -->
            <div class="mt-8 text-center">
                <a href="{% url 'super_admin:super_admin_login' %}" 
                   class="text-gray-400 hover:text-white text-sm transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة لتسجيل الدخول
                </a>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-gray-400 text-xs">
                    <i class="fas fa-shield-alt ml-1"></i>
                    محمي بنظام أمان متعدد الطبقات
                </p>
                <p class="text-gray-500 text-xs mt-2">
                    © {{ "now"|date:"Y" }} منصة استقدامي السحابية
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Countdown Timer
        let timeLeft = 60; // 1 minute
        const countdownElement = document.getElementById('countdown');
        const resendBtn = document.getElementById('resend-btn');

        function updateCountdown() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                countdownElement.textContent = 'انتهت الصلاحية';
                countdownElement.classList.add('text-red-400');
                resendBtn.disabled = false;
                return;
            }
            
            timeLeft--;
            setTimeout(updateCountdown, 1000);
        }

        // Start countdown
        updateCountdown();

        // Auto-focus on code input
        document.getElementById('verification_code').focus();

        // Auto-submit when 6 digits entered
        document.getElementById('verification_code').addEventListener('input', function(e) {
            if (e.target.value.length === 6) {
                // Auto-submit after a short delay
                setTimeout(() => {
                    document.querySelector('form').submit();
                }, 500);
            }
        });

        // Resend code function
        function resendCode() {
            Swal.fire({
                title: 'إعادة إرسال الرمز',
                text: 'هل تريد إعادة إرسال رمز التحقق؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، أعد الإرسال',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Redirect to resend endpoint
                    window.location.href = "{% url 'super_admin:resend_2fa_code' %}";
                }
            });
        }

        // Security monitoring
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent right-click
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير أمني',
                    text: 'العمليات محمية في هذه المنطقة',
                    confirmButtonColor: '#dc2626'
                });
            });

            // Prevent F12
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'وصول مرفوض',
                        text: 'أدوات المطور محظورة في هذه المنطقة',
                        confirmButtonColor: '#dc2626'
                    });
                }
            });
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const code = document.getElementById('verification_code').value;

            if (!code || code.length !== 6) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الرمز',
                    text: 'يرجى إدخال رمز التحقق المكون من 6 أرقام',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري التحقق...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });
        });
    </script>
</body>
</html>
