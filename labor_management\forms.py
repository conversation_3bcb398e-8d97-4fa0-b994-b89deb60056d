from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import Company, Worker, Client, Contract, DailyService, Daily24HService, CustomService, BloodTest, SponsorshipTransfer, WorkerOperation, WorkerTransfer, Branch

class SuperAdminLoginForm(forms.Form):
    """Form for super admin login."""
    username = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المستخدم'}),
        label='اسم المستخدم'
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'كلمة المرور'}),
        label='كلمة المرور'
    )
    secure_code = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز الدخول الآمن'}),
        label='رمز الدخول الآمن'
    )

class CompanyForm(forms.ModelForm):
    """Form for creating and editing companies."""
    class Meta:
        model = Company
        fields = [
            'name', 'serial_number', 'logo', 'email', 'phone', 'address', 'status',
            'db_name', 'db_user', 'db_password', 'db_host', 'db_port',
            'admin_username', 'admin_password', 'admin_email'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'serial_number': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'db_name': forms.TextInput(attrs={'class': 'form-control'}),
            'db_user': forms.TextInput(attrs={'class': 'form-control'}),
            'db_password': forms.PasswordInput(attrs={'class': 'form-control'}),
            'db_host': forms.TextInput(attrs={'class': 'form-control'}),
            'db_port': forms.NumberInput(attrs={'class': 'form-control'}),
            'admin_username': forms.TextInput(attrs={'class': 'form-control'}),
            'admin_password': forms.PasswordInput(attrs={'class': 'form-control'}),
            'admin_email': forms.EmailInput(attrs={'class': 'form-control'}),
        }
        labels = {
            'name': 'اسم الشركة',
            'serial_number': 'الرقم التسلسلي',
            'logo': 'شعار الشركة',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'address': 'العنوان',
            'status': 'الحالة',
            'db_name': 'اسم قاعدة البيانات',
            'db_user': 'اسم المستخدم',
            'db_password': 'كلمة المرور',
            'db_host': 'المضيف',
            'db_port': 'المنفذ',
            'admin_username': 'اسم المستخدم',
            'admin_password': 'كلمة المرور',
            'admin_email': 'البريد الإلكتروني',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make admin_password not required when editing
        if self.instance.pk:
            self.fields['admin_password'].required = False

class WorkerForm(forms.ModelForm):
    """Form for creating and editing workers."""
    class Meta:
        model = Worker
        fields = '__all__'
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'passport_expiry': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'visa_expiry': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'iqama_expiry': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class ClientForm(forms.ModelForm):
    """Form for creating and editing clients."""
    class Meta:
        model = Client
        fields = '__all__'

class ContractForm(forms.ModelForm):
    """Form for creating and editing contracts."""
    class Meta:
        model = Contract
        fields = '__all__'
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class DailyServiceForm(forms.ModelForm):
    """Form for creating and editing daily services."""
    class Meta:
        model = DailyService
        fields = [
            'service_number', 'client', 'workers', 'service_date', 'service_time',
            'hours', 'overtime_hours', 'workers_required', 'booking_type',
            'fee', 'overtime_fee', 'location', 'status', 'notes'
        ]
        widgets = {
            'service_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'service_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'hours': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '24'}),
            'overtime_hours': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '12'}),
            'workers_required': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'booking_type': forms.Select(attrs={'class': 'form-select'}),
            'fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'overtime_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'location': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter active workers only
        self.fields['workers'].queryset = Worker.objects.filter(status='active')

class Daily24HServiceForm(forms.ModelForm):
    """Form for creating and editing 24-hour services."""
    class Meta:
        model = Daily24HService
        fields = [
            'service_number', 'client', 'workers', 'start_date', 'start_time',
            'end_date', 'workers_required', 'fee', 'location', 'status', 'notes'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'workers_required': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'location': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter active workers only
        self.fields['workers'].queryset = Worker.objects.filter(status='active')

class CustomServiceForm(forms.ModelForm):
    """Form for creating and editing custom services."""
    class Meta:
        model = CustomService
        fields = [
            'service_number', 'client', 'workers', 'start_date', 'end_date',
            'service_days', 'service_time', 'duration_hours', 'workers_required',
            'fee', 'location', 'status', 'notes'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'service_days': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: 1,5,10,15,20,25 (أرقام الأيام مفصولة بفواصل)'}),
            'service_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'duration_hours': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '24'}),
            'workers_required': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'location': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': '3'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter active workers only
        self.fields['workers'].queryset = Worker.objects.filter(status='active')

class BloodTestForm(forms.ModelForm):
    """Form for creating and editing blood tests."""
    class Meta:
        model = BloodTest
        fields = '__all__'
        widgets = {
            'test_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'result_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class SponsorshipTransferForm(forms.ModelForm):
    """Form for creating and editing sponsorship transfers."""
    class Meta:
        model = SponsorshipTransfer
        fields = '__all__'
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class WorkerOperationForm(forms.ModelForm):
    """Form for creating and editing worker operations."""
    class Meta:
        model = WorkerOperation
        fields = '__all__'
        widgets = {
            'operation_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class WorkerTransferForm(forms.ModelForm):
    """Form for creating and editing worker transfers between branches."""
    class Meta:
        model = WorkerTransfer
        fields = '__all__'
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

class BranchForm(forms.ModelForm):
    """Form for creating and editing branches."""
    class Meta:
        model = Branch
        fields = '__all__'
