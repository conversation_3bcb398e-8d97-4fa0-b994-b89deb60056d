{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}استعادة النسخة الاحتياطية | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    .restore-card {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .restore-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .restore-steps {
        counter-reset: step;
    }

    .restore-step {
        position: relative;
        padding-right: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .restore-step::before {
        counter-increment: step;
        content: counter(step);
        position: absolute;
        right: 0;
        top: 0;
        width: 1.75rem;
        height: 1.75rem;
        background-color: #3b82f6;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .restore-progress {
        height: 0.5rem;
        border-radius: 0.25rem;
        overflow: hidden;
    }

    .restore-progress-bar {
        height: 100%;
        background: linear-gradient(to right, #3b82f6, #2563eb);
        transition: width 0.5s ease;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .pulse {
        animation: pulse 1.5s infinite;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-blue-800 flex items-center">
            <i class="fas fa-undo-alt text-blue-600 ml-3 text-2xl"></i>
            استعادة النسخة الاحتياطية
        </h2>
        <a href="{% url 'super_admin:system_backup' %}" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-colors shadow-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة للنسخ الاحتياطي
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md" role="alert">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
                {% endif %}
                <span class="block sm:inline">{{ message }}</span>
            </div>
        {% endfor %}
    {% endif %}

    <div class="restore-card bg-white p-6 rounded-xl border border-gray-200 shadow-md">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-green-400 to-green-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-undo-alt text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">استعادة النسخة الاحتياطية</h3>
                <p class="text-gray-500 text-sm mt-1">استعادة البيانات من النسخة الاحتياطية المحددة</p>
            </div>
        </div>

        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
            <div class="flex items-start">
                <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full ml-3 mt-1">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <h4 class="font-bold text-yellow-700">تنبيه هام</h4>
                    <p class="text-sm text-yellow-600 mt-2">استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. تأكد من أنك تريد المتابعة.</p>
                </div>
            </div>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
            <div class="flex items-center">
                <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h4 class="font-bold text-blue-700">معلومات النسخة الاحتياطية</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                        <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100">
                            <p class="text-sm text-gray-500">اسم الملف</p>
                            <p class="font-semibold text-gray-800">{{ backup.filename }}</p>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100">
                            <p class="text-sm text-gray-500">تاريخ الإنشاء</p>
                            <p class="font-semibold text-gray-800">{{ backup.created_at }}</p>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100">
                            <p class="text-sm text-gray-500">حجم الملف</p>
                            <p class="font-semibold text-gray-800">{{ backup.size_formatted }}</p>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100">
                            <p class="text-sm text-gray-500">نوع النسخة</p>
                            <p class="font-semibold text-gray-800">{{ backup.type|default:"نسخة كاملة" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="restore-steps mb-6">
            <div class="restore-step">
                <h4 class="font-bold text-gray-800">التحقق من النسخة الاحتياطية</h4>
                <p class="text-gray-600 text-sm mt-1">التحقق من سلامة ملف النسخة الاحتياطية قبل البدء في عملية الاستعادة.</p>
            </div>
            <div class="restore-step">
                <h4 class="font-bold text-gray-800">إنشاء نسخة احتياطية للبيانات الحالية</h4>
                <p class="text-gray-600 text-sm mt-1">إنشاء نسخة احتياطية من البيانات الحالية كإجراء احترازي.</p>
            </div>
            <div class="restore-step">
                <h4 class="font-bold text-gray-800">استعادة البيانات</h4>
                <p class="text-gray-600 text-sm mt-1">استعادة البيانات من النسخة الاحتياطية المحددة.</p>
            </div>
            <div class="restore-step">
                <h4 class="font-bold text-gray-800">التحقق من اكتمال الاستعادة</h4>
                <p class="text-gray-600 text-sm mt-1">التحقق من اكتمال عملية الاستعادة والتأكد من سلامة البيانات.</p>
            </div>
        </div>

        <div id="restore-progress-container" class="mb-6 hidden">
            <h4 class="font-bold text-gray-800 mb-2">تقدم الاستعادة</h4>
            <div class="restore-progress bg-gray-200">
                <div id="restore-progress-bar" class="restore-progress-bar" style="width: 0%"></div>
            </div>
            <p id="restore-status" class="text-sm text-gray-600 mt-2 pulse">جاري التحضير للاستعادة...</p>
        </div>

        <div class="flex justify-end space-x-3 space-x-reverse">
            <a href="{% url 'super_admin:system_backup' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">إلغاء</a>
            <button id="start-restore" type="button" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-2 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors flex items-center">
                <i class="fas fa-undo-alt ml-2"></i> بدء الاستعادة
            </button>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الاستعادة -->
<div id="confirm-restore-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4">
            <h3 class="text-lg font-bold">تأكيد استعادة النسخة الاحتياطية</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center mb-4 bg-red-50 p-4 rounded-lg border border-red-200">
                <div class="bg-red-100 text-red-600 p-2 rounded-full ml-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="text-red-700">تحذير: ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية. هل أنت متأكد من المتابعة؟</p>
            </div>
            <form id="restore-form" method="post" action="{% url 'super_admin:restore_backup' %}">
                {% csrf_token %}
                <input type="hidden" name="filename" value="{{ backup.filename }}">
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" id="cancel-restore" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">إلغاء</button>
                    <button type="submit" class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-red-700 transition-colors">تأكيد الاستعادة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startRestoreBtn = document.getElementById('start-restore');
        const confirmRestoreModal = document.getElementById('confirm-restore-modal');
        const cancelRestoreBtn = document.getElementById('cancel-restore');
        const restoreForm = document.getElementById('restore-form');
        const progressContainer = document.getElementById('restore-progress-container');
        const progressBar = document.getElementById('restore-progress-bar');
        const statusText = document.getElementById('restore-status');

        // عرض نافذة التأكيد
        startRestoreBtn.addEventListener('click', function() {
            confirmRestoreModal.classList.remove('hidden');
        });

        // إغلاق نافذة التأكيد
        cancelRestoreBtn.addEventListener('click', function() {
            confirmRestoreModal.classList.add('hidden');
        });

        // تقديم نموذج الاستعادة
        restoreForm.addEventListener('submit', function(e) {
            e.preventDefault();
            confirmRestoreModal.classList.add('hidden');
            progressContainer.classList.remove('hidden');
            startRestoreBtn.disabled = true;
            startRestoreBtn.classList.add('opacity-50', 'cursor-not-allowed');

            // محاكاة تقدم الاستعادة
            let progress = 0;
            const interval = setInterval(function() {
                progress += 5;
                progressBar.style.width = progress + '%';
                
                if (progress <= 25) {
                    statusText.textContent = 'جاري التحقق من النسخة الاحتياطية...';
                } else if (progress <= 50) {
                    statusText.textContent = 'جاري إنشاء نسخة احتياطية للبيانات الحالية...';
                } else if (progress <= 75) {
                    statusText.textContent = 'جاري استعادة البيانات...';
                } else if (progress < 100) {
                    statusText.textContent = 'جاري التحقق من اكتمال الاستعادة...';
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    statusText.textContent = 'تمت الاستعادة بنجاح!';
                    statusText.classList.remove('pulse');
                    
                    // إرسال النموذج فعليًا
                    setTimeout(function() {
                        restoreForm.submit();
                    }, 1000);
                }
            }, 200);
        });
    });
</script>
{% endblock %}
