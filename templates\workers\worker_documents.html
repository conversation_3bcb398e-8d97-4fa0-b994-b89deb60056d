{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}مستندات العامل{% endblock %}

{% block page_title %}مستندات العامل: {{ worker.first_name }} {{ worker.last_name }}{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--secondary-blue); text-decoration: none;">العمال</a></li>
<li style="margin: 0 8px;"> / </li>
<li><a href="{% url 'workers:worker_detail' worker.id %}" style="color: var(--secondary-blue); text-decoration: none;">{{ worker.first_name }} {{ worker.last_name }}</a></li>
<li style="margin: 0 8px;"> / </li>
<li><span style="color: var(--text-secondary);">المستندات</span></li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- بطاقة المستندات -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>مستندات العامل</h5>
            <div>
                <a href="{% url 'workers:add_worker_document' worker.id %}" class="btn btn-light">
                    <i class="fas fa-plus me-1"></i> إضافة مستند جديد
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- معلومات العامل -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-user-circle fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading">معلومات العامل</h5>
                        <p class="mb-0">الاسم: <strong>{{ worker.first_name }} {{ worker.last_name }}</strong></p>
                        <p class="mb-0">رقم جواز السفر: <strong>{{ worker.passport_number }}</strong></p>
                        <p class="mb-0">الجنسية: <strong>{{ worker.get_nationality_display }}</strong></p>
                    </div>
                </div>
            </div>

            <!-- جدول المستندات -->
            {% if documents %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>نوع المستند</th>
                                <th>العنوان</th>
                                <th>تاريخ الرفع</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الملف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for document in documents %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ document.get_document_type_display }}</td>
                                    <td>{{ document.title }}</td>
                                    <td>{{ document.upload_date|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if document.expiry_date %}
                                            {{ document.expiry_date|date:"Y-m-d" }}
                                            {% if document.expiry_date < today %}
                                                <span class="badge bg-danger">منتهي</span>
                                            {% elif document.expiry_date < expiry_warning %}
                                                <span class="badge bg-warning">قريب من الانتهاء</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ document.file.url }}" target="_blank" class="btn btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ document.file.url }}" download class="btn btn-success" title="تنزيل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <form method="POST" action="{% url 'workers:delete_worker_document' document.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">لا توجد مستندات</h5>
                            <p class="mb-0">لا توجد مستندات لهذا العامل. يمكنك إضافة مستندات جديدة باستخدام زر "إضافة مستند جديد".</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- أنواع المستندات المطلوبة -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>أنواع المستندات المطلوبة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">مستندات إلزامية</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            جواز السفر
                                            {% if has_passport %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-danger rounded-pill"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            تأشيرة العمل
                                            {% if has_visa %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-danger rounded-pill"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            عقد العمل
                                            {% if has_contract %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-danger rounded-pill"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">مستندات إضافية</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            الشهادات المهنية
                                            {% if has_certificates %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-warning rounded-pill"><i class="fas fa-exclamation"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            الفحص الطبي
                                            {% if has_medical %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-warning rounded-pill"><i class="fas fa-exclamation"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            السيرة الذاتية
                                            {% if has_cv %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-warning rounded-pill"><i class="fas fa-exclamation"></i></span>
                                            {% endif %}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">مستندات أخرى</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            صور شخصية
                                            {% if has_photos %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill"><i class="fas fa-minus"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            وثائق تعريفية أخرى
                                            {% if has_other_id %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill"><i class="fas fa-minus"></i></span>
                                            {% endif %}
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            مستندات أخرى
                                            {% if has_other %}
                                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill"><i class="fas fa-minus"></i></span>
                                            {% endif %}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                <a href="{% url 'workers:worker_detail' worker.id %}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل العامل
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // يمكن إضافة أي كود JavaScript خاص بصفحة المستندات هنا
</script>
{% endblock %}
