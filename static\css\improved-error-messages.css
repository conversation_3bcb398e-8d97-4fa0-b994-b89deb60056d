/**
 * تحسين تصميم رسائل الخطأ في النظام
 */

/* تنسيق عام لرسائل الخطأ */
.error-message,
.alert-danger,
.django-error,
.form-error,
[class*="error"],
[class*="alert-danger"] {
    border-radius: 8px !important;
    padding: 15px 20px !important;
    margin: 15px 0 !important;
    position: relative !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    display: flex !important;
    align-items: center !important;
    background-color: #fff5f5 !important;
    color: #e53e3e !important;
    border-right: 4px solid #e53e3e !important;
}

/* أيقونة الخطأ */
.error-message::before,
.alert-danger::before,
.django-error::before,
.form-error::before,
[class*="error"]::before,
[class*="alert-danger"]::before {
    content: "\f071" !important;
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    margin-left: 15px !important;
    font-size: 18px !important;
    color: #e53e3e !important;
}

/* زر إغلاق رسالة الخطأ */
.error-message .close,
.alert-danger .close,
.django-error .close,
.form-error .close,
[class*="error"] .close,
[class*="alert-danger"] .close,
[class*="error"] .btn-close,
[class*="alert-danger"] .btn-close {
    position: absolute !important;
    left: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    font-size: 16px !important;
    color: #e53e3e !important;
    opacity: 0.7 !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease !important;
}

.error-message .close:hover,
.alert-danger .close:hover,
.django-error .close:hover,
.form-error .close:hover,
[class*="error"] .close:hover,
[class*="alert-danger"] .close:hover,
[class*="error"] .btn-close:hover,
[class*="alert-danger"] .btn-close:hover {
    opacity: 1 !important;
}

/* تنسيق خاص لأخطاء CSRF */
.csrf-error {
    background-color: #fff8f0 !important;
    color: #dd6b20 !important;
    border-right: 4px solid #dd6b20 !important;
    padding: 20px !important;
    margin: 20px 0 !important;
    border-radius: 10px !important;
    box-shadow: 0 6px 20px rgba(221, 107, 32, 0.15) !important;
    display: flex !important;
    flex-direction: column !important;
}

.csrf-error-header {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

.csrf-error-header i {
    font-size: 22px !important;
    margin-left: 12px !important;
    color: #dd6b20 !important;
}

.csrf-error-header h4 {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: #dd6b20 !important;
}

.csrf-error-body {
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-right: 34px !important;
}

.csrf-error-actions {
    margin-top: 15px !important;
    display: flex !important;
    gap: 10px !important;
    margin-right: 34px !important;
}

.csrf-error-actions button {
    padding: 8px 15px !important;
    border-radius: 6px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.csrf-error-refresh {
    background-color: #dd6b20 !important;
    color: white !important;
    border: none !important;
}

.csrf-error-refresh:hover {
    background-color: #c05621 !important;
}

.csrf-error-dismiss {
    background-color: transparent !important;
    color: #dd6b20 !important;
    border: 1px solid #dd6b20 !important;
}

.csrf-error-dismiss:hover {
    background-color: rgba(221, 107, 32, 0.1) !important;
}

/* تنسيق رسائل الخطأ في النماذج */
.form-error-message {
    color: #e53e3e !important;
    font-size: 13px !important;
    margin-top: 5px !important;
    display: flex !important;
    align-items: center !important;
}

.form-error-message i {
    margin-left: 5px !important;
    font-size: 12px !important;
}

/* تأثير ظهور رسائل الخطأ */
@keyframes errorFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-message,
.alert-danger,
.django-error,
.form-error,
.csrf-error,
[class*="error"],
[class*="alert-danger"] {
    animation: errorFadeIn 0.3s ease-out forwards !important;
}
