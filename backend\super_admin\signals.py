"""
إشارات Django لتتبع التغييرات في هيكل قاعدة البيانات وتطبيقها على جميع قواعد بيانات الشركات
"""

import logging
from django.db.models.signals import post_migrate
from django.dispatch import receiver
from django.conf import settings
from django.utils import timezone
from django.apps import apps

# إعداد السجل
logger = logging.getLogger(__name__)

@receiver(post_migrate)
def handle_post_migrate(sender, **kwargs):
    """
    معالجة إشارة post_migrate لتتبع التغييرات في هيكل قاعدة البيانات

    يتم استدعاء هذه الوظيفة بعد تطبيق الهجرات على قاعدة البيانات الافتراضية
    """
    # التحقق مما إذا كان التطبيق المرسل هو أحد تطبيقات الشركة
    app_name = sender.name.split('.')[-1]
    company_apps = getattr(settings, 'COMPANY_APPS', [])

    if app_name in company_apps:
        logger.info(f"تم اكتشاف تغيير في هيكل قاعدة البيانات للتطبيق: {app_name}")

        # تسجيل التغيير في سجل التغييرات
        log_schema_change(app_name)

        # جدولة تحديث قواعد بيانات الشركات
        schedule_company_databases_update()

def log_schema_change(app_name):
    """
    تسجيل التغيير في هيكل قاعدة البيانات

    Args:
        app_name: اسم التطبيق الذي تم تغيير هيكله
    """
    try:
        # استيراد النموذج هنا لتجنب الاستيراد الدائري
        from backend.super_admin.models import SchemaChange, SystemLog

        # إنشاء سجل جديد للتغيير
        change = SchemaChange.objects.create(
            app_name=app_name,
            change_type='migrate',
            description=f'تم تطبيق هجرات جديدة على التطبيق {app_name}',
            applied_to_all=False
        )

        # تسجيل العملية في سجل النظام
        SystemLog.objects.create(
            action='update',
            description=f'تم اكتشاف تغيير في هيكل قاعدة البيانات للتطبيق {app_name}',
            ip_address='127.0.0.1'  # عنوان IP محلي للإشارات
        )

        logger.info(f"تم تسجيل التغيير في هيكل قاعدة البيانات للتطبيق {app_name} بنجاح")

    except Exception as e:
        logger.error(f"خطأ في تسجيل التغيير في هيكل قاعدة البيانات: {str(e)}")

def schedule_company_databases_update():
    """
    جدولة تحديث قواعد بيانات الشركات

    يمكن تنفيذ هذه الوظيفة بشكل غير متزامن باستخدام Celery أو مهام مجدولة
    """
    try:
        # في بيئة الإنتاج، يمكن استخدام Celery لتنفيذ هذه المهمة بشكل غير متزامن
        # لكن في هذا المثال، سنقوم بتسجيل رسالة فقط
        logger.info("تم جدولة تحديث قواعد بيانات الشركات")

        # يمكن استدعاء الأمر هنا باستخدام call_command
        # من django.core.management import call_command
        # call_command('update_company_databases')

    except Exception as e:
        logger.error(f"خطأ في جدولة تحديث قواعد بيانات الشركات: {str(e)}")
