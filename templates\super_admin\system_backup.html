{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}النسخ الاحتياطي - لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/backup_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/system_logs_table_fix.css' %}">
<link rel="stylesheet" href="{% static 'css/backup_logs_specific_fix.css' %}">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-800">النسخ الاحتياطي للنظام</h1>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'super_admin_dashboard' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-arrow-right ml-2"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    {% if messages %}
    <div class="mb-6">
        {% for message in messages %}
        <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700 border border-green-200{% elif message.tags == 'error' %}bg-red-100 text-red-700 border border-red-200{% else %}bg-blue-100 text-blue-700 border border-blue-200{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md col-span-1 md:col-span-2">
            <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-md">
                    <i class="fas fa-database text-xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800">النسخ الاحتياطية</h3>
                    <p class="text-gray-500 text-sm mt-1">قائمة النسخ الاحتياطية المحفوظة في النظام</p>
                </div>
                <div class="mr-auto">
                    <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full shadow-sm">
                        {% if backups %}{{ backups|length }} نسخة{% else %}لا توجد نسخ{% endif %}
                    </span>
                </div>
            </div>

            <div class="overflow-x-auto rounded-lg shadow">
                <table class="backup-table backup-logs-specific min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-blue-50 to-blue-100">
                        <tr>
                            <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tr-lg py-3 px-4">الاسم</th>
                            <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">التاريخ</th>
                            <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">الحجم</th>
                            <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tl-lg py-3 px-4">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% if backups %}
                            {% for backup in backups %}
                            <tr class="hover:bg-blue-50 transition-colors">
                                <td class="text-sm font-medium text-gray-900 py-4 px-4 filename" title="{{ backup.filename }}">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                            <i class="fas fa-file-archive"></i>
                                        </div>
                                        <span class="truncate">{{ backup.filename }}</span>
                                    </div>
                                </td>
                                <td class="text-sm text-gray-600 py-4 px-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-alt text-gray-400 ml-2"></i>
                                        {{ backup.created_at }}
                                    </div>
                                </td>
                                <td class="text-sm text-gray-600 py-4 px-4">
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-md">
                                        {{ backup.size_formatted }}
                                    </span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-3 space-x-reverse justify-center">
                                        <a href="{% url 'download_system_backup' backup.filename %}" class="text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 p-2 rounded-full transition-colors shadow-sm action-button" title="تنزيل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button"
                                            class="text-red-600 hover:text-red-800 bg-red-50 hover:bg-red-100 p-2 rounded-full transition-colors shadow-sm action-button"
                                            title="حذف"
                                            data-delete-url="{% url 'delete_system_backup' backup.filename %}"
                                            onclick="window.customizeDeleteModal(
                                                this.getAttribute('data-delete-url'),
                                                'تأكيد حذف النسخة الاحتياطية',
                                                'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.',
                                                'fa-trash-alt',
                                                'text-red-600',
                                                'تأكيد الحذف',
                                                'bg-red-600 hover:bg-red-700'
                                            );">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button type="button"
                                            class="text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 p-2 rounded-full transition-colors shadow-sm action-button"
                                            title="استعادة"
                                            data-delete-url="{% url 'restore_system_backup' backup.filename %}"
                                            onclick="window.customizeDeleteModal(
                                                this.getAttribute('data-delete-url'),
                                                'تأكيد استعادة النسخة الاحتياطية',
                                                'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.',
                                                'fa-undo-alt',
                                                'text-green-600',
                                                'تأكيد الاستعادة',
                                                'bg-green-600 hover:bg-green-700'
                                            );">
                                            <i class="fas fa-undo-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="4" class="text-center py-12 text-gray-500">
                                    <div class="flex flex-col items-center bg-gray-50 p-8 rounded-lg">
                                        <div class="bg-blue-100 text-blue-500 p-4 rounded-full mb-4 shadow-sm">
                                            <i class="fas fa-database text-3xl"></i>
                                        </div>
                                        <p class="text-lg font-medium text-gray-700">لا توجد نسخ احتياطية متاحة</p>
                                        <p class="text-sm text-gray-500 mt-2 max-w-md">قم بإنشاء نسخة احتياطية جديدة باستخدام الزر أدناه للحفاظ على بياناتك</p>
                                        <button type="button" class="mt-4 flex items-center py-2 px-4 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors" onclick="document.getElementById('create-backup-btn').click()">
                                            <i class="fas fa-plus-circle ml-2"></i> إنشاء نسخة احتياطية الآن
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md">
            <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
                <div class="bg-gradient-to-br from-green-400 to-green-600 text-white p-3 rounded-full ml-4 shadow-md">
                    <i class="fas fa-cogs text-lg"></i>
                </div>
                <div>
                    <h4 class="text-lg font-bold text-gray-800">إجراءات النسخ الاحتياطي</h4>
                    <p class="text-gray-500 text-sm mt-1">إنشاء واستعادة النسخ الاحتياطية</p>
                </div>
            </div>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-5 rounded-lg border border-blue-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-500 text-white p-3 rounded-full ml-3 shadow-sm">
                            <i class="fas fa-download"></i>
                        </div>
                        <div>
                            <h5 class="font-medium text-blue-800">إنشاء نسخة احتياطية</h5>
                            <p class="text-sm text-blue-600 mt-1">إنشاء نسخة احتياطية جديدة من قاعدة البيانات</p>
                        </div>
                    </div>
                    <form method="post" action="{% url 'create_backup' %}">
                        {% csrf_token %}
                        <button id="create-backup-btn" type="submit" class="w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <i class="fas fa-download ml-2 text-lg"></i> إنشاء نسخة احتياطية جديدة
                        </button>
                    </form>
                </div>

                <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-5 rounded-lg border border-yellow-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="bg-yellow-500 text-white p-3 rounded-full ml-3 shadow-sm">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div>
                            <h5 class="font-medium text-yellow-800">استعادة نسخة احتياطية</h5>
                            <p class="text-sm text-yellow-600 mt-1">استعادة قاعدة البيانات من نسخة احتياطية سابقة</p>
                        </div>
                    </div>
                    <form method="post" action="{% url 'restore_backup' %}" id="restore-backup-form">
                        {% csrf_token %}
                        <button type="submit" class="w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors">
                            <i class="fas fa-upload ml-2 text-lg"></i> استعادة نسخة احتياطية
                        </button>
                    </form>
                </div>

                {% if settings.last_backup_date %}
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-5 rounded-lg border border-blue-200 shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="bg-blue-500 text-white p-3 rounded-full ml-4 shadow-sm">
                                <i class="fas fa-info-circle text-lg"></i>
                            </div>
                            <div>
                                <p class="text-blue-700 font-medium">آخر نسخة احتياطية</p>
                                <p class="text-blue-800 font-bold text-xl">{{ settings.last_backup_date|date:"j F Y - H:i" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/backup_table.js' %}"></script>
<script src="{% static 'js/system_logs_table_fix.js' %}"></script>
<script src="{% static 'js/backup_logs_specific_fix.js' %}"></script>
<script>
    // تنفيذ عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // معالجة نموذج استعادة النسخة الاحتياطية
        const restoreBackupForm = document.getElementById('restore-backup-form');
        if (restoreBackupForm) {
            restoreBackupForm.addEventListener('submit', function(e) {
                e.preventDefault();

                window.customizeDeleteModal(
                    restoreBackupForm.getAttribute('action'),
                    'تأكيد استعادة النسخة الاحتياطية',
                    'تحذير: هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.',
                    'fa-upload',
                    'text-yellow-600',
                    'تأكيد الاستعادة',
                    'bg-yellow-600 hover:bg-yellow-700'
                );
            });
        }
    });
</script>
{% endblock %}
