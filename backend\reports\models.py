from django.db import models
from django.utils import timezone
from backend.contracts.models import Contract
from backend.workers.models import Worker
from backend.services.models import BookingSchedule

class FinancialReport(models.Model):
    REPORT_TYPE_CHOICES = (
        ('income', 'تقرير الدخل'),
        ('expense', 'تقرير المصروفات'),
        ('profit_loss', 'تقرير الربح والخسارة'),
        ('tax', 'تقرير الضرائب'),
        ('salary', 'تقرير الرواتب'),
        ('custom', 'تقرير مخصص'),
    )

    title = models.CharField(max_length=200, default='', verbose_name='العنوان')
    report_type = models.CharField(max_length=20, choices=REPORT_TYPE_CHOICES, default='income', verbose_name='نوع التقرير')
    start_date = models.DateField(default=timezone.now, verbose_name='تاريخ البداية')
    end_date = models.DateField(default=timezone.now, verbose_name='تاريخ النهاية')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    date_created = models.DateField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def __str__(self):
        return f"{self.title} ({self.start_date} - {self.end_date})"

    class Meta:
        verbose_name = 'تقرير مالي'
        verbose_name_plural = 'التقارير المالية'

class WorkerReport(models.Model):
    title = models.CharField(max_length=200, default='', verbose_name='العنوان')
    workers = models.ManyToManyField(Worker, related_name='reports', verbose_name='العمال')
    start_date = models.DateField(default=timezone.now, verbose_name='تاريخ البداية')
    end_date = models.DateField(default=timezone.now, verbose_name='تاريخ النهاية')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    date_created = models.DateField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def __str__(self):
        return f"{self.title} ({self.start_date} - {self.end_date})"

    class Meta:
        verbose_name = 'تقرير العمال'
        verbose_name_plural = 'تقارير العمال'

class ContractReport(models.Model):
    title = models.CharField(max_length=200, default='', verbose_name='العنوان')
    contracts = models.ManyToManyField(Contract, related_name='reports', verbose_name='العقود')
    start_date = models.DateField(default=timezone.now, verbose_name='تاريخ البداية')
    end_date = models.DateField(default=timezone.now, verbose_name='تاريخ النهاية')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    date_created = models.DateField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def __str__(self):
        return f"{self.title} ({self.start_date} - {self.end_date})"

    class Meta:
        verbose_name = 'تقرير العقود'
        verbose_name_plural = 'تقارير العقود'

class ServiceReport(models.Model):
    title = models.CharField(max_length=200, default='', verbose_name='العنوان')
    schedules = models.ManyToManyField(BookingSchedule, related_name='reports', verbose_name='جداول الخدمات')
    start_date = models.DateField(default=timezone.now, verbose_name='تاريخ البداية')
    end_date = models.DateField(default=timezone.now, verbose_name='تاريخ النهاية')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    date_created = models.DateField(default=timezone.now, verbose_name='تاريخ الإنشاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def __str__(self):
        return f"{self.title} ({self.start_date} - {self.end_date})"

    class Meta:
        verbose_name = 'تقرير الخدمات'
        verbose_name_plural = 'تقارير الخدمات'
