{% extends 'layouts/super_admin.html' %}

{% block title %}اشتراكات الشركات{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-green-600 to-teal-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-building ml-2"></i>
                    اشتراكات الشركات
                </h1>
                <p class="text-green-100">
                    إدارة ومتابعة اشتراكات جميع الشركات المسجلة
                </p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold">{{ stats.total_subscriptions|default:0 }}</div>
                <div class="text-sm text-green-100">اشتراك</div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ stats.active_subscriptions|default:0 }}</div>
                    <div class="text-sm text-gray-500">اشتراكات نشطة</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-blue-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ stats.trial_subscriptions|default:0 }}</div>
                    <div class="text-sm text-gray-500">فترات تجريبية</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-times-circle text-red-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ stats.expired_subscriptions|default:0 }}</div>
                    <div class="text-sm text-gray-500">اشتراكات منتهية</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ stats.total_revenue|default:0|floatformat:0 }}</div>
                    <div class="text-sm text-gray-500">إجمالي الإيرادات (ريال)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-filter ml-2"></i>
            تصفية الاشتراكات
        </h2>

        <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">حالة الاشتراك</label>
                <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-900 bg-white">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                <input type="text"
                       name="search"
                       id="search"
                       value="{{ search_query }}"
                       placeholder="اسم الشركة..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-900 bg-white placeholder-gray-500">
            </div>

            <!-- Search Button -->
            <div class="flex items-end">
                <button type="submit"
                        class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Subscriptions Table -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-list ml-2"></i>
                قائمة الاشتراكات
            </h2>
        </div>

        {% if subscriptions %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الشركة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            خطة الاشتراك
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ الانتهاء
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for subscription in subscriptions %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <i class="fas fa-building text-green-600"></i>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">{{ subscription.company.name }}</div>
                                    <div class="text-sm text-gray-500">{{ subscription.company.serial_number }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ subscription.plan.name }}</div>
                            <div class="text-sm text-gray-500">{{ subscription.plan.get_billing_cycle_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if subscription.status == 'trial' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-clock ml-1"></i>
                                    فترة تجريبية
                                </span>
                            {% elif subscription.status == 'active' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    نشط
                                </span>
                            {% elif subscription.status == 'expired' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle ml-1"></i>
                                    منتهي
                                </span>
                            {% elif subscription.status == 'suspended' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-pause-circle ml-1"></i>
                                    معلق
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ subscription.get_status_display }}
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ subscription.end_date|date:"Y/m/d" }}
                            {% if subscription.days_remaining > 0 %}
                                <div class="text-xs text-gray-500">
                                    ({{ subscription.days_remaining }} يوم متبقي)
                                </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ subscription.total_amount }} ريال</div>
                            <div class="text-xs text-gray-500">
                                مدفوع: {{ subscription.paid_amount }} ريال
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="text-blue-600 hover:text-blue-900 ml-3">
                                <i class="fas fa-eye"></i>
                                عرض
                            </button>
                            <button class="text-green-600 hover:text-green-900 ml-3">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="text-purple-600 hover:text-purple-900">
                                <i class="fas fa-credit-card"></i>
                                دفعات
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if subscriptions.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if subscriptions.has_previous %}
                    <a href="?page={{ subscriptions.previous_page_number }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        السابق
                    </a>
                {% endif %}
                {% if subscriptions.has_next %}
                    <a href="?page={{ subscriptions.next_page_number }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        التالي
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض
                        <span class="font-medium">{{ subscriptions.start_index }}</span>
                        إلى
                        <span class="font-medium">{{ subscriptions.end_index }}</span>
                        من
                        <span class="font-medium">{{ subscriptions.paginator.count }}</span>
                        نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if subscriptions.has_previous %}
                            <a href="?page={{ subscriptions.previous_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                السابق
                            </a>
                        {% endif %}

                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            {{ subscriptions.number }}
                        </span>

                        {% if subscriptions.has_next %}
                            <a href="?page={{ subscriptions.next_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                التالي
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-building text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد اشتراكات</h3>
            <p class="text-gray-500">لم يتم العثور على اشتراكات تطابق معايير البحث</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
