# Generated by Django 5.2 on 2025-05-07 22:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0007_databasebackup'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SchemaChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(max_length=100, verbose_name='اسم التطبيق')),
                ('change_type', models.CharField(choices=[('migrate', 'هجرة'), ('manual', 'يدوي'), ('reset', 'إعادة تعيين')], default='migrate', max_length=20, verbose_name='نوع التغيير')),
                ('description', models.TextField(verbose_name='وصف التغيير')),
                ('details', models.TextField(blank=True, null=True, verbose_name='تفاصيل التغيير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('applied_to_all', models.BooleanField(default=False, verbose_name='تم تطبيقه على جميع الشركات')),
                ('applied_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التطبيق')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='schema_changes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تغيير في هيكل قاعدة البيانات',
                'verbose_name_plural': 'تغييرات هيكل قاعدة البيانات',
                'ordering': ['-created_at'],
            },
        ),
    ]
