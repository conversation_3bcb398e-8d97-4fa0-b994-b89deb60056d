from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>

from .views import SettingViewSet, RecruitmentCompanyViewSet

# إنشاء الموجه
router = DefaultRouter()
router.register(r'settings', SettingViewSet, basename='setting')
router.register(r'recruitment-companies', RecruitmentCompanyViewSet, basename='recruitment-company')

# تعريف مسارات الإعدادات
urlpatterns = [
    # مسارات الإعدادات
    path('', include(router.urls)),
]
