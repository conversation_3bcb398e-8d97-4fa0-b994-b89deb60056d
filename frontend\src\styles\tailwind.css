@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Cairo', sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-800;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-3xl;
  }

  h2 {
    @apply text-2xl;
  }

  h3 {
    @apply text-xl;
  }

  h4 {
    @apply text-lg;
  }
}

@layer components {
  /* الأزرار */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-dark focus:ring-primary;
  }

  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary;
  }

  .btn-success {
    @apply btn bg-success text-white hover:bg-success-dark focus:ring-success;
  }

  .btn-danger {
    @apply btn bg-danger text-white hover:bg-danger-dark focus:ring-danger;
  }

  .btn-warning {
    @apply btn bg-warning text-white hover:bg-warning-dark focus:ring-warning;
  }

  .btn-info {
    @apply btn bg-info text-white hover:bg-info-dark focus:ring-info;
  }

  .btn-neutral {
    @apply btn bg-neutral text-neutral-dark hover:bg-neutral-dark hover:text-white focus:ring-neutral;
  }

  .btn-outline-primary {
    @apply btn bg-transparent border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  }

  .btn-outline-secondary {
    @apply btn bg-transparent border border-secondary text-secondary hover:bg-secondary hover:text-white focus:ring-secondary;
  }

  .btn-outline {
    @apply btn bg-transparent border border-neutral text-neutral-dark hover:bg-neutral-light focus:ring-neutral;
  }

  .btn-sm {
    @apply px-3 py-1 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  .btn-icon {
    @apply p-2 rounded-full;
  }

  /* البطاقات */
  .card {
    @apply bg-white rounded-card shadow-card overflow-hidden transition-shadow duration-200 hover:shadow-card-hover;
  }

  .card-header {
    @apply px-6 py-4 border-b border-neutral-light bg-primary-lighter;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-neutral-light bg-primary-lighter;
  }

  /* بطاقات ملونة */
  .card-primary {
    @apply border border-primary-light;
  }

  .card-primary .card-header {
    @apply bg-primary-light text-primary-dark border-primary-light;
  }

  .card-secondary {
    @apply border border-secondary-light;
  }

  .card-secondary .card-header {
    @apply bg-secondary-light text-secondary-dark border-secondary-light;
  }

  /* حقول الإدخال */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-neutral-dark mb-1;
  }

  .form-input {
    @apply block w-full px-3 py-2 border border-neutral rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
  }

  .form-input.error {
    @apply border-danger focus:ring-danger focus:border-danger;
  }

  .form-error {
    @apply mt-1 text-sm text-danger;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-neutral rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
  }

  .form-checkbox {
    @apply h-4 w-4 text-primary focus:ring-primary border-neutral rounded;
  }

  .form-radio {
    @apply h-4 w-4 text-primary focus:ring-primary border-neutral;
  }

  /* حقول البحث */
  .search-input {
    @apply form-input pr-10 bg-primary-lighter border-primary-light focus:bg-white;
  }

  /* الجداول */
  .data-table {
    @apply min-w-full divide-y divide-neutral-light;
  }

  .data-table thead {
    @apply bg-primary text-white;
  }

  .data-table th {
    @apply px-6 py-3 text-right text-xs font-medium uppercase tracking-wider;
  }

  .data-table tbody {
    @apply bg-white divide-y divide-neutral-light;
  }

  .data-table tbody tr {
    @apply hover:bg-primary-lighter;
  }

  .data-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-neutral-dark;
  }

  /* جداول ملونة */
  .table-secondary thead {
    @apply bg-secondary text-white;
  }

  .table-info thead {
    @apply bg-info text-white;
  }

  /* الشارات */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-light text-primary-dark;
  }

  .badge-secondary {
    @apply bg-secondary-light text-secondary-dark;
  }

  .badge-success {
    @apply bg-success-light text-success-dark;
  }

  .badge-danger {
    @apply bg-danger-light text-white;
  }

  .badge-warning {
    @apply bg-warning-light text-warning-dark;
  }

  .badge-info {
    @apply bg-info-light text-info-dark;
  }

  .badge-neutral {
    @apply bg-neutral-light text-neutral-dark;
  }

  /* التنبيهات */
  .alert {
    @apply p-4 rounded-lg mb-4 border;
  }

  .alert-primary {
    @apply bg-primary-light border-primary text-primary-dark;
  }

  .alert-secondary {
    @apply bg-secondary-light border-secondary text-secondary-dark;
  }

  .alert-success {
    @apply bg-success-light border-success text-success-dark;
  }

  .alert-danger {
    @apply bg-danger-light border-danger text-white;
  }

  .alert-warning {
    @apply bg-warning-light border-warning text-warning-dark;
  }

  .alert-info {
    @apply bg-info-light border-info text-info-dark;
  }

  /* التبويبات */
  .tabs {
    @apply w-full;
  }

  .tabs-header {
    @apply flex border-b border-neutral-light;
  }

  .tab-btn {
    @apply py-3 px-4 text-center border-b-2 border-transparent font-medium text-neutral hover:text-primary hover:border-neutral focus:outline-none;
  }

  .tab-btn.active {
    @apply border-primary text-primary;
  }

  .tab-pane {
    @apply hidden;
  }

  .tab-pane.active {
    @apply block;
  }

  /* عناصر التفاصيل */
  .detail-item {
    @apply mb-4;
  }

  .detail-label {
    @apply text-sm font-medium text-neutral mb-1;
  }

  .detail-value {
    @apply text-base font-medium text-neutral-dark;
  }

  /* قوائم المعلومات */
  .info-list {
    @apply divide-y divide-neutral-light;
  }

  .info-list-item {
    @apply py-3 flex justify-between;
  }

  .info-list-label {
    @apply text-sm font-medium text-neutral;
  }

  .info-list-value {
    @apply text-sm text-neutral-dark font-medium;
  }

  /* أزرار الإجراءات في الصفوف */
  .row-actions {
    @apply flex items-center space-x-reverse space-x-2;
  }

  .action-btn {
    @apply p-1 rounded-full hover:bg-primary-lighter focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary;
  }

  .action-btn.view {
    @apply text-info;
  }

  .action-btn.edit {
    @apply text-warning;
  }

  .action-btn.delete {
    @apply text-danger;
  }

  .action-btn.info {
    @apply text-primary;
  }

  /* أيقونات الحالة */
  .status-icon {
    @apply w-8 h-8 rounded-full flex items-center justify-center;
  }

  .status-icon-primary {
    @apply bg-primary-light text-primary;
  }

  .status-icon-secondary {
    @apply bg-secondary-light text-secondary;
  }

  .status-icon-success {
    @apply bg-success-light text-success;
  }

  .status-icon-danger {
    @apply bg-danger-light text-danger;
  }

  .status-icon-warning {
    @apply bg-warning-light text-warning;
  }

  .status-icon-info {
    @apply bg-info-light text-info;
  }

  /* بطاقات الإحصائيات */
  .stat-card {
    @apply p-6 rounded-lg shadow-sm border border-neutral-light hover:shadow-md transition-shadow duration-300;
  }

  .stat-card-primary {
    @apply border-primary-light;
  }

  .stat-card-secondary {
    @apply border-secondary-light;
  }

  .stat-card-success {
    @apply border-success-light;
  }

  .stat-card-danger {
    @apply border-danger-light;
  }

  .stat-card-warning {
    @apply border-warning-light;
  }

  .stat-card-info {
    @apply border-info-light;
  }
}
