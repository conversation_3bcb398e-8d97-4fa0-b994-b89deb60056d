"""
وحدة واجهة برمجة التطبيقات (API) للحسابات
تتضمن وظائف للتحقق من الرقم التسلسلي وقاعدة البيانات
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.utils import timezone
import json
import logging

from backend.super_admin.models import Company
from backend.super_admin.database_connector import test_company_db_connection, setup_company_db_connection

# إعداد السجل
logger = logging.getLogger(__name__)

@csrf_exempt
def test_api(request):
    """اختبار بسيط للـ API"""
    return JsonResponse({
        'success': True,
        'message': 'API يعمل بشكل صحيح',
        'method': request.method,
        'content_type': request.content_type,
        'body': request.body.decode('utf-8') if request.body else None
    })

@csrf_exempt
def check_serial_number_api(request):
    """
    التحقق من الرقم التسلسلي للشركة - المرحلة الأولى
    """
    if request.method != 'POST':
        return JsonResponse({
            'success': False,
            'message': 'طريقة الطلب غير مدعومة'
        }, status=405)

    try:
        # استخراج الرقم التسلسلي من البيانات
        serial_number = None

        # محاولة الحصول على البيانات من JSON
        if request.content_type == 'application/json' and request.body:
            try:
                data = json.loads(request.body)
                serial_number = data.get('serial_number') or data.get('serial')
            except json.JSONDecodeError:
                pass

        # محاولة الحصول على البيانات من POST
        if not serial_number:
            serial_number = request.POST.get('serial_number') or request.POST.get('serial')

        if not serial_number:
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال الرقم التسلسلي',
                'error_type': 'missing_serial'
            }, status=400)

        # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        clean_serial = serial_number.replace('-', '').replace(' ', '').upper()

        # التحقق من طول الرقم التسلسلي
        if len(clean_serial) != 16:
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي يجب أن يتكون من 16 حرف وأرقام',
                'error_type': 'invalid_length'
            }, status=400)

        # تنسيق الرقم التسلسلي (إضافة الشرطات)
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])

        # البحث عن الشركة بالرقم التسلسلي
        company = Company.objects.filter(serial_number__in=[serial_number, formatted_serial, clean_serial]).first()

        if not company:
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي غير مسجل في النظام',
                'error_type': 'serial_not_found'
            }, status=404)

        # التحقق من حالة الشركة
        status_messages = {
            'inactive': {
                'message': '🚫 الشركة غير مفعلة',
                'details': 'تم إيقاف هذه الشركة من قبل الإدارة. للاستفسار يرجى التواصل مع الدعم الفني.',
                'icon': '🚫'
            },
            'suspended': {
                'message': '⏸️ الشركة معلقة مؤقتاً',
                'details': 'تم تعليق الشركة مؤقتاً. للاستفسار يرجى التواصل مع الدعم الفني.',
                'icon': '⏸️'
            },
            'maintenance': {
                'message': '🛠️ النظام قيد الصيانة',
                'details': 'النظام قيد الصيانة حالياً. يرجى المحاولة لاحقاً.',
                'icon': '🛠️'
            },
            'trial_expired': {
                'message': '⏳ انتهت الفترة التجريبية',
                'details': 'انتهت الفترة التجريبية للشركة. يرجى التواصل مع الإدارة لتفعيل الاشتراك.',
                'icon': '⏳'
            }
        }

        if company.status != 'active':
            status_info = status_messages.get(company.status, {
                'message': '❌ حالة الشركة غير صالحة',
                'details': 'يرجى التواصل مع الدعم الفني لحل هذه المشكلة.',
                'icon': '❌'
            })

            return JsonResponse({
                'success': False,
                'message': status_info['message'],
                'details': status_info['details'],
                'error_type': 'company_status',
                'company_status': company.status,
                'icon': status_info['icon']
            }, status=403)

        # التحقق من تاريخ انتهاء الاشتراك
        if company.expiry_date and company.expiry_date < timezone.now().date():
            return JsonResponse({
                'success': False,
                'message': '⛔ انتهت صلاحية الاشتراك',
                'details': f'انتهت صلاحية اشتراك الشركة بتاريخ {company.expiry_date.strftime("%Y/%m/%d")}. يرجى تجديد الاشتراك للمتابعة.',
                'error_type': 'subscription_expired',
                'expiry_date': company.expiry_date.strftime('%Y-%m-%d'),
                'icon': '⛔'
            }, status=403)

        # نجح التحقق من الرقم التسلسلي والشركة
        return JsonResponse({
            'success': True,
            'message': '✅ تم التحقق من الرقم التسلسلي بنجاح',
            'company': {
                'id': company.id,
                'name': company.name,
                'serial_number': formatted_serial,
                'status': company.status
            },
            'data': {
                'company': {
                    'name': company.name,
                    'id': company.id,
                    'status': company.status
                },
                'company_name': company.name
            }
        })

    except Exception as e:
        logger.exception(f"خطأ في التحقق من الرقم التسلسلي: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء التحقق من الرقم التسلسلي',
            'error_type': 'system_error'
        }, status=500)

@csrf_exempt
def company_login_api(request):
    """
    تسجيل دخول الشركة - المرحلة الثانية
    """
    if request.method != 'POST':
        return JsonResponse({
            'success': False,
            'message': 'طريقة الطلب غير مدعومة'
        }, status=405)

    try:
        # استخراج البيانات
        data = {}
        if request.content_type == 'application/json' and request.body:
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                pass

        # الحصول على البيانات من POST إذا لم تكن في JSON
        username = data.get('username') or request.POST.get('username')
        password = data.get('password') or request.POST.get('password')
        serial_number = data.get('serial_number') or request.POST.get('serial_number')

        if not all([username, password, serial_number]):
            return JsonResponse({
                'success': False,
                'message': 'جميع الحقول مطلوبة',
                'error_type': 'missing_fields'
            }, status=400)

        # تنظيف الرقم التسلسلي
        clean_serial = serial_number.replace('-', '').replace(' ', '').upper()
        formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])

        # البحث عن الشركة
        company = Company.objects.filter(
            serial_number__in=[serial_number, formatted_serial, clean_serial],
            status='active'
        ).first()

        if not company:
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي غير صحيح أو الشركة غير نشطة',
                'error_type': 'invalid_company'
            }, status=404)

        # التحقق من المستخدم
        from django.contrib.auth import authenticate, login
        user = authenticate(username=username, password=password)

        if not user:
            return JsonResponse({
                'success': False,
                'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
                'error_type': 'invalid_credentials'
            }, status=401)

        # تسجيل دخول المستخدم فعلياً
        login(request, user)

        # حفظ معلومات الشركة في الجلسة
        request.session['current_company_id'] = company.id
        request.session['serial_number'] = formatted_serial
        request.session['company_name'] = company.name

        # نجح تسجيل الدخول
        return JsonResponse({
            'success': True,
            'message': '✅ تم تسجيل الدخول بنجاح',
            'redirect_url': '/company/dashboard/',
            'user': {
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            },
            'company': {
                'id': company.id,
                'name': company.name,
                'serial_number': formatted_serial,
                'status': company.status
            }
        })

    except Exception as e:
        logger.exception(f"خطأ في تسجيل الدخول: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء تسجيل الدخول',
            'error_type': 'system_error'
        }, status=500)

@csrf_exempt
@require_POST
def verify_database_api(request):
    """
    التحقق من صحة قاعدة بيانات الشركة باستخدام API
    """
    try:
        # طباعة محتوى الطلب للتصحيح
        logger.debug(f"محتوى الطلب: {request.body.decode('utf-8')}")

        try:
            data = json.loads(request.body)
            logger.debug(f"بيانات JSON: {data}")
            serial_number = data.get('serial')

            # إذا لم نجد 'serial'، نحاول 'serial_number'
            if not serial_number:
                serial_number = data.get('serial_number')

            logger.info(f"طلب التحقق من قاعدة البيانات للرقم التسلسلي: {serial_number}")
        except json.JSONDecodeError:
            # إذا لم يكن الطلب JSON، نحاول الحصول على البيانات من POST
            logger.debug("فشل تحليل JSON، محاولة الحصول على البيانات من POST")
            serial_number = request.POST.get('serial') or request.POST.get('serial_number')

        if not serial_number:
            logger.warning("لم يتم تقديم الرقم التسلسلي")
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال الرقم التسلسلي',
                'error_type': 'missing_serial',
                'details': 'الرقم التسلسلي مطلوب للمتابعة. يرجى إدخال الرقم المكون من 16 حرف.'
            }, status=400)

        # تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        clean_serial = serial_number.replace('-', '').replace(' ', '')

        # البحث عن الشركة بالرقم التسلسلي
        # نحاول البحث بالتنسيق المُدخل أولاً، ثم بالتنسيق المنسق، ثم بالتنسيق النظيف
        company = Company.objects.filter(serial_number=serial_number).first()
        if not company:
            # تنسيق الرقم التسلسلي (إضافة الشرطات)
            formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, len(clean_serial), 4)])
            company = Company.objects.filter(serial_number=formatted_serial).first()
        if not company:
            company = Company.objects.filter(serial_number=clean_serial).first()

        if not company:
            logger.warning(f"لم يتم العثور على شركة بالرقم التسلسلي: {serial_number}")
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي غير صحيح',
                'error_type': 'serial_not_found',
                'details': 'الرقم التسلسلي المدخل غير مسجل في النظام. يرجى التأكد من صحة الرقم أو التواصل مع الإدارة.'
            }, status=404)

        # التحقق من حالة الشركة
        if company.status != 'active':
            logger.warning(f"الشركة غير نشطة: {company.name} (الحالة: {company.status})")

            # تحديد رسالة الخطأ حسب حالة الشركة
            if company.status == 'inactive':
                message = 'الشركة غير مفعلة'
                details = 'تم إيقاف هذه الشركة من قبل الإدارة. للاستفسار يرجى التواصل مع الدعم الفني.'
                error_type = 'company_status'
            elif company.status == 'suspended':
                message = 'الشركة معلقة مؤقتاً'
                details = 'تم تعليق الشركة مؤقتاً. للاستفسار يرجى التواصل مع الدعم الفني.'
                error_type = 'company_status'
            elif company.status == 'trial':
                message = 'الشركة في الفترة التجريبية'
                details = 'الشركة لا تزال في الفترة التجريبية. يرجى التواصل مع الإدارة لتفعيل الاشتراك الكامل.'
                error_type = 'company_status'
            else:
                message = 'حالة الشركة غير صالحة'
                details = 'يرجى التواصل مع الدعم الفني لحل هذه المشكلة.'
                error_type = 'company_status'

            return JsonResponse({
                'success': False,
                'message': message,
                'error_type': error_type,
                'details': details,
                'company_status': company.status
            }, status=403)

        # التحقق من تاريخ انتهاء الاشتراك
        if company.expiry_date and company.expiry_date < timezone.now().date():
            logger.warning(f"انتهى اشتراك الشركة: {company.name} (تاريخ الانتهاء: {company.expiry_date})")
            return JsonResponse({
                'success': False,
                'message': 'انتهت صلاحية الاشتراك',
                'error_type': 'subscription_expired',
                'details': f'انتهت صلاحية اشتراك الشركة بتاريخ {company.expiry_date.strftime("%Y/%m/%d")}. يرجى تجديد الاشتراك للمتابعة.',
                'expiry_date': company.expiry_date.strftime('%Y-%m-%d')
            }, status=403)

        # اختبار الاتصال بقاعدة البيانات
        logger.info(f"اختبار الاتصال بقاعدة بيانات الشركة: {company.name}")
        connection_test = test_company_db_connection(company)

        if not connection_test['success']:
            logger.warning(f"فشل الاتصال بقاعدة البيانات: {connection_test['message']}")
            return JsonResponse({
                'success': False,
                'message': f'فشل الاتصال بقاعدة البيانات: {connection_test["message"]}'
            }, status=500)

        # إعداد اتصال قاعدة البيانات في Django
        logger.info(f"إعداد اتصال قاعدة البيانات في Django: {company.database_name}")
        setup_success = setup_company_db_connection(company)

        if not setup_success:
            logger.error(f"فشل في إعداد اتصال قاعدة البيانات في Django: {company.database_name}")
            return JsonResponse({
                'success': False,
                'message': 'فشل في إعداد اتصال قاعدة البيانات'
            }, status=500)

        logger.info(f"تم التحقق من قاعدة البيانات بنجاح: {company.name}")
        return JsonResponse({
            'success': True,
            'message': 'تم التحقق من قاعدة البيانات بنجاح',
            'data': {
                'company': {
                    'name': company.name,
                    'id': company.id,
                    'status': company.status
                },
                'database': {
                    'name': company.database_name,
                    'type': connection_test.get('db_type', 'unknown'),
                    'tables_count': connection_test.get('tables_count', 0)
                },
                'company_name': company.name,  # للتوافق مع الكود القديم
                'tables_count': connection_test.get('tables_count', 0)  # للتوافق مع الكود القديم
            }
        })

    except Exception as e:
        logger.exception(f"استثناء غير متوقع: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء التحقق من قاعدة البيانات: {str(e)}'
        }, status=500)
