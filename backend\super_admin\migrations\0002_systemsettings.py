# Generated by Django 5.2 on 2025-04-29 19:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(default='نظام إدارة العمالة', max_length=100, verbose_name='اسم التطبيق')),
                ('default_language', models.CharField(default='ar', max_length=10, verbose_name='اللغة الافتراضية')),
                ('enable_registration', models.BooleanField(default=True, verbose_name='تفعيل التسجيل للشركات الجديدة')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='وضع الصيانة')),
                ('two_factor_for_admins', models.BooleanField(default=False, verbose_name='تفعيل التحقق الثنائي للمسؤولين')),
                ('session_timeout', models.IntegerField(default=60, verbose_name='مدة صلاحية الجلسة (بالدقائق)')),
                ('last_backup_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر نسخة احتياطية')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
    ]
