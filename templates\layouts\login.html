<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>تسجيل الدخول | استقدامي</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#174785',
                        secondary: '#42d3d8',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #174785 0%, #42d3d8 100%);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="flex-1 flex items-center justify-center">
        <div class="max-w-md w-full mx-4" x-data="loginForm">
            <!-- Logo -->
            <div class="text-center mb-6">
                <img src="{% load static %}{% static 'images/logo.webp' %}" alt="شعار استقدامي" class="h-20 mx-auto hover:animate-pulse transition-all duration-300" onerror="this.src='{% static 'images/logo-placeholder.png' %}'; this.onerror=null;">
                <h1 class="text-2xl font-bold text-primary mt-2">منصة استقدامي</h1>
                <p class="text-gray-600">نظام إدارة العمالة</p>
            </div>

            <!-- Login Card -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-4 bg-primary text-white">
                    <h2 class="text-xl font-bold">تسجيل دخول الشركات</h2>
                </div>

                <!-- Step 1: Serial Number -->
                <div x-show="step === 1" class="p-6">
                    <div class="mb-4">
                        <label for="serial_number" class="block text-gray-700 font-medium mb-2">الرقم التسلسلي للشركة</label>
                        <div class="relative">
                            <input
                                type="text"
                                id="serial_number"
                                x-model="serialNumber"
                                @input="formatSerialNumber()"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-center font-mono"
                                placeholder="XXXX-XXXX-XXXX-XXXX"
                                maxlength="19"
                                style="direction: ltr; letter-spacing: 2px;"
                                required
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-hashtag text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <button
                        type="button"
                        @click="checkDatabase()"
                        :disabled="!serialNumber || serialNumber.length < 19 || loading"
                        class="w-full bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <span x-show="!loading">
                            <i class="fas fa-database ml-2"></i>
                            فحص الاتصال
                        </span>
                        <span x-show="loading">
                            <i class="fas fa-spinner fa-spin ml-2"></i>
                            جاري الفحص...
                        </span>
                    </button>

                    <!-- Connection Status -->
                    <div x-show="connectionStatus.show" class="mt-4 p-3 rounded-lg" :class="connectionStatus.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
                        <div class="flex items-center">
                            <i :class="connectionStatus.icon" class="ml-2"></i>
                            <span x-text="connectionStatus.message"></span>
                        </div>
                        <div x-show="connectionStatus.type === 'success' && companyName" class="mt-2 text-sm">
                            <strong>اسم الشركة:</strong> <span x-text="companyName"></span>
                        </div>
                    </div>

                    <div x-show="dbVerified" class="mt-4">
                        <button
                            type="button"
                            @click="step = 2"
                            class="w-full bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors"
                        >
                            <i class="fas fa-arrow-left ml-2"></i>
                            التالي
                        </button>
                    </div>
                </div>

                <!-- Step 2: Login -->
                <div x-show="step === 2" class="p-6">
                    <form method="POST">
                        {% csrf_token %}
                        <input type="hidden" name="serial_number" :value="serialNumber">
                        <input type="hidden" name="db_verified" value="true">

                        <div class="mb-4">
                            <label for="username" class="block text-gray-700 font-medium mb-2">اسم المستخدم</label>
                            <div class="relative">
                                <input type="text" id="username" name="username" x-model="username" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" required>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="password" class="block text-gray-700 font-medium mb-2">كلمة المرور</label>
                            <div class="relative">
                                <input type="password" id="password" name="password" x-model="password" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" required>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="flex space-x-3 space-x-reverse">
                            <button
                                type="button"
                                @click="step = 1; dbVerified = false"
                                class="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors"
                            >
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </button>

                            <button
                                type="submit"
                                :disabled="!username || !password"
                                class="flex-1 bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <i class="fas fa-sign-in-alt ml-2"></i>
                                دخول
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Error Messages -->
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-2">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Footer Links -->
            <div class="mt-6 text-center">
                <a href="{% url 'super_admin:login' %}" class="text-primary hover:text-blue-700 text-sm transition-colors">
                    <i class="fas fa-user-shield ml-1"></i>
                    تسجيل دخول المسؤول الأعلى
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500 text-sm">
                © {{ now.year }} منصة استقدامي - جميع الحقوق محفوظة
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('loginForm', () => ({
                step: 1,
                serialNumber: '',
                username: '',
                password: '',
                dbVerified: false,
                loading: false,
                companyName: '',
                dbInfo: null,
                connectionStatus: {
                    show: false,
                    type: 'success',
                    message: '',
                    icon: 'fas fa-check-circle'
                },

                formatSerialNumber() {
                    // تنسيق الرقم التسلسلي (إضافة شرطات كل 4 أحرف)
                    let value = this.serialNumber.replace(/[^A-Z0-9]/gi, '').toUpperCase();
                    let formattedValue = '';

                    for (let i = 0; i < value.length; i++) {
                        if (i > 0 && i % 4 === 0 && formattedValue.length < 19) {
                            formattedValue += '-';
                        }
                        if (formattedValue.length < 19) {
                            formattedValue += value[i];
                        }
                    }

                    this.serialNumber = formattedValue;
                },

                async checkDatabase() {
                    if (!this.serialNumber || this.serialNumber.length < 19) {
                        this.showConnectionStatus('error', 'يرجى إدخال الرقم التسلسلي كاملاً', 'fas fa-exclamation-circle');
                        return;
                    }

                    this.loading = true;

                    try {
                        // محاكاة فحص قاعدة البيانات
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // محاكاة نجاح الاتصال
                        this.dbVerified = true;
                        this.companyName = 'شركة افق الخليج'; // يمكن تحديثها من الخادم
                        this.showConnectionStatus('success', 'تم الاتصال بقاعدة البيانات بنجاح', 'fas fa-check-circle');

                    } catch (error) {
                        this.showConnectionStatus('error', 'فشل في الاتصال بقاعدة البيانات', 'fas fa-times-circle');
                    } finally {
                        this.loading = false;
                    }
                },

                showConnectionStatus(type, message, icon) {
                    this.connectionStatus = {
                        show: true,
                        type: type,
                        message: message,
                        icon: icon
                    };
                }
            }))
        })
    </script>
</body>
</html>
