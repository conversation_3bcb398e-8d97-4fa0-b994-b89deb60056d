{#
    مكون الأنشطة الأخيرة
    
    الاستخدام:
    {% include 'components/recent-activity.html' with title='الأنشطة الأخيرة' activities=activities maxItems=5 showAll=True %}
#}

{% load static %}

<div class="bg-white dark:bg-gray-800 shadow-md hover:shadow-lg rounded-xl p-6 transition-all duration-300">
    
    <!-- رأس القسم -->
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-history text-white text-sm"></i>
            </div>
            <h3 class="font-bold text-gray-700 dark:text-gray-200 text-lg">{{ title|default:'الأنشطة الأخيرة' }}</h3>
        </div>
        
        {% if showAll and activities|length > maxItems|default:5 %}
            <a href="/activities" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors duration-200">
                عرض الكل ({{ activities|length }})
                <i class="fas fa-arrow-left mr-1"></i>
            </a>
        {% endif %}
    </div>
    
    <!-- قائمة الأنشطة -->
    {% if activities %}
        <div class="space-y-3">
            {% for activity in activities|slice:":"|add:maxItems|default:5 %}
                <div class="flex items-start space-x-3 space-x-reverse p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    
                    <!-- أيقونة النشاط -->
                    <div class="flex-shrink-0 mt-1">
                        {% if activity.type == 'worker' %}
                            <i class="fas fa-user text-green-500"></i>
                        {% elif activity.type == 'client' %}
                            <i class="fas fa-user-tie text-cyan-500"></i>
                        {% elif activity.type == 'contract' %}
                            <i class="fas fa-file-contract text-blue-500"></i>
                        {% elif activity.type == 'service' %}
                            <i class="fas fa-concierge-bell text-purple-500"></i>
                        {% elif activity.type == 'delete' %}
                            <i class="fas fa-trash text-red-500"></i>
                        {% elif activity.type == 'edit' %}
                            <i class="fas fa-edit text-yellow-500"></i>
                        {% elif activity.type == 'create' %}
                            <i class="fas fa-plus text-green-500"></i>
                        {% else %}
                            <i class="fas fa-info-circle text-gray-500"></i>
                        {% endif %}
                    </div>
                    
                    <!-- محتوى النشاط -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-600 dark:text-gray-300 font-medium">
                                {{ activity.description|default:'نشاط غير محدد' }}
                            </p>
                            <span class="text-xs text-gray-400 dark:text-gray-500 whitespace-nowrap mr-2">
                                {{ activity.time|default:'منذ قليل' }}
                            </span>
                        </div>
                        
                        {% if activity.details %}
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ activity.details }}
                            </p>
                        {% endif %}
                        
                        {% if activity.user %}
                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                <i class="fas fa-user mr-1"></i>
                                بواسطة: {{ activity.user }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <!-- إجراءات إضافية -->
                    {% if activity.link %}
                        <div class="flex-shrink-0">
                            <a href="{{ activity.link }}" 
                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200">
                                <i class="fas fa-external-link-alt text-xs"></i>
                            </a>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- حالة عدم وجود أنشطة -->
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-history text-2xl text-gray-400"></i>
            </div>
            <h4 class="text-lg font-medium text-gray-500 dark:text-gray-400 mb-2">لا توجد أنشطة حديثة</h4>
            <p class="text-sm text-gray-400 dark:text-gray-500">لم يتم تسجيل أي أنشطة مؤخراً</p>
        </div>
    {% endif %}
    
    <!-- قدم القسم -->
    {% if activities %}
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>
                    <i class="fas fa-clock mr-1"></i>
                    آخر تحديث: {% now "H:i" %}
                </span>
                <span>
                    عرض {% if activities|length > maxItems|default:5 %}{{ maxItems|default:5 }}{% else %}{{ activities|length }}{% endif %} من {{ activities|length }}
                </span>
            </div>
        </div>
    {% endif %}
</div>
