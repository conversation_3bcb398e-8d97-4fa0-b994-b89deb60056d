{% extends 'layouts/super_admin.html' %}

{% block title %}خطط الاشتراك{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-credit-card ml-2"></i>
                    خطط الاشتراك
                </h1>
                <p class="text-purple-100">
                    إدارة خطط الاشتراك المتاحة للشركات
                </p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold">{{ plans.count }}</div>
                <div class="text-sm text-purple-100">خطة متاحة</div>
            </div>
        </div>
    </div>

    <!-- Add New Plan Button -->
    <div class="flex justify-end">
        <button class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>
            إضافة خطة جديدة
        </button>
    </div>

    <!-- Plans Grid -->
    {% if plans %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for plan in plans %}
        <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <!-- Plan Header -->
            <div class="bg-gradient-to-r {% if plan.plan_type == 'basic' %}from-green-500 to-green-600{% elif plan.plan_type == 'standard' %}from-blue-500 to-blue-600{% elif plan.plan_type == 'premium' %}from-purple-500 to-purple-600{% else %}from-gray-800 to-gray-900{% endif %} p-6 text-white text-center">
                <h3 class="text-xl font-bold mb-2">{{ plan.name }}</h3>
                <div class="text-3xl font-bold mb-1">{{ plan.price }} ريال</div>
                <div class="text-sm opacity-90">{{ plan.get_billing_cycle_display }}</div>
                {% if plan.billing_cycle != 'monthly' %}
                <div class="text-xs opacity-75 mt-1">
                    ({{ plan.get_price_per_month|floatformat:2 }} ريال/شهر)
                </div>
                {% endif %}
            </div>

            <!-- Plan Details -->
            <div class="p-6">
                <p class="text-gray-600 text-sm mb-4">{{ plan.description }}</p>
                
                <!-- Features -->
                <div class="space-y-3 mb-6">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">المستخدمين:</span>
                        <span class="font-medium">{{ plan.max_users }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">العمال:</span>
                        <span class="font-medium">{{ plan.max_workers }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">العملاء:</span>
                        <span class="font-medium">{{ plan.max_clients }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">التخزين:</span>
                        <span class="font-medium">{{ plan.storage_limit_gb }} جيجابايت</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">الفترة التجريبية:</span>
                        <span class="font-medium">{{ plan.trial_days }} يوم</span>
                    </div>
                </div>

                <!-- Additional Features -->
                {% if plan.features %}
                <div class="border-t border-gray-200 pt-4 mb-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">الميزات المتاحة:</h4>
                    <div class="space-y-2">
                        {% for feature, enabled in plan.features.items %}
                        {% if enabled %}
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check text-green-500 ml-2"></i>
                            <span class="text-gray-600">
                                {% if feature == 'workers_management' %}إدارة العمال
                                {% elif feature == 'clients_management' %}إدارة العملاء
                                {% elif feature == 'basic_reports' %}التقارير الأساسية
                                {% elif feature == 'advanced_reports' %}التقارير المتقدمة
                                {% elif feature == 'email_support' %}الدعم عبر البريد
                                {% elif feature == 'phone_support' %}الدعم الهاتفي
                                {% elif feature == 'priority_support' %}الدعم المتميز
                                {% else %}{{ feature }}{% endif %}
                            </span>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Status -->
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">الحالة:</span>
                    {% if plan.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle ml-1"></i>
                            نشط
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle ml-1"></i>
                            معطل
                        </span>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <button class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit ml-1"></i>
                        تعديل
                    </button>
                    <button class="px-3 py-2 {% if plan.is_active %}bg-red-600 hover:bg-red-700{% else %}bg-green-600 hover:bg-green-700{% endif %} text-white text-sm rounded-lg transition-colors">
                        <i class="fas fa-{% if plan.is_active %}pause{% else %}play{% endif %} ml-1"></i>
                        {% if plan.is_active %}تعطيل{% else %}تفعيل{% endif %}
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="bg-white rounded-xl shadow-lg p-12 text-center">
        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-credit-card text-2xl text-purple-600"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد خطط اشتراك</h3>
        <p class="text-gray-500 mb-6">لم يتم إنشاء أي خطط اشتراك بعد</p>
        <button class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>
            إنشاء أول خطة اشتراك
        </button>
    </div>
    {% endif %}

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-layer-group text-green-600 text-xl"></i>
            </div>
            <div class="text-2xl font-bold text-gray-900">{{ plans.count }}</div>
            <div class="text-sm text-gray-500">إجمالي الخطط</div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-check-circle text-blue-600 text-xl"></i>
            </div>
            <div class="text-2xl font-bold text-gray-900">{{ plans|length }}</div>
            <div class="text-sm text-gray-500">خطط نشطة</div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-star text-purple-600 text-xl"></i>
            </div>
            <div class="text-2xl font-bold text-gray-900">0</div>
            <div class="text-sm text-gray-500">خطط متميزة</div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
            </div>
            <div class="text-2xl font-bold text-gray-900">0</div>
            <div class="text-sm text-gray-500">اشتراكات نشطة</div>
        </div>
    </div>

    <!-- Under Development Notice -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-600 text-xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-blue-800">
                    قسم تحت التطوير
                </h3>
                <p class="text-blue-700 mt-1">
                    هذا القسم قيد التطوير حالياً. سيتم إضافة المزيد من الميزات قريباً.
                </p>
                <div class="mt-4">
                    <h4 class="font-medium text-blue-800 mb-2">الميزات المخططة:</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> إضافة وتعديل خطط الاشتراك</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> تفعيل وتعطيل الخطط</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> إدارة الميزات والحدود</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> تقارير الاشتراكات</li>
                        <li><i class="fas fa-check-circle text-green-500 ml-1"></i> إعدادات التسعير المتقدمة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
