{% extends 'layouts/dashboard.html' %}
{% load static %}

{% block title %}إدارة العمال | منصة استقدامي السحابية{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة العمال</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">إدارة العمال حسب نوع العقد والخدمة</p>
        </div>
    </div>

    <!-- إحصائيات عامة -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6 mb-8">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">
            <i class="fas fa-chart-pie text-[#2563EB] dark:text-[#2563EB] ml-2"></i>
            إحصائيات العمال
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-gray-50 dark:bg-[#0F172A] rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-[#2563EB] dark:text-[#2563EB]" data-stat="total_workers">{{ total_workers }}</div>
                <div class="text-sm text-gray-600 dark:text-slate-400 mt-1">إجمالي العمال</div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-green-500 dark:text-green-400" data-stat="active_workers">{{ active_workers }}</div>
                <div class="text-sm text-gray-600 dark:text-slate-400 mt-1">العمال النشطين</div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-amber-500 dark:text-amber-400" data-stat="on_leave_workers">{{ on_leave_workers }}</div>
                <div class="text-sm text-gray-600 dark:text-slate-400 mt-1">في إجازة</div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-red-500 dark:text-red-400" data-stat="inactive_workers">{{ inactive_workers }}</div>
                <div class="text-sm text-gray-600 dark:text-slate-400 mt-1">غير نشطين</div>
            </div>
        </div>
    </div>

    <!-- Worker Categories Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- بطاقة 1: عمال العقود الدائمية -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-r-4 border-[#2563EB] dark:border-[#2563EB]">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عمال العقود الدائمية</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">عرض وتعديل العمال المرتبطين بالعقود الدائمية</p>
                        <div class="mt-2 text-[#2563EB] dark:text-[#2563EB] font-bold text-xl" data-stat="contract_workers_count">{{ contract_workers_count }}</div>
                    </div>
                    <div class="text-4xl text-[#2563EB] dark:text-[#2563EB]">
                        <i class="fas fa-file-contract"></i>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] p-3 flex justify-between items-center">
                <a href="{% url 'workers:worker_list' %}?type=contract" class="text-[#2563EB] dark:text-[#2563EB] hover:underline text-sm flex items-center">
                    <span>عرض الكل</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
                <a href="{% url 'workers:worker_create' %}?type=contract" class="bg-[#2563EB] text-white px-3 py-1 rounded-md hover:bg-[#1E40AF] transition-colors duration-300 text-sm flex items-center">
                    <i class="fas fa-plus ml-1"></i>
                    <span>إضافة عامل</span>
                </a>
            </div>
        </div>

        <!-- بطاقة 2: عمال الخدمات المخصصة -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-r-4 border-[#F97316] dark:border-[#F97316]">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عمال الخدمات المخصصة</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">خدمات مخصصة أو غير نمطية</p>
                        <div class="mt-2 text-[#F97316] dark:text-[#F97316] font-bold text-xl" data-stat="custom_workers_count">{{ custom_workers_count }}</div>
                    </div>
                    <div class="text-4xl text-[#F97316] dark:text-[#F97316]">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] p-3 flex justify-between items-center">
                <a href="{% url 'workers:worker_list' %}?type=custom" class="text-[#F97316] dark:text-[#F97316] hover:underline text-sm flex items-center">
                    <span>عرض الكل</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
                <a href="{% url 'workers:worker_create' %}?type=custom" class="bg-[#F97316] text-white px-3 py-1 rounded-md hover:bg-[#EA580C] transition-colors duration-300 text-sm flex items-center">
                    <i class="fas fa-plus ml-1"></i>
                    <span>إضافة عامل</span>
                </a>
            </div>
        </div>

        <!-- بطاقة 3: عمال العقود الشهرية -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-r-4 border-[#A855F7] dark:border-[#A855F7]">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عمال العقود الشهرية</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العمال المرتبطين بالخدمات الشهرية المتكررة</p>
                        <div class="mt-2 text-[#A855F7] dark:text-[#A855F7] font-bold text-xl" data-stat="monthly_workers_count">{{ monthly_workers_count }}</div>
                    </div>
                    <div class="text-4xl text-[#A855F7] dark:text-[#A855F7]">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-[#0F172A] p-3 flex justify-between items-center">
                <a href="{% url 'workers:worker_list' %}?type=monthly" class="text-[#A855F7] dark:text-[#A855F7] hover:underline text-sm flex items-center">
                    <span>عرض الكل</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
                <a href="{% url 'workers:worker_create' %}?type=monthly" class="bg-[#A855F7] text-white px-3 py-1 rounded-md hover:bg-[#9333EA] transition-colors duration-300 text-sm flex items-center">
                    <i class="fas fa-plus ml-1"></i>
                    <span>إضافة عامل</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Workers Section -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white">
                <i class="fas fa-users text-[#2563EB] dark:text-[#2563EB] ml-2"></i>
                آخر العمال المضافين
            </h3>
            <a href="{% url 'workers:worker_list' %}" class="text-[#2563EB] hover:text-[#1E40AF] dark:text-[#2563EB] dark:hover:text-[#1E40AF] flex items-center">
                عرض الكل
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        {% if recent_workers %}
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm text-right">
                <thead class="bg-gray-50 dark:bg-[#0F172A] text-gray-600 dark:text-slate-300">
                    <tr>
                        <th class="py-3 px-4 font-medium text-center">#</th>
                        <th class="py-3 px-4 font-medium">الاسم</th>
                        <th class="py-3 px-4 font-medium">الجنسية</th>
                        <th class="py-3 px-4 font-medium">رقم الجواز</th>
                        <th class="py-3 px-4 font-medium">النوع</th>
                        <th class="py-3 px-4 font-medium">تاريخ الإضافة</th>
                        <th class="py-3 px-4 font-medium text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-slate-600">
                    {% for worker in recent_workers %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-150">
                        <td class="py-3 px-4 text-center">
                            <div class="flex items-center justify-center">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <input type="checkbox" class="worker-checkbox" value="{{ worker.id }}">
                                </div>
                            </div>
                        </td>
                        <td class="py-3 px-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8 ml-3">
                                    {% if worker.photo %}
                                        <img class="h-8 w-8 rounded-full object-cover" src="{{ worker.photo.url }}" alt="{{ worker.first_name }}">
                                    {% else %}
                                        <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-slate-900 flex items-center justify-center text-blue-500 dark:text-blue-300">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-white">{{ worker.first_name }} {{ worker.last_name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="py-3 px-4 dark:text-slate-300">{{ worker.get_nationality_display }}</td>
                        <td class="py-3 px-4 dark:text-slate-300">{{ worker.passport_number }}</td>
                        <td class="py-3 px-4 dark:text-slate-300">{{ worker.get_gender_display }}</td>
                        <td class="py-3 px-4 dark:text-slate-300">{{ worker.date_joined|date:"Y-m-d" }}</td>
                        <td class="py-3 px-4 text-center">
                            <div class="flex items-center justify-center space-x-3 space-x-reverse">
                                <a href="{% url 'workers:worker_detail' worker.id %}" class="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'workers:worker_update' worker.id %}" class="text-yellow-600 hover:text-yellow-800 dark:text-amber-300 dark:hover:text-amber-200" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                    data-delete-url="{% url 'workers:worker_delete' worker.id %}"
                                    onclick="window.deleteItem(this.getAttribute('data-delete-url'));"
                                    class="text-red-600 hover:text-red-800 dark:text-red-300 dark:hover:text-red-200 border-none bg-transparent p-0"
                                    title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500 dark:text-slate-400">
            <i class="fas fa-users text-4xl mb-3 text-gray-300 dark:text-slate-600"></i>
            <p class="text-lg font-medium dark:text-slate-300">لا يوجد عمال مضافين حديثاً</p>
            <a href="{% url 'workers:worker_create' %}" class="mt-4 inline-block bg-primary dark:bg-slate-700 text-white px-4 py-2 rounded-md hover:bg-primary-dark dark:hover:bg-slate-600 transition-colors duration-300">
                <i class="fas fa-plus ml-2"></i>
                إضافة عامل جديد
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Recent Activities Section -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white">
                <i class="fas fa-history text-[#2563EB] dark:text-[#2563EB] ml-2"></i>
                الأنشطة الأخيرة
            </h3>
        </div>

        {% if recent_activities %}
        <div class="space-y-4">
            {% for activity in recent_activities %}
            <div class="flex items-start border-r-4 {% if 'added' in activity.activity_type %}border-green-500{% elif 'updated' in activity.activity_type %}border-blue-500{% elif 'deleted' in activity.activity_type %}border-red-500{% else %}border-gray-500{% endif %} pr-4 py-2">
                <div class="rounded-full {% if 'added' in activity.activity_type %}bg-green-100 text-green-600{% elif 'updated' in activity.activity_type %}bg-blue-100 text-blue-600{% elif 'deleted' in activity.activity_type %}bg-red-100 text-red-600{% else %}bg-gray-100 text-gray-600{% endif %} p-2 ml-3">
                    <i class="fas {% if 'added' in activity.activity_type %}fa-plus{% elif 'updated' in activity.activity_type %}fa-edit{% elif 'deleted' in activity.activity_type %}fa-trash{% else %}fa-check{% endif %}"></i>
                </div>
                <div class="flex-1">
                    <div class="flex justify-between items-start">
                        <p class="font-medium text-gray-900 dark:text-white">{{ activity.description }}</p>
                        <span class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">{{ activity.date_created|date:"H:i Y-m-d" }}</span>
                    </div>
                    {% if activity.related_worker %}
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <a href="{% url 'workers:worker_detail' activity.related_worker.id %}" class="text-blue-600 hover:underline dark:text-blue-400">
                            {{ activity.related_worker.first_name }} {{ activity.related_worker.last_name }}
                        </a>
                    </p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500 dark:text-slate-400">
            <i class="fas fa-history text-4xl mb-3 text-gray-300 dark:text-slate-600"></i>
            <p class="text-lg font-medium dark:text-slate-300">لا توجد أنشطة حديثة</p>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">
            <i class="fas fa-bolt text-[#2563EB] dark:text-[#2563EB] ml-2"></i>
            إجراءات سريعة
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'workers:worker_create' %}" class="flex items-center p-4 bg-gray-50 dark:bg-[#1E293B] rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-300">
                <div class="rounded-full bg-[#A855F7] bg-opacity-10 dark:bg-[#1E293B] p-3 ml-3 w-12 h-12 flex items-center justify-center">
                    <i class="fas fa-user-plus text-[#A855F7] dark:text-[#A855F7]"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">إضافة عامل جديد</h4>
                    <p class="text-sm text-gray-600 dark:text-slate-400">إضافة عامل جديد إلى النظام</p>
                </div>
            </a>
            <a href="{% url 'workers:worker_list' %}" class="flex items-center p-4 bg-gray-50 dark:bg-[#1E293B] rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-300">
                <div class="rounded-full bg-[#2563EB] bg-opacity-10 dark:bg-[#1E293B] p-3 ml-3 w-12 h-12 flex items-center justify-center">
                    <i class="fas fa-list text-[#2563EB] dark:text-[#2563EB]"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">قائمة العمال</h4>
                    <p class="text-sm text-gray-600 dark:text-slate-400">عرض قائمة جميع العمال</p>
                </div>
            </a>
            <a href="{% url 'workers:contract_workers' %}" class="flex items-center p-4 bg-gray-50 dark:bg-[#1E293B] rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-300">
                <div class="rounded-full bg-[#F97316] bg-opacity-10 dark:bg-[#1E293B] p-3 ml-3 w-12 h-12 flex items-center justify-center">
                    <i class="fas fa-file-contract text-[#F97316] dark:text-[#F97316]"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">عمال العقود الدائمية</h4>
                    <p class="text-sm text-gray-600 dark:text-slate-400">عرض قائمة عمال العقود الدائمية</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- JavaScript for Checkbox Functionality and Real-time Statistics -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.worker-checkbox');

        // Create select all checkbox
        const selectAllCheckbox = document.createElement('input');
        selectAllCheckbox.type = 'checkbox';
        selectAllCheckbox.className = 'select-all-checkbox';

        // Add select all checkbox to the header
        const headerCheckboxCell = document.querySelector('thead tr th:first-child');
        if (headerCheckboxCell) {
            headerCheckboxCell.innerHTML = '';
            headerCheckboxCell.appendChild(selectAllCheckbox);
        }

        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });

        // Individual checkbox change
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Check if all checkboxes are checked
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
            });
        });

        // تحديث الإحصائيات في الوقت الفعلي
        function updateStatistics() {
            fetch('/workers/api/statistics/')
                .then(response => response.json())
                .then(data => {
                    // تحديث إحصائيات العمال حسب النوع
                    document.querySelectorAll('[data-stat="contract_workers_count"]').forEach(el => {
                        el.textContent = data.contract_workers_count;
                    });
                    document.querySelectorAll('[data-stat="custom_workers_count"]').forEach(el => {
                        el.textContent = data.custom_workers_count;
                    });
                    document.querySelectorAll('[data-stat="monthly_workers_count"]').forEach(el => {
                        el.textContent = data.monthly_workers_count;
                    });

                    // تحديث الإحصائيات العامة
                    document.querySelectorAll('[data-stat="total_workers"]').forEach(el => {
                        el.textContent = data.total_workers;
                    });
                    document.querySelectorAll('[data-stat="active_workers"]').forEach(el => {
                        el.textContent = data.active_workers;
                    });
                    document.querySelectorAll('[data-stat="inactive_workers"]').forEach(el => {
                        el.textContent = data.inactive_workers;
                    });
                    document.querySelectorAll('[data-stat="on_leave_workers"]').forEach(el => {
                        el.textContent = data.on_leave_workers;
                    });
                })
                .catch(error => console.error('Error updating statistics:', error));
        }

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(updateStatistics, 30000);
    });
</script>
{% endblock %}
