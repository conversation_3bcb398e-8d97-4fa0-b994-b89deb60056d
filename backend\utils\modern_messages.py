"""
نظام الرسائل العصري
Modern Messages System for Django
"""

from django.contrib import messages
from django.http import JsonResponse


class ModernMessages:
    """فئة لإدارة الرسائل العصرية"""
    
    # أيقونات الرسائل
    ICONS = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️',
        'delete': '🗑️',
        'edit': '✏️',
        'add': '➕',
        'save': '💾',
        'upload': '📤',
        'download': '📥',
        'email': '📧',
        'phone': '📞',
        'user': '👤',
        'company': '🏢',
        'contract': '📄',
        'money': '💰',
        'calendar': '📅',
        'settings': '⚙️',
        'security': '🔒',
        'backup': '💾',
        'restore': '🔄'
    }
    
    @classmethod
    def success(cls, request, message, icon='success'):
        """رسالة نجاح"""
        icon_symbol = cls.ICONS.get(icon, cls.ICONS['success'])
        formatted_message = f"{icon_symbol} {message}"
        messages.success(request, formatted_message)
    
    @classmethod
    def error(cls, request, message, icon='error'):
        """رسالة خطأ"""
        icon_symbol = cls.ICONS.get(icon, cls.ICONS['error'])
        formatted_message = f"{icon_symbol} {message}"
        messages.error(request, formatted_message)
    
    @classmethod
    def warning(cls, request, message, icon='warning'):
        """رسالة تحذير"""
        icon_symbol = cls.ICONS.get(icon, cls.ICONS['warning'])
        formatted_message = f"{icon_symbol} {message}"
        messages.warning(request, formatted_message)
    
    @classmethod
    def info(cls, request, message, icon='info'):
        """رسالة معلومات"""
        icon_symbol = cls.ICONS.get(icon, cls.ICONS['info'])
        formatted_message = f"{icon_symbol} {message}"
        messages.info(request, formatted_message)
    
    @classmethod
    def json_response(cls, success=True, message="", data=None, icon=None):
        """استجابة JSON للطلبات AJAX"""
        if icon:
            icon_symbol = cls.ICONS.get(icon, '')
            message = f"{icon_symbol} {message}" if icon_symbol else message
        
        response_data = {
            'success': success,
            'message': message
        }
        
        if data:
            response_data.update(data)
        
        return JsonResponse(response_data)


# دوال مساعدة سريعة
def success_message(request, message, icon='success'):
    """رسالة نجاح سريعة"""
    ModernMessages.success(request, message, icon)

def error_message(request, message, icon='error'):
    """رسالة خطأ سريعة"""
    ModernMessages.error(request, message, icon)

def warning_message(request, message, icon='warning'):
    """رسالة تحذير سريعة"""
    ModernMessages.warning(request, message, icon)

def info_message(request, message, icon='info'):
    """رسالة معلومات سريعة"""
    ModernMessages.info(request, message, icon)

def json_success(message, data=None, icon='success'):
    """استجابة JSON نجاح"""
    return ModernMessages.json_response(True, message, data, icon)

def json_error(message, data=None, icon='error'):
    """استجابة JSON خطأ"""
    return ModernMessages.json_response(False, message, data, icon)


# رسائل محددة للعمليات الشائعة
class CommonMessages:
    """رسائل شائعة للعمليات المختلفة"""
    
    @staticmethod
    def created(request, item_name, icon='add'):
        """رسالة إنشاء عنصر"""
        ModernMessages.success(request, f"تم إنشاء {item_name} بنجاح", icon)
    
    @staticmethod
    def updated(request, item_name, icon='edit'):
        """رسالة تحديث عنصر"""
        ModernMessages.success(request, f"تم تحديث {item_name} بنجاح", icon)
    
    @staticmethod
    def deleted(request, item_name, icon='delete'):
        """رسالة حذف عنصر"""
        ModernMessages.success(request, f"تم حذف {item_name} بنجاح", icon)
    
    @staticmethod
    def saved(request, item_name, icon='save'):
        """رسالة حفظ عنصر"""
        ModernMessages.success(request, f"تم حفظ {item_name} بنجاح", icon)
    
    @staticmethod
    def uploaded(request, item_name, icon='upload'):
        """رسالة رفع ملف"""
        ModernMessages.success(request, f"تم رفع {item_name} بنجاح", icon)
    
    @staticmethod
    def sent(request, item_name, icon='email'):
        """رسالة إرسال"""
        ModernMessages.success(request, f"تم إرسال {item_name} بنجاح", icon)
    
    @staticmethod
    def validation_error(request, icon='error'):
        """رسالة خطأ في التحقق"""
        ModernMessages.error(request, "يرجى تصحيح الأخطاء أدناه", icon)
    
    @staticmethod
    def permission_denied(request, icon='security'):
        """رسالة عدم وجود صلاحية"""
        ModernMessages.error(request, "ليس لديك صلاحية للوصول لهذه الصفحة", icon)
    
    @staticmethod
    def not_found(request, item_name, icon='error'):
        """رسالة عدم وجود عنصر"""
        ModernMessages.error(request, f"لم يتم العثور على {item_name}", icon)
    
    @staticmethod
    def server_error(request, icon='error'):
        """رسالة خطأ في الخادم"""
        ModernMessages.error(request, "حدث خطأ في الخادم، يرجى المحاولة مرة أخرى", icon)


# رسائل خاصة بالعملاء
class ClientMessages:
    """رسائل خاصة بإدارة العملاء"""
    
    @staticmethod
    def created(request, client_name):
        ModernMessages.success(request, f"تم إضافة العميل {client_name} بنجاح", 'user')
    
    @staticmethod
    def updated(request, client_name):
        ModernMessages.success(request, f"تم تحديث بيانات العميل {client_name} بنجاح", 'edit')
    
    @staticmethod
    def deleted(request, client_name):
        ModernMessages.success(request, f"تم حذف العميل {client_name} بنجاح", 'delete')
    
    @staticmethod
    def status_changed(request, client_name, new_status):
        status_text = {
            'active': 'تفعيل',
            'inactive': 'إلغاء تفعيل',
            'pending': 'تعليق',
            'suspended': 'إيقاف'
        }.get(new_status, new_status)
        ModernMessages.success(request, f"تم {status_text} العميل {client_name}", 'settings')


# رسائل خاصة بالعقود
class ContractMessages:
    """رسائل خاصة بإدارة العقود"""
    
    @staticmethod
    def created(request, contract_number):
        ModernMessages.success(request, f"تم إنشاء العقد {contract_number} بنجاح", 'contract')
    
    @staticmethod
    def updated(request, contract_number):
        ModernMessages.success(request, f"تم تحديث العقد {contract_number} بنجاح", 'edit')
    
    @staticmethod
    def deleted(request, contract_number):
        ModernMessages.success(request, f"تم حذف العقد {contract_number} بنجاح", 'delete')
    
    @staticmethod
    def renewed(request, contract_number):
        ModernMessages.success(request, f"تم تجديد العقد {contract_number} بنجاح", 'calendar')


# رسائل خاصة بالعمال
class WorkerMessages:
    """رسائل خاصة بإدارة العمال"""
    
    @staticmethod
    def created(request, worker_name):
        ModernMessages.success(request, f"تم إضافة العامل {worker_name} بنجاح", 'user')
    
    @staticmethod
    def updated(request, worker_name):
        ModernMessages.success(request, f"تم تحديث بيانات العامل {worker_name} بنجاح", 'edit')
    
    @staticmethod
    def deleted(request, worker_name):
        ModernMessages.success(request, f"تم حذف العامل {worker_name} بنجاح", 'delete')


# رسائل خاصة بالنظام
class SystemMessages:
    """رسائل خاصة بالنظام"""
    
    @staticmethod
    def backup_created(request):
        ModernMessages.success(request, "تم إنشاء النسخة الاحتياطية بنجاح", 'backup')
    
    @staticmethod
    def backup_restored(request):
        ModernMessages.success(request, "تم استعادة النسخة الاحتياطية بنجاح", 'restore')
    
    @staticmethod
    def settings_saved(request):
        ModernMessages.success(request, "تم حفظ الإعدادات بنجاح", 'settings')
    
    @staticmethod
    def email_sent(request):
        ModernMessages.success(request, "تم إرسال البريد الإلكتروني بنجاح", 'email')
    
    @staticmethod
    def password_changed(request):
        ModernMessages.success(request, "تم تغيير كلمة المرور بنجاح", 'security')
    
    @staticmethod
    def login_success(request):
        ModernMessages.success(request, "تم تسجيل الدخول بنجاح", 'user')
    
    @staticmethod
    def logout_success(request):
        ModernMessages.info(request, "تم تسجيل الخروج بنجاح", 'user')
