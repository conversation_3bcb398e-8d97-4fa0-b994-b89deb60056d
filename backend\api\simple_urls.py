from django.urls import path
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import permission_classes


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_root(request):
    """
    الصفحة الرئيسية لواجهة برمجة التطبيقات (API) لنظام إدارة العمالة.
    """
    return Response({
        'message': 'مرحباً بك في واجهة برمجة التطبيقات لنظام إدارة العمالة',
        'version': 'v1.0',
    })


urlpatterns = [
    path('', api_root, name='api-root'),
]
