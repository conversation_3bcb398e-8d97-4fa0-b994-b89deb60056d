<!DOCTYPE html>
<html class="h-full" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'لوحة تحكم المسؤول الأعلى | استقدامي')</title>

    <!-- Google Fonts - Tajawal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'admin-primary': '#0F2557',
                        'admin-secondary': '#1E5F74',
                        'admin-accent': '#4B8F8C',
                        'admin-light': '#E5F1F1',
                        'admin-dark': '#0A1A3F',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            },
            darkMode: 'class',
        }
    </script>

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer></script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        
        .hover-effect {
            transition: all 0.3s ease;
        }
        
        .hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .status-badge {
            @apply px-2 py-1 rounded-full text-xs font-medium;
        }
        
        .status-active {
            @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
        }
        
        .status-inactive {
            @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
        }
        
        .status-pending {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
        }
    </style>

    @yield('extra_css')
</head>
<body class="min-h-screen flex flex-col bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200" x-data="{ sidebarOpen: true, darkMode: localStorage.getItem('darkMode') === 'true' }" :class="{ 'dark': darkMode }">
    
    <!-- Top Navigation Bar -->
    <header class="bg-admin-primary dark:bg-admin-dark text-white shadow-md z-30 fixed top-0 inset-x-0">
        <div class="flex justify-between items-center px-4 py-2">
            <!-- Left - Logo and System Name -->
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                <button @click="sidebarOpen = !sidebarOpen" class="p-1 rounded-full hover:bg-admin-secondary transition-colors">
                    <i class="fas fa-bars"></i>
                </button>
                <img src="/static/images/logo.webp" alt="شعار المنصة"
                     class="w-10 h-10 object-contain hover:animate-pulse transition-all duration-300"
                     onerror="this.src='/static/images/logo-placeholder.png'; this.onerror=null;">
                <div class="flex flex-col">
                    <span class="text-lg font-bold text-admin-accent">استقدامي</span>
                    <span class="text-xs text-gray-300">لوحة المسؤول الأعلى</span>
                </div>
            </div>
            
            <!-- Right - User Menu and Dark Mode Toggle -->
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <!-- Dark Mode Toggle -->
                <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)" class="p-1 rounded-full hover:bg-admin-secondary transition-colors">
                    <i class="fas" :class="darkMode ? 'fa-sun' : 'fa-moon'"></i>
                </button>
                
                <!-- User Menu -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-2 rtl:space-x-reverse hover:bg-admin-secondary p-1 rounded-full transition-colors">
                        <div class="w-8 h-8 rounded-full bg-admin-accent flex items-center justify-center text-white">
                            <i class="fas fa-user"></i>
                        </div>
                    </button>
                    
                    <!-- Dropdown Menu -->
                    <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-xl text-xs z-50 border border-gray-200 dark:border-gray-700 overflow-hidden transform origin-top-right transition-all duration-300">
                        <div class="p-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="font-bold text-gray-900 dark:text-gray-100 text-xs">{{ auth()->user()->username ?? 'المسؤول الأعلى' }}</div>
                            <div class="text-gray-500 dark:text-gray-400 text-xs">المسؤول الأعلى</div>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('super_admin.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                <i class="fas fa-user-circle ml-2"></i>
                                الملف الشخصي
                            </a>
                            <a href="{{ route('super_admin.settings') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                <i class="fas fa-cog ml-2"></i>
                                الإعدادات
                            </a>
                            <form method="POST" action="{{ route('super_admin.logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700">
                                    <i class="fas fa-sign-out-alt ml-2"></i>
                                    تسجيل الخروج
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container with Sidebar -->
    <div class="flex flex-1 pt-14">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 pt-14 bg-admin-primary dark:bg-admin-dark text-white z-20 transition-all duration-300 shadow-lg"
               :class="sidebarOpen ? 'w-64' : 'w-16'" 
               style="transition: width 0.3s ease;">
            <div class="h-full flex flex-col overflow-y-auto">
                <!-- Sidebar Menu -->
                <nav class="flex-1 py-4 px-2">
                    <ul class="space-y-1">
                        <!-- Dashboard -->
                        <li>
                            <a href="{{ route('super_admin.dashboard') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-tachometer-alt text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">لوحة التحكم</span>
                            </a>
                        </li>
                        
                        <!-- Companies Management -->
                        <li>
                            <a href="{{ route('super_admin.companies') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-building text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">إدارة الشركات</span>
                            </a>
                        </li>
                        
                        <!-- Database Management -->
                        <li>
                            <a href="{{ route('super_admin.database') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-database text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">إدارة قواعد البيانات</span>
                            </a>
                        </li>
                        
                        <!-- Subscription Settings -->
                        <li>
                            <a href="{{ route('super_admin.subscriptions') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-credit-card text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">إعدادات الاشتراك</span>
                            </a>
                        </li>
                        
                        <!-- Backup Management -->
                        <li>
                            <a href="{{ route('super_admin.backups') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-save text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">إدارة النسخ الاحتياطي</span>
                            </a>
                        </li>
                        
                        <!-- Reports -->
                        <li>
                            <a href="{{ route('super_admin.reports') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-chart-bar text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">التقارير العامة</span>
                            </a>
                        </li>
                        
                        <!-- System Logs -->
                        <li>
                            <a href="{{ route('super_admin.logs') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-history text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">سجلات النظام</span>
                            </a>
                        </li>
                        
                        <!-- Settings -->
                        <li>
                            <a href="{{ route('super_admin.settings') }}" class="flex items-center px-3 py-2 rounded-lg hover:bg-admin-secondary transition-colors" :class="{'justify-center': !sidebarOpen}">
                                <i class="fas fa-cog text-admin-accent w-5 h-5 flex items-center justify-center"></i>
                                <span class="mr-3" x-show="sidebarOpen">الإعدادات</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <!-- Sidebar Footer -->
                <div class="p-3 border-t border-admin-secondary text-xs text-center" x-show="sidebarOpen">
                    <p>استقدامي - الإصدار 1.0</p>
                    <p class="text-admin-accent">{{ date('Y-m-d') }}</p>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 transition-all duration-300" :class="sidebarOpen ? 'mr-64' : 'mr-16'">
            <div class="container mx-auto px-4 py-6">
                <!-- Page Header -->
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-admin-primary dark:text-admin-accent">@yield('page_title', 'لوحة التحكم')</h1>
                    <p class="text-gray-600 dark:text-gray-400">@yield('page_subtitle', 'مرحباً بك في لوحة تحكم المسؤول الأعلى')</p>
                </div>
                
                <!-- Page Content -->
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 left-4 z-50 flex flex-col space-y-4">
        @if(session('success'))
        <div class="toast-success bg-green-100 border-r-4 border-green-500 text-green-700 p-4 rounded shadow-md flex items-center" role="alert">
            <i class="fas fa-check-circle ml-3 text-green-500"></i>
            <div>{{ session('success') }}</div>
            <button class="mr-auto text-green-500 hover:text-green-700" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        @endif

        @if(session('error'))
        <div class="toast-error bg-red-100 border-r-4 border-red-500 text-red-700 p-4 rounded shadow-md flex items-center" role="alert">
            <i class="fas fa-exclamation-circle ml-3 text-red-500"></i>
            <div>{{ session('error') }}</div>
            <button class="mr-auto text-red-500 hover:text-red-700" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        @endif
    </div>

    <!-- Scripts -->
    @yield('extra_js')
    
    <script>
        // Auto-hide toast notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelectorAll('.toast-success, .toast-error').forEach(function(toast) {
                    toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                    setTimeout(function() {
                        toast.remove();
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
