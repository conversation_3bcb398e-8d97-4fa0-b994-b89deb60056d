"""
وحدة المساعدة لإدارة قواعد البيانات
تتضمن وظائف لتصفير قاعدة البيانات واستخراج هيكلها
"""

import os
import json
import sqlite3
import datetime
import subprocess
from django.conf import settings
from django.db import connections
from django.utils import timezone
import logging

# إعداد السجل
logger = logging.getLogger(__name__)

def get_database_path(database_name):
    """الحصول على المسار الكامل لملف قاعدة البيانات"""
    return os.path.join(settings.BASE_DIR, 'data', 'company_dbs', f"{database_name}.sqlite3")

def extract_database_schema(database_name):
    """
    استخراج هيكل قاعدة البيانات (الجداول والأعمدة والعلاقات)

    Args:
        database_name: اسم قاعدة البيانات

    Returns:
        dict: قاموس يحتوي على هيكل قاعدة البيانات
    """
    try:
        db_path = get_database_path(database_name)

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return {
                'success': False,
                'message': 'قاعدة البيانات غير موجودة',
                'schema': None
            }

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
        tables = [table[0] for table in cursor.fetchall()]

        schema = {
            'database_name': database_name,
            'tables': {},
            'foreign_keys': [],
            'indexes': []
        }

        # استخراج هيكل كل جدول
        for table in tables:
            # استخراج معلومات الأعمدة
            cursor.execute(f"PRAGMA table_info({table});")
            columns_info = cursor.fetchall()

            columns = {}
            primary_keys = []

            for col in columns_info:
                col_id, col_name, col_type, not_null, default_value, is_pk = col

                columns[col_name] = {
                    'type': col_type,
                    'not_null': bool(not_null),
                    'default': default_value,
                    'is_primary_key': bool(is_pk)
                }

                if is_pk:
                    primary_keys.append(col_name)

            # استخراج المفاتيح الأجنبية
            cursor.execute(f"PRAGMA foreign_key_list({table});")
            foreign_keys_info = cursor.fetchall()

            table_foreign_keys = []

            for fk in foreign_keys_info:
                fk_id, seq, ref_table, from_col, to_col, on_update, on_delete, match = fk

                foreign_key = {
                    'from_column': from_col,
                    'to_table': ref_table,
                    'to_column': to_col,
                    'on_update': on_update,
                    'on_delete': on_delete
                }

                table_foreign_keys.append(foreign_key)

                # إضافة إلى قائمة المفاتيح الأجنبية العامة
                schema['foreign_keys'].append({
                    'table': table,
                    'from_column': from_col,
                    'to_table': ref_table,
                    'to_column': to_col,
                    'on_update': on_update,
                    'on_delete': on_delete
                })

            # استخراج الفهارس
            cursor.execute(f"PRAGMA index_list({table});")
            indexes_info = cursor.fetchall()

            table_indexes = []

            for idx in indexes_info:
                idx_seq, idx_name, idx_unique = idx

                # استخراج الأعمدة المفهرسة
                cursor.execute(f"PRAGMA index_info({idx_name});")
                index_columns_info = cursor.fetchall()
                index_columns = [col[2] for col in index_columns_info]

                index = {
                    'name': idx_name,
                    'unique': bool(idx_unique),
                    'columns': index_columns
                }

                table_indexes.append(index)

                # إضافة إلى قائمة الفهارس العامة
                schema['indexes'].append({
                    'table': table,
                    'name': idx_name,
                    'unique': bool(idx_unique),
                    'columns': index_columns
                })

            # إضافة معلومات الجدول إلى المخطط
            schema['tables'][table] = {
                'columns': columns,
                'primary_keys': primary_keys,
                'foreign_keys': table_foreign_keys,
                'indexes': table_indexes
            }

        conn.close()

        return {
            'success': True,
            'message': 'تم استخراج هيكل قاعدة البيانات بنجاح',
            'schema': schema
        }

    except Exception as e:
        logger.error(f"خطأ في استخراج هيكل قاعدة البيانات: {str(e)}")
        return {
            'success': False,
            'message': f'حدث خطأ أثناء استخراج هيكل قاعدة البيانات: {str(e)}',
            'schema': None
        }

def reset_database(database_name, keep_schema=True):
    """
    تصفير قاعدة البيانات (حذف البيانات مع الحفاظ على الهيكل)

    Args:
        database_name: اسم قاعدة البيانات
        keep_schema: الاحتفاظ بهيكل قاعدة البيانات (True) أو حذفها بالكامل (False)

    Returns:
        dict: نتيجة العملية
    """
    try:
        db_path = get_database_path(database_name)

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return {
                'success': False,
                'message': 'قاعدة البيانات غير موجودة'
            }

        # إنشاء نسخة احتياطية قبل التصفير
        backup_dir = os.path.join(settings.BASE_DIR, 'data', 'backups', 'reset_backups')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f"{database_name}_before_reset_{timestamp}.sqlite3")

        # نسخ قاعدة البيانات
        import shutil
        shutil.copy2(db_path, backup_file)

        if keep_schema:
            # استخراج هيكل قاعدة البيانات
            schema_result = extract_database_schema(database_name)

            if not schema_result['success']:
                return {
                    'success': False,
                    'message': f'فشل في استخراج هيكل قاعدة البيانات: {schema_result["message"]}'
                }

            schema = schema_result['schema']

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # تعطيل القيود المؤقتة
            cursor.execute("PRAGMA foreign_keys = OFF;")

            # بدء المعاملة
            cursor.execute("BEGIN TRANSACTION;")

            try:
                # حذف البيانات من جميع الجداول مع الحفاظ على الهيكل
                for table in schema['tables'].keys():
                    # تجاهل جداول django الخاصة بالهجرة
                    if table != 'django_migrations' and table != 'sqlite_sequence':
                        cursor.execute(f"DELETE FROM {table};")

                # إعادة ضبط تسلسل المعرفات
                cursor.execute("DELETE FROM sqlite_sequence;")

                # تأكيد المعاملة
                cursor.execute("COMMIT;")

                # تفعيل القيود مرة أخرى
                cursor.execute("PRAGMA foreign_keys = ON;")

                # إغلاق الاتصال
                conn.close()

                return {
                    'success': True,
                    'message': 'تم تصفير قاعدة البيانات مع الحفاظ على الهيكل بنجاح',
                    'backup_file': os.path.basename(backup_file)
                }

            except Exception as e:
                # التراجع عن المعاملة في حالة حدوث خطأ
                cursor.execute("ROLLBACK;")
                conn.close()
                raise e

        else:
            # حذف قاعدة البيانات بالكامل وإنشاء قاعدة بيانات فارغة
            os.remove(db_path)

            # إنشاء قاعدة بيانات فارغة
            conn = sqlite3.connect(db_path)
            conn.close()

            return {
                'success': True,
                'message': 'تم حذف قاعدة البيانات وإنشاء قاعدة بيانات فارغة بنجاح',
                'backup_file': os.path.basename(backup_file)
            }

    except Exception as e:
        logger.error(f"خطأ في تصفير قاعدة البيانات: {str(e)}")
        return {
            'success': False,
            'message': f'حدث خطأ أثناء تصفير قاعدة البيانات: {str(e)}'
        }

def clone_database_structure(source_db, target_db):
    """
    نسخ هيكل قاعدة بيانات من قاعدة بيانات مصدر إلى قاعدة بيانات هدف

    Args:
        source_db: اسم قاعدة البيانات المصدر
        target_db: اسم قاعدة البيانات الهدف

    Returns:
        dict: نتيجة العملية
    """
    try:
        source_path = get_database_path(source_db)
        target_path = get_database_path(target_db)

        # التحقق من وجود قاعدة البيانات المصدر
        if not os.path.exists(source_path):
            return {
                'success': False,
                'message': 'قاعدة البيانات المصدر غير موجودة'
            }

        # إنشاء مجلد قواعد البيانات إذا لم يكن موجودًا
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        # استخراج هيكل قاعدة البيانات المصدر
        schema_result = extract_database_schema(source_db)

        if not schema_result['success']:
            return {
                'success': False,
                'message': f'فشل في استخراج هيكل قاعدة البيانات المصدر: {schema_result["message"]}'
            }

        schema = schema_result['schema']

        # إنشاء قاعدة البيانات الهدف
        conn = sqlite3.connect(target_path)
        cursor = conn.cursor()

        # تعطيل القيود المؤقتة
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # بدء المعاملة
        cursor.execute("BEGIN TRANSACTION;")

        try:
            # إنشاء الجداول
            for table_name, table_info in schema['tables'].items():
                # تجاهل جداول django الخاصة بالهجرة
                if table_name == 'django_migrations' or table_name == 'sqlite_sequence':
                    continue

                # استخراج تعريف الجدول من قاعدة البيانات المصدر
                source_conn = sqlite3.connect(source_path)
                source_cursor = source_conn.cursor()
                source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}';")
                create_table_sql = source_cursor.fetchone()[0]
                source_conn.close()

                # إنشاء الجدول في قاعدة البيانات الهدف
                cursor.execute(create_table_sql)

            # إنشاء الفهارس
            for index in schema['indexes']:
                table = index['table']
                # تجاهل جداول django الخاصة بالهجرة
                if table == 'django_migrations' or table == 'sqlite_sequence':
                    continue

                # استخراج تعريف الفهرس من قاعدة البيانات المصدر
                source_conn = sqlite3.connect(source_path)
                source_cursor = source_conn.cursor()
                source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='index' AND name='{index['name']}';")
                create_index_sql = source_cursor.fetchone()
                source_conn.close()

                if create_index_sql and create_index_sql[0]:
                    cursor.execute(create_index_sql[0])

            # تأكيد المعاملة
            cursor.execute("COMMIT;")

            # تفعيل القيود مرة أخرى
            cursor.execute("PRAGMA foreign_keys = ON;")

            # إغلاق الاتصال
            conn.close()

            return {
                'success': True,
                'message': f'تم نسخ هيكل قاعدة البيانات من {source_db} إلى {target_db} بنجاح'
            }

        except Exception as e:
            # التراجع عن المعاملة في حالة حدوث خطأ
            cursor.execute("ROLLBACK;")
            conn.close()
            raise e

    except Exception as e:
        logger.error(f"خطأ في نسخ هيكل قاعدة البيانات: {str(e)}")
        return {
            'success': False,
            'message': f'حدث خطأ أثناء نسخ هيكل قاعدة البيانات: {str(e)}'
        }

def create_empty_database(database_name, template_db=None):
    """
    إنشاء قاعدة بيانات فارغة

    Args:
        database_name: اسم قاعدة البيانات الجديدة
        template_db: اسم قاعدة بيانات القالب (اختياري)

    Returns:
        dict: نتيجة العملية
    """
    try:
        db_path = get_database_path(database_name)

        # التحقق من عدم وجود قاعدة البيانات مسبقًا
        if os.path.exists(db_path):
            return {
                'success': False,
                'message': 'قاعدة البيانات موجودة بالفعل'
            }

        # إنشاء مجلد قواعد البيانات إذا لم يكن موجودًا
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        if template_db:
            # نسخ هيكل قاعدة البيانات من القالب
            result = clone_database_structure(template_db, database_name)
            return result
        else:
            # إنشاء قاعدة بيانات فارغة
            conn = sqlite3.connect(db_path)
            conn.close()

            return {
                'success': True,
                'message': 'تم إنشاء قاعدة بيانات فارغة بنجاح'
            }

    except Exception as e:
        logger.error(f"خطأ في إنشاء قاعدة بيانات فارغة: {str(e)}")
        return {
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء قاعدة بيانات فارغة: {str(e)}'
        }

def get_database_tables_count(database_name):
    """
    الحصول على عدد الجداول في قاعدة البيانات

    Args:
        database_name: اسم قاعدة البيانات

    Returns:
        int: عدد الجداول
    """
    try:
        db_path = get_database_path(database_name)

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return 0

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج عدد الجداول
        cursor.execute("SELECT count(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
        count = cursor.fetchone()[0]

        conn.close()

        return count

    except Exception as e:
        logger.error(f"خطأ في الحصول على عدد الجداول: {str(e)}")
        return 0

def get_database_size(database_name):
    """
    الحصول على حجم قاعدة البيانات

    Args:
        database_name: اسم قاعدة البيانات

    Returns:
        int: حجم قاعدة البيانات بالبايت
    """
    try:
        db_path = get_database_path(database_name)

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return 0

        # الحصول على حجم الملف
        return os.path.getsize(db_path)

    except Exception as e:
        logger.error(f"خطأ في الحصول على حجم قاعدة البيانات: {str(e)}")
        return 0

def format_size(size_bytes):
    """
    تنسيق حجم الملف بصيغة مقروءة

    Args:
        size_bytes: حجم الملف بالبايت

    Returns:
        str: الحجم بصيغة مقروءة
    """
    if size_bytes < 1024:
        return f"{size_bytes} بايت"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.2f} كيلوبايت"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.2f} ميجابايت"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} جيجابايت"
