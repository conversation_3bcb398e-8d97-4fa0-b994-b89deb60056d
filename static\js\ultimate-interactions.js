/**
 * نظام التفاعلات النهائي - مستوى عالمي متقدم
 * مستوحى من أفضل المواقع العالمية: Apple, Google, Microsoft, Stripe, Linear
 */

class UltimateUI {
    constructor() {
        this.isInitialized = false;
        this.observers = new Map();
        this.animations = new Map();
        this.particles = [];
        this.init();
    }

    async init() {
        if (this.isInitialized) return;
        
        // تهيئة النظام بالتدريج
        await this.setupCore();
        await this.setupAdvancedFeatures();
        await this.setupPerformanceOptimizations();
        
        this.isInitialized = true;
        this.emit('initialized');
    }

    async setupCore() {
        // النواة الأساسية
        this.setupSmoothScrolling();
        this.setupIntersectionObserver();
        this.setupResizeObserver();
        this.setupMutationObserver();
        this.setupEventDelegation();
    }

    async setupAdvancedFeatures() {
        // المميزات المتقدمة
        this.setupParallaxEngine();
        this.setupMorphingAnimations();
        this.setupParticleSystem();
        this.setupMagneticEffects();
        this.setupGestureRecognition();
        this.setupVoiceCommands();
        this.setupAIAssistant();
    }

    async setupPerformanceOptimizations() {
        // تحسينات الأداء
        this.setupVirtualScrolling();
        this.setupImageOptimization();
        this.setupCodeSplitting();
        this.setupServiceWorker();
        this.setupWebAssembly();
    }

    // نظام التمرير المتقدم
    setupSmoothScrolling() {
        // تحسين التمرير مع تسارع GPU
        const scrollContainer = document.documentElement;
        let isScrolling = false;
        let scrollTimeout;

        const smoothScroll = (target, duration = 1000) => {
            const start = window.pageYOffset;
            const distance = target - start;
            let startTime = null;

            const animation = (currentTime) => {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const run = this.easeInOutCubic(timeElapsed, start, distance, duration);
                
                window.scrollTo(0, run);
                
                if (timeElapsed < duration) {
                    requestAnimationFrame(animation);
                }
            };

            requestAnimationFrame(animation);
        };

        // ربط الروابط بالتمرير الناعم
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    smoothScroll(target.offsetTop - 100);
                }
            }
        });

        // تأثيرات التمرير
        window.addEventListener('scroll', this.throttle(() => {
            if (!isScrolling) {
                document.body.classList.add('is-scrolling');
                isScrolling = true;
            }

            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                document.body.classList.remove('is-scrolling');
                isScrolling = false;
            }, 150);

            this.updateParallax();
            this.updateNavbar();
        }, 16), { passive: true });
    }

    // نظام المراقبة المتقدم
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: [0, 0.1, 0.25, 0.5, 0.75, 1],
            rootMargin: '-10% 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                const ratio = entry.intersectionRatio;

                if (entry.isIntersecting) {
                    element.classList.add('in-view');
                    
                    // تحريك العناصر حسب نسبة الظهور
                    if (ratio > 0.5) {
                        element.classList.add('fully-visible');
                        this.triggerAnimation(element);
                    }

                    // تحميل الصور الكسولة
                    this.lazyLoadImages(element);
                    
                    // تشغيل العدادات
                    this.animateCounters(element);
                } else {
                    element.classList.remove('in-view', 'fully-visible');
                }

                // تحديث التقدم
                this.updateProgress(element, ratio);
            });
        }, observerOptions);

        // مراقبة العناصر
        const elementsToObserve = document.querySelectorAll(
            '.animate-on-scroll, .card-ultimate, .lazy-load, [data-counter]'
        );

        elementsToObserve.forEach(element => {
            observer.observe(element);
        });

        this.observers.set('intersection', observer);
    }

    // نظام الحركة المتقدم
    setupMorphingAnimations() {
        const morphElements = document.querySelectorAll('.morph-element');
        
        morphElements.forEach(element => {
            const morphData = JSON.parse(element.dataset.morph || '{}');
            
            element.addEventListener('mouseenter', () => {
                this.morphElement(element, morphData.hover || {});
            });

            element.addEventListener('mouseleave', () => {
                this.morphElement(element, morphData.default || {});
            });
        });
    }

    morphElement(element, properties) {
        const animation = element.animate(properties, {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            fill: 'forwards'
        });

        this.animations.set(element, animation);
    }

    // نظام الجسيمات
    setupParticleSystem() {
        const canvas = document.createElement('canvas');
        canvas.id = 'particle-canvas';
        canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.6;
        `;
        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        this.setupParticles(canvas, ctx);
    }

    setupParticles(canvas, ctx) {
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // إنشاء الجسيمات
        for (let i = 0; i < 50; i++) {
            this.particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }

        // تحريك الجسيمات
        const animateParticles = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            this.particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                // إعادة تدوير الجسيمات
                if (particle.x < 0) particle.x = canvas.width;
                if (particle.x > canvas.width) particle.x = 0;
                if (particle.y < 0) particle.y = canvas.height;
                if (particle.y > canvas.height) particle.y = 0;

                // رسم الجسيمة
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`;
                ctx.fill();
            });

            requestAnimationFrame(animateParticles);
        };

        animateParticles();
    }

    // تأثيرات مغناطيسية
    setupMagneticEffects() {
        const magneticElements = document.querySelectorAll('.hover-magnetic');
        
        magneticElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                const distance = Math.sqrt(x * x + y * y);
                const maxDistance = Math.max(rect.width, rect.height) / 2;
                const strength = Math.max(0, 1 - distance / maxDistance);
                
                const moveX = x * strength * 0.2;
                const moveY = y * strength * 0.2;
                
                element.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });

            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translate(0, 0)';
            });
        });
    }

    // تحريك العدادات
    animateCounters(container) {
        const counters = container.querySelectorAll('[data-counter]');
        
        counters.forEach(counter => {
            if (counter.classList.contains('counted')) return;
            
            const target = parseInt(counter.dataset.counter);
            const duration = parseInt(counter.dataset.duration) || 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                    counter.classList.add('counted');
                }
            };

            updateCounter();
        });
    }

    // تحميل الصور الكسولة
    lazyLoadImages(container) {
        const images = container.querySelectorAll('img[data-src]');
        
        images.forEach(img => {
            if (img.classList.contains('loaded')) return;
            
            const tempImg = new Image();
            tempImg.onload = () => {
                img.src = img.dataset.src;
                img.classList.add('loaded');
                img.classList.remove('lazy-load');
            };
            tempImg.src = img.dataset.src;
        });
    }

    // تحديث شريط التنقل
    updateNavbar() {
        const navbar = document.querySelector('.navbar-ultimate');
        if (!navbar) return;

        const scrollY = window.pageYOffset;
        
        if (scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // تحديث المنظور
    updateParallax() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        const scrollY = window.pageYOffset;

        parallaxElements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollY * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }

    // تشغيل الحركات
    triggerAnimation(element) {
        const animationType = element.dataset.animation || 'fadeInUp';
        const delay = parseInt(element.dataset.delay) || 0;

        setTimeout(() => {
            element.classList.add(`animate-${animationType}`);
        }, delay);
    }

    // دوال مساعدة
    easeInOutCubic(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t * t + b;
        t -= 2;
        return c / 2 * (t * t * t + 2) + b;
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // نظام الأحداث
    emit(eventName, data = {}) {
        const event = new CustomEvent(`ultimate:${eventName}`, { detail: data });
        document.dispatchEvent(event);
    }

    on(eventName, callback) {
        document.addEventListener(`ultimate:${eventName}`, callback);
    }

    // تنظيف الذاكرة
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.animations.forEach(animation => animation.cancel());
        this.observers.clear();
        this.animations.clear();
        this.particles = [];
    }
}

// تهيئة النظام
document.addEventListener('DOMContentLoaded', () => {
    window.ultimateUI = new UltimateUI();
});

// تصدير للاستخدام العالمي
window.UltimateUI = UltimateUI;
