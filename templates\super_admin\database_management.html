{% extends 'super_admin/base.html' %}

{% block title %}إدارة قواعد البيانات | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    .db-card {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }

    .db-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .db-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b8 100%);
        color: white;
        padding: 1rem;
    }

    .db-body {
        padding: 1.5rem;
    }

    .db-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .db-stat {
        text-align: center;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 5px;
        flex: 1;
        margin: 0 0.25rem;
    }

    .db-stat-value {
        font-size: 1.25rem;
        font-weight: bold;
        color: #2541b8;
    }

    .db-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .db-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .db-action {
        flex: 1;
        min-width: 120px;
    }

    .modal-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b8 100%);
        color: white;
    }

    .modal-header .btn-close {
        color: white;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-active {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #842029;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        visibility: visible;
        opacity: 1;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #2541b8;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">
            <i class="fas fa-database me-2"></i>
            إدارة قواعد البيانات
        </h2>
        <a href="{% url 'super_admin:dashboard' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوحة التحكم
        </a>
    </div>

    <div class="row">
        {% for company in companies %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card db-card shadow-sm">
                <div class="db-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ company.name }}</h5>
                        <span class="status-badge {% if company.status == 'active' %}status-active{% elif company.status == 'inactive' %}status-inactive{% else %}status-pending{% endif %}">
                            {% if company.status == 'active' %}
                                <i class="fas fa-check-circle me-1"></i> نشط
                            {% elif company.status == 'inactive' %}
                                <i class="fas fa-times-circle me-1"></i> غير نشط
                            {% else %}
                                <i class="fas fa-clock me-1"></i> معلق
                            {% endif %}
                        </span>
                    </div>
                </div>
                <div class="db-body">
                    <div class="db-stats">
                        <div class="db-stat">
                            <div class="db-stat-value">{{ company.db_tables_count }}</div>
                            <div class="db-stat-label">الجداول</div>
                        </div>
                        <div class="db-stat">
                            <div class="db-stat-value">{{ company.db_size_formatted }}</div>
                            <div class="db-stat-label">الحجم</div>
                        </div>
                        <div class="db-stat">
                            <div class="db-stat-value">{{ company.serial_number|slice:":4" }}...</div>
                            <div class="db-stat-label">الرقم التسلسلي</div>
                        </div>
                    </div>

                    <div class="db-actions">
                        <a href="{% url 'super_admin:view_database_schema' company.id %}" class="btn btn-sm btn-outline-primary db-action">
                            <i class="fas fa-sitemap me-1"></i> عرض الهيكل
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-warning db-action" data-bs-toggle="modal" data-bs-target="#resetModal{{ company.id }}">
                            <i class="fas fa-eraser me-1"></i> تصفير
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info db-action" data-bs-toggle="modal" data-bs-target="#cloneModal{{ company.id }}">
                            <i class="fas fa-copy me-1"></i> نسخ الهيكل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal: تصفير قاعدة البيانات -->
        <div class="modal fade" id="resetModal{{ company.id }}" tabindex="-1" aria-labelledby="resetModalLabel{{ company.id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="resetModalLabel{{ company.id }}">تصفير قاعدة البيانات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> سيؤدي هذا الإجراء إلى حذف جميع البيانات من قاعدة البيانات مع الاحتفاظ بالهيكل. هذا الإجراء لا يمكن التراجع عنه.
                        </div>
                        <p>هل أنت متأكد من رغبتك في تصفير قاعدة البيانات للشركة <strong>{{ company.name }}</strong>؟</p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmReset{{ company.id }}" required>
                            <label class="form-check-label" for="confirmReset{{ company.id }}">
                                نعم، أنا متأكد من رغبتي في تصفير قاعدة البيانات
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger reset-db-btn" data-company-id="{{ company.id }}">
                            <i class="fas fa-eraser me-1"></i> تصفير قاعدة البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal: نسخ هيكل قاعدة البيانات -->
        <div class="modal fade" id="cloneModal{{ company.id }}" tabindex="-1" aria-labelledby="cloneModalLabel{{ company.id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="cloneModalLabel{{ company.id }}">نسخ هيكل قاعدة البيانات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>يمكنك نسخ هيكل قاعدة البيانات من <strong>{{ company.name }}</strong> إلى شركة أخرى.</p>
                        <div class="mb-3">
                            <label for="targetCompany{{ company.id }}" class="form-label">الشركة الهدف:</label>
                            <select class="form-select" id="targetCompany{{ company.id }}" required>
                                <option value="">-- اختر الشركة الهدف --</option>
                                {% for target_company in companies %}
                                    {% if target_company.id != company.id %}
                                        <option value="{{ target_company.id }}">{{ target_company.name }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم نسخ هيكل قاعدة البيانات فقط (الجداول والعلاقات) دون البيانات.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary clone-db-btn" data-company-id="{{ company.id }}">
                            <i class="fas fa-copy me-1"></i> نسخ الهيكل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد شركات مسجلة حالياً. قم بإنشاء شركة جديدة لإدارة قاعدة بياناتها.
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تصفير قاعدة البيانات
        document.querySelectorAll('.reset-db-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const companyId = this.getAttribute('data-company-id');
                const confirmCheckbox = document.getElementById(`confirmReset${companyId}`);

                if (!confirmCheckbox.checked) {
                    alert('يرجى تأكيد رغبتك في تصفير قاعدة البيانات');
                    return;
                }

                // إظهار شاشة التحميل
                document.getElementById('loadingOverlay').classList.add('show');

                // إرسال طلب AJAX
                const formData = new FormData();
                formData.append('action', 'reset_database');
                formData.append('company_id', companyId);
                formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

                fetch('{% url "super_admin:database_management" %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // إخفاء شاشة التحميل
                    document.getElementById('loadingOverlay').classList.remove('show');

                    // إغلاق النافذة المنبثقة
                    bootstrap.Modal.getInstance(document.getElementById(`resetModal${companyId}`)).hide();

                    if (data.success) {
                        alert('تم تصفير قاعدة البيانات بنجاح');
                        location.reload();
                    } else {
                        alert(`فشل في تصفير قاعدة البيانات: ${data.message}`);
                    }
                })
                .catch(error => {
                    // إخفاء شاشة التحميل
                    document.getElementById('loadingOverlay').classList.remove('show');
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    console.error('Error:', error);
                });
            });
        });

        // نسخ هيكل قاعدة البيانات
        document.querySelectorAll('.clone-db-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const companyId = this.getAttribute('data-company-id');
                const targetCompanySelect = document.getElementById(`targetCompany${companyId}`);
                const targetCompanyId = targetCompanySelect.value;

                if (!targetCompanyId) {
                    alert('يرجى اختيار الشركة الهدف');
                    return;
                }

                // إظهار شاشة التحميل
                document.getElementById('loadingOverlay').classList.add('show');

                // إرسال طلب AJAX
                const formData = new FormData();
                formData.append('action', 'clone_structure');
                formData.append('company_id', companyId);
                formData.append('target_company_id', targetCompanyId);
                formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

                fetch('{% url "super_admin:database_management" %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // إخفاء شاشة التحميل
                    document.getElementById('loadingOverlay').classList.remove('show');

                    // إغلاق النافذة المنبثقة
                    bootstrap.Modal.getInstance(document.getElementById(`cloneModal${companyId}`)).hide();

                    if (data.success) {
                        alert('تم نسخ هيكل قاعدة البيانات بنجاح');
                        location.reload();
                    } else {
                        alert(`فشل في نسخ هيكل قاعدة البيانات: ${data.message}`);
                    }
                })
                .catch(error => {
                    // إخفاء شاشة التحميل
                    document.getElementById('loadingOverlay').classList.remove('show');
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    console.error('Error:', error);
                });
            });
        });
    });
</script>
{% endblock %}
