/* تحسينات شاملة للتصميم العام */

/* إعدادات عامة محسنة */
* {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    box-sizing: border-box;
}

body {
    direction: rtl;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.dark body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* تحسينات الحركة والتفاعل */
.smooth-transition {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    will-change: transform, box-shadow;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* تأثيرات الزجاج المحسنة */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-effect {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* شريط التنقل المحسن */
.navbar-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.dark .navbar-enhanced {
    background: rgba(17, 24, 39, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* أزرار متدرجة محسنة */
.btn-gradient {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
    background-size: 200% 200%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: none;
    color: white;
    font-weight: 600;
    border-radius: 12px;
    padding: 12px 24px;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.btn-gradient:hover {
    background-position: 100% 0;
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
}

.btn-gradient:hover::before {
    left: 100%;
}

/* أيقونات متحركة */
.icon-bounce {
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.icon-bounce:hover {
    transform: scale(1.2) rotate(5deg);
}

/* تأثيرات النبض المحسنة */
.pulse-enhanced {
    animation: pulseEnhanced 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulseEnhanced {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* تأثيرات التدرج المتحركة */
.gradient-animated {
    background: linear-gradient(-45deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تحسينات البطاقات */
.card-enhanced {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.dark .card-enhanced {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-enhanced:hover::before {
    opacity: 1;
}

/* تحسينات الجداول */
.table-enhanced {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-enhanced tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-enhanced tbody tr:hover {
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    transform: translateX(5px);
}

/* تأثيرات الظلال المحسنة */
.shadow-enhanced {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.shadow-enhanced:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* تحسينات النصوص */
.text-gradient {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تأثيرات الحدود المتحركة */
.border-animated {
    position: relative;
    overflow: hidden;
}

.border-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #6366f1, transparent);
    animation: borderSlide 3s infinite;
}

@keyframes borderSlide {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تأثيرات التحميل */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
    }

    .navbar-enhanced {
        backdrop-filter: blur(10px);
    }
}

/* تحسينات إضافية للأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* تأثيرات الإشعارات */
.notification-enhanced {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات الأزرار التفاعلية */
.btn-interactive {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-interactive::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-interactive:active::after {
    width: 300px;
    height: 300px;
}

/* تحسينات روابط التنقل */
.nav-link-enhanced {
    position: relative;
    overflow: hidden;
}

.nav-link-enhanced::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    transition: width 0.3s ease;
}

.nav-link-enhanced:hover::before {
    width: 100%;
}

.nav-link-enhanced:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

/* تحسينات إضافية للبطاقات */
.card-enhanced:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

/* تأثيرات الأرقام المتحركة */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-count {
    animation: countUp 0.8s ease-out;
}

/* تحسينات شريط التقدم */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}
