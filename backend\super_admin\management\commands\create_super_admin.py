from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from backend.super_admin.models import SuperAdminUser
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'إنشاء مستخدم المسؤول الأعلى (Super Admin) بالبيانات المحددة'

    def handle(self, *args, **options):
        # بيانات المسؤول الأعلى
        username = 'MUNTADER_WISSAM'
        password = 'MUNTADDEERR13163@@13163'
        email = '<EMAIL>'
        
        # التحقق من وجود المستخدم
        if User.objects.filter(username=username).exists():
            # تحديث بيانات المستخدم الموجود
            user = User.objects.get(username=username)
            user.set_password(password)
            user.email = email
            user.is_superuser = True
            user.is_staff = True
            user.save()
            
            # الحصول على أو إنشاء ملف تعريف المسؤول الأعلى
            super_admin, created = SuperAdminUser.objects.get_or_create(user=user)
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'تم إنشاء ملف تعريف المسؤول الأعلى للمستخدم {username}'))
            else:
                self.stdout.write(self.style.SUCCESS(f'تم تحديث ملف تعريف المسؤول الأعلى للمستخدم {username}'))
                
            self.stdout.write(self.style.SUCCESS(f'تم تحديث بيانات المستخدم {username} بنجاح'))
        else:
            # إنشاء مستخدم جديد
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password
            )
            
            # إنشاء ملف تعريف المسؤول الأعلى
            SuperAdminUser.objects.create(user=user)
            
            self.stdout.write(self.style.SUCCESS(f'تم إنشاء المستخدم {username} وملف تعريف المسؤول الأعلى بنجاح'))
        
        # إزالة أي مستخدمين آخرين بصلاحيات المسؤول الأعلى
        other_superusers = User.objects.filter(is_superuser=True).exclude(username=username)
        if other_superusers.exists():
            count = other_superusers.count()
            # إزالة صلاحيات المسؤول الأعلى من المستخدمين الآخرين
            other_superusers.update(is_superuser=False)
            self.stdout.write(self.style.SUCCESS(f'تم إزالة صلاحيات المسؤول الأعلى من {count} مستخدم آخر'))
