{% extends 'super_admin/base.html' %}

{% block title %}الأرقام التسلسلية المحظورة | لوحة المسؤول الأعلى{% endblock %}

{% block page_title %}إدارة الأرقام التسلسلية المحظورة{% endblock %}

{% block extra_css %}
<style>
    .blocked-serial-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .blocked-serial-badge.permanent {
        background-color: rgba(239, 68, 68, 0.1);
        color: #b91c1c;
    }
    .blocked-serial-badge.temporary {
        background-color: rgba(245, 158, 11, 0.1);
        color: #92400e;
    }
    .blocked-serial-badge.expired {
        background-color: rgba(107, 114, 128, 0.1);
        color: #374151;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <div class="p-6 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-800">قائمة الأرقام التسلسلية المحظورة</h2>
        <button id="add-blocked-serial-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-plus-circle ml-2"></i>
            <span>إضافة رقم محظور</span>
        </button>
    </div>

    <div class="p-6">
        {% if messages %}
            {% for message in messages %}
                <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ message }}</span>
                </div>
            {% endfor %}
        {% endif %}

        <div class="mb-6 flex flex-wrap gap-2">
            <button class="blocked-filter px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium {% if not filter %}bg-blue-100 text-blue-800{% endif %}" data-filter="all">
                الكل <span class="bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs mr-1">{{ blocked_serials|length }}</span>
            </button>
            <button class="blocked-filter px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium {% if filter == 'permanent' %}bg-blue-100 text-blue-800{% endif %}" data-filter="permanent">
                دائم <span class="bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs mr-1">{{ permanent_count }}</span>
            </button>
            <button class="blocked-filter px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium {% if filter == 'temporary' %}bg-blue-100 text-blue-800{% endif %}" data-filter="temporary">
                مؤقت <span class="bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs mr-1">{{ temporary_count }}</span>
            </button>
            <button class="blocked-filter px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium {% if filter == 'expired' %}bg-blue-100 text-blue-800{% endif %}" data-filter="expired">
                منتهي <span class="bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs mr-1">{{ expired_count }}</span>
            </button>
        </div>

        {% if blocked_serials %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرقم التسلسلي</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سبب الحظر</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for serial in blocked_serials %}
                            <tr class="hover:bg-gray-50 blocked-serial-row {% if serial.is_permanent %}permanent{% elif serial.is_active %}temporary{% else %}expired{% endif %}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ serial.serial_number }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ serial.reason|truncatechars:50 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if serial.is_permanent %}
                                        <span class="blocked-serial-badge permanent">دائم</span>
                                    {% elif serial.is_active %}
                                        <span class="blocked-serial-badge temporary">مؤقت (حتى {{ serial.blocked_until }})</span>
                                    {% else %}
                                        <span class="blocked-serial-badge expired">منتهي</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ serial.created_at|date:"Y-m-d H:i" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        <a href="{% url 'edit_blocked_serial' serial.id %}" class="text-blue-600 hover:text-blue-900" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" onclick="if(confirm('هل أنت متأكد من حذف الرقم التسلسلي {{ serial.serial_number }}؟ هذا الإجراء لا يمكن التراجع عنه.')) { window.location.href = '{% url 'delete_blocked_serial' serial.id %}'; } return false;" class="text-red-600 hover:text-red-900" title="حذف">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-8">
                <div class="text-gray-400 mb-2"><i class="fas fa-ban text-4xl"></i></div>
                <p class="text-gray-500">لا توجد أرقام تسلسلية محظورة حاليًا</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- نموذج إضافة رقم تسلسلي محظور -->
<div id="add-blocked-serial-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">إضافة رقم تسلسلي محظور</h3>
            <button id="close-modal" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form method="post" action="{% url 'add_blocked_serial' %}" class="p-6 space-y-4">
            {% csrf_token %}

            <div class="space-y-2">
                <label for="{{ form.serial_number.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.serial_number.label }}</label>
                {{ form.serial_number }}
                {% if form.serial_number.help_text %}
                    <p class="text-xs text-gray-500">{{ form.serial_number.help_text }}</p>
                {% endif %}
                {% if form.serial_number.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.serial_number.errors.0 }}</p>
                {% endif %}
            </div>

            <div class="space-y-2">
                <label for="{{ form.reason.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.reason.label }}</label>
                {{ form.reason }}
                {% if form.reason.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.reason.errors.0 }}</p>
                {% endif %}
            </div>

            <div class="space-y-2">
                <div class="flex items-center">
                    {{ form.is_permanent }}
                    <label for="{{ form.is_permanent.id_for_label }}" class="mr-2 block text-sm text-gray-700">{{ form.is_permanent.label }}</label>
                </div>
                {% if form.is_permanent.help_text %}
                    <p class="text-xs text-gray-500">{{ form.is_permanent.help_text }}</p>
                {% endif %}
            </div>

            <div id="blocked-until-container" class="space-y-2">
                <label for="{{ form.blocked_until.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.blocked_until.label }}</label>
                {{ form.blocked_until }}
                {% if form.blocked_until.help_text %}
                    <p class="text-xs text-gray-500">{{ form.blocked_until.help_text }}</p>
                {% endif %}
                {% if form.blocked_until.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.blocked_until.errors.0 }}</p>
                {% endif %}
            </div>

            <div class="pt-4 flex justify-end">
                <button type="button" id="cancel-add" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ml-3">
                    إلغاء
                </button>
                <button type="submit" class="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    إضافة
                </button>
            </div>
        </form>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء نموذج إضافة رقم تسلسلي محظور
        const addBtn = document.getElementById('add-blocked-serial-btn');
        const modal = document.getElementById('add-blocked-serial-modal');
        const closeBtn = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-add');

        addBtn.addEventListener('click', function() {
            modal.classList.remove('hidden');
        });

        closeBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        // إظهار/إخفاء حقل تاريخ انتهاء الحظر بناءً على نوع الحظر
        const isPermanentCheckbox = document.getElementById('is_permanent_checkbox');
        const blockedUntilContainer = document.getElementById('blocked-until-container');

        function toggleBlockedUntil() {
            if (isPermanentCheckbox.checked) {
                blockedUntilContainer.classList.add('hidden');
            } else {
                blockedUntilContainer.classList.remove('hidden');
            }
        }

        isPermanentCheckbox.addEventListener('change', toggleBlockedUntil);
        toggleBlockedUntil(); // تطبيق الحالة الأولية



        // تصفية الأرقام التسلسلية المحظورة
        const filterButtons = document.querySelectorAll('.blocked-filter');
        const serialRows = document.querySelectorAll('.blocked-serial-row');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.dataset.filter;

                // تحديث حالة الزر النشط
                filterButtons.forEach(btn => btn.classList.remove('bg-blue-100', 'text-blue-800'));
                this.classList.add('bg-blue-100', 'text-blue-800');

                // تصفية الصفوف
                serialRows.forEach(row => {
                    if (filter === 'all') {
                        row.classList.remove('hidden');
                    } else {
                        if (row.classList.contains(filter)) {
                            row.classList.remove('hidden');
                        } else {
                            row.classList.add('hidden');
                        }
                    }
                });
            });
        });
    });
</script>
{% endblock %}
