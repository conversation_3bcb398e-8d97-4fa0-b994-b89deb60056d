{% comment %}
قالب موحد للجداول في النظام
الاستخدام:
{% include 'components/data_table.html' with
    table_id="workers-table"
    columns=columns
    data=workers
    actions=True
    pagination=workers
    empty_message="لا يوجد عمال مسجلين"
%}

حيث:
- table_id: معرف الجدول (اختياري)
- columns: قائمة بأعمدة الجدول، كل عمود عبارة عن قاموس يحتوي على:
  - name: اسم العمود
  - key: مفتاح البيانات
  - sortable: هل يمكن الترتيب حسب هذا العمود (اختياري)
  - width: عرض العمود (اختياري)
- data: قائمة بالبيانات المعروضة
- actions: هل يتم عرض أعمدة الإجراءات (اختياري، افتراضي: True)
- pagination: كائن الترقيم (اختياري)
- empty_message: الرسالة التي تظهر عند عدم وجود بيانات (اختياري)
{% endcomment %}

<div class="card">
    {% if title %}
    <div class="card-header">
        <h3>{{ title }}</h3>
        {% if add_url %}
        <a href="{{ add_url }}" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i>
            <span>إضافة جديد</span>
        </a>
        {% endif %}
    </div>
    {% endif %}

    <div class="card-body p-0">
        <div class="table-container">
            <table class="data-table" {% if table_id %}id="{{ table_id }}"{% endif %}>
                <thead>
                    <tr>
                        {% for column in columns %}
                        <th {% if column.width %}style="width: {{ column.width }};"{% endif %}>
                            {% if column.sortable %}
                            <a href="?sort={% if request.GET.sort == column.key %}-{% endif %}{{ column.key }}" class="text-white flex items-center">
                                {{ column.name }}
                                {% if request.GET.sort == column.key %}
                                <i class="fas fa-sort-up mr-1"></i>
                                {% elif request.GET.sort == '-'|add:column.key %}
                                <i class="fas fa-sort-down mr-1"></i>
                                {% else %}
                                <i class="fas fa-sort mr-1 opacity-50"></i>
                                {% endif %}
                            </a>
                            {% else %}
                            {{ column.name }}
                            {% endif %}
                        </th>
                        {% endfor %}

                        {% if actions %}
                        <th>إجراءات</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% if data %}
                        {% for item in data %}
                        <tr>
                            {% for column in columns %}
                            <td>
                                {% if column.key == 'status' %}
                                    {% if item.status == 'active' %}
                                    <span class="badge badge-success">نشط</span>
                                    {% elif item.status == 'pending' %}
                                    <span class="badge badge-warning">بانتظار</span>
                                    {% elif item.status == 'inactive' %}
                                    <span class="badge badge-danger">غير نشط</span>
                                    {% else %}
                                    <span class="badge badge-info">{{ item.get_status_display }}</span>
                                    {% endif %}
                                {% elif column.key == 'days_remaining' %}
                                    <span class="{% if item.days_remaining < 7 %}text-danger{% elif item.days_remaining < 30 %}text-warning{% else %}text-success{% endif %}">
                                        {{ item.days_remaining }} يوم
                                    </span>
                                {% elif column.template %}
                                    {% include column.template with item=item %}
                                {% else %}
                                    {{ item|getattribute:column.key }}
                                {% endif %}
                            </td>
                            {% endfor %}

                            {% if actions %}
                            <td>
                                <div class="table-actions">
                                    {% if view_url %}
                                    <a href="{{ view_url|replace_id:item.id }}" class="table-action-btn view" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% endif %}

                                    {% if edit_url %}
                                    <a href="{{ edit_url|replace_id:item.id }}" class="table-action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}

                                    {% if delete_url %}
                                    <button type="button"
                                        data-delete-url="{{ delete_url|replace_id:item.id }}"
                                        onclick="window.deleteItem(this.getAttribute('data-delete-url'));"
                                        class="table-action-btn delete"
                                        title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="{{ columns|length|add:1 }}" class="text-center text-gray-500 py-8">
                                {{ empty_message|default:"لا توجد بيانات" }}
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    {% if pagination and pagination.has_other_pages %}
    <div class="card-footer">
        <div class="pagination-info">
            عرض {{ pagination.start_index }} إلى {{ pagination.end_index }} من {{ pagination.paginator.count }} سجل
        </div>

        <ul class="pagination">
            {% if pagination.has_previous %}
                <li class="pagination-item">
                    <a href="?page={{ pagination.previous_page_number }}" class="pagination-link">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% else %}
                <li class="pagination-item">
                    <span class="pagination-link disabled">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                </li>
            {% endif %}

            {% for i in pagination.paginator.page_range %}
                {% if pagination.number == i %}
                    <li class="pagination-item">
                        <span class="pagination-link active">
                            {{ i }}
                        </span>
                    </li>
                {% else %}
                    <li class="pagination-item">
                        <a href="?page={{ i }}" class="pagination-link">
                            {{ i }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
                <li class="pagination-item">
                    <a href="?page={{ pagination.next_page_number }}" class="pagination-link">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% else %}
                <li class="pagination-item">
                    <span class="pagination-link disabled">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>
