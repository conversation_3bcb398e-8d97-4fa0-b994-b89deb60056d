/**
 * نظام التحقق العصري للنماذج
 * Modern Form Validation System
 */

class ModernValidation {
    constructor() {
        this.rules = {
            required: {
                test: (value) => value && value.trim() !== '',
                message: 'هذا الحقل مطلوب'
            },
            email: {
                test: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                message: 'يرجى إدخال بريد إلكتروني صحيح'
            },
            phone: {
                test: (value) => /^[0-9+\-\s()]{8,}$/.test(value),
                message: 'يرجى إدخال رقم هاتف صحيح'
            },
            minLength: {
                test: (value, min) => value && value.length >= min,
                message: (min) => `يجب أن يحتوي على ${min} أحرف على الأقل`
            },
            maxLength: {
                test: (value, max) => !value || value.length <= max,
                message: (max) => `يجب ألا يزيد عن ${max} حرف`
            },
            number: {
                test: (value) => !isNaN(value) && !isNaN(parseFloat(value)),
                message: 'يرجى إدخال رقم صحيح'
            },
            url: {
                test: (value) => /^https?:\/\/.+\..+/.test(value),
                message: 'يرجى إدخال رابط صحيح'
            },
            password: {
                test: (value) => value && value.length >= 6,
                message: 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل'
            },
            confirmPassword: {
                test: (value, originalPassword) => value === originalPassword,
                message: 'كلمة المرور غير متطابقة'
            }
        };

        this.messages = {
            ar: {
                required: 'هذا الحقل مطلوب',
                email: 'يرجى إدخال بريد إلكتروني صحيح',
                phone: 'يرجى إدخال رقم هاتف صحيح',
                number: 'يرجى إدخال رقم صحيح',
                url: 'يرجى إدخال رابط صحيح',
                password: 'كلمة المرور ضعيفة',
                confirmPassword: 'كلمة المرور غير متطابقة',
                minLength: (min) => `يجب أن يحتوي على ${min} أحرف على الأقل`,
                maxLength: (max) => `يجب ألا يزيد عن ${max} حرف`
            }
        };

        this.init();
    }

    init() {
        // إزالة التحقق الافتراضي للمتصفح
        document.addEventListener('DOMContentLoaded', () => {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.setAttribute('novalidate', 'true');
                this.setupForm(form);
            });
        });
    }

    setupForm(form) {
        const fields = form.querySelectorAll('input, select, textarea');
        
        fields.forEach(field => {
            this.setupField(field);
        });

        // التحقق عند إرسال النموذج
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    setupField(field) {
        const fieldContainer = this.createFieldContainer(field);
        
        // التحقق عند فقدان التركيز
        field.addEventListener('blur', () => {
            this.validateField(field);
        });

        // التحقق أثناء الكتابة (مع تأخير)
        let timeout;
        field.addEventListener('input', () => {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                this.validateField(field);
            }, 500);
        });

        // إزالة رسالة الخطأ عند التركيز
        field.addEventListener('focus', () => {
            this.clearFieldError(field);
        });
    }

    createFieldContainer(field) {
        if (field.closest('.form-field')) {
            return field.closest('.form-field');
        }

        const container = document.createElement('div');
        container.className = 'form-field';
        
        // إضافة class للحقول المطلوبة
        if (field.hasAttribute('required')) {
            container.classList.add('required');
        }

        field.parentNode.insertBefore(container, field);
        container.appendChild(field);

        return container;
    }

    validateField(field) {
        const container = field.closest('.form-field');
        const value = field.value;
        const rules = this.getFieldRules(field);
        
        let isValid = true;
        let errorMessage = '';

        // التحقق من كل قاعدة
        for (const rule of rules) {
            const result = this.applyRule(value, rule, field);
            if (!result.valid) {
                isValid = false;
                errorMessage = result.message;
                break;
            }
        }

        if (isValid) {
            this.showFieldSuccess(container, field);
        } else {
            this.showFieldError(container, field, errorMessage);
        }

        return isValid;
    }

    getFieldRules(field) {
        const rules = [];
        
        // قاعدة الحقل المطلوب
        if (field.hasAttribute('required')) {
            rules.push({ type: 'required' });
        }

        // قواعد حسب نوع الحقل
        const type = field.getAttribute('type') || field.tagName.toLowerCase();
        switch (type) {
            case 'email':
                rules.push({ type: 'email' });
                break;
            case 'tel':
                rules.push({ type: 'phone' });
                break;
            case 'number':
                rules.push({ type: 'number' });
                break;
            case 'url':
                rules.push({ type: 'url' });
                break;
            case 'password':
                rules.push({ type: 'password' });
                break;
        }

        // قواعد الطول
        const minLength = field.getAttribute('minlength');
        if (minLength) {
            rules.push({ type: 'minLength', value: parseInt(minLength) });
        }

        const maxLength = field.getAttribute('maxlength');
        if (maxLength) {
            rules.push({ type: 'maxLength', value: parseInt(maxLength) });
        }

        // قاعدة تأكيد كلمة المرور
        const confirmPassword = field.getAttribute('data-confirm-password');
        if (confirmPassword) {
            const originalField = document.querySelector(`[name="${confirmPassword}"]`);
            if (originalField) {
                rules.push({ type: 'confirmPassword', value: originalField.value });
            }
        }

        return rules;
    }

    applyRule(value, rule, field) {
        const ruleConfig = this.rules[rule.type];
        if (!ruleConfig) {
            return { valid: true };
        }

        let isValid;
        let message;

        if (rule.type === 'required') {
            isValid = ruleConfig.test(value);
            message = ruleConfig.message;
        } else if (rule.type === 'minLength' || rule.type === 'maxLength') {
            isValid = ruleConfig.test(value, rule.value);
            message = ruleConfig.message(rule.value);
        } else if (rule.type === 'confirmPassword') {
            isValid = ruleConfig.test(value, rule.value);
            message = ruleConfig.message;
        } else {
            // تخطي التحقق إذا كان الحقل فارغ وغير مطلوب
            if (!value && !field.hasAttribute('required')) {
                return { valid: true };
            }
            isValid = ruleConfig.test(value);
            message = ruleConfig.message;
        }

        return { valid: isValid, message };
    }

    showFieldError(container, field, message) {
        this.clearFieldMessages(container);
        
        container.classList.remove('success');
        container.classList.add('error');

        const messageElement = document.createElement('div');
        messageElement.className = 'validation-message error show';
        messageElement.textContent = message;

        container.appendChild(messageElement);
    }

    showFieldSuccess(container, field) {
        this.clearFieldMessages(container);
        
        container.classList.remove('error');
        container.classList.add('success');
    }

    clearFieldError(container) {
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        if (!container) return;

        container.classList.remove('error');
        this.clearFieldMessages(container);
    }

    clearFieldMessages(container) {
        const messages = container.querySelectorAll('.validation-message');
        messages.forEach(msg => msg.remove());
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input, select, textarea');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    // دوال مساعدة للاستخدام الخارجي
    showMessage(selector, message, type = 'error') {
        const container = document.querySelector(selector);
        if (!container) return;

        this.clearFieldMessages(container);
        
        const messageElement = document.createElement('div');
        messageElement.className = `validation-message ${type} show`;
        messageElement.textContent = message;

        container.appendChild(messageElement);
    }

    clearMessage(selector) {
        const container = document.querySelector(selector);
        if (container) {
            this.clearFieldMessages(container);
            container.classList.remove('error', 'success');
        }
    }
}

// تهيئة النظام
const modernValidation = new ModernValidation();

// تصدير للاستخدام العام
window.ModernValidation = ModernValidation;
window.validation = modernValidation;
