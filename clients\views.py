from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Count
from django.core.paginator import Paginator
import re

from .models import Client, ClientDocument
from .forms import ClientForm, ClientDocumentForm

def extract_coordinates_from_google_maps_url(url):
    """استخراج الإحداثيات من رابط خرائط Google"""
    if not url:
        return None, None

    # نمط للبحث عن الإحداثيات في رابط خرائط Google
    # مثال: https://www.google.com/maps/place/24.7136,46.6753
    # أو: https://maps.google.com/?q=24.7136,46.6753
    # أو: https://www.google.com/maps/@24.7136,46.6753,15z
    patterns = [
        r'@(-?\d+\.\d+),(-?\d+\.\d+)',  # نمط @lat,lng
        r'place/(-?\d+\.\d+),(-?\d+\.\d+)',  # نمط place/lat,lng
        r'q=(-?\d+\.\d+),(-?\d+\.\d+)',  # نمط q=lat,lng
        r'll=(-?\d+\.\d+),(-?\d+\.\d+)'  # نمط ll=lat,lng
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            try:
                lat = float(match.group(1))
                lng = float(match.group(2))
                return lat, lng
            except (ValueError, IndexError):
                pass

    return None, None

@login_required
def client_dashboard(request):
    """عرض لوحة تحكم العملاء"""
    # إحصائيات العملاء حسب النوع
    permanent_clients_count = Client.objects.filter(client_type='permanent').count()
    monthly_clients_count = Client.objects.filter(client_type='monthly').count()
    custom_clients_count = Client.objects.filter(client_type='custom').count()

    # آخر العملاء المضافين
    recent_clients = Client.objects.all().order_by('-created_at')[:5]

    context = {
        'permanent_clients_count': permanent_clients_count,
        'monthly_clients_count': monthly_clients_count,
        'custom_clients_count': custom_clients_count,
        'recent_clients': recent_clients,
    }

    return render(request, 'clients/dashboard.html', context)

@login_required
def client_list(request):
    """عرض قائمة العملاء مع إمكانية التصفية"""
    # الحصول على معلمات التصفية
    client_type = request.GET.get('type', '')
    search_query = request.GET.get('search', '')
    status = request.GET.get('status', '')

    # تصفية العملاء
    clients = Client.objects.all().order_by('-created_at')

    if client_type:
        clients = clients.filter(client_type=client_type)

    if search_query:
        clients = clients.filter(name__icontains=search_query) | \
                 clients.filter(phone_1__icontains=search_query) | \
                 clients.filter(national_id__icontains=search_query)

    if status:
        clients = clients.filter(status=status)

    # التقسيم إلى صفحات
    paginator = Paginator(clients, 10)  # 10 عملاء في كل صفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'clients': page_obj,
        'client_type': client_type,
        'search_query': search_query,
        'status': status,
        'client_types': Client.CLIENT_TYPE_CHOICES,
        'status_choices': Client.STATUS_CHOICES,
    }

    return render(request, 'clients/client_list.html', context)

@login_required
def client_create(request):
    """إضافة عميل جديد"""
    # الحصول على نوع العميل من معلمات الاستعلام
    client_type = request.GET.get('type', 'permanent')

    if request.method == 'POST':
        form = ClientForm(request.POST)
        if form.is_valid():
            client = form.save(commit=False)
            client.created_by = request.user

            # معالجة رابط الخريطة إذا كان نوع العميل هو خدمة مخصصة
            if client.client_type == 'custom' and 'map_url' in request.POST:
                map_url = request.POST.get('map_url')
                lat, lng = extract_coordinates_from_google_maps_url(map_url)
                if lat and lng:
                    client.location_lat = lat
                    client.location_lng = lng

            client.save()

            messages.success(request, f'تم إضافة العميل {client.name} بنجاح')

            # معالجة المستندات المرفقة
            if request.FILES:
                for field_name, file in request.FILES.items():
                    if field_name.startswith('document_'):
                        document_type = field_name.replace('document_', '')
                        description = request.POST.get(f'description_{document_type}', '')
                        expiry_date = request.POST.get(f'expiry_date_{document_type}', None)

                        document = ClientDocument(
                            client=client,
                            document_type=document_type,
                            file=file,
                            description=description,
                            expiry_date=expiry_date,
                            uploaded_by=request.user
                        )
                        document.save()

            return redirect('clients:client_detail', client_id=client.id)
    else:
        # إنشاء نموذج جديد مع تعيين نوع العميل
        form = ClientForm(initial={'client_type': client_type})

    context = {
        'form': form,
        'client_type': client_type,
        'is_edit': False,
        'title': f'إضافة عميل {dict(Client.CLIENT_TYPE_CHOICES)[client_type]}',
    }

    return render(request, 'clients/client_form.html', context)

@login_required
def client_detail(request, client_id):
    """عرض تفاصيل العميل"""
    client = get_object_or_404(Client, id=client_id)
    documents = client.documents.all()

    context = {
        'client': client,
        'documents': documents,
    }

    return render(request, 'clients/client_detail.html', context)

@login_required
def client_edit(request, client_id):
    """تعديل بيانات العميل"""
    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        form = ClientForm(request.POST, instance=client)
        if form.is_valid():
            client = form.save(commit=False)

            # معالجة رابط الخريطة إذا كان نوع العميل هو خدمة مخصصة
            if client.client_type == 'custom' and 'map_url' in request.POST:
                map_url = request.POST.get('map_url')
                lat, lng = extract_coordinates_from_google_maps_url(map_url)
                if lat and lng:
                    client.location_lat = lat
                    client.location_lng = lng

            client.save()

            messages.success(request, f'تم تحديث بيانات العميل {client.name} بنجاح')

            # معالجة المستندات المرفقة
            if request.FILES:
                for field_name, file in request.FILES.items():
                    if field_name.startswith('document_'):
                        document_type = field_name.replace('document_', '')
                        description = request.POST.get(f'description_{document_type}', '')
                        expiry_date = request.POST.get(f'expiry_date_{document_type}', None)

                        document = ClientDocument(
                            client=client,
                            document_type=document_type,
                            file=file,
                            description=description,
                            expiry_date=expiry_date,
                            uploaded_by=request.user
                        )
                        document.save()

            return redirect('clients:client_detail', client_id=client.id)
    else:
        # إذا كان العميل من نوع خدمة مخصصة ولديه إحداثيات، قم بإنشاء رابط خرائط Google
        initial_data = {}
        if client.client_type == 'custom' and client.location_lat and client.location_lng:
            initial_data['map_url'] = f"https://www.google.com/maps?q={client.location_lat},{client.location_lng}"

        form = ClientForm(instance=client, initial=initial_data)

    context = {
        'form': form,
        'client': client,
        'client_type': client.client_type,
        'is_edit': True,
        'title': f'تعديل بيانات العميل {client.name}',
        'documents': client.documents.all(),
    }

    return render(request, 'clients/client_form.html', context)

@login_required
def client_delete(request, client_id):
    """حذف العميل"""
    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        client_name = client.name
        client.delete()
        messages.success(request, f'تم حذف العميل {client_name} بنجاح')
        return redirect('clients:dashboard')

    return render(request, 'clients/client_confirm_delete.html', {'client': client})

@login_required
def client_document_add(request, client_id):
    """إضافة مستند للعميل"""
    client = get_object_or_404(Client, id=client_id)

    if request.method == 'POST':
        form = ClientDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.client = client
            document.uploaded_by = request.user
            document.save()

            messages.success(request, 'تم إضافة المستند بنجاح')
            return redirect('clients:client_detail', client_id=client.id)
    else:
        form = ClientDocumentForm()

    context = {
        'form': form,
        'client': client,
    }

    return render(request, 'clients/client_document_form.html', context)

@login_required
def client_document_delete(request, document_id):
    """حذف مستند العميل"""
    document = get_object_or_404(ClientDocument, id=document_id)
    client_id = document.client.id

    if request.method == 'POST':
        document.delete()
        messages.success(request, 'تم حذف المستند بنجاح')
        return redirect('clients:client_detail', client_id=client_id)

    return render(request, 'clients/document_confirm_delete.html', {'document': document})
