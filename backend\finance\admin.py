from django.contrib import admin
from .models import FinancialAccount, Transaction, Budget, Invoice


@admin.register(FinancialAccount)
class FinancialAccountAdmin(admin.ModelAdmin):
    list_display = ['name', 'account_type', 'balance', 'is_active', 'created_at']
    list_filter = ['account_type', 'is_active']
    search_fields = ['name', 'account_number', 'bank_name']


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ['transaction_number', 'account', 'transaction_type', 'category', 'amount', 'transaction_date']
    list_filter = ['transaction_type', 'category', 'transaction_date']
    search_fields = ['transaction_number', 'description', 'reference_number']
    readonly_fields = ['transaction_number']


@admin.register(Budget)
class BudgetAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'planned_amount', 'actual_amount', 'start_date', 'end_date', 'is_active']
    list_filter = ['category', 'is_active']
    search_fields = ['name']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'client_name', 'total_amount', 'status', 'issue_date', 'due_date']
    list_filter = ['status', 'issue_date']
    search_fields = ['invoice_number', 'client_name', 'client_email']
    readonly_fields = ['invoice_number', 'total_amount']
