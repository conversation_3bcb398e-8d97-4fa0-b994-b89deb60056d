<!-- نموذج العميل الدائم -->
<div class="space-y-6">
    <!-- معلومات العميل الأساسية -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">المعلومات الأساسية</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- الاسم -->
            <div>
                <label for="id_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم <span class="text-red-500">*</span></label>
                {{ form.name }}
                {% if form.name.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- رقم البطاقة الوطنية -->
            <div>
                <label for="id_national_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم البطاقة الوطنية <span class="text-red-500">*</span></label>
                {{ form.national_id }}
                {% if form.national_id.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.national_id.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- الحالة -->
            <div>
                <label for="id_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.status.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- معلومات الاتصال -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">معلومات الاتصال</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- رقم الهاتف 1 -->
            <div>
                <label for="id_phone_1" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف 1 <span class="text-red-500">*</span></label>
                {{ form.phone_1 }}
                {% if form.phone_1.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.phone_1.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- رقم الهاتف 2 -->
            <div>
                <label for="id_phone_2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف 2</label>
                {{ form.phone_2 }}
                {% if form.phone_2.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.phone_2.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- العنوان -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">العنوان</h3>

        <div>
            <label for="id_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العنوان <span class="text-red-500">*</span></label>
            {{ form.address }}
            {% if form.address.errors %}
            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.address.errors.0 }}</p>
            {% endif %}
        </div>
    </div>

    <!-- ملاحظات إضافية -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">ملاحظات إضافية</h3>

        <div>
            <label for="id_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
            {{ form.notes }}
            {% if form.notes.errors %}
            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.notes.errors.0 }}</p>
            {% endif %}
        </div>
    </div>

    <!-- المستندات المطلوبة -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg border border-gray-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 border-r-4 border-blue-500 pr-3">المستندات المطلوبة</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- البطاقة الوطنية (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">البطاقة الوطنية (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_national_id_front" class="hidden" id="document_national_id_front">
                        <button type="button" onclick="document.getElementById('document_national_id_front').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_national_id_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- البطاقة الوطنية (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">البطاقة الوطنية (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_national_id_back" class="hidden" id="document_national_id_back">
                        <button type="button" onclick="document.getElementById('document_national_id_back').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_national_id_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- بطاقة السكن (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">بطاقة السكن (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_residence_card_front" class="hidden" id="document_residence_card_front">
                        <button type="button" onclick="document.getElementById('document_residence_card_front').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_residence_card_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- بطاقة السكن (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">بطاقة السكن (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_residence_card_back" class="hidden" id="document_residence_card_back">
                        <button type="button" onclick="document.getElementById('document_residence_card_back').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_residence_card_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- هوية العمل (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">هوية العمل (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_work_id_front" class="hidden" id="document_work_id_front">
                        <button type="button" onclick="document.getElementById('document_work_id_front').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_work_id_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- هوية العمل (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">هوية العمل (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_work_id_back" class="hidden" id="document_work_id_back">
                        <button type="button" onclick="document.getElementById('document_work_id_back').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_work_id_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- هوية الزوجة (وجه) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">هوية الزوجة (وجه)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_spouse_id_front" class="hidden" id="document_spouse_id_front">
                        <button type="button" onclick="document.getElementById('document_spouse_id_front').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_spouse_id_front" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>

            <!-- هوية الزوجة (ظهر) -->
            <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">هوية الزوجة (ظهر)</h4>
                <div class="flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-lg p-4 mb-2">
                    <div class="text-center">
                        <i class="fas fa-upload text-gray-400 dark:text-gray-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب الملف هنا أو انقر للتصفح</p>
                        <input type="file" name="document_spouse_id_back" class="hidden" id="document_spouse_id_back">
                        <button type="button" onclick="document.getElementById('document_spouse_id_back').click()" class="mt-2 px-4 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-300">
                            اختر ملف
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" name="description_spouse_id_back" placeholder="وصف المستند (اختياري)" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 text-sm">
                </div>
            </div>
        </div>
    </div>
</div>
