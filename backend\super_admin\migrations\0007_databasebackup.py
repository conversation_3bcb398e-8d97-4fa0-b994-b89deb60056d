# Generated by Django 5.2 on 2025-05-07 21:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0006_failedloginattempt'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DatabaseBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_path', models.CharField(max_length=500, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف (بايت)')),
                ('backup_type', models.CharField(choices=[('manual', 'يدوي'), ('automatic', 'تلقائي'), ('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='manual', max_length=20, verbose_name='نوع النسخة الاحتياطية')),
                ('is_encrypted', models.BooleanField(default=False, verbose_name='مشفرة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backups', to='super_admin.company', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_backups', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'نسخة احتياطية',
                'verbose_name_plural': 'النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
    ]
