<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد تطبيق المصادقة - منصة استقدامي</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .setup-card {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }
        
        .qr-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            display: inline-block;
        }
        
        .secret-box {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            letter-spacing: 2px;
            word-break: break-all;
        }
        
        .copy-btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-2px);
        }
        
        .step-card {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #10b981;
        }
        
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
    </style>
</head>
<body>

    <div class="min-h-screen flex items-center justify-center p-6">
        <div class="setup-card rounded-2xl p-8 w-full max-w-4xl">
            
            <!-- Header -->
            <div class="text-center mb-8">
                {% load static %}
                <div class="mb-6">
                    <img src="{% static 'images/logo.webp' %}" alt="استقدامي" class="h-16 w-auto mx-auto">
                </div>
                
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-mobile-alt text-blue-400 ml-2"></i>
                    إعداد تطبيق المصادقة
                </h1>
                <p class="text-gray-300">إعداد Google Authenticator للمسؤول الأعلى</p>
            </div>

            <!-- Status Messages -->
            {% if message %}
            <div class="success-message rounded-lg p-4 mb-6 text-center">
                <i class="fas fa-check-circle ml-2"></i>
                {{ message }}
            </div>
            {% endif %}

            {% if error %}
            <div class="error-message rounded-lg p-4 mb-6 text-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                {{ error }}
            </div>
            {% endif %}

            {% if not error %}
            <!-- Setup Steps -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- QR Code Section -->
                <div class="step-card">
                    <h2 class="text-xl font-bold text-white mb-4">
                        <i class="fas fa-qrcode text-blue-400 ml-2"></i>
                        الخطوة 1: مسح رمز QR
                    </h2>
                    
                    {% if qr_code %}
                    <div class="text-center mb-4">
                        <div class="qr-container">
                            <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="w-48 h-48">
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <p class="text-gray-300 text-sm mb-4">
                            امسح هذا الرمز باستخدام تطبيق Google Authenticator
                        </p>
                        
                        <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" 
                           target="_blank" 
                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                            <i class="fab fa-google-play ml-2"></i>
                            تحميل التطبيق
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center text-gray-400">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <p>لا يمكن إنشاء رمز QR</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Manual Setup Section -->
                <div class="step-card">
                    <h2 class="text-xl font-bold text-white mb-4">
                        <i class="fas fa-key text-green-400 ml-2"></i>
                        الخطوة 2: الإعداد اليدوي
                    </h2>
                    
                    {% if secret %}
                    <div class="mb-4">
                        <label class="block text-gray-300 text-sm font-bold mb-2">
                            المفتاح السري:
                        </label>
                        <div class="secret-box text-blue-200" id="secret-text">
                            {{ secret }}
                        </div>
                        <button onclick="copySecret()" 
                                class="copy-btn w-full mt-3 py-2 px-4 rounded-lg text-white font-bold">
                            <i class="fas fa-copy ml-2"></i>
                            نسخ المفتاح
                        </button>
                    </div>
                    
                    <div class="text-sm text-gray-400 space-y-2">
                        <p><strong>اسم الحساب:</strong> {{ user.email|default:"<EMAIL>" }}</p>
                        <p><strong>الجهة المصدرة:</strong> منصة استقدامي السحابية</p>
                        <p><strong>نوع الرمز:</strong> TOTP (Time-based)</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Instructions -->
            <div class="step-card mt-8">
                <h2 class="text-xl font-bold text-white mb-4">
                    <i class="fas fa-list-ol text-yellow-400 ml-2"></i>
                    تعليمات الإعداد
                </h2>
                
                <div class="space-y-4 text-gray-300">
                    <div class="flex items-start">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-1">1</span>
                        <p>حمل تطبيق Google Authenticator من متجر التطبيقات</p>
                    </div>
                    
                    <div class="flex items-start">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-1">2</span>
                        <p>افتح التطبيق واضغط على "+" لإضافة حساب جديد</p>
                    </div>
                    
                    <div class="flex items-start">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-1">3</span>
                        <p>اختر "مسح رمز QR" أو "إدخال مفتاح الإعداد"</p>
                    </div>
                    
                    <div class="flex items-start">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-1">4</span>
                        <p>امسح الرمز أعلاه أو أدخل المفتاح السري يدوياً</p>
                    </div>
                    
                    <div class="flex items-start">
                        <span class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold ml-3 mt-1">5</span>
                        <p>الآن يمكنك استخدام التطبيق لتسجيل الدخول!</p>
                    </div>
                </div>
            </div>

            <!-- Status Info -->
            <div class="mt-8 p-4 bg-gray-800 rounded-lg">
                <h3 class="text-lg font-bold text-white mb-3">
                    <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                    معلومات الحالة
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-400">المستخدم:</span>
                        <span class="text-white font-bold">{{ user.username|default:"غير محدد" }}</span>
                    </div>
                    
                    <div>
                        <span class="text-gray-400">المصادقة الثنائية:</span>
                        {% if two_factor_enabled %}
                        <span class="text-green-400 font-bold">
                            <i class="fas fa-check-circle ml-1"></i>
                            مفعلة
                        </span>
                        {% else %}
                        <span class="text-yellow-400 font-bold">
                            <i class="fas fa-exclamation-triangle ml-1"></i>
                            غير مفعلة
                        </span>
                        {% endif %}
                    </div>
                    
                    <div>
                        <span class="text-gray-400">سر التطبيق:</span>
                        {% if secret %}
                        <span class="text-green-400 font-bold">
                            <i class="fas fa-check-circle ml-1"></i>
                            موجود
                        </span>
                        {% else %}
                        <span class="text-red-400 font-bold">
                            <i class="fas fa-times-circle ml-1"></i>
                            غير موجود
                        </span>
                        {% endif %}
                    </div>
                    
                    <div>
                        <span class="text-gray-400">البريد الإلكتروني:</span>
                        <span class="text-white font-bold">{{ user.email|default:"<EMAIL>" }}</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-8 text-center space-x-4 space-x-reverse">
                <a href="/super_admin/login/" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold transition-colors">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تجربة تسجيل الدخول
                </a>
                
                <button onclick="window.location.reload()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-bold transition-colors">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث الصفحة
                </button>
            </div>
            {% endif %}

        </div>
    </div>

    <script>
        function copySecret() {
            const secretText = document.getElementById('secret-text').textContent;
            navigator.clipboard.writeText(secretText).then(function() {
                // تغيير نص الزر مؤقتاً
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check ml-2"></i>تم النسخ!';
                btn.classList.add('bg-green-600');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('bg-green-600');
                }, 2000);
            });
        }
    </script>

</body>
</html>
