# Generated by Django 5.2 on 2025-04-09 17:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system_settings', '0002_currency'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف')], max_length=20, verbose_name='نوع الإجراء')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('record_id', models.IntegerField(blank=True, null=True, verbose_name='معرف السجل')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('previous_data', models.JSONField(blank=True, null=True, verbose_name='البيانات السابقة')),
                ('current_data', models.JSONField(blank=True, null=True, verbose_name='البيانات الحالية')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('completed', 'مكتمل'), ('undone', 'تم التراجع')], default='completed', max_length=20, verbose_name='الحالة')),
                ('can_undo', models.BooleanField(default=True, verbose_name='يمكن التراجع')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('undone_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التراجع')),
                ('undone_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='undone_actions', to=settings.AUTH_USER_MODEL, verbose_name='تم التراجع بواسطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='system_actions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إجراء النظام',
                'verbose_name_plural': 'إجراءات النظام',
                'ordering': ['-created_at'],
            },
        ),
    ]
