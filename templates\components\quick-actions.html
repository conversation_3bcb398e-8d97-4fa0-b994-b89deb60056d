{#
    مكون الإجراءات السريعة
    
    الاستخدام:
    {% include 'components/quick-actions.html' with title='إجراءات سريعة' actions=actions columns=2 %}
#}

{% load static %}

<div class="bg-white dark:bg-gray-800 shadow-md hover:shadow-lg rounded-xl p-6 transition-all duration-300">
    
    <!-- رأس القسم -->
    <div class="flex items-center mb-6">
        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
            <i class="fas fa-bolt text-white text-sm"></i>
        </div>
        <h3 class="font-bold text-gray-700 dark:text-gray-200 text-lg">{{ title|default:'إجراءات سريعة' }}</h3>
    </div>
    
    <!-- شبكة الإجراءات -->
    <div class="grid 
        {% if columns == 1 %}grid-cols-1
        {% elif columns == 3 %}grid-cols-1 md:grid-cols-2 lg:grid-cols-3
        {% elif columns == 4 %}grid-cols-1 md:grid-cols-2 lg:grid-cols-4
        {% else %}grid-cols-1 md:grid-cols-2{% endif %} gap-4">
        
        {% for action in actions %}
            <div class="group">
                {% if action.link %}
                    <a href="{{ action.link }}" class="block">
                {% elif action.onclick %}
                    <button onclick="{{ action.onclick }}" class="w-full">
                {% else %}
                    <div class="cursor-pointer">
                {% endif %}
                
                    <div class="relative overflow-hidden bg-white dark:bg-gray-700 border-2 
                        {% if action.color == 'blue' %}border-blue-200 dark:border-gray-600
                        {% elif action.color == 'green' %}border-green-200 dark:border-gray-600
                        {% elif action.color == 'cyan' %}border-cyan-200 dark:border-gray-600
                        {% elif action.color == 'purple' %}border-purple-200 dark:border-gray-600
                        {% elif action.color == 'red' %}border-red-200 dark:border-gray-600
                        {% elif action.color == 'yellow' %}border-yellow-200 dark:border-gray-600
                        {% elif action.color == 'indigo' %}border-indigo-200 dark:border-gray-600
                        {% else %}border-gray-200 dark:border-gray-600{% endif %}
                        rounded-xl p-4 hover:shadow-lg transition-all duration-300 group-hover:transform group-hover:scale-105">
                        
                        <!-- خلفية متدرجة -->
                        <div class="absolute inset-0 
                            {% if action.color == 'blue' %}bg-blue-500
                            {% elif action.color == 'green' %}bg-green-500
                            {% elif action.color == 'cyan' %}bg-cyan-500
                            {% elif action.color == 'purple' %}bg-purple-500
                            {% elif action.color == 'red' %}bg-red-500
                            {% elif action.color == 'yellow' %}bg-yellow-500
                            {% elif action.color == 'indigo' %}bg-indigo-500
                            {% else %}bg-gray-500{% endif %}
                            opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        
                        <!-- المحتوى -->
                        <div class="relative flex items-center justify-center flex-col text-center">
                            
                            <!-- الأيقونة -->
                            <div class="w-12 h-12 
                                {% if action.color == 'blue' %}bg-blue-500 group-hover:bg-blue-600
                                {% elif action.color == 'green' %}bg-green-500 group-hover:bg-green-600
                                {% elif action.color == 'cyan' %}bg-cyan-500 group-hover:bg-cyan-600
                                {% elif action.color == 'purple' %}bg-purple-500 group-hover:bg-purple-600
                                {% elif action.color == 'red' %}bg-red-500 group-hover:bg-red-600
                                {% elif action.color == 'yellow' %}bg-yellow-500 group-hover:bg-yellow-600
                                {% elif action.color == 'indigo' %}bg-indigo-500 group-hover:bg-indigo-600
                                {% else %}bg-gray-500 group-hover:bg-gray-600{% endif %}
                                rounded-full flex items-center justify-center mb-3 transition-colors duration-300">
                                <i class="{{ action.icon|default:'fas fa-cog' }} text-white text-lg"></i>
                            </div>
                            
                            <!-- النص -->
                            <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 
                                {% if action.color == 'blue' %}group-hover:text-blue-600
                                {% elif action.color == 'green' %}group-hover:text-green-600
                                {% elif action.color == 'cyan' %}group-hover:text-cyan-600
                                {% elif action.color == 'purple' %}group-hover:text-purple-600
                                {% elif action.color == 'red' %}group-hover:text-red-600
                                {% elif action.color == 'yellow' %}group-hover:text-yellow-600
                                {% elif action.color == 'indigo' %}group-hover:text-indigo-600
                                {% else %}group-hover:text-gray-600{% endif %}
                                transition-colors duration-300">
                                {{ action.label|default:'إجراء' }}
                            </span>
                            
                            <!-- وصف إضافي -->
                            {% if action.description %}
                                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {{ action.description }}
                                </span>
                            {% endif %}
                        </div>
                        
                        <!-- مؤشر التفاعل -->
                        <div class="absolute bottom-0 left-0 w-full h-1 
                            {% if action.color == 'blue' %}bg-blue-500
                            {% elif action.color == 'green' %}bg-green-500
                            {% elif action.color == 'cyan' %}bg-cyan-500
                            {% elif action.color == 'purple' %}bg-purple-500
                            {% elif action.color == 'red' %}bg-red-500
                            {% elif action.color == 'yellow' %}bg-yellow-500
                            {% elif action.color == 'indigo' %}bg-indigo-500
                            {% else %}bg-gray-500{% endif %}
                            transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                    </div>
                
                {% if action.link %}
                    </a>
                {% elif action.onclick %}
                    </button>
                {% else %}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
    
    <!-- إجراءات إضافية -->
    {% if showMore %}
        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button class="w-full text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-colors duration-200">
                <i class="fas fa-ellipsis-h mr-2"></i>
                عرض المزيد من الإجراءات
            </button>
        </div>
    {% endif %}
</div>
