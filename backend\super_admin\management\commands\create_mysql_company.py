"""
أمر إدارة Django لإنشاء شركة جديدة في النظام باستخدام MySQL أو SQLite
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
import logging
import os
import random
import string
import sqlite3
from backend.super_admin.models import Company, SystemLog
from backend.super_admin.database_connector import test_company_db_connection

# محاولة استيراد وحدة MySQL
try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'إنشاء شركة جديدة في النظام باستخدام MySQL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            help='اسم الشركة'
        )
        parser.add_argument(
            '--db-name',
            type=str,
            help='اسم قاعدة البيانات'
        )
        parser.add_argument(
            '--db-user',
            type=str,
            help='اسم مستخدم قاعدة البيانات'
        )
        parser.add_argument(
            '--db-password',
            type=str,
            help='كلمة مرور قاعدة البيانات'
        )
        parser.add_argument(
            '--db-host',
            type=str,
            default='localhost',
            help='مضيف قاعدة البيانات'
        )
        parser.add_argument(
            '--status',
            type=str,
            default='active',
            choices=['active', 'inactive', 'suspended', 'trial'],
            help='حالة الشركة'
        )

    def handle(self, *args, **options):
        # الحصول على معلومات الشركة من الخيارات
        name = options.get('name')
        db_name = options.get('db_name')
        db_user = options.get('db_user')
        db_password = options.get('db_password')
        db_host = options.get('db_host')
        status = options.get('status')

        # إذا لم يتم تحديد اسم الشركة، اطلب من المستخدم إدخاله
        if not name:
            name = input('أدخل اسم الشركة: ')

        # إذا لم يتم تحديد اسم قاعدة البيانات، استخدم اسم الشركة مع إزالة المسافات والأحرف الخاصة
        if not db_name:
            db_name = ''.join(c for c in name if c.isalnum()).lower()
            self.stdout.write(self.style.SUCCESS(f"تم تعيين اسم قاعدة البيانات تلقائيًا: {db_name}"))

        # إذا لم يتم تحديد اسم مستخدم قاعدة البيانات، استخدم 'admin'
        if not db_user:
            db_user = 'admin'
            self.stdout.write(self.style.SUCCESS(f"تم تعيين اسم مستخدم قاعدة البيانات تلقائيًا: {db_user}"))

        # إذا لم يتم تحديد كلمة مرور قاعدة البيانات، قم بتوليد كلمة مرور عشوائية
        if not db_password:
            db_password = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(12))
            self.stdout.write(self.style.SUCCESS(f"تم توليد كلمة مرور قاعدة البيانات تلقائيًا: {db_password}"))

        # التحقق من نوع قاعدة البيانات
        is_mysql = db_host not in ['localhost', '127.0.0.1']

        # التحقق من توفر وحدة MySQL إذا كانت قاعدة البيانات من نوع MySQL
        if is_mysql and not MYSQL_AVAILABLE:
            self.stdout.write(self.style.ERROR(f"وحدة mysql.connector غير متوفرة. قم بتثبيتها باستخدام pip install mysql-connector-python"))
            return

        try:
            # إنشاء الشركة
            with transaction.atomic():
                company = Company(
                    name=name,
                    database_name=db_name,
                    database_user=db_user,
                    database_password=db_password,
                    database_host=db_host,
                    status=status,
                )
                company.save()

                # تسجيل العملية في سجل النظام
                SystemLog.objects.create(
                    action='create_company',
                    details=f"تم إنشاء شركة جديدة: {name}",
                    ip_address='127.0.0.1',
                    user_agent='Django Management Command',
                )

                self.stdout.write(self.style.SUCCESS(f"تم إنشاء الشركة بنجاح:"))
                self.stdout.write(self.style.SUCCESS(f"- اسم الشركة: {company.name}"))
                self.stdout.write(self.style.SUCCESS(f"- الرقم التسلسلي: {company.serial_number}"))
                self.stdout.write(self.style.SUCCESS(f"- اسم قاعدة البيانات: {company.database_name}"))
                self.stdout.write(self.style.SUCCESS(f"- مستخدم قاعدة البيانات: {company.database_user}"))
                self.stdout.write(self.style.SUCCESS(f"- كلمة مرور قاعدة البيانات: {company.database_password}"))
                self.stdout.write(self.style.SUCCESS(f"- مضيف قاعدة البيانات: {company.database_host}"))
                self.stdout.write(self.style.SUCCESS(f"- الحالة: {company.status}"))
                self.stdout.write(self.style.SUCCESS(f"- تاريخ الإنشاء: {company.created_at}"))

                # إنشاء قاعدة البيانات
                if is_mysql:
                    # إنشاء قاعدة بيانات MySQL
                    self.create_mysql_database(db_name, db_user, db_password, db_host)
                else:
                    # إنشاء قاعدة بيانات SQLite
                    self.create_sqlite_database(db_name)

                # اختبار الاتصال بقاعدة البيانات
                connection_test = test_company_db_connection(company)

                if connection_test['success']:
                    self.stdout.write(self.style.SUCCESS(f"تم الاتصال بقاعدة البيانات بنجاح."))
                    self.stdout.write(self.style.SUCCESS(f"نوع قاعدة البيانات: {connection_test['db_type']}"))
                    self.stdout.write(self.style.SUCCESS(f"عدد الجداول: {connection_test['tables_count']}"))
                else:
                    self.stdout.write(self.style.ERROR(f"فشل في الاتصال بقاعدة البيانات: {connection_test['message']}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء إنشاء الشركة: {str(e)}"))
            logger.error(f"حدث خطأ أثناء إنشاء الشركة: {str(e)}", exc_info=True)

    def create_mysql_database(self, db_name, db_user, db_password, db_host):
        """
        إنشاء قاعدة بيانات MySQL

        Args:
            db_name: اسم قاعدة البيانات
            db_user: اسم المستخدم
            db_password: كلمة المرور
            db_host: المضيف
        """
        try:
            # الاتصال بخادم MySQL
            connection = mysql.connector.connect(
                host=db_host,
                user=db_user,
                password=db_password
            )

            if connection.is_connected():
                cursor = connection.cursor()

                # التحقق من وجود قاعدة البيانات
                cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
                result = cursor.fetchone()

                if result:
                    # قاعدة البيانات موجودة بالفعل
                    self.stdout.write(self.style.WARNING(f"قاعدة البيانات {db_name} موجودة بالفعل"))

                    # إعادة تهيئة قاعدة البيانات (حذف وإعادة إنشاء)
                    cursor.execute(f"DROP DATABASE `{db_name}`")
                    self.stdout.write(self.style.SUCCESS(f"تم حذف قاعدة البيانات {db_name}"))

                # إنشاء قاعدة البيانات
                cursor.execute(f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                self.stdout.write(self.style.SUCCESS(f"تم إنشاء قاعدة البيانات {db_name}"))

                # إنشاء هيكل قاعدة البيانات
                self.create_database_structure(db_name, db_user, db_password, db_host)

                # إغلاق الاتصال
                cursor.close()
                connection.close()

                self.stdout.write(self.style.SUCCESS(f"تم إنشاء وتهيئة قاعدة البيانات {db_name} بنجاح"))
            else:
                self.stdout.write(self.style.ERROR(f"فشل في الاتصال بخادم قاعدة البيانات"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء إنشاء قاعدة بيانات MySQL: {str(e)}"))

    def create_sqlite_database(self, db_name):
        """
        إنشاء قاعدة بيانات SQLite

        Args:
            db_name: اسم قاعدة البيانات
        """
        try:
            # تحديد مسار قاعدة البيانات
            db_dir = os.path.join('data', 'databases')
            db_path = os.path.join(db_dir, f"{db_name}.db")

            # إنشاء المجلد إذا لم يكن موجودًا
            os.makedirs(db_dir, exist_ok=True)

            # التحقق من وجود قاعدة البيانات
            if os.path.exists(db_path):
                # قاعدة البيانات موجودة بالفعل
                self.stdout.write(self.style.WARNING(f"قاعدة البيانات {db_name} موجودة بالفعل"))

                # حذف قاعدة البيانات
                os.remove(db_path)
                self.stdout.write(self.style.SUCCESS(f"تم حذف قاعدة البيانات {db_name}"))

            # إنشاء قاعدة البيانات
            connection = sqlite3.connect(db_path)
            cursor = connection.cursor()

            # إنشاء هيكل قاعدة البيانات
            self.create_sqlite_structure(cursor)

            # إغلاق الاتصال
            cursor.close()
            connection.close()

            self.stdout.write(self.style.SUCCESS(f"تم إنشاء وتهيئة قاعدة البيانات {db_name} بنجاح"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء إنشاء قاعدة بيانات SQLite: {str(e)}"))

    def create_sqlite_structure(self, cursor):
        """
        إنشاء هيكل قاعدة بيانات SQLite

        Args:
            cursor: مؤشر قاعدة البيانات
        """
        # جدول العمال
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                nationality TEXT NOT NULL,
                passport_number TEXT NOT NULL,
                passport_issue_date DATE,
                passport_expiry_date DATE,
                visa_number TEXT,
                visa_issue_date DATE,
                entry_date DATE,
                birth_date DATE,
                gender TEXT NOT NULL,
                marital_status TEXT,
                religion TEXT,
                education_level TEXT,
                phone TEXT,
                address TEXT,
                status TEXT NOT NULL DEFAULT 'active',
                photo TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # جدول العملاء
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                client_type TEXT NOT NULL,
                id_number TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                status TEXT NOT NULL DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # جدول الخدمات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                service_type TEXT NOT NULL,
                description TEXT,
                price REAL NOT NULL,
                duration_hours INTEGER,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # جدول الحجوزات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bookings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_id INTEGER NOT NULL,
                service_id INTEGER NOT NULL,
                booking_date DATE NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                total_price REAL NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
                FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
            );
        """)

        # جدول تفاصيل الحجوزات (العمال المخصصين للحجز)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS booking_workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                booking_id INTEGER NOT NULL,
                worker_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'assigned',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
                FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE
            );
        """)

    def create_database_structure(self, db_name, db_user, db_password, db_host):
        """
        إنشاء هيكل قاعدة بيانات MySQL للشركة

        Args:
            db_name: اسم قاعدة البيانات
            db_user: اسم المستخدم
            db_password: كلمة المرور
            db_host: المضيف
        """
        try:
            # إنشاء اتصال بقاعدة البيانات
            connection = mysql.connector.connect(
                host=db_host,
                user=db_user,
                password=db_password,
                database=db_name
            )

            if connection.is_connected():
                cursor = connection.cursor()

                # إنشاء جداول قاعدة البيانات

                # جدول العمال
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `workers` (
                        `id` INT AUTO_INCREMENT PRIMARY KEY,
                        `full_name` VARCHAR(255) NOT NULL,
                        `nationality` VARCHAR(100) NOT NULL,
                        `passport_number` VARCHAR(50) NOT NULL,
                        `passport_issue_date` DATE,
                        `passport_expiry_date` DATE,
                        `visa_number` VARCHAR(50),
                        `visa_issue_date` DATE,
                        `entry_date` DATE,
                        `birth_date` DATE,
                        `gender` VARCHAR(10) NOT NULL,
                        `marital_status` VARCHAR(20),
                        `religion` VARCHAR(50),
                        `education_level` VARCHAR(100),
                        `phone` VARCHAR(20),
                        `address` TEXT,
                        `status` VARCHAR(20) NOT NULL DEFAULT 'active',
                        `photo` VARCHAR(255),
                        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                """)

                # جدول العملاء
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `clients` (
                        `id` INT AUTO_INCREMENT PRIMARY KEY,
                        `name` VARCHAR(255) NOT NULL,
                        `client_type` VARCHAR(50) NOT NULL,
                        `id_number` VARCHAR(50),
                        `phone` VARCHAR(20),
                        `email` VARCHAR(255),
                        `address` TEXT,
                        `status` VARCHAR(20) NOT NULL DEFAULT 'active',
                        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                """)

                # جدول الخدمات
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `services` (
                        `id` INT AUTO_INCREMENT PRIMARY KEY,
                        `name` VARCHAR(255) NOT NULL,
                        `service_type` VARCHAR(50) NOT NULL,
                        `description` TEXT,
                        `price` DECIMAL(10,2) NOT NULL,
                        `duration_hours` INT,
                        `is_active` BOOLEAN DEFAULT TRUE,
                        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                """)

                # جدول الحجوزات
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `bookings` (
                        `id` INT AUTO_INCREMENT PRIMARY KEY,
                        `client_id` INT NOT NULL,
                        `service_id` INT NOT NULL,
                        `booking_date` DATE NOT NULL,
                        `start_time` TIME NOT NULL,
                        `end_time` TIME NOT NULL,
                        `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
                        `total_price` DECIMAL(10,2) NOT NULL,
                        `notes` TEXT,
                        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE CASCADE,
                        FOREIGN KEY (`service_id`) REFERENCES `services`(`id`) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                """)

                # جدول تفاصيل الحجوزات (العمال المخصصين للحجز)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `booking_workers` (
                        `id` INT AUTO_INCREMENT PRIMARY KEY,
                        `booking_id` INT NOT NULL,
                        `worker_id` INT NOT NULL,
                        `status` VARCHAR(20) NOT NULL DEFAULT 'assigned',
                        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (`booking_id`) REFERENCES `bookings`(`id`) ON DELETE CASCADE,
                        FOREIGN KEY (`worker_id`) REFERENCES `workers`(`id`) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                """)

                # إغلاق الاتصال
                cursor.close()
                connection.close()

                self.stdout.write(self.style.SUCCESS(f"تم إنشاء هيكل قاعدة البيانات {db_name} بنجاح"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء إنشاء هيكل قاعدة البيانات: {str(e)}"))
            raise
