# فهرس الملفات المهمة في نظام إدارة العمالة

هذا الفهرس يساعد في الوصول السريع إلى الملفات المهمة في المشروع لتسهيل عمليات الصيانة والتطوير.

## ملفات التكوين الرئيسية

| الملف | الوصف | المسار |
|-------|-------|--------|
| إعدادات Django | إعدادات المشروع الرئيسية | `labor_management/settings.py` |
| مسارات URL | تكوين مسارات URL الرئيسية | `labor_management/urls.py` |
| تكوين قواعد البيانات | تكوين قواعد بيانات الشركات | `config/database_custom.json` |
| تكوين Tailwind CSS | تكوين CSS الرئيسي | `tailwind.config.js` |
| تكوين المتصفح | إعدادات التطبيق للمتصفح | `frontend/public/manifest.json` |

## نصوص التشغيل

| الملف | الوصف | المسار |
|-------|-------|--------|
| تشغيل الخادم | تشغيل خادم Django | `start_server.bat` |
| أداة الإدارة | أداة إدارة Django | `manage.py` |
| النسخ الاحتياطي المجدول | تنفيذ النسخ الاحتياطي المجدول | `super_admin/management/commands/run_scheduled_backups.py` |
| إنشاء الإعدادات الأولية | إنشاء إعدادات النظام الأولية | `system_settings/management/commands/create_initial_settings.py` |

## تطبيقات النظام

### المسؤول الأعلى (super_admin)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات المسؤول الأعلى | `super_admin/models.py` |
| وظائف العرض | وظائف عرض المسؤول الأعلى | `super_admin/views.py` |
| وظائف قاعدة البيانات | وظائف إدارة قاعدة البيانات | `super_admin/views_database.py` |
| أدوات قاعدة البيانات | أدوات إدارة قاعدة البيانات | `super_admin/database_utils.py` |
| أدوات النسخ الاحتياطي | أدوات النسخ الاحتياطي | `super_admin/backup_utils.py` |
| مهيئ قاعدة البيانات | تهيئة قاعدة البيانات | `super_admin/database_initializer.py` |

### الحسابات (accounts)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات الحسابات | `accounts/models.py` |
| وظائف العرض | وظائف عرض الحسابات | `accounts/views.py` |
| تكوين قاعدة البيانات | تكوين قاعدة البيانات | `accounts/database_config.py` |
| موجه قاعدة البيانات | توجيه قاعدة البيانات | `accounts/router.py` |

### لوحة التحكم (dashboard)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات لوحة التحكم | `dashboard/models.py` |
| وظائف العرض | وظائف عرض لوحة التحكم | `dashboard/views.py` |
| مسارات URL | مسارات URL لوحة التحكم | `dashboard/urls.py` |

### العمال (workers)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات العمال | `workers/models.py` |
| وظائف العرض | وظائف عرض العمال | `workers/views.py` |
| مسارات URL | مسارات URL العمال | `workers/urls.py` |
| المهيئات | تهيئة بيانات العمال | `workers/initializers.py` |

### العقود (contracts)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات العقود | `contracts/models.py` |
| وظائف العرض | وظائف عرض العقود | `contracts/views.py` |
| مسارات URL | مسارات URL العقود | `contracts/urls.py` |

### الخدمات (services)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات الخدمات | `services/models.py` |
| وظائف العرض | وظائف عرض الخدمات | `services/views.py` |
| مسارات URL | مسارات URL الخدمات | `services/urls.py` |

### المستندات (documents)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات المستندات | `documents/models.py` |
| وظائف العرض | وظائف عرض المستندات | `documents/views.py` |
| مسارات URL | مسارات URL المستندات | `documents/urls.py` |

### الإجراءات (procedures)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات الإجراءات | `procedures/models.py` |
| وظائف العرض | وظائف عرض الإجراءات | `procedures/views.py` |
| مسارات URL | مسارات URL الإجراءات | `procedures/urls.py` |

### الماسح الضوئي (scanner)

| الملف | الوصف | المسار |
|-------|-------|--------|
| وظائف العرض | وظائف عرض الماسح الضوئي | `scanner/views.py` |
| مسارات URL | مسارات URL الماسح الضوئي | `scanner/urls.py` |
| معالجة جوازات السفر | معالجة جوازات السفر | `scanner/passport_ocr.py` |
| معالجة الصور | معالجة الصور على غرار CamScanner | `scanner/camscanner_like.py` |

### التقارير (reports)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات التقارير | `reports/models.py` |
| وظائف العرض | وظائف عرض التقارير | `reports/views.py` |
| مسارات URL | مسارات URL التقارير | `reports/urls.py` |

### إعدادات النظام (system_settings)

| الملف | الوصف | المسار |
|-------|-------|--------|
| النماذج | نماذج بيانات الإعدادات | `system_settings/models.py` |
| وظائف العرض | وظائف عرض الإعدادات | `system_settings/views.py` |
| مسارات URL | مسارات URL الإعدادات | `system_settings/urls.py` |
| النماذج | نماذج إدخال البيانات | `system_settings/forms.py` |

## واجهة برمجة التطبيقات (API)

| الملف | الوصف | المسار |
|-------|-------|--------|
| مسارات URL الرئيسية | مسارات URL الرئيسية للـ API | `api/urls.py` |
| مسارات URL البسيطة | مسارات URL البسيطة للـ API | `api/simple_urls.py` |
| مسارات URL العقود | مسارات URL العقود للـ API | `api/contracts/urls.py` |
| مسارات URL المستندات | مسارات URL المستندات للـ API | `api/documents/urls.py` |
| مسارات URL الإجراءات | مسارات URL الإجراءات للـ API | `api/procedures/urls.py` |
| مسارات URL الخدمات | مسارات URL الخدمات للـ API | `api/services/urls.py` |
| مسارات URL الإعدادات | مسارات URL الإعدادات للـ API | `api/settings/urls.py` |

## واجهة المستخدم الرئيسية (frontend)

| الملف | الوصف | المسار |
|-------|-------|--------|
| المكون الرئيسي | المكون الرئيسي للتطبيق | `frontend/src/App.jsx` |
| المسارات | تكوين مسارات التطبيق | `frontend/src/routes.jsx` |
| نقطة الدخول | نقطة دخول التطبيق | `frontend/src/index.jsx` |
| صفحة تسجيل الدخول | صفحة تسجيل الدخول | `frontend/src/pages/Login.jsx` |
| لوحة التحكم | صفحة لوحة التحكم | `frontend/src/pages/Dashboard.jsx` |
| سياق المصادقة | سياق المصادقة | `frontend/src/context/AuthContext.jsx` |
| خدمة API | خدمة الاتصال بالـ API | `frontend/src/services/api.js` |

## محرر القوالب (react-template-editor)

| الملف | الوصف | المسار |
|-------|-------|--------|
| صفحة المحرر | صفحة محرر القوالب | `react-template-editor/src/pages/EditorPage.jsx` |
| صفحة الإعدادات | صفحة إعدادات المحرر | `react-template-editor/src/pages/SettingsPage.jsx` |
| مخزن المحرر | مخزن حالة المحرر | `react-template-editor/src/store/editorStore.js` |
| مخزن المتغيرات | مخزن حالة المتغيرات | `react-template-editor/src/store/variablesStore.js` |
| مكون اللوحة | مكون لوحة المحرر | `react-template-editor/src/components/editor/Canvas.jsx` |
| مكون الشريط الجانبي | مكون الشريط الجانبي | `react-template-editor/src/components/editor/Sidebar.jsx` |

## الملفات الثابتة

| الملف | الوصف | المسار |
|-------|-------|--------|
| ماسح MRZ | ماسح منطقة القراءة الآلية | `static/js/mrz-scanner.js` |
| إصلاح التواريخ | إصلاح حقول التاريخ | `static/js/date-field-fix.js` |
| إصلاح الخلفيات | إصلاح خلفيات الصفحات | `static/js/fix_backgrounds.js` |
| تحسين رسائل الخطأ | تحسين عرض رسائل الخطأ | `static/js/improved-error-messages.js` |
| إزالة شريط الأدوات المكرر | إزالة شريط الأدوات المكرر | `static/js/remove-duplicate-toolbar.js` |
| تكوين Dynamic Web TWAIN | تكوين مكتبة المسح الضوئي | `static/dwt-resources/dynamsoft.webtwain.config.js` |

## القوالب

| الملف | الوصف | المسار |
|-------|-------|--------|
| القالب الرئيسي | القالب الرئيسي للتطبيق | `templates/layout.html` |
| قالب تسجيل الدخول | قالب صفحة تسجيل الدخول | `templates/accounts/login.html` |
| قالب لوحة التحكم | قالب صفحة لوحة التحكم | `templates/dashboard/dashboard.html` |
| محرر العقود البسيط | محرر العقود البسيط | `templates/contracts/simple_editor_direct.html` |
| القالب الموحد | القالب الموحد للتصميم الجديد | `templates/unified_layout.html` |
