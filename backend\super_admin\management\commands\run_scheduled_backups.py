from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth.models import User
import os
import json
from super_admin.models import BackupSchedule, Company, SystemLog

class Command(BaseCommand):
    help = 'تنفيذ النسخ الاحتياطي المجدول للشركات'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء تنفيذ النسخ الاحتياطي المجدول...'))

        # الحصول على جميع جدولات النسخ الاحتياطي المفعلة
        now = timezone.now()
        schedules = BackupSchedule.objects.filter(
            is_active=True,
            next_backup__lte=now
        )

        self.stdout.write(f'تم العثور على {schedules.count()} جدولة نسخ احتياطي للتنفيذ')

        # تنفيذ النسخ الاحتياطي لكل جدولة
        for schedule in schedules:
            try:
                company = schedule.company
                self.stdout.write(f'جاري إنشاء نسخة احتياطية للشركة: {company.name}')

                # إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
                backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                # إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت
                timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
                backup_filename = f'{company.database_name}_{timestamp}.sql'
                backup_file = os.path.join(backup_dir, backup_filename)

                # إنشاء ملف JSON للمعلومات الإضافية
                info_filename = f'{company.database_name}_{timestamp}_info.json'
                info_file = os.path.join(backup_dir, info_filename)

                # تنفيذ أمر النسخ الاحتياطي حسب نوع قاعدة البيانات
                if company.database_engine == 'mysql':
                    backup_command = f"mysqldump -u {company.database_user} -p{company.database_password} -h {company.database_host} {company.database_name} > {backup_file}"
                    os.system(backup_command)
                elif company.database_engine == 'postgresql':
                    backup_command = f"PGPASSWORD={company.database_password} pg_dump -U {company.database_user} -h {company.database_host} -d {company.database_name} -f {backup_file}"
                    os.system(backup_command)
                elif company.database_engine == 'sqlite3':
                    # للـ SQLite، نسخ الملف مباشرة
                    import shutil
                    from django.conf import settings
                    db_path = os.path.join(settings.BASE_DIR, 'company_dbs', f"{company.database_name}.sqlite3")
                    shutil.copy2(db_path, backup_file)
                else:
                    # إذا كان نوع قاعدة البيانات غير معروف، إنشاء ملف فارغ مع معلومات
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(f'-- نسخة احتياطية تلقائية لقاعدة بيانات {company.database_name}\n')
                        f.write(f'-- تاريخ النسخ: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                        f.write(f'-- الشركة: {company.name}\n\n')
                        f.write('-- لم يتم تنفيذ النسخ الاحتياطي: نوع قاعدة البيانات غير مدعوم\n')

                # الحصول على مستخدم النظام (المسؤول الأعلى)
                system_user = User.objects.filter(is_superuser=True).first()

                # حفظ معلومات إضافية في ملف JSON
                backup_info = {
                    'company_id': company.id,
                    'company_name': company.name,
                    'database_name': company.database_name,
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'created_by': system_user.username if system_user else 'النظام',
                    'file_size': os.path.getsize(backup_file),
                    'backup_type': 'auto',
                    'description': f'نسخة احتياطية تلقائية ({schedule.get_frequency_display()})',
                    'sql_file': backup_filename
                }

                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, ensure_ascii=False, indent=4)

                # تحديث تاريخ آخر نسخة احتياطية
                schedule.last_backup = now

                # حساب موعد النسخة الاحتياطية التالية
                schedule.next_backup = schedule.calculate_next_backup()
                schedule.save()

                # تسجيل العملية
                if system_user:
                    SystemLog.objects.create(
                        user=system_user,
                        action='backup',
                        company=company,
                        description=f'تم إنشاء نسخة احتياطية تلقائية لقاعدة بيانات {company.database_name}: {backup_filename}',
                        ip_address='127.0.0.1'
                    )

                # حذف النسخ الاحتياطية القديمة
                self.cleanup_old_backups(company, schedule.retention_period)

                self.stdout.write(self.style.SUCCESS(f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}'))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية للشركة {schedule.company.name}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS('تم الانتهاء من تنفيذ النسخ الاحتياطي المجدول'))

    def cleanup_old_backups(self, company, retention_days):
        """حذف النسخ الاحتياطية القديمة بناءً على فترة الاحتفاظ"""
        backup_dir = os.path.join(os.getcwd(), 'backups', str(company.id))
        if not os.path.exists(backup_dir):
            return

        # حساب تاريخ الحد الأدنى للاحتفاظ
        min_date = timezone.now() - timezone.timedelta(days=retention_days)
        min_date_str = min_date.strftime('%Y-%m-%d')

        # الحصول على قائمة ملفات المعلومات
        info_files = [f for f in os.listdir(backup_dir) if f.endswith('_info.json')]

        for info_file in info_files:
            try:
                with open(os.path.join(backup_dir, info_file), 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)

                # التحقق من تاريخ الإنشاء
                created_at = backup_info.get('created_at', '')
                if created_at and created_at.split(' ')[0] < min_date_str:
                    # حذف ملف SQL
                    sql_file = backup_info.get('sql_file', '')
                    if sql_file and os.path.exists(os.path.join(backup_dir, sql_file)):
                        os.remove(os.path.join(backup_dir, sql_file))

                    # حذف ملف المعلومات
                    os.remove(os.path.join(backup_dir, info_file))

                    self.stdout.write(f'تم حذف النسخة الاحتياطية القديمة: {sql_file}')

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'حدث خطأ أثناء حذف النسخة الاحتياطية القديمة: {str(e)}'))
