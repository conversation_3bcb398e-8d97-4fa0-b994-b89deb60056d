@extends('layouts.master_admin')

@section('title', 'إدارة قواعد البيانات | المسؤول الأعلى')

@section('page_title', 'إدارة قواعد البيانات')
@section('page_subtitle', 'عرض وإدارة قواعد بيانات الشركات')

@section('extra_css')
<style>
    .db-card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all duration-300;
    }
    
    .db-card:hover {
        @apply shadow-lg transform -translate-y-1;
    }
    
    .db-header {
        @apply p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-admin-primary to-admin-secondary text-white;
    }
    
    .db-body {
        @apply p-4;
    }
    
    .db-footer {
        @apply p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600;
    }
    
    .db-stats {
        @apply grid grid-cols-2 gap-4 mb-4;
    }
    
    .db-stat {
        @apply text-center p-3 bg-admin-light dark:bg-gray-700 rounded-lg;
    }
    
    .db-stat-value {
        @apply font-medium;
    }
    
    .db-stat-label {
        @apply text-xs text-gray-500 dark:text-gray-400;
    }
    
    .db-type-badge {
        @apply px-3 py-1 rounded-full text-xs font-medium;
    }
    
    .db-type-mysql {
        @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
    }
    
    .db-type-sqlite {
        @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
    }
    
    .db-connection-status {
        @apply flex items-center text-xs;
    }
    
    .connection-success {
        @apply text-green-600 dark:text-green-400;
    }
    
    .connection-error {
        @apply text-red-600 dark:text-red-400;
    }
</style>
@endsection

@section('content')
<!-- Filters and Actions -->
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
    <!-- Search and Filters -->
    <div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
        <div class="relative">
            <input type="text" id="db-search" placeholder="بحث عن قاعدة بيانات..." class="pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 w-full">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>
        
        <select id="db-type-filter" class="rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2">
            <option value="all">جميع الأنواع</option>
            <option value="mysql">MySQL</option>
            <option value="sqlite">SQLite</option>
        </select>
    </div>
    
    <!-- Actions -->
    <div class="flex gap-3 w-full md:w-auto">
        <button id="test-all-connections-btn" class="bg-admin-primary hover:bg-admin-dark text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plug ml-2"></i>
            اختبار جميع الاتصالات
        </button>
        
        <button id="backup-all-btn" class="bg-admin-secondary hover:bg-admin-accent text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-save ml-2"></i>
            نسخ احتياطي للكل
        </button>
    </div>
</div>

<!-- Database Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @if(isset($databases) && count($databases) > 0)
        @foreach($databases as $db)
        <div class="db-card" data-db-name="{{ $db->database_name }}" data-db-type="{{ $db->type }}">
            <div class="db-header flex justify-between items-center">
                <h3 class="text-lg font-bold">{{ $db->database_name }}</h3>
                <span class="db-type-badge {{ $db->type == 'mysql' ? 'db-type-mysql' : 'db-type-sqlite' }}">
                    {{ $db->type == 'mysql' ? 'MySQL' : 'SQLite' }}
                </span>
            </div>
            
            <div class="db-body">
                <div class="db-connection-status mb-3 {{ $db->connection_status ? 'connection-success' : 'connection-error' }}">
                    <i class="fas {{ $db->connection_status ? 'fa-check-circle' : 'fa-times-circle' }} ml-2"></i>
                    <span>{{ $db->connection_status ? 'متصل' : 'غير متصل' }}</span>
                </div>
                
                <div class="db-stats">
                    <div class="db-stat">
                        <div class="db-stat-label">عدد الجداول</div>
                        <div class="db-stat-value">{{ $db->tables_count }}</div>
                    </div>
                    <div class="db-stat">
                        <div class="db-stat-label">حجم قاعدة البيانات</div>
                        <div class="db-stat-value">{{ $db->size }}</div>
                    </div>
                </div>
                
                <div class="flex items-center text-sm mb-2">
                    <i class="fas fa-server text-admin-accent ml-2"></i>
                    <span>{{ $db->host }}</span>
                </div>
                
                <div class="flex items-center text-sm">
                    <i class="fas fa-building text-admin-accent ml-2"></i>
                    <span>{{ $db->company_name }}</span>
                </div>
            </div>
            
            <div class="db-footer flex justify-between">
                <button class="text-admin-primary dark:text-admin-accent hover:underline test-connection-btn" data-id="{{ $db->id }}">
                    <i class="fas fa-plug ml-1"></i>
                    اختبار الاتصال
                </button>
                
                <a href="{{ route('super_admin.database.structure', $db->id) }}" class="text-admin-secondary dark:text-admin-accent hover:underline">
                    <i class="fas fa-table ml-1"></i>
                    عرض الهيكل
                </a>
                
                <button class="text-green-600 dark:text-green-400 hover:underline backup-db-btn" data-id="{{ $db->id }}">
                    <i class="fas fa-save ml-1"></i>
                    نسخ احتياطي
                </button>
            </div>
        </div>
        @endforeach
    @else
        <div class="col-span-3 text-center py-12">
            <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
                <i class="fas fa-database"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-500 dark:text-gray-400 mb-2">لا توجد قواعد بيانات</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">لم يتم العثور على أي قواعد بيانات في النظام</p>
            <a href="{{ route('super_admin.companies.create') }}" class="bg-admin-primary hover:bg-admin-dark text-white px-6 py-2 rounded-lg inline-flex items-center">
                <i class="fas fa-plus ml-2"></i>
                إنشاء شركة جديدة
            </a>
        </div>
    @endif
</div>

<!-- System Database Card -->
<div class="mt-8">
    <h3 class="text-xl font-bold text-admin-primary dark:text-admin-accent mb-4">قاعدة بيانات النظام</h3>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 bg-gradient-to-r from-admin-dark to-admin-primary text-white">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-bold">قاعدة بيانات المسؤول الأعلى</h3>
                <span class="db-type-badge db-type-sqlite">SQLite</span>
            </div>
        </div>
        
        <div class="p-4">
            <div class="db-connection-status mb-3 connection-success">
                <i class="fas fa-check-circle ml-2"></i>
                <span>متصل</span>
            </div>
            
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="db-stat">
                    <div class="db-stat-label">عدد الجداول</div>
                    <div class="db-stat-value">{{ $system_db->tables_count ?? 0 }}</div>
                </div>
                <div class="db-stat">
                    <div class="db-stat-label">حجم قاعدة البيانات</div>
                    <div class="db-stat-value">{{ $system_db->size ?? '0 MB' }}</div>
                </div>
                <div class="db-stat">
                    <div class="db-stat-label">عدد الشركات</div>
                    <div class="db-stat-value">{{ $system_db->companies_count ?? 0 }}</div>
                </div>
            </div>
            
            <div class="flex items-center text-sm mb-2">
                <i class="fas fa-calendar-alt text-admin-accent ml-2"></i>
                <span>آخر تحديث: {{ $system_db->last_update ?? 'غير معروف' }}</span>
            </div>
            
            <div class="flex items-center text-sm">
                <i class="fas fa-save text-admin-accent ml-2"></i>
                <span>آخر نسخة احتياطية: {{ $system_db->last_backup ?? 'لا يوجد' }}</span>
            </div>
        </div>
        
        <div class="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-between">
            <button id="optimize-system-db-btn" class="text-admin-primary dark:text-admin-accent hover:underline">
                <i class="fas fa-wrench ml-1"></i>
                تحسين قاعدة البيانات
            </button>
            
            <button id="backup-system-db-btn" class="text-green-600 dark:text-green-400 hover:underline">
                <i class="fas fa-save ml-1"></i>
                نسخ احتياطي
            </button>
        </div>
    </div>
</div>
@endsection

@section('extra_js')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('db-search');
        searchInput.addEventListener('input', function() {
            filterDatabases();
        });
        
        // Database type filter
        const dbTypeFilter = document.getElementById('db-type-filter');
        dbTypeFilter.addEventListener('change', function() {
            filterDatabases();
        });
        
        // Test connection buttons
        const testConnectionButtons = document.querySelectorAll('.test-connection-btn');
        testConnectionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const dbId = this.dataset.id;
                const dbCard = this.closest('.db-card');
                const statusElement = dbCard.querySelector('.db-connection-status');
                
                // Show loading state
                statusElement.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i><span>جاري الاختبار...</span>';
                statusElement.className = 'db-connection-status mb-3';
                
                // Send test connection request
                fetch(`/super_admin/database/${dbId}/test-connection`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusElement.innerHTML = '<i class="fas fa-check-circle ml-2"></i><span>متصل</span>';
                        statusElement.className = 'db-connection-status mb-3 connection-success';
                        showToast('تم الاتصال بقاعدة البيانات بنجاح', 'success');
                    } else {
                        statusElement.innerHTML = '<i class="fas fa-times-circle ml-2"></i><span>غير متصل</span>';
                        statusElement.className = 'db-connection-status mb-3 connection-error';
                        showToast(data.message || 'فشل الاتصال بقاعدة البيانات', 'error');
                    }
                })
                .catch(error => {
                    statusElement.innerHTML = '<i class="fas fa-times-circle ml-2"></i><span>غير متصل</span>';
                    statusElement.className = 'db-connection-status mb-3 connection-error';
                    showToast('حدث خطأ أثناء اختبار الاتصال', 'error');
                    console.error(error);
                });
            });
        });
        
        // Backup database buttons
        const backupButtons = document.querySelectorAll('.backup-db-btn');
        backupButtons.forEach(button => {
            button.addEventListener('click', function() {
                const dbId = this.dataset.id;
                const dbCard = this.closest('.db-card');
                const dbName = dbCard.dataset.dbName;
                
                showToast(`جاري إنشاء نسخة احتياطية لقاعدة البيانات "${dbName}"...`, 'info');
                
                // Send backup request
                fetch(`/super_admin/database/${dbId}/backup`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`تم إنشاء نسخة احتياطية لقاعدة البيانات "${dbName}" بنجاح`, 'success');
                    } else {
                        showToast(data.message || 'فشل إنشاء نسخة احتياطية', 'error');
                    }
                })
                .catch(error => {
                    showToast('حدث خطأ أثناء إنشاء نسخة احتياطية', 'error');
                    console.error(error);
                });
            });
        });
        
        // Test all connections button
        document.getElementById('test-all-connections-btn').addEventListener('click', function() {
            showToast('جاري اختبار جميع الاتصالات...', 'info');
            
            // Implement test all connections functionality
            const testButtons = document.querySelectorAll('.test-connection-btn');
            let index = 0;
            
            function testNext() {
                if (index < testButtons.length) {
                    testButtons[index].click();
                    index++;
                    setTimeout(testNext, 1000);
                } else {
                    showToast('تم اختبار جميع الاتصالات', 'success');
                }
            }
            
            testNext();
        });
        
        // Backup all button
        document.getElementById('backup-all-btn').addEventListener('click', function() {
            showToast('جاري إنشاء نسخة احتياطية لجميع قواعد البيانات...', 'info');
            
            // Implement backup all functionality
            fetch('/super_admin/database/backup-all', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم إنشاء نسخة احتياطية لجميع قواعد البيانات بنجاح', 'success');
                } else {
                    showToast(data.message || 'فشل إنشاء نسخة احتياطية لبعض قواعد البيانات', 'warning');
                }
            })
            .catch(error => {
                showToast('حدث خطأ أثناء إنشاء نسخة احتياطية', 'error');
                console.error(error);
            });
        });
        
        // System database buttons
        document.getElementById('optimize-system-db-btn').addEventListener('click', function() {
            showToast('جاري تحسين قاعدة بيانات النظام...', 'info');
            
            // Implement optimize system database functionality
            fetch('/super_admin/database/system/optimize', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم تحسين قاعدة بيانات النظام بنجاح', 'success');
                } else {
                    showToast(data.message || 'فشل تحسين قاعدة بيانات النظام', 'error');
                }
            })
            .catch(error => {
                showToast('حدث خطأ أثناء تحسين قاعدة بيانات النظام', 'error');
                console.error(error);
            });
        });
        
        document.getElementById('backup-system-db-btn').addEventListener('click', function() {
            showToast('جاري إنشاء نسخة احتياطية لقاعدة بيانات النظام...', 'info');
            
            // Implement backup system database functionality
            fetch('/super_admin/database/system/backup', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم إنشاء نسخة احتياطية لقاعدة بيانات النظام بنجاح', 'success');
                } else {
                    showToast(data.message || 'فشل إنشاء نسخة احتياطية لقاعدة بيانات النظام', 'error');
                }
            })
            .catch(error => {
                showToast('حدث خطأ أثناء إنشاء نسخة احتياطية لقاعدة بيانات النظام', 'error');
                console.error(error);
            });
        });
        
        // Filter databases function
        function filterDatabases() {
            const searchTerm = searchInput.value.toLowerCase();
            const dbType = dbTypeFilter.value;
            
            const dbCards = document.querySelectorAll('.db-card');
            
            dbCards.forEach(card => {
                const dbName = card.dataset.dbName.toLowerCase();
                const dbTypeValue = card.dataset.dbType;
                
                const matchesSearch = dbName.includes(searchTerm);
                const matchesType = dbType === 'all' || dbType === dbTypeValue;
                
                if (matchesSearch && matchesType) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    });
</script>
@endsection
