/**
 * نظام تسجيل الدخول المحسن للشركات
 * يتضمن التحقق من الرقم التسلسلي وتسجيل الدخول الآمن
 */

// متغيرات عامة
let isSerialVerified = false;
let currentCompany = null;

// رسائل النظام
const messages = {
    serialRequired: 'يرجى إدخال الرقم التسلسلي',
    serialInvalid: 'الرقم التسلسلي يجب أن يتكون من 16 حرف',
    serialNotFound: 'الرقم التسلسلي غير مسجل في النظام',
    companyInactive: 'الشركة غير نشطة حالياً',
    databaseError: 'خطأ في الاتصال بقاعدة البيانات',
    verifying: 'جاري التحقق...',
    verified: 'تم التحقق بنجاح',
    loginRequired: 'يجب التحقق من الرقم التسلسلي أولاً',
    credentialsRequired: 'يرجى إدخال اسم المستخدم وكلمة المرور',
    loginFailed: 'اسم المستخدم أو كلمة المرور غير صحيحة',
    systemError: 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى'
};

// عناصر DOM
const elements = {
    serialInput: null,
    verifyBtn: null,
    usernameInput: null,
    passwordInput: null,
    loginBtn: null,
    dbVerifiedInput: null,
    companyNameDisplay: null,
    loginForm: null
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    setupFormValidation();
});

// تهيئة عناصر DOM
function initializeElements() {
    elements.serialInput = document.getElementById('serial_number');
    elements.verifyBtn = document.getElementById('verify-btn');
    elements.usernameInput = document.getElementById('username');
    elements.passwordInput = document.getElementById('password');
    elements.loginBtn = document.getElementById('login-btn');
    elements.dbVerifiedInput = document.getElementById('db_verified');
    elements.companyNameDisplay = document.getElementById('company-name');
    elements.loginForm = document.getElementById('login-form');

    // التحقق من وجود العناصر المطلوبة
    if (!elements.serialInput || !elements.verifyBtn || !elements.loginBtn) {
        console.error('عناصر مطلوبة مفقودة في الصفحة');
        return;
    }

    console.log('تم تهيئة عناصر DOM بنجاح');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // التحقق من الرقم التسلسلي
    if (elements.verifyBtn) {
        elements.verifyBtn.addEventListener('click', handleSerialVerification);
    }

    // تنسيق الرقم التسلسلي أثناء الكتابة
    if (elements.serialInput) {
        elements.serialInput.addEventListener('input', formatSerialNumber);
        elements.serialInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleSerialVerification();
            }
        });
    }

    // تسجيل الدخول
    if (elements.loginForm) {
        elements.loginForm.addEventListener('submit', handleLogin);
    }

    // تفعيل زر تسجيل الدخول عند الضغط على Enter
    if (elements.passwordInput) {
        elements.passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleLogin(e);
            }
        });
    }
}

// تنسيق الرقم التسلسلي (إضافة الشرطات)
function formatSerialNumber(e) {
    let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();

    if (value.length > 16) {
        value = value.substring(0, 16);
    }

    // إضافة الشرطات كل 4 أحرف
    const formatted = value.replace(/(.{4})/g, '$1-').replace(/-$/, '');
    e.target.value = formatted;

    // إعادة تعيين حالة التحقق إذا تم تغيير الرقم
    if (isSerialVerified) {
        resetVerificationState();
    }
}

// التحقق من الرقم التسلسلي
async function handleSerialVerification() {
    const serialNumber = elements.serialInput.value.trim();

    if (!serialNumber) {
        showError(messages.serialRequired);
        return;
    }

    const cleanSerial = serialNumber.replace(/[^A-Za-z0-9]/g, '');
    if (cleanSerial.length !== 16) {
        showError(messages.serialInvalid);
        return;
    }

    setVerifyButtonState(true, messages.verifying);

    try {
        const response = await fetch('/accounts/api/check-serial-number/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                serial_number: serialNumber
            })
        });

        const data = await response.json();

        if (data.success) {
            handleVerificationSuccess(data);
        } else {
            handleVerificationError(data.message || messages.serialNotFound);
        }
    } catch (error) {
        console.error('خطأ في التحقق من الرقم التسلسلي:', error);
        handleVerificationError(messages.databaseError);
    }
}

// معالجة نجاح التحقق
function handleVerificationSuccess(data) {
    isSerialVerified = true;
    currentCompany = data.company;

    // تحديث واجهة المستخدم
    setVerifyButtonState(false, messages.verified, true);
    elements.dbVerifiedInput.value = 'true';

    if (elements.companyNameDisplay && data.company) {
        const span = elements.companyNameDisplay.querySelector('span');
        if (span) {
            span.textContent = data.company.name;
        }
        elements.companyNameDisplay.classList.remove('hidden');
    }

    // تفعيل حقول تسجيل الدخول
    enableLoginFields();
    showSuccess(`تم التحقق من شركة ${data.company?.name || 'الشركة'} بنجاح`);

    // التركيز على حقل اسم المستخدم
    if (elements.usernameInput) {
        elements.usernameInput.focus();
    }
}

// معالجة خطأ التحقق
function handleVerificationError(message) {
    isSerialVerified = false;
    currentCompany = null;

    setVerifyButtonError(message);
    elements.dbVerifiedInput.value = 'false';

    disableLoginFields();
    showError(message);
}

// إعادة تعيين حالة التحقق
function resetVerificationState() {
    isSerialVerified = false;
    currentCompany = null;
    elements.dbVerifiedInput.value = 'false';
    setVerifyButtonState(false, 'تحقق');
    disableLoginFields();

    if (elements.companyNameDisplay) {
        elements.companyNameDisplay.classList.add('hidden');
    }
}

// تعيين حالة زر التحقق
function setVerifyButtonState(loading, text, success = false) {
    if (!elements.verifyBtn) return;

    const icon = elements.verifyBtn.querySelector('.verify-icon i');
    const textElement = elements.verifyBtn.querySelector('.verify-text');
    const spinner = elements.verifyBtn.querySelector('.verify-spinner');

    // تحديث النص
    if (textElement) {
        textElement.textContent = text;
    } else {
        elements.verifyBtn.textContent = text;
    }

    // إدارة حالة التحميل
    if (loading) {
        elements.verifyBtn.classList.add('loading');
        elements.verifyBtn.disabled = true;

        if (icon) icon.style.display = 'none';
        if (spinner) spinner.classList.remove('hidden');
        if (textElement) textElement.textContent = 'جاري التحقق...';
    } else {
        elements.verifyBtn.classList.remove('loading');
        elements.verifyBtn.disabled = success; // تعطيل فقط عند النجاح

        if (spinner) spinner.classList.add('hidden');
        if (icon) icon.style.display = 'inline';
    }

    // إدارة حالة النجاح
    if (success) {
        elements.verifyBtn.classList.add('success');
        elements.verifyBtn.classList.remove('error');

        if (icon) {
            icon.className = 'fas fa-check-circle text-lg';
        }
        if (textElement) {
            textElement.textContent = 'تم التحقق';
        }

        // إزالة حالة النجاح بعد 3 ثوان
        setTimeout(() => {
            elements.verifyBtn.classList.remove('success');
        }, 3000);
    } else if (!loading) {
        elements.verifyBtn.classList.remove('success', 'error');

        if (icon) {
            icon.className = 'fas fa-shield-alt text-lg';
        }
    }
}

// إضافة حالة خطأ للزر
function setVerifyButtonError(message) {
    if (!elements.verifyBtn) return;

    const icon = elements.verifyBtn.querySelector('.verify-icon i');
    const textElement = elements.verifyBtn.querySelector('.verify-text');
    const spinner = elements.verifyBtn.querySelector('.verify-spinner');

    elements.verifyBtn.classList.remove('loading', 'success');
    elements.verifyBtn.classList.add('error');
    elements.verifyBtn.disabled = false;

    if (spinner) spinner.classList.add('hidden');
    if (icon) {
        icon.style.display = 'inline';
        icon.className = 'fas fa-exclamation-triangle text-lg';
    }
    if (textElement) {
        textElement.textContent = 'خطأ';
    }

    // إزالة حالة الخطأ بعد 3 ثوان
    setTimeout(() => {
        elements.verifyBtn.classList.remove('error');
        if (icon) {
            icon.className = 'fas fa-shield-alt text-lg';
        }
        if (textElement) {
            textElement.textContent = 'تحقق';
        }
    }, 3000);
}

// تفعيل حقول تسجيل الدخول
function enableLoginFields() {
    if (elements.usernameInput) elements.usernameInput.disabled = false;
    if (elements.passwordInput) elements.passwordInput.disabled = false;
    if (elements.loginBtn) elements.loginBtn.disabled = false;
}

// تعطيل حقول تسجيل الدخول
function disableLoginFields() {
    if (elements.usernameInput) elements.usernameInput.disabled = true;
    if (elements.passwordInput) elements.passwordInput.disabled = true;
    if (elements.loginBtn) elements.loginBtn.disabled = true;
}

// تعيين حالة زر تسجيل الدخول
function setLoginButtonState(loading) {
    if (!elements.loginBtn) return;

    if (loading) {
        elements.loginBtn.disabled = true;
        elements.loginBtn.innerHTML = `
            <i class="fas fa-spinner fa-spin ml-2"></i>
            جاري تسجيل الدخول...
        `;
        elements.loginBtn.classList.add('opacity-75');
    } else {
        elements.loginBtn.disabled = false;
        elements.loginBtn.innerHTML = `
            <i class="fas fa-sign-in-alt ml-2"></i>
            تسجيل الدخول
        `;
        elements.loginBtn.classList.remove('opacity-75');
    }
}

// معالجة تسجيل الدخول
async function handleLogin(e) {
    e.preventDefault();

    if (!isSerialVerified) {
        showError(messages.loginRequired);
        return false;
    }

    const username = elements.usernameInput?.value.trim();
    const password = elements.passwordInput?.value;
    const serialNumber = elements.serialInput?.value.trim();

    if (!username || !password) {
        showError(messages.credentialsRequired);
        return false;
    }

    // تعطيل زر تسجيل الدخول وإظهار حالة التحميل
    setLoginButtonState(true);

    try {
        // استخدام API الجديد للتسجيل مع نظام Multi-tenant
        const response = await fetch('/accounts/api/company-login/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                serial_number: serialNumber,
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');

            // التوجيه إلى لوحة التحكم
            setTimeout(() => {
                window.location.href = data.redirect_url || '/dashboard/';
            }, 1500);
        } else {
            showError(data.message || messages.loginFailed);
            setLoginButtonState(false);
        }
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showError(messages.systemError);
        setLoginButtonState(false);
    }

    return false;
}

// إعداد التحقق من صحة النموذج
function setupFormValidation() {
    // التحقق من الحقول المطلوبة
    [elements.usernameInput, elements.passwordInput].forEach(input => {
        if (input) {
            input.addEventListener('blur', validateField);
            input.addEventListener('input', clearFieldError);
        }
    });
}

// التحقق من صحة الحقل
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();

    if (!value) {
        showFieldError(field, 'هذا الحقل مطلوب');
    }
}

// مسح خطأ الحقل
function clearFieldError(e) {
    const field = e.target;
    hideFieldError(field);
}

// عرض خطأ الحقل
function showFieldError(field, message) {
    field.classList.add('border-red-500');

    let errorElement = field.parentNode.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error text-red-500 text-sm mt-1';
        field.parentNode.appendChild(errorElement);
    }
    errorElement.textContent = message;
}

// إخفاء خطأ الحقل
function hideFieldError(field) {
    field.classList.remove('border-red-500');

    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

// عرض رسالة خطأ
function showError(message) {
    showMessage(message, 'error');
}

// عرض رسالة نجاح
function showSuccess(message) {
    showMessage(message, 'success');
}

// عرض رسالة عامة
function showMessage(message, type) {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert-message p-4 rounded-lg mb-4 ${
        type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
        'bg-green-100 border border-green-400 text-green-700'
    }`;
    alertDiv.textContent = message;

    // إدراج الرسالة في أعلى النموذج
    const form = elements.loginForm || document.querySelector('form');
    if (form) {
        form.insertBefore(alertDiv, form.firstChild);
    }

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// الحصول على CSRF Token
function getCSRFToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                  document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                  getCookie('csrftoken');
    return token;
}

// الحصول على Cookie
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// تصدير الوظائف للاستخدام الخارجي
window.CompanyLogin = {
    handleSerialVerification,
    handleLogin,
    resetVerificationState,
    isSerialVerified: () => isSerialVerified,
    currentCompany: () => currentCompany
};

console.log('تم تحميل نظام تسجيل الدخول للشركات بنجاح');
