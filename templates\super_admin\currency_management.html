{% extends 'layouts/super_admin.html' %}

{% block title %}إدارة العملات{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">
                    <i class="fas fa-coins ml-2"></i>
                    إدارة العملات
                </h1>
                <p class="text-yellow-100">
                    إدارة العملات المدعومة وإعدادات التنسيق
                </p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold">{{ stats.total_currencies }}</div>
                <div class="text-sm text-yellow-100">عملة مدعومة</div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="admin-card rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-white">{{ stats.active_currencies }}</div>
                    <div class="text-sm text-gray-300">عملات نشطة</div>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-star text-white text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-white">{{ stats.default_currency }}</div>
                    <div class="text-sm text-gray-300">العملة الافتراضية</div>
                </div>
            </div>
        </div>

        <div class="admin-card rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exchange-alt text-white text-xl"></i>
                </div>
                <div class="mr-4">
                    <div class="text-2xl font-bold text-white">
                        {% if system_settings.show_currency_converter %}نشط{% else %}معطل{% endif %}
                    </div>
                    <div class="text-sm text-gray-300">محول العملات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Currency Settings -->
    <div class="admin-card rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-white mb-6">
            <i class="fas fa-cog ml-2"></i>
            إعدادات العملة العامة
        </h2>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            <input type="hidden" name="action" value="update_currency_settings">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Default Currency -->
                <div>
                    <label for="default_currency" class="block text-sm font-medium text-white mb-2">
                        العملة الافتراضية
                    </label>
                    <select name="default_currency"
                            id="default_currency"
                            class="w-full px-3 py-2 border border-gray-500 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 text-gray-900 bg-white"
                            onchange="this.form.action='{% url 'super_admin:currency_management' %}'; this.form.elements['action'].value='update_default_currency'; this.form.submit();">
                        <option value="IQD" {% if system_settings.default_currency == 'IQD' %}selected{% endif %}>
                            الدينار العراقي (IQD)
                        </option>
                        <option value="USD" {% if system_settings.default_currency == 'USD' %}selected{% endif %}>
                            الدولار الأمريكي (USD)
                        </option>
                    </select>
                </div>

                <!-- Currency Converter -->
                <div class="flex items-center justify-between p-4 border border-gray-500 rounded-lg">
                    <div>
                        <h3 class="font-medium text-white">إظهار محول العملات</h3>
                        <p class="text-sm text-gray-300">عرض أداة تحويل العملات في الواجهات</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox"
                               name="show_currency_converter"
                               {% if system_settings.show_currency_converter %}checked{% endif %}
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                </div>
            </div>

            <!-- Auto Update Exchange Rates -->
            <div class="flex items-center justify-between p-4 border border-gray-500 rounded-lg">
                <div>
                    <h3 class="font-medium text-white">تحديث أسعار الصرف تلقائياً</h3>
                    <p class="text-sm text-gray-300">تحديث أسعار الصرف من مصادر خارجية (قيد التطوير)</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox"
                           name="auto_update_exchange_rates"
                           {% if system_settings.auto_update_exchange_rates %}checked{% endif %}
                           class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit"
                        class="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </form>
    </div>

    <!-- Currencies List -->
    <div class="admin-card rounded-xl shadow-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-700 border-b border-gray-600">
            <h2 class="text-lg font-semibold text-white">
                <i class="fas fa-list ml-2"></i>
                العملات المدعومة
            </h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-600">
                <thead class="bg-gradient-to-r from-yellow-600 to-orange-600">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            العملة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            الرمز
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            التنسيق
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            سعر الصرف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-600">
                    {% for currency in currencies %}
                    <tr class="hover:bg-gray-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-yellow-600 flex items-center justify-center">
                                        <i class="fas fa-coins text-white"></i>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-white">{{ currency.name }}</div>
                                    <div class="text-sm text-gray-300">{{ currency.code }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white font-mono">{{ currency.symbol }}</div>
                            <div class="text-sm text-gray-300">{{ currency.get_symbol_position_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">
                                <strong>مثال:</strong>
                                {% load currency_tags %}
                                <span class="text-yellow-400">{{ 1000|format_currency:currency.code }}</span>
                            </div>
                            <div class="text-sm text-gray-300">
                                خانات عشرية: {{ currency.decimal_places }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <form method="post" class="inline-flex items-center space-x-2">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="update_exchange_rate">
                                <input type="hidden" name="currency_id" value="{{ currency.id }}">
                                <input type="number"
                                       name="exchange_rate"
                                       value="{{ currency.exchange_rate_to_usd }}"
                                       step="0.0001"
                                       class="w-24 px-2 py-1 text-sm border border-gray-500 rounded focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 text-gray-900 bg-white">
                                <button type="submit"
                                        class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-save"></i>
                                </button>
                            </form>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if currency.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-600 text-white">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-600 text-white">
                                    <i class="fas fa-times-circle ml-1"></i>
                                    معطل
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <form method="post" class="inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="toggle_currency_status">
                                <input type="hidden" name="currency_id" value="{{ currency.id }}">
                                <button type="submit"
                                        onclick="return confirm('هل أنت متأكد من {% if currency.is_active %}تعطيل{% else %}تفعيل{% endif %} هذه العملة؟')"
                                        class="{% if currency.is_active %}text-red-400 hover:text-red-300{% else %}text-green-400 hover:text-green-300{% endif %}">
                                    <i class="fas fa-{% if currency.is_active %}pause{% else %}play{% endif %} ml-1"></i>
                                    {% if currency.is_active %}تعطيل{% else %}تفعيل{% endif %}
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Currency Examples -->
    <div class="admin-card rounded-xl shadow-lg p-6">
        <h2 class="text-lg font-semibold text-white mb-6">
            <i class="fas fa-eye ml-2"></i>
            أمثلة على تنسيق العملات
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for currency in currencies %}
            {% if currency.is_active %}
            <div class="border border-gray-500 rounded-lg p-4 bg-gray-700">
                <h3 class="font-medium text-white mb-3">{{ currency.name }}</h3>
                <div class="space-y-2 text-sm">
                    {% load currency_tags %}
                    <div class="flex justify-between">
                        <span class="text-gray-300">1,000:</span>
                        <span class="font-mono text-yellow-400">{{ 1000|format_currency:currency.code }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">1,500:</span>
                        <span class="font-mono text-yellow-400">{{ 1500|format_currency:currency.code }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">10,000:</span>
                        <span class="font-mono text-yellow-400">{{ 10000|format_currency:currency.code }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">100,000:</span>
                        <span class="font-mono text-yellow-400">{{ 100000|format_currency:currency.code }}</span>
                    </div>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>

    <!-- Under Development Notice -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-600 text-xl"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-lg font-medium text-blue-800">
                    معلومات مهمة
                </h3>
                <div class="text-blue-700 mt-1 space-y-2">
                    <p>• <strong>الدينار العراقي:</strong> يتم عرضه بدون خانات عشرية مع فاصل الآلاف</p>
                    <p>• <strong>الدولار الأمريكي:</strong> يتم عرضه بخانتين عشريتين</p>
                    <p>• يمكن استخدام "DUR" أو "دولار" كرمز للدولار الأمريكي</p>
                    <p>• أسعار الصرف يتم تحديثها يدوياً حالياً</p>
                    <p>• العملة الافتراضية تؤثر على عرض الأسعار في جميع أنحاء النظام</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
