{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}قائمة العملاء | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">قائمة العملاء</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                عرض وإدارة جميع العملاء في النظام
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'clients:dashboard' %}"
               class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                <i class="fas fa-th-large ml-2 text-sm"></i>
                <span>لوحة التحكم</span>
            </a>
            <div class="dropdown relative" x-data="{ open: false }">
                <button @click="open = !open" class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                    <i class="fas fa-plus ml-2 text-sm"></i>
                    <span>إضافة عميل</span>
                    <i class="fas fa-chevron-down mr-2 text-xs"></i>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg z-10">
                    <a href="{% url 'clients:client_create' %}?type=permanent" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-slate-700">
                        <i class="fas fa-user-tie ml-2 text-blue-500"></i>
                        عميل عقد دائم
                    </a>
                    <a href="{% url 'clients:client_create' %}?type=monthly" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-slate-700">
                        <i class="fas fa-user-tie ml-2 text-purple-500"></i>
                        عميل عقد شهري
                    </a>
                    <a href="{% url 'clients:client_create' %}?type=custom" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-slate-700">
                        <i class="fas fa-user-tie ml-2 text-orange-500"></i>
                        عميل خدمة مخصصة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6 mb-6 border border-gray-100 dark:border-slate-700">
        <div class="flex flex-col md:flex-row md:items-end md:justify-between space-y-4 md:space-y-0">
            <div class="flex flex-col md:flex-row md:space-x-4 md:space-x-reverse space-y-4 md:space-y-0">
                <!-- Search Input -->
                <div class="w-full md:w-64">
                    <label for="search-input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">بحث</label>
                    <div class="relative">
                        <input type="text" id="search-input" value="{{ search_query }}" placeholder="بحث بالاسم أو رقم الهاتف..."
                               class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md pl-10 pr-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Client Type Filter -->
                <div class="w-full md:w-48">
                    <label for="client-type-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع العميل</label>
                    <select id="client-type-filter" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in client_types %}
                        <option value="{{ value }}" {% if client_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div class="w-full md:w-48">
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                    <select id="status-filter" class="w-full border border-gray-300 dark:border-slate-600 dark:bg-slate-800 dark:text-white rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <!-- Filter Button -->
            <div>
                <button id="filter-button" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-300">
                    <i class="fas fa-filter ml-2"></i>
                    تصفية
                </button>
            </div>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
        {% if clients %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                <thead class="bg-gray-50 dark:bg-slate-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            اسم العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            نوع العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            رقم الهاتف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            العنوان
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            تاريخ الإضافة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                    {% for client in clients %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-slate-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ client.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if client.client_type == 'permanent' %}
                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                {% elif client.client_type == 'monthly' %}
                                    bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                {% else %}
                                    bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                {% endif %}">
                                {{ client.get_client_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-400">
                            {{ client.phone_1 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-400">
                            {{ client.address|truncatechars:30 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if client.status == 'active' %}
                                    bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                {% elif client.status == 'inactive' %}
                                    bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                {% else %}
                                    bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                {% endif %}">
                                {{ client.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-400">
                            {{ client.created_at|date:"Y-m-d" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                            <a href="{% url 'clients:client_detail' client.id %}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-3" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'clients:client_edit' client.id %}" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 ml-3" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'clients:client_delete' client.id %}" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="حذف" 
                               onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if clients.has_other_pages %}
        <div class="px-6 py-4 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500 dark:text-slate-400">
                    عرض {{ clients.start_index }} إلى {{ clients.end_index }} من {{ clients.paginator.count }} عميل
                </div>
                <div class="flex space-x-1 space-x-reverse">
                    {% if clients.has_previous %}
                    <a href="?page={{ clients.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if client_type %}&type={{ client_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}" 
                       class="px-3 py-1 bg-white dark:bg-slate-700 text-gray-700 dark:text-white border border-gray-300 dark:border-slate-600 rounded-md hover:bg-gray-100 dark:hover:bg-slate-600">
                        السابق
                    </a>
                    {% else %}
                    <span class="px-3 py-1 bg-gray-100 dark:bg-slate-800 text-gray-400 dark:text-slate-500 border border-gray-300 dark:border-slate-600 rounded-md cursor-not-allowed">
                        السابق
                    </span>
                    {% endif %}
                    
                    {% for i in clients.paginator.page_range %}
                        {% if clients.number == i %}
                        <span class="px-3 py-1 bg-blue-600 text-white border border-blue-600 rounded-md">
                            {{ i }}
                        </span>
                        {% elif i > clients.number|add:'-3' and i < clients.number|add:'3' %}
                        <a href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if client_type %}&type={{ client_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}" 
                           class="px-3 py-1 bg-white dark:bg-slate-700 text-gray-700 dark:text-white border border-gray-300 dark:border-slate-600 rounded-md hover:bg-gray-100 dark:hover:bg-slate-600">
                            {{ i }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if clients.has_next %}
                    <a href="?page={{ clients.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if client_type %}&type={{ client_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}" 
                       class="px-3 py-1 bg-white dark:bg-slate-700 text-gray-700 dark:text-white border border-gray-300 dark:border-slate-600 rounded-md hover:bg-gray-100 dark:hover:bg-slate-600">
                        التالي
                    </a>
                    {% else %}
                    <span class="px-3 py-1 bg-gray-100 dark:bg-slate-800 text-gray-400 dark:text-slate-500 border border-gray-300 dark:border-slate-600 rounded-md cursor-not-allowed">
                        التالي
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-8">
            <div class="text-gray-400 dark:text-slate-500 mb-2">
                <i class="fas fa-users text-5xl"></i>
            </div>
            <p class="text-gray-500 dark:text-slate-400">لا يوجد عملاء مطابقين لمعايير البحث</p>
            <p class="text-sm text-gray-400 dark:text-slate-500 mt-1">جرب تغيير معايير البحث أو إضافة عميل جديد</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-input');
        const clientTypeFilter = document.getElementById('client-type-filter');
        const statusFilter = document.getElementById('status-filter');
        const filterButton = document.getElementById('filter-button');

        // تطبيق التصفية عند النقر على زر التصفية
        filterButton.addEventListener('click', function() {
            applyFilters();
        });

        // تطبيق التصفية عند الضغط على Enter في حقل البحث
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });

        // دالة تطبيق التصفية
        function applyFilters() {
            const searchTerm = searchInput.value;
            const clientType = clientTypeFilter.value;
            const status = statusFilter.value;

            let url = window.location.pathname + '?';

            if (searchTerm) {
                url += `search=${encodeURIComponent(searchTerm)}&`;
            }

            if (clientType) {
                url += `type=${encodeURIComponent(clientType)}&`;
            }

            if (status) {
                url += `status=${encodeURIComponent(status)}&`;
            }

            // إزالة & النهائية إذا وجدت
            if (url.endsWith('&')) {
                url = url.slice(0, -1);
            }

            window.location.href = url;
        }
    });
</script>
{% endblock %}
