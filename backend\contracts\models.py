from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from backend.workers.models import Worker
from backend.clients.models import Client


class Contract(models.Model):
    """نموذج العقود"""

    CONTRACT_TYPE_CHOICES = [
        ('monthly', 'عقد شهري'),
        ('permanent', 'عقد دائم'),
        ('temporary', 'عقد مؤقت'),
        ('trial', 'عقد تجريبي'),
    ]

    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('active', 'نشط'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('expired', 'منتهي الصلاحية'),
    ]

    # معلومات أساسية
    contract_number = models.CharField(max_length=50, unique=True, verbose_name='رقم العقد')
    contract_type = models.Char<PERSON><PERSON>(max_length=20, choices=CONTRACT_TYPE_CHOICES, verbose_name='نوع العقد')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='الحالة')

    # الأطراف
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, verbose_name='العامل')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, verbose_name='العميل')

    # التواريخ
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')
    trial_period_days = models.IntegerField(default=0, verbose_name='فترة التجربة (أيام)')

    # المالية
    salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الراتب')
    currency = models.CharField(max_length=3, default='SAR', verbose_name='العملة')
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='نسبة العمولة (%)'
    )

    # شروط العقد
    working_hours_per_day = models.IntegerField(default=8, verbose_name='ساعات العمل اليومية')
    working_days_per_week = models.IntegerField(default=6, verbose_name='أيام العمل الأسبوعية')
    overtime_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=1.5,
        verbose_name='معدل الوقت الإضافي'
    )

    # الملاحظات والشروط
    terms_and_conditions = models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عقد'
        verbose_name_plural = 'العقود'
        ordering = ['-created_at']

    def __str__(self):
        return f"عقد {self.contract_number} - {self.worker.name} مع {self.client.name}"

    def get_contract_type_display_ar(self):
        """عرض نوع العقد بالعربية"""
        type_mapping = {
            'monthly': 'عقد شهري',
            'permanent': 'عقد دائم',
            'temporary': 'عقد مؤقت',
            'trial': 'عقد تجريبي'
        }
        return type_mapping.get(self.contract_type, self.contract_type)

    def get_status_display_ar(self):
        """عرض حالة العقد بالعربية"""
        status_mapping = {
            'draft': 'مسودة',
            'active': 'نشط',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'expired': 'منتهي الصلاحية'
        }
        return status_mapping.get(self.status, self.status)

    @property
    def is_active(self):
        """التحقق من كون العقد نشط"""
        return self.status == 'active'

    @property
    def is_expired(self):
        """التحقق من انتهاء صلاحية العقد"""
        if self.end_date:
            return timezone.now().date() > self.end_date
        return False

    @property
    def remaining_days(self):
        """الأيام المتبقية في العقد"""
        if self.end_date:
            remaining = (self.end_date - timezone.now().date()).days
            return max(0, remaining)
        return None

    @property
    def duration_days(self):
        """مدة العقد بالأيام"""
        if self.end_date:
            return (self.end_date - self.start_date).days
        return None


class ContractDocument(models.Model):
    """مستندات العقود"""

    DOCUMENT_TYPE_CHOICES = [
        ('contract_copy', 'نسخة العقد'),
        ('amendment', 'تعديل'),
        ('termination', 'إنهاء'),
        ('renewal', 'تجديد'),
        ('other', 'أخرى'),
    ]

    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name='العقد'
    )
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPE_CHOICES,
        verbose_name='نوع المستند'
    )
    title = models.CharField(max_length=255, verbose_name='عنوان المستند')
    file = models.FileField(
        upload_to='contracts/documents/%Y/%m/',
        verbose_name='الملف'
    )
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')

    class Meta:
        verbose_name = 'مستند عقد'
        verbose_name_plural = 'مستندات العقود'
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.contract.contract_number} - {self.title}"


class ContractPayment(models.Model):
    """مدفوعات العقود"""

    PAYMENT_TYPE_CHOICES = [
        ('salary', 'راتب'),
        ('commission', 'عمولة'),
        ('bonus', 'مكافأة'),
        ('overtime', 'وقت إضافي'),
        ('deduction', 'خصم'),
    ]

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('paid', 'مدفوع'),
        ('cancelled', 'ملغي'),
    ]

    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name='العقد'
    )
    payment_type = models.CharField(
        max_length=20,
        choices=PAYMENT_TYPE_CHOICES,
        verbose_name='نوع الدفعة'
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ')
    currency = models.CharField(max_length=3, default='SAR', verbose_name='العملة')
    payment_date = models.DateField(verbose_name='تاريخ الدفع')
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='الحالة'
    )
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'دفعة عقد'
        verbose_name_plural = 'دفعات العقود'
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.contract.contract_number} - {self.get_payment_type_display()} - {self.amount} {self.currency}"


class ContractRenewal(models.Model):
    """تجديد العقود"""

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
    ]

    original_contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name='renewals',
        verbose_name='العقد الأصلي'
    )
    new_start_date = models.DateField(verbose_name='تاريخ البداية الجديد')
    new_end_date = models.DateField(verbose_name='تاريخ النهاية الجديد')
    new_salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الراتب الجديد')
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='الحالة'
    )
    reason = models.TextField(verbose_name='سبب التجديد')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')

    class Meta:
        verbose_name = 'تجديد عقد'
        verbose_name_plural = 'تجديدات العقود'
        ordering = ['-created_at']

    def __str__(self):
        return f"تجديد {self.original_contract.contract_number}"
