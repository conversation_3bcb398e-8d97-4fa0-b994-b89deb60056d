# نظام النسخ الاحتياطي الشامل

## 📋 نظرة عامة

نظام النسخ الاحتياطي في منصة استقدامي مصمم لحماية جميع بيانات الشركات بشكل شامل وآمن. يشمل النظام:

- **قواعد البيانات**: جميع جداول البيانات لكل شركة
- **الملفات والمستندات**: جميع الملفات المرفوعة والمستندات
- **الإعدادات**: إعدادات الشركة والمستخدمين
- **البيانات التشغيلية**: العمال، العملاء، العقود، الخدمات، الإجراءات، التقارير

## 🏗️ هيكل النظام

### 1. قواعد البيانات المنفصلة
```
data/company_dbs/
├── company_001.sqlite3    # قاعدة بيانات الشركة الأولى
├── company_002.sqlite3    # قاعدة بيانات الشركة الثانية
└── ...
```

### 2. ملفات الشركات
```
data/media/companies/
├── 1/                     # ملفات الشركة رقم 1
│   ├── workers/           # صور ومستندات العمال
│   ├── clients/           # مستندات العملاء
│   ├── contracts/         # ملفات العقود
│   ├── documents/         # المعاملات والمستندات
│   └── reports/           # التقارير المحفوظة
├── 2/                     # ملفات الشركة رقم 2
└── ...
```

### 3. النسخ الاحتياطية
```
data/backups/
├── company_001/
│   ├── company_001_complete_20250128_143022.zip
│   ├── company_001_complete_20250127_020000.zip
│   └── ...
├── company_002/
└── ...
```

## 📦 محتويات النسخة الاحتياطية

كل نسخة احتياطية تحتوي على:

### 1. قاعدة البيانات (company_name.sqlite3)
- **المستخدمون**: جميع مستخدمي الشركة وصلاحياتهم
- **العمال**: بيانات العمال الكاملة
- **العملاء**: معلومات العملاء والعقود
- **الخدمات**: جميع الخدمات والحجوزات
- **الإجراءات**: المعاملات والإجراءات الإدارية
- **التقارير**: بيانات التقارير المحفوظة
- **الإعدادات**: إعدادات الشركة والنظام
- **السجلات**: سجلات النشاطات والتغييرات

### 2. مجلد الملفات (media/)
- **صور العمال**: جوازات السفر، الصور الشخصية
- **مستندات العمال**: العقود، التأشيرات، الشهادات
- **ملفات العملاء**: الهويات، العقود، المراسلات
- **مستندات الخدمات**: اتفاقيات الخدمة، الفواتير
- **ملفات الإجراءات**: المعاملات الرسمية، المراسلات
- **التقارير المحفوظة**: ملفات PDF، Excel

### 3. ملف المعلومات (backup_info.json)
```json
{
    "company_id": 1,
    "company_name": "شركة الخليج للاستقدام",
    "database_name": "company_001",
    "backup_type": "automatic",
    "created_at": "2025-01-28T14:30:22",
    "created_by": "admin",
    "description": "نسخة احتياطية شاملة تلقائية",
    "includes": {
        "database": true,
        "media_files": true,
        "documents": true,
        "settings": true,
        "users": true,
        "workers": true,
        "clients": true,
        "contracts": true,
        "services": true,
        "procedures": true,
        "reports": true
    },
    "version": "2.1.0"
}
```

## 🔧 استخدام النظام

### 1. النسخ الاحتياطي اليدوي

#### من واجهة المسؤول الأعلى:
1. انتقل إلى **إدارة قواعد البيانات**
2. اختر الشركة المطلوبة
3. انقر على **إنشاء نسخة احتياطية**
4. أدخل وصف النسخة الاحتياطية
5. انقر **إنشاء**

#### من سطر الأوامر:
```bash
# نسخة احتياطية لشركة محددة
python manage.py backup_companies --company-id=1

# نسخة احتياطية لجميع الشركات
python manage.py backup_companies --all-companies

# تنظيف النسخ القديمة
python manage.py backup_companies --clean-old --retention-days=30
```

### 2. النسخ الاحتياطي التلقائي

#### تشغيل المجدول:
```bash
# نسخ احتياطية يومية
python scripts/backup_scheduler.py --daily

# نسخ احتياطية أسبوعية
python scripts/backup_scheduler.py --weekly

# نسخ احتياطية شهرية
python scripts/backup_scheduler.py --monthly

# جميع الأنواع
python scripts/backup_scheduler.py --all
```

#### جدولة باستخدام Cron (Linux/Mac):
```bash
# إضافة إلى crontab
crontab -e

# نسخة احتياطية يومية في الساعة 2:00 صباحاً
0 2 * * * cd /path/to/project && python manage.py backup_companies --all-companies --backup-type=daily

# نسخة احتياطية أسبوعية يوم الأحد في الساعة 3:00 صباحاً
0 3 * * 0 cd /path/to/project && python manage.py backup_companies --all-companies --backup-type=weekly

# نسخة احتياطية شهرية في اليوم الأول من كل شهر
0 4 1 * * cd /path/to/project && python manage.py backup_companies --all-companies --backup-type=monthly
```

### 3. استعادة النسخ الاحتياطية

#### من واجهة المسؤول الأعلى:
1. انتقل إلى **إدارة قواعد البيانات**
2. اختر **النسخ الاحتياطية**
3. اختر النسخة المطلوبة
4. انقر **استعادة**
5. أكد العملية

⚠️ **تحذير**: استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية!

## 🔒 الأمان والحماية

### 1. التشفير
- النسخ الاحتياطية مضغوطة بتنسيق ZIP
- يمكن إضافة كلمة مرور للملفات المضغوطة
- تخزين آمن في مجلدات محمية

### 2. التحقق من السلامة
```python
# التحقق من سلامة النسخة الاحتياطية
from backend.super_admin.backup_utils import verify_backup_integrity

is_valid, details = verify_backup_integrity(backup_id)
if is_valid:
    print("النسخة الاحتياطية سليمة")
else:
    print(f"مشكلة في النسخة الاحتياطية: {details}")
```

### 3. سجل العمليات
جميع عمليات النسخ الاحتياطي والاستعادة مسجلة في:
- **سجل النظام**: تفاصيل العمليات والأخطاء
- **قاعدة البيانات**: معلومات النسخ الاحتياطية
- **ملفات السجل**: `data/backups/backup_scheduler.log`

## 📊 المراقبة والإحصائيات

### عرض الإحصائيات:
```bash
# إحصائيات النسخ الاحتياطية
python scripts/backup_scheduler.py --stats

# تفاصيل النسخ الاحتياطية
python manage.py backup_companies --company-id=1 --show-stats
```

### المعلومات المتاحة:
- إجمالي عدد النسخ الاحتياطية
- الحجم الإجمالي للنسخ
- النسخ الحديثة (آخر 24 ساعة)
- إحصائيات حسب النوع (يدوي/تلقائي/يومي/أسبوعي/شهري)
- أحدث نسخة احتياطية لكل شركة

## 🚨 استكشاف الأخطاء

### المشاكل الشائعة:

1. **مساحة القرص ممتلئة**
   - تنظيف النسخ القديمة: `--clean-old`
   - زيادة مساحة القرص

2. **فشل في النسخ الاحتياطي**
   - فحص صلاحيات الملفات
   - التأكد من وجود مجلد البيانات
   - فحص سجل الأخطاء

3. **ملف تالف**
   - استخدام `verify_backup_integrity()`
   - إنشاء نسخة احتياطية جديدة

### السجلات:
- **Django logs**: في مجلد `logs/`
- **Backup logs**: في `data/backups/backup_scheduler.log`
- **System logs**: في قاعدة البيانات الرئيسية

## 🔄 الصيانة الدورية

### يومياً:
- فحص نجاح النسخ الاحتياطية التلقائية
- مراقبة مساحة القرص

### أسبوعياً:
- تنظيف النسخ القديمة
- فحص سلامة النسخ الاحتياطية

### شهرياً:
- مراجعة إعدادات النسخ الاحتياطي
- اختبار استعادة النسخ الاحتياطية
- تحديث سياسات الاحتفاظ
