<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\SystemLog;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('super_admin.login')
                ->with('error', 'يجب تسجيل الدخول للوصول إلى لوحة المسؤول الأعلى');
        }

        // Check if user is a super admin
        if (!Auth::user()->is_super_admin) {
            // Log unauthorized access attempt
            SystemLog::create([
                'user_id' => Auth::id(),
                'action' => 'unauthorized_access',
                'description' => 'محاولة وصول غير مصرح بها إلى صفحة المسؤول الأعلى: ' . $request->path(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return redirect()->route('company.login')
                ->with('error', 'هذه الصفحة خاصة بالمسؤول الأعلى فقط ولا يمكن الوصول إليها');
        }

        // Check if the user's session has expired
        $settings = app('system_settings');
        $sessionTimeout = $settings->session_timeout ?? 60; // Default: 60 minutes
        
        $lastActivity = $request->session()->get('last_activity');
        $now = time();
        
        if ($lastActivity && ($now - $lastActivity) > ($sessionTimeout * 60)) {
            // Log session timeout
            SystemLog::create([
                'user_id' => Auth::id(),
                'action' => 'session_timeout',
                'description' => 'انتهت صلاحية جلسة المسؤول الأعلى',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
            
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            return redirect()->route('super_admin.login')
                ->with('error', 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
        }
        
        // Update last activity timestamp
        $request->session()->put('last_activity', $now);

        return $next($request);
    }
}
