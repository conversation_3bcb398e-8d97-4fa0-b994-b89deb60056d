{% extends 'labor_management/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li><a href="{% url 'workers:worker_list' %}" style="color: var(--secondary-blue); text-decoration: none;">العمال</a></li>
<li style="margin: 0 8px;"> / </li>
<li><a href="{% url 'workers:worker_detail' worker.id %}" style="color: var(--secondary-blue); text-decoration: none;">{{ worker.first_name }} {{ worker.last_name }}</a></li>
<li style="margin: 0 8px;"> / </li>
<li><a href="{% url 'workers:worker_documents' worker.id %}" style="color: var(--secondary-blue); text-decoration: none;">المستندات</a></li>
<li style="margin: 0 8px;"> / </li>
<li><span style="color: var(--text-secondary);">إضافة مستند</span></li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- بطاقة إضافة مستند -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-file-upload me-2"></i>{{ title }}</h5>
        </div>
        <div class="card-body">
            <!-- معلومات العامل -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-user-circle fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading">معلومات العامل</h5>
                        <p class="mb-0">الاسم: <strong>{{ worker.first_name }} {{ worker.last_name }}</strong></p>
                        <p class="mb-0">رقم جواز السفر: <strong>{{ worker.passport_number }}</strong></p>
                        <p class="mb-0">الجنسية: <strong>{{ worker.get_nationality_display }}</strong></p>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة مستند -->
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات المستند</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.document_type.id_for_label }}" class="form-label">{{ form.document_type.label }}</label>
                                    {{ form.document_type }}
                                    {% if form.document_type.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.document_type.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">اختر نوع المستند المناسب</small>
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                                    {{ form.title }}
                                    {% if form.title.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.title.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">أدخل عنوانًا وصفيًا للمستند</small>
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.expiry_date.id_for_label }}" class="form-label">{{ form.expiry_date.label }}</label>
                                    {{ form.expiry_date }}
                                    {% if form.expiry_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.expiry_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">أدخل تاريخ انتهاء المستند (اختياري)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-upload me-2"></i>تحميل الملف والملاحظات</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.file.id_for_label }}" class="form-label">{{ form.file.label }}</label>
                                    {{ form.file }}
                                    {% if form.file.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.file.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">الملفات المدعومة: PDF، JPG، PNG، DOCX (الحد الأقصى: 10 ميجابايت)</small>
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">أضف أي ملاحظات إضافية حول المستند (اختياري)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات مساعدة -->
                <div class="alert alert-warning mt-3">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">ملاحظات هامة</h5>
                            <ul class="mb-0">
                                <li>تأكد من صحة المعلومات المدخلة قبل الحفظ.</li>
                                <li>يجب أن تكون الملفات واضحة وكاملة.</li>
                                <li>إذا كان المستند له تاريخ انتهاء، تأكد من إدخاله بشكل صحيح.</li>
                                <li>سيتم تنبيهك قبل انتهاء صلاحية المستندات.</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ
                    </button>
                    <a href="{% url 'workers:worker_documents' worker.id %}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم عند اختيار الملفات
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('{{ form.file.id_for_label }}');
        const fileLabel = fileInput.nextElementSibling;

        fileInput.addEventListener('change', function(e) {
            if (fileInput.files.length > 0) {
                const fileName = fileInput.files[0].name;
                const fileSize = (fileInput.files[0].size / 1024 / 1024).toFixed(2);
                fileLabel.textContent = `${fileName} (${fileSize} MB)`;
            } else {
                fileLabel.textContent = 'اختر ملفًا';
            }
        });

        // تحقق من تاريخ انتهاء الصلاحية
        const expiryDateInput = document.getElementById('{{ form.expiry_date.id_for_label }}');
        expiryDateInput.addEventListener('change', function() {
            const selectedDate = new Date(expiryDateInput.value);
            const today = new Date();
            
            if (selectedDate < today) {
                alert('تحذير: لقد اخترت تاريخًا في الماضي. تأكد من صحة تاريخ انتهاء الصلاحية.');
            }
        });
    });
</script>
{% endblock %}
