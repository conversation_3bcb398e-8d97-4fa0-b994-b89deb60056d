"""
URL configuration for labor_management project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include
from django.views.generic import TemplateView, RedirectView
from django.conf import settings
from django.conf.urls.static import static
from backend.accounts.database_config import ensure_all_databases_have_atomic_requests
from company import views_api

# التأكد من أن جميع قواعد البيانات تحتوي على إعداد ATOMIC_REQUESTS
ensure_all_databases_have_atomic_requests()

urlpatterns = [
    # توجيه الصفحة الرئيسية إلى صفحة تسجيل دخول الشركات
    path('', RedirectView.as_view(url='/company/login/', permanent=True), name='home'),

    # إعادة توجيه مسار login إلى صفحة تسجيل دخول الشركات
    path('login/', RedirectView.as_view(url='/company/login/', permanent=True)),

    # مسارات المسؤول الأعلى - يجب أن تكون قبل مسارات الحسابات العامة
    path('super_admin/', include('backend.super_admin.urls', namespace='super_admin')),

    # مسارات الشركات
    path('company/', include('company.urls', namespace='company')),

    # إعادة توجيه من company/workers إلى workers
    path('company/workers/', RedirectView.as_view(url='/workers/', permanent=True)),
    path('company/workers/<path:subpath>', RedirectView.as_view(url='/workers/%(subpath)s', permanent=True)),

    # مسارات التطبيقات الرئيسية
    # path('reports/', include('backend.reports.urls')),  # معطل مؤقتًا
    # إعادة توجيه مسار dashboard إلى صفحة لوحة تحكم الشركة
    path('dashboard/', RedirectView.as_view(url='/company/', permanent=True)),
    path('workers/', include('backend.workers.urls')),
    path('clients/', include('clients.urls', namespace='clients')),
    path('contracts/', include('backend.contracts.urls')),
    path('services/', include('backend.services.urls')),
    path('settings/', include('backend.system_settings.urls')),
    # path('scanner/', include('backend.scanner.urls')),  # معطل - المجلد غير موجود
    path('documents/', include('backend.documents.urls')),
    path('procedures/', include('backend.procedures.urls')),
    path('labor/', include('backend.labor_management.app_urls', namespace='labor_management')),

    # صفحات اختبار
    path('test/', TemplateView.as_view(template_name='test.html'), name='test'),
    path('examples/notifications/', TemplateView.as_view(template_name='examples/notifications_example.html'), name='notifications_example'),
    # path('examples/modern-inputs/', login_required(TemplateView.as_view(template_name='examples/modern_inputs_example.html')), name='modern_inputs_example'),

    # مسارات الحسابات العامة - يجب أن تكون بعد مسارات المسؤول الأعلى
    path('', include('backend.accounts.urls')),



    # واجهة برمجة التطبيقات (API)
    path('api/', include('backend.api.simple_urls')),

    # واجهة API الرئيسية
    path('api-docs/', TemplateView.as_view(template_name='api/frontend/index.html'), name='api-docs'),

    # مسار التحقق من الرقم التسلسلي (مطلق)
    path('accounts/api/check-serial-number/', views_api.check_serial_number_api),
]

# إضافة URLs لخدمة ملفات الوسائط والملفات الثابتة في وضع التطوير
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    # إضافة مسار إضافي للملفات الثابتة من المجلد static
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
