from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.permissions import IsAuthenticated


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_root(request, format=None):
    """
    الصفحة الرئيسية لواجهة برمجة التطبيقات (API) لنظام إدارة العمالة.
    توفر روابط لجميع نقاط النهاية الرئيسية.
    """
    return Response({
        'docs': {
            'swagger': reverse('schema-swagger-ui', request=request, format=format),
            'redoc': reverse('schema-redoc', request=request, format=format),
        }
    })
