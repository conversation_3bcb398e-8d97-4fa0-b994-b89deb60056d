/* نظام التحقق العصري للحقول المطلوبة */

/* إخفاء رسائل التحقق الافتراضية للمتصفح */
input:invalid {
    box-shadow: none;
    outline: none;
}

input:focus:invalid {
    box-shadow: none;
    outline: none;
}

/* تصميم الحقول مع التحقق */
.form-field {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-field input,
.form-field select,
.form-field textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #ffffff !important;
    color: #1f2937 !important;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* حالة الخطأ */
.form-field.error input,
.form-field.error select,
.form-field.error textarea {
    border-color: #ef4444;
    background-color: #fef2f2 !important;
    color: #1f2937 !important;
}

.form-field.error input:focus,
.form-field.error select:focus,
.form-field.error textarea:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* حالة النجاح */
.form-field.success input,
.form-field.success select,
.form-field.success textarea {
    border-color: #10b981;
    background-color: #f0fdf4 !important;
    color: #1f2937 !important;
}

.form-field.success input:focus,
.form-field.success select:focus,
.form-field.success textarea:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* رسائل التحقق العصرية */
.validation-message {
    display: none;
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    animation: slideDown 0.3s ease;
}

.validation-message.show {
    display: flex;
    align-items: center;
    gap: 8px;
}

.validation-message.error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.validation-message.success {
    background-color: #f0fdf4;
    color: #059669;
    border: 1px solid #bbf7d0;
}

.validation-message.warning {
    background-color: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.validation-message.info {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* أيقونات الرسائل */
.validation-message::before {
    content: '';
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
    flex-shrink: 0;
}

.validation-message.error::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc2626' viewBox='0 0 20 20'%3E%3Cpath fill-rule='evenodd' d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

.validation-message.success::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23059669' viewBox='0 0 20 20'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

.validation-message.warning::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23d97706' viewBox='0 0 20 20'%3E%3Cpath fill-rule='evenodd' d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

.validation-message.info::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232563eb' viewBox='0 0 20 20'%3E%3Cpath fill-rule='evenodd' d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

/* حركة الظهور */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تصميم خاص للحقول المطلوبة */
.form-field.required label::after {
    content: ' *';
    color: #ef4444;
    font-weight: bold;
}

/* تصميم tooltip للحقول */
.form-field .field-tooltip {
    position: absolute;
    top: -8px;
    right: 12px;
    background-color: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.form-field .field-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 50%;
    transform: translateX(50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
}

.form-field:hover .field-tooltip {
    opacity: 1;
    visibility: visible;
    top: -12px;
}

/* تصميم responsive */
@media (max-width: 640px) {
    .form-field input,
    .form-field select,
    .form-field textarea {
        padding: 10px 14px;
        font-size: 16px; /* منع zoom في iOS */
    }

    .validation-message {
        font-size: 12px;
        padding: 6px 10px;
    }
}

/* إلغاء تأثير الوضع المظلم - نريد الحقول بيضاء دائماً */
@media (prefers-color-scheme: dark) {
    .form-field input,
    .form-field select,
    .form-field textarea {
        background-color: #ffffff !important;
        border-color: #e2e8f0 !important;
        color: #1f2937 !important;
    }

    .form-field input:focus,
    .form-field select:focus,
    .form-field textarea:focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }

    .form-field.error input,
    .form-field.error select,
    .form-field.error textarea {
        background-color: #fef2f2 !important;
        border-color: #ef4444 !important;
        color: #1f2937 !important;
    }

    .form-field.success input,
    .form-field.success select,
    .form-field.success textarea {
        background-color: #f0fdf4 !important;
        border-color: #10b981 !important;
        color: #1f2937 !important;
    }
}

/* قواعد إضافية لضمان الألوان الصحيحة */
input, select, textarea {
    background-color: #ffffff !important;
    color: #1f2937 !important;
}

input:focus, select:focus, textarea:focus {
    background-color: #ffffff !important;
    color: #1f2937 !important;
}

/* إلغاء تأثير autocomplete styling */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #ffffff inset !important;
    -webkit-text-fill-color: #1f2937 !important;
    background-color: #ffffff !important;
}
