# Generated by Django 5.2 on 2025-04-09 15:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('category', models.CharField(default='general', max_length=50, verbose_name='الفئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['category', 'key'],
            },
        ),
        migrations.CreateModel(
            name='WorkerSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('setting_key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الإعداد')),
                ('setting_value', models.TextField(verbose_name='قيمة الإعداد')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد العمال',
                'verbose_name_plural': 'إعدادات العمال',
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('view', 'عرض'), ('export', 'تصدير'), ('import', 'استيراد'), ('other', 'أخرى')], max_length=20, verbose_name='الإجراء')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('record_id', models.IntegerField(blank=True, null=True, verbose_name='رقم السجل')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='متصفح المستخدم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل التدقيق',
                'verbose_name_plural': 'سجلات التدقيق',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'مدير النظام'), ('manager', 'مدير'), ('supervisor', 'مشرف'), ('staff', 'موظف')], default='staff', max_length=20, verbose_name='الدور')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pics/', verbose_name='الصورة الشخصية')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP آخر تسجيل دخول')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='NotificationSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(max_length=50, verbose_name='نوع الإشعار')),
                ('email_enabled', models.BooleanField(default=True, verbose_name='تفعيل البريد الإلكتروني')),
                ('sms_enabled', models.BooleanField(default=False, verbose_name='تفعيل الرسائل القصيرة')),
                ('in_app_enabled', models.BooleanField(default=True, verbose_name='تفعيل إشعارات التطبيق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إعداد الإشعارات',
                'verbose_name_plural': 'إعدادات الإشعارات',
                'unique_together': {('user', 'notification_type')},
            },
        ),
        migrations.CreateModel(
            name='UserPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('can_view', models.BooleanField(default=False, verbose_name='يمكن العرض')),
                ('can_add', models.BooleanField(default=False, verbose_name='يمكن الإضافة')),
                ('can_edit', models.BooleanField(default=False, verbose_name='يمكن التعديل')),
                ('can_delete', models.BooleanField(default=False, verbose_name='يمكن الحذف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية المستخدم',
                'verbose_name_plural': 'صلاحيات المستخدمين',
                'unique_together': {('user', 'module')},
            },
        ),
    ]
