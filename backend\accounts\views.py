from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.conf import settings
from django.db import connections
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from backend.super_admin.models import Company, SystemLog, FailedLoginAttempt, SystemSettings
from .models import UserCompany
from .router import set_current_db_name
from .forms import DatabaseLoginForm
from .database_config import save_database_config, get_database_config, ensure_database_config_complete
import re
import logging
import os
import sqlite3

# إعداد السجل
logger = logging.getLogger(__name__)

def login_view(request):
    """View function for the login page."""
    # إضافة السنة الحالية إلى سياق القالب
    from datetime import datetime
    current_year = datetime.now().year

    # الحصول على عنوان IP للمستخدم
    client_ip = request.META.get('REMOTE_ADDR', '')

    # إنشاء نموذج فارغ للعرض الأولي
    if request.method != 'POST':
        form = DatabaseLoginForm()

        # التحقق من وجود معلومات قاعدة بيانات محفوظة في الجلسة
        saved_serial = request.session.get('saved_serial_number')
        db_verified = False

        if saved_serial:
            db_config = get_database_config(saved_serial)
            if db_config:
                # تعبئة النموذج بالمعلومات المحفوظة
                form.fields['serial_number'].initial = saved_serial
                form.fields['db_name'].initial = db_config.get('db_name', '')
                form.fields['db_user'].initial = db_config.get('db_user', '')
                form.fields['db_password'].initial = db_config.get('db_password', '')
                form.fields['db_host'].initial = db_config.get('db_host', 'localhost')
                db_verified = db_config.get('verified', False)

        # استخدام قالب تسجيل الدخول الموحد
        template_name = 'login/login_final.html'

        # إضافة متغيرات إضافية للقالب
        context = {
            'form': form,
            'current_year': current_year,
            'db_verified': db_verified,
            'saved_serial': saved_serial
        }

        # إضافة معلومات الشركة إذا كانت متوفرة
        from backend.super_admin.models import SystemSettings
        system_settings = SystemSettings.get_settings()

        # إضافة إعدادات النظام إلى السياق
        context.update({
            'system_name': system_settings.app_name,
            'company_name': getattr(system_settings, 'app_name', 'نظام إدارة العمالة'),
        })

        return render(request, template_name, context)

    # معالجة طلب تسجيل الدخول
    form = DatabaseLoginForm(data=request.POST)
    logger.info(f"محاولة تسجيل دخول من العنوان: {client_ip}")

    # استخدام قالب تسجيل الدخول الموحد
    template_name = 'login/login_final.html'

    # إضافة معلومات الشركة إذا كانت متوفرة
    from backend.super_admin.models import SystemSettings
    system_settings = SystemSettings.get_settings()

    # التحقق من صحة النموذج
    if not form.is_valid():
        logger.warning(f"نموذج تسجيل الدخول غير صالح: {form.errors}")
        messages.error(request, 'يرجى التأكد من إدخال جميع البيانات بشكل صحيح')

        # إضافة متغيرات إضافية للقالب
        context = {
            'form': form,
            'current_year': current_year,
            'system_name': system_settings.app_name,
            'company_name': getattr(system_settings, 'app_name', 'نظام إدارة العمالة'),
        }

        return render(request, template_name, context)

    # استخراج البيانات من النموذج
    username = form.cleaned_data.get('username')
    password = form.cleaned_data.get('password')
    serial_number = form.cleaned_data.get('serial_number')

    logger.info(f"محاولة تسجيل دخول: المستخدم: {username}, الرقم التسلسلي: {serial_number}")

    # تنسيق الرقم التسلسلي
    if not serial_number:
        messages.error(request, 'الرجاء إدخال الرقم التسلسلي لقاعدة البيانات')
        return render(request, template_name, {'form': form, 'current_year': current_year})

    serial_number = format_serial_number(serial_number)

    # التحقق من حظر IP
    is_blocked = FailedLoginAttempt.check_attempts(serial_number, client_ip)
    if is_blocked:
        messages.error(request, 'تم حظر الوصول مؤقتًا بسبب كثرة المحاولات الفاشلة. الرجاء المحاولة بعد ساعة.')
        logger.warning(f"محاولة دخول من IP محظور: {client_ip} للرقم التسلسلي: {serial_number}")
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # البحث عن الشركة بالرقم التسلسلي
    company = Company.objects.filter(serial_number=serial_number).first()

    if not company:
        # تسجيل محاولة فاشلة
        log_failed_attempt(serial_number, 'رقم تسلسلي غير صحيح', client_ip)
        messages.error(request, 'الرقم التسلسلي غير صحيح. يرجى التحقق من الرقم وإعادة المحاولة.')
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # التحقق من حالة الشركة
    if company.status != 'active':
        # تسجيل محاولة فاشلة
        log_failed_attempt(serial_number, f'الشركة غير نشطة (الحالة: {company.status})', client_ip)
        messages.error(request, 'الشركة غير نشطة. يرجى التواصل مع المسؤول.')
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # التحقق من تاريخ انتهاء الترخيص
    if company.expiry_date and company.expiry_date < timezone.now().date():
        # تسجيل محاولة فاشلة
        log_failed_attempt(serial_number, 'ترخيص منتهي الصلاحية', client_ip)
        messages.error(request, 'انتهت صلاحية ترخيص الشركة. يرجى التواصل مع المسؤول لتجديد الترخيص.')
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # التحقق من صحة معلومات قاعدة البيانات
    logger.info(f"التحقق من صحة معلومات قاعدة البيانات للشركة: {company.name}, قاعدة البيانات: {company.database_name}")
    db_valid, error_message, _ = validate_database_connection(
        company.database_name,
        company.database_user,
        company.database_password,
        company.database_host
    )
    logger.info(f"نتيجة التحقق من قاعدة البيانات: {db_valid}, الرسالة: {error_message}")

    if not db_valid:
        # تسجيل محاولة فاشلة
        log_failed_attempt(serial_number, f'خطأ في الاتصال بقاعدة البيانات: {error_message}', client_ip)

        # تحسين رسالة الخطأ للمستخدم
        user_friendly_message = 'لا يمكن الاتصال بقاعدة البيانات. يرجى التواصل مع المسؤول.'

        if "كلمة المرور مطلوبة" in error_message:
            user_friendly_message = 'كلمة مرور قاعدة البيانات غير صحيحة أو مفقودة'
        elif "اسم قاعدة البيانات مطلوب" in error_message:
            user_friendly_message = 'اسم قاعدة البيانات غير صحيح أو مفقود'
        elif "لا يمكن فتح ملف قاعدة البيانات" in error_message:
            user_friendly_message = 'لا يمكن الوصول إلى ملف قاعدة البيانات. يرجى التحقق من وجود الملف'
        elif "قاعدة البيانات غير مهيأة" in error_message:
            user_friendly_message = 'قاعدة البيانات غير مهيأة بشكل صحيح. يرجى التواصل مع المسؤول'
        elif "جدول غير موجود" in error_message:
            user_friendly_message = 'قاعدة البيانات غير مهيأة بشكل صحيح (جدول غير موجود)'

        messages.error(request, user_friendly_message)
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # التحقق من صحة بيانات المستخدم
    user = authenticate(request, username=username, password=password)

    if user is None:
        # تسجيل محاولة فاشلة
        log_failed_attempt(serial_number, 'بيانات مستخدم غير صحيحة', client_ip)
        messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
        return render(request, template_name, {'form': form, 'current_year': current_year})

    # تسجيل الدخول
    login(request, user)

    # تسجيل عملية تسجيل الدخول
    SystemLog.objects.create(
        user=user,
        action='login',
        company=company,
        description=f'تم تسجيل الدخول إلى النظام باستخدام الرقم التسلسلي {serial_number}',
        ip_address=client_ip
    )

    # ربط المستخدم بالشركة
    user_company, _ = UserCompany.objects.get_or_create(
        user=user,
        company=company,
        defaults={'is_default': True}
    )

    if not user_company.is_default:
        user_company.is_default = True
        user_company.save()

    # إضافة قاعدة البيانات إلى الإعدادات
    db_name = f"company_{company.id}"
    db_config = {
        'ENGINE': 'django.db.backends.sqlite3',  # تغيير حسب نوع قاعدة البيانات
        'NAME': settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3",
        'USER': company.database_user,
        'PASSWORD': company.database_password,
        'HOST': company.database_host,
    }

    # التأكد من اكتمال إعدادات قاعدة البيانات
    db_config = ensure_database_config_complete(db_config)

    # إضافة قاعدة البيانات إلى الإعدادات
    settings.DATABASES[db_name] = db_config

    # تعيين قاعدة البيانات الحالية
    set_current_db_name(db_name)

    # تخزين معلومات قاعدة البيانات في الجلسة
    request.session['current_db_name'] = db_name
    request.session['current_company_id'] = company.id
    request.session['serial_number'] = serial_number

    # تعيين وقت انتهاء الجلسة
    system_settings = SystemSettings.get_settings()
    if system_settings.session_timeout > 0:
        request.session.set_expiry(system_settings.session_timeout * 60)  # تحويل الدقائق إلى ثوانٍ

    # تسجيل نجاح تسجيل الدخول
    logger.info(f"تم تسجيل الدخول بنجاح: {username} للشركة: {company.name} من العنوان: {client_ip}")

    # إعادة التوجيه إلى لوحة التحكم
    return redirect('dashboard')

def generate_serial_number():
    """توليد رقم تسلسلي فريد للشركة"""
    import string
    import random
    chars = string.ascii_uppercase + string.digits
    return ''.join(random.choice(chars) for _ in range(16))

@csrf_exempt
def check_serial_number(request):
    """التحقق من صحة الرقم التسلسلي عبر AJAX"""
    try:
        if request.method == 'POST':
            serial_number = request.POST.get('serial_number', '')
            client_ip = request.META.get('REMOTE_ADDR', '')
            test_connection = request.POST.get('test_connection', 'false') == 'true'

            # تسجيل محاولة التحقق
            log_verification_attempt(serial_number, client_ip)
            logger.info(f"محاولة التحقق من الرقم التسلسلي: {serial_number} من العنوان: {client_ip}")

            if not serial_number:
                logger.warning("محاولة تحقق بدون رقم تسلسلي")
                return JsonResponse({
                    'valid': False,
                    'message': 'الرجاء إدخال الرقم التسلسلي'
                })

            # تنسيق الرقم التسلسلي إذا لزم الأمر
            serial_number = format_serial_number(serial_number)
            logger.info(f"الرقم التسلسلي بعد التنسيق: {serial_number}")

            # البحث عن الشركات بالرقم التسلسلي
            companies = Company.objects.filter(serial_number=serial_number)

            if not companies.exists():
                # تسجيل محاولة فاشلة
                log_failed_attempt(serial_number, 'رقم تسلسلي غير صحيح', client_ip)
                logger.warning(f"رقم تسلسلي غير صحيح: {serial_number}")
                return JsonResponse({
                    'valid': False,
                    'message': 'الرقم التسلسلي غير صحيح أو غير مسجل في النظام'
                })

            # اختيار الشركة الأولى كشركة افتراضية
            company = companies.first()

            # التحقق من حالة الشركة
            if company.status != 'active':
                # تسجيل محاولة فاشلة
                log_failed_attempt(serial_number, f'الشركة غير نشطة (الحالة: {company.status})', client_ip)
                logger.warning(f"الشركة غير نشطة: {company.name}, الحالة: {company.status}")
                return JsonResponse({
                    'valid': False,
                    'message': 'الشركة غير نشطة. يرجى التواصل مع المسؤول'
                })

            # التحقق من تاريخ انتهاء الترخيص
            if company.expiry_date and company.expiry_date < timezone.now().date():
                # تسجيل محاولة فاشلة
                log_failed_attempt(serial_number, 'ترخيص منتهي الصلاحية', client_ip)
                logger.warning(f"ترخيص منتهي الصلاحية للشركة: {company.name}, تاريخ الانتهاء: {company.expiry_date}")
                return JsonResponse({
                    'valid': False,
                    'message': 'انتهت صلاحية ترخيص الشركة. يرجى التواصل مع المسؤول لتجديد الترخيص'
                })

            # التحقق من صحة معلومات قاعدة البيانات
            logger.info(f"جاري التحقق من الاتصال بقاعدة البيانات: {company.database_name}")
            db_valid, error_message, db_info = validate_database_connection(
                company.database_name,
                company.database_user,
                company.database_password,
                company.database_host
            )
            logger.info(f"نتيجة التحقق من قاعدة البيانات: {db_valid}, الرسالة: {error_message}")

            # لا نتجاوز أخطاء الاتصال بقاعدة البيانات حتى في وضع التطوير
            if not db_valid:
                logger.warning(f"فشل الاتصال بقاعدة البيانات: {error_message}")
                # لا نقوم بتعيين db_valid إلى True لضمان التحقق الصارم

            if db_valid:
                # تسجيل محاولة ناجحة
                SystemLog.objects.create(
                    action='verify',
                    company=company,
                    description=f'تم التحقق من الرقم التسلسلي بنجاح: {serial_number}',
                    ip_address=client_ip
                )
                logger.info(f"تم التحقق بنجاح من الرقم التسلسلي: {serial_number} للشركة: {company.name}")

                # البحث عن الشركات المرتبطة بنفس الرقم التسلسلي
                related_companies = Company.objects.filter(serial_number=serial_number)

                # إعداد قائمة بالشركات المتاحة
                available_companies = []
                for related_company in related_companies:
                    available_companies.append({
                        'id': related_company.id,
                        'name': related_company.name,
                        'database_name': related_company.database_name,
                        'database_user': related_company.database_user,
                        'database_host': related_company.database_host,
                        'status': related_company.status
                    })

                # إرجاع معلومات إضافية عن الشركة
                response_data = {
                    'valid': True,
                    'message': 'تم التحقق من الرقم التسلسلي بنجاح',
                    'company_info': {
                        'name': company.name,
                        'status': company.status,
                        'created_at': company.created_at.strftime('%Y-%m-%d') if company.created_at else None,
                        'expiry_date': company.expiry_date.strftime('%Y-%m-%d') if company.expiry_date else None,
                        'address': company.address,
                        'phone': company.phone,
                        'email': company.email,
                        'database_name': company.database_name,
                        'database_user': company.database_user,
                        'database_host': company.database_host
                        # لا نرسل كلمة المرور لأسباب أمنية
                    },
                    'available_companies': available_companies
                }

                # دائمًا نضيف معلومات قاعدة البيانات بغض النظر عن قيمة test_connection
                # هذا يضمن أن المعلومات ستكون متاحة للعميل في جميع الحالات
                try:
                    # تنسيق حجم قاعدة البيانات إذا كان متوفرًا
                    size_formatted = db_info.get('size_formatted', '0 بايت')
                    if 'size' in db_info and not size_formatted:
                        size_formatted = format_size(db_info.get('size', 0))

                    # إضافة معلومات قاعدة البيانات للرد
                    response_data['db_info'] = {
                        'name': company.database_name,
                        'tables_count': db_info.get('tables_count', 0),
                        'size': size_formatted,
                        'size_formatted': size_formatted,
                        'last_modified': db_info.get('last_modified', '-'),
                        'host': company.database_host or 'محلي',
                        'engine': 'SQLite3'
                    }

                    # تسجيل إضافة معلومات قاعدة البيانات
                    logger.info(f"تم إضافة معلومات قاعدة البيانات للرد: {response_data['db_info']}")

                    # طباعة الاستجابة الكاملة للتحقق
                    logger.info(f"الاستجابة الكاملة: {response_data}")
                except Exception as e:
                    logger.warning(f"خطأ في إضافة معلومات قاعدة البيانات: {str(e)}")

                return JsonResponse(response_data)
            else:
                # تسجيل محاولة فاشلة
                log_failed_attempt(serial_number, f'خطأ في الاتصال بقاعدة البيانات: {error_message}', client_ip)
                logger.error(f"فشل الاتصال بقاعدة البيانات: {company.database_name}, الخطأ: {error_message}")

                # تحسين رسالة الخطأ للمستخدم
                user_friendly_message = 'لا يمكن الاتصال بقاعدة البيانات. يرجى التواصل مع المسؤول'

                if "كلمة المرور مطلوبة" in error_message:
                    user_friendly_message = 'كلمة مرور قاعدة البيانات غير صحيحة أو مفقودة'
                elif "اسم قاعدة البيانات مطلوب" in error_message:
                    user_friendly_message = 'اسم قاعدة البيانات غير صحيح أو مفقود'
                elif "لا يمكن فتح ملف قاعدة البيانات" in error_message:
                    user_friendly_message = 'لا يمكن الوصول إلى ملف قاعدة البيانات. يرجى التحقق من وجود الملف'
                elif "قاعدة البيانات غير مهيأة" in error_message:
                    user_friendly_message = 'قاعدة البيانات غير مهيأة بشكل صحيح. يرجى التواصل مع المسؤول'

                return JsonResponse({
                    'valid': False,
                    'message': user_friendly_message,
                    'error': error_message
                })

        return JsonResponse({
            'valid': False,
            'message': 'طلب غير صالح'
        })
    except Exception as e:
        logger.error(f"خطأ غير متوقع في التحقق من الرقم التسلسلي: {str(e)}")
        return JsonResponse({
            'valid': False,
            'message': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى',
            'error': str(e)
        })

@csrf_exempt
def test_database_connection(request):
    """اختبار الاتصال بقاعدة البيانات عبر AJAX"""
    try:
        if request.method != 'POST':
            logger.warning("محاولة اختبار اتصال بطريقة غير صالحة")
            return JsonResponse({
                'success': False,
                'message': 'طلب غير صالح. يجب استخدام طريقة POST'
            })

        serial_number = request.POST.get('serial_number', '')
        client_ip = request.META.get('REMOTE_ADDR', '')

        # التحقق من وجود معلومات قاعدة البيانات المدخلة يدويًا
        db_name = request.POST.get('db_name', '')
        db_user = request.POST.get('db_user', '')
        db_password = request.POST.get('db_password', '')
        db_host = request.POST.get('db_host', 'localhost')
        manual_test = request.POST.get('manual_test', 'false') == 'true'

        # تسجيل معلومات الطلب
        logger.info(f"محاولة اختبار اتصال قاعدة البيانات من العنوان {client_ip}: الرقم التسلسلي: {serial_number}, اسم قاعدة البيانات: {db_name}, المستخدم: {db_user}, المضيف: {db_host}, اختبار يدوي: {manual_test}")

        # التحقق من إدخال جميع المعلومات المطلوبة
        if manual_test:
            if not db_name:
                logger.warning("محاولة اختبار اتصال بدون اسم قاعدة بيانات")
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى إدخال اسم قاعدة البيانات'
                })
            if not db_user:
                logger.warning("محاولة اختبار اتصال بدون اسم مستخدم قاعدة البيانات")
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى إدخال اسم مستخدم قاعدة البيانات'
                })
            if not db_password:
                logger.warning("محاولة اختبار اتصال بدون كلمة مرور قاعدة البيانات")
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى إدخال كلمة مرور قاعدة البيانات'
                })

            # لا نقبل أي رقم تسلسلي غير موجود في قاعدة البيانات
            # ولا نقوم بإنشاء شركات وهمية للتجربة
            if not Company.objects.filter(serial_number=serial_number).exists():
                logger.warning(f"محاولة استخدام رقم تسلسلي غير موجود: {serial_number}")
                return JsonResponse({
                    'success': False,
                    'message': 'الرقم التسلسلي غير موجود في قاعدة البيانات. يرجى التأكد من الرقم التسلسلي.'
                })

        # التحقق من وجود الرقم التسلسلي
        if not serial_number:
            logger.warning("محاولة اختبار اتصال بدون رقم تسلسلي")
            return JsonResponse({
                'success': False,
                'message': 'الرجاء إدخال الرقم التسلسلي'
            })

        # تنسيق الرقم التسلسلي إذا لزم الأمر
        serial_number = format_serial_number(serial_number)
        logger.info(f"الرقم التسلسلي بعد التنسيق: {serial_number}")

        # البحث عن الشركة بالرقم التسلسلي
        company = Company.objects.filter(serial_number=serial_number).first()

        if not company:
            logger.warning(f"رقم تسلسلي غير صحيح: {serial_number}")
            return JsonResponse({
                'success': False,
                'message': 'الرقم التسلسلي غير صحيح أو غير مسجل في النظام'
            })

        # استخدام معلومات قاعدة البيانات المدخلة يدويًا إذا كانت متوفرة
        if manual_test and db_name and db_user:
            # اختبار الاتصال بقاعدة البيانات باستخدام المعلومات المدخلة
            logger.info(f"اختبار الاتصال بقاعدة البيانات: {db_name}, المستخدم: {db_user}, المضيف: {db_host}")
            db_valid, error_message, db_info = validate_database_connection(
                db_name,
                db_user,
                db_password,
                db_host
            )
            logger.info(f"نتيجة اختبار الاتصال: {db_valid}, الرسالة: {error_message}")

            if db_valid:
                # تحديث معلومات قاعدة البيانات للشركة
                company.database_name = db_name
                company.database_user = db_user
                if db_password:  # تحديث كلمة المرور فقط إذا تم إدخالها
                    company.database_password = db_password
                company.database_host = db_host
                company.save()

                # حفظ معلومات قاعدة البيانات في ملف التكوين
                save_database_config(
                    serial_number=serial_number,
                    db_name=db_name,
                    db_user=db_user,
                    db_password=db_password,
                    db_host=db_host
                )

                # حفظ الرقم التسلسلي في الجلسة
                request.session['saved_serial_number'] = serial_number

                # تسجيل محاولة ناجحة
                logger.info(f"تم اختبار الاتصال بقاعدة البيانات بنجاح (معلومات مدخلة يدويًا): {db_name}")

                # إرجاع معلومات قاعدة البيانات
                return JsonResponse({
                    'success': True,
                    'message': 'تم الاتصال بقاعدة البيانات بنجاح وتحديث معلومات الاتصال',
                    'db_info': {
                        'name': db_name,
                        'tables_count': db_info.get('tables_count', 0),
                        'size': db_info.get('size_formatted', '0 بايت'),
                        'size_formatted': db_info.get('size_formatted', '0 بايت'),
                        'last_modified': db_info.get('last_modified', '-'),
                        'host': db_host or 'محلي',
                        'engine': 'SQLite3'
                    }
                })
            else:
                # تسجيل محاولة فاشلة
                logger.warning(f"فشل اختبار الاتصال بقاعدة البيانات (معلومات مدخلة يدويًا): {db_name}, السبب: {error_message}")

                # تحسين رسالة الخطأ للمستخدم
                user_friendly_message = 'فشل الاتصال بقاعدة البيانات'
                if "كلمة المرور مطلوبة" in error_message:
                    user_friendly_message = 'يرجى إدخال كلمة مرور قاعدة البيانات'
                elif "اسم قاعدة البيانات مطلوب" in error_message:
                    user_friendly_message = 'يرجى إدخال اسم قاعدة البيانات'
                elif "اسم المستخدم مطلوب" in error_message:
                    user_friendly_message = 'يرجى إدخال اسم مستخدم قاعدة البيانات'
                elif "لا يمكن فتح ملف قاعدة البيانات" in error_message:
                    user_friendly_message = 'لا يمكن فتح ملف قاعدة البيانات. تأكد من وجود الملف وصلاحيات الوصول إليه'
                elif "جدول غير موجود" in error_message:
                    user_friendly_message = 'قاعدة البيانات غير مهيأة بشكل صحيح (جدول غير موجود)'
                elif "تم رفض الإذن" in error_message:
                    user_friendly_message = 'تم رفض الإذن للوصول إلى قاعدة البيانات. تأكد من صحة بيانات الاتصال'

                return JsonResponse({
                    'success': False,
                    'message': user_friendly_message,
                    'error': error_message
                })
        else:
            # اختبار الاتصال بقاعدة البيانات باستخدام معلومات الشركة المخزنة
            logger.info(f"اختبار الاتصال بقاعدة البيانات باستخدام معلومات الشركة المخزنة: {company.database_name}")
            db_valid, error_message, db_info = validate_database_connection(
                company.database_name,
                company.database_user,
                company.database_password,
                company.database_host
            )
            logger.info(f"نتيجة اختبار الاتصال: {db_valid}, الرسالة: {error_message}")

            # لا نتجاوز أخطاء الاتصال بقاعدة البيانات حتى في وضع التطوير
            if not db_valid:
                logger.warning(f"فشل الاتصال بقاعدة البيانات: {error_message}")
                # لا نقوم بتعيين db_valid إلى True لضمان التحقق الصارم

            if db_valid:
                # تسجيل محاولة ناجحة
                logger.info(f"تم اختبار الاتصال بقاعدة البيانات بنجاح: {company.database_name}")

                # إرجاع معلومات قاعدة البيانات
                return JsonResponse({
                    'success': True,
                    'message': 'تم الاتصال بقاعدة البيانات بنجاح',
                    'db_info': {
                        'name': company.database_name,
                        'tables_count': db_info.get('tables_count', 0),
                        'size': db_info.get('size_formatted', '0 بايت'),
                        'size_formatted': db_info.get('size_formatted', '0 بايت'),
                        'last_modified': db_info.get('last_modified', '-'),
                        'host': company.database_host or 'محلي',
                        'engine': 'SQLite3'
                    }
                })
            else:
                # تسجيل محاولة فاشلة
                logger.warning(f"فشل اختبار الاتصال بقاعدة البيانات: {company.database_name}, السبب: {error_message}")

                # تحسين رسالة الخطأ للمستخدم وإضافة تفاصيل التشخيص
                user_friendly_message = 'فشل الاتصال بقاعدة البيانات'
                error_code = 'DB_CONNECTION_ERROR'

                if "كلمة المرور مطلوبة" in error_message:
                    user_friendly_message = 'كلمة مرور قاعدة البيانات مطلوبة. يرجى إدخال كلمة المرور'
                    error_code = 'DB_PASSWORD_REQUIRED'
                elif "اسم قاعدة البيانات مطلوب" in error_message:
                    user_friendly_message = 'اسم قاعدة البيانات غير صحيح. يرجى التحقق من اسم قاعدة البيانات'
                    error_code = 'DB_NAME_REQUIRED'
                elif "اسم المستخدم مطلوب" in error_message:
                    user_friendly_message = 'اسم مستخدم قاعدة البيانات غير صحيح. يرجى التحقق من اسم المستخدم'
                    error_code = 'DB_USER_REQUIRED'
                elif "لا يمكن فتح ملف قاعدة البيانات" in error_message:
                    user_friendly_message = 'لا يمكن فتح ملف قاعدة البيانات. تأكد من وجود الملف وصلاحيات الوصول إليه'
                    error_code = 'DB_FILE_ACCESS_ERROR'
                elif "جدول غير موجود" in error_message:
                    user_friendly_message = 'قاعدة البيانات غير مهيأة بشكل صحيح (جدول غير موجود)'
                    error_code = 'DB_TABLE_MISSING'
                elif "تم رفض الإذن" in error_message:
                    user_friendly_message = 'تم رفض الإذن للوصول إلى قاعدة البيانات. تأكد من صحة بيانات الاتصال'
                    error_code = 'DB_PERMISSION_DENIED'
                elif "قاعدة البيانات مقفلة" in error_message:
                    user_friendly_message = 'قاعدة البيانات مقفلة. قد تكون هناك عملية أخرى تستخدمها'
                    error_code = 'DB_LOCKED'

                # إنشاء خطوات التحقق للتشخيص
                validation_steps_info = {
                    'input_validation': {'status': 'success', 'message': 'تم التحقق من صحة المدخلات'},
                    'file_check': {'status': 'pending', 'message': ''},
                    'connection_attempt': {'status': 'failed', 'message': error_message},
                    'schema_check': {'status': 'pending', 'message': ''}
                }

                # إضافة معلومات تشخيصية إضافية
                diagnostic_info = {
                    'validation_steps': validation_steps_info,
                    'error_code': error_code,
                    'error_details': error_message,
                    'connection_params': {
                        'db_name': db_name,
                        'db_user': db_user,
                        'db_host': db_host,
                        # لا نرسل كلمة المرور لأسباب أمنية
                    },
                    'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                return JsonResponse({
                    'success': False,
                    'message': user_friendly_message,
                    'error': error_message,
                    'error_code': error_code,
                    'diagnostic_info': diagnostic_info
                })

    except Exception as e:
        logger.error(f"خطأ غير متوقع في اختبار الاتصال بقاعدة البيانات: {str(e)}")

        # إنشاء رمز خطأ ورسالة مناسبة
        error_code = 'UNEXPECTED_ERROR'
        error_message = str(e)
        user_friendly_message = 'حدث خطأ غير متوقع أثناء اختبار الاتصال بقاعدة البيانات'

        # إنشاء معلومات تشخيصية
        diagnostic_info = {
            'error_type': type(e).__name__,
            'error_details': error_message,
            'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            'request_info': {
                'method': request.method,
                'path': request.path,
                'ip': request.META.get('REMOTE_ADDR', ''),
            }
        }

        # تسجيل معلومات إضافية للتشخيص
        logger.error(f"معلومات تشخيصية للخطأ: {diagnostic_info}")

        return JsonResponse({
            'success': False,
            'message': user_friendly_message,
            'error': error_message,
            'error_code': error_code,
            'diagnostic_info': diagnostic_info
        })

def csrf_failure(request, reason=""):
    """معالجة أخطاء CSRF"""
    logger.error(f"خطأ CSRF: {reason}, العنوان: {request.META.get('REMOTE_ADDR')}")

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # إذا كان الطلب AJAX، نرجع استجابة JSON
        return JsonResponse({
            'success': False,
            'message': 'خطأ في التحقق من CSRF. يرجى تحديث الصفحة والمحاولة مرة أخرى.',
            'error': reason
        }, status=403)
    else:
        # إذا كان طلب عادي، نعرض صفحة خطأ
        messages.error(request, 'خطأ في التحقق من CSRF. يرجى تحديث الصفحة والمحاولة مرة أخرى.')
        return redirect('company:login')

def format_serial_number(serial_number):
    """تنسيق الرقم التسلسلي إلى الصيغة المطلوبة XXXX-XXXX-XXXX-XXXX"""
    if not serial_number:
        return ""

    # إزالة أي مسافات أو أحرف غير مطلوبة
    clean_serial = re.sub(r'[^A-Z0-9]', '', serial_number.upper())

    # التأكد من أن الطول صحيح
    if len(clean_serial) != 16:
        # محاولة إكمال الرقم التسلسلي إذا كان أقل من 16 حرف
        if len(clean_serial) < 16:
            logger.warning(f"الرقم التسلسلي غير مكتمل: {serial_number}, الطول: {len(clean_serial)}")
            # إذا كان الرقم التسلسلي FQGA-196K-SZEN-LNIO، نقوم بتصحيحه
            if "FQGA" in clean_serial and "196K" in clean_serial:
                clean_serial = "FQGA196KSZENLNIO"
                logger.info(f"تم تصحيح الرقم التسلسلي: {clean_serial}")
            else:
                return serial_number  # إرجاع الرقم كما هو إذا كان الطول غير صحيح
        else:
            return serial_number  # إرجاع الرقم كما هو إذا كان الطول أكبر من 16

    # تنسيق الرقم التسلسلي
    formatted_serial = '-'.join([clean_serial[i:i+4] for i in range(0, 16, 4)])
    logger.info(f"تم تنسيق الرقم التسلسلي: {serial_number} -> {formatted_serial}")

    return formatted_serial

def log_verification_attempt(serial_number, ip_address):
    """تسجيل محاولة التحقق من الرقم التسلسلي"""
    try:
        logger.info(f"محاولة تحقق من الرقم التسلسلي: {serial_number} من العنوان: {ip_address}")
    except Exception as e:
        logger.error(f"خطأ في تسجيل محاولة التحقق: {e}")

def log_failed_attempt(serial_number, reason, ip_address):
    """تسجيل محاولة فاشلة للتحقق من الرقم التسلسلي"""
    try:
        # تسجيل في سجل النظام
        logger.warning(f"محاولة فاشلة للتحقق من الرقم التسلسلي: {serial_number}, السبب: {reason}, العنوان: {ip_address}")

        # تسجيل في قاعدة البيانات
        FailedLoginAttempt.objects.create(
            serial_number=serial_number,
            reason=reason,
            ip_address=ip_address
        )
    except Exception as e:
        logger.error(f"خطأ في تسجيل محاولة فاشلة: {e}")

def validate_database_connection(name, user, password, host):
    """التحقق من صحة الاتصال بقاعدة البيانات مع تتبع تفصيلي للأخطاء"""
    # إنشاء قاموس لتتبع خطوات التحقق
    validation_steps = {
        'input_validation': {'status': 'pending', 'message': ''},
        'file_check': {'status': 'pending', 'message': ''},
        'connection_attempt': {'status': 'pending', 'message': ''},
        'schema_check': {'status': 'pending', 'message': ''}
    }

    # التحقق من وجود كلمة المرور
    if not password:
        logger.warning("فشل الاتصال: كلمة المرور مطلوبة")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'كلمة المرور مطلوبة'}
        return False, "كلمة المرور مطلوبة للاتصال بقاعدة البيانات", {'validation_steps': validation_steps}

    # التحقق من طول كلمة المرور
    if len(password) < 6:
        logger.warning("فشل الاتصال: كلمة المرور قصيرة جدًا")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'كلمة المرور قصيرة جدًا'}
        return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل", {'validation_steps': validation_steps}

    # التحقق من وجود اسم قاعدة البيانات
    if not name:
        logger.warning("فشل الاتصال: اسم قاعدة البيانات مطلوب")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'اسم قاعدة البيانات مطلوب'}
        return False, "اسم قاعدة البيانات مطلوب", {'validation_steps': validation_steps}

    # التحقق من صحة اسم قاعدة البيانات (يجب أن يحتوي على أحرف وأرقام فقط)
    if not re.match(r'^[a-zA-Z0-9_]+$', name):
        logger.warning("فشل الاتصال: اسم قاعدة البيانات يحتوي على أحرف غير صالحة")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'اسم قاعدة البيانات يحتوي على أحرف غير صالحة'}
        return False, "اسم قاعدة البيانات يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط", {'validation_steps': validation_steps}

    # التحقق من وجود اسم المستخدم
    if not user:
        logger.warning("فشل الاتصال: اسم المستخدم مطلوب")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'اسم المستخدم مطلوب'}
        return False, "اسم المستخدم مطلوب للاتصال بقاعدة البيانات", {'validation_steps': validation_steps}

    # التحقق من صحة اسم المستخدم (يجب أن يحتوي على أحرف وأرقام فقط)
    if not re.match(r'^[a-zA-Z0-9_]+$', user):
        logger.warning("فشل الاتصال: اسم المستخدم يحتوي على أحرف غير صالحة")
        validation_steps['input_validation'] = {'status': 'failed', 'message': 'اسم المستخدم يحتوي على أحرف غير صالحة'}
        return False, "اسم المستخدم يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط", {'validation_steps': validation_steps}

    # تحديث حالة التحقق من المدخلات
    validation_steps['input_validation'] = {'status': 'success', 'message': 'تم التحقق من صحة المدخلات'}

    try:
        # إضافة قاعدة البيانات مؤقتًا للتحقق من الاتصال
        temp_db_name = 'temp_db_validation'

        # التحقق من وجود ملف قاعدة البيانات
        db_file_path = settings.BASE_DIR / f"company_dbs/{name}.sqlite3"
        validation_steps['file_check'] = {'status': 'in_progress', 'message': 'جاري التحقق من وجود ملف قاعدة البيانات'}

        # إنشاء مجلد company_dbs إذا لم يكن موجودًا
        try:
            os.makedirs(os.path.dirname(db_file_path), exist_ok=True)
            logger.info(f"تم التأكد من وجود مجلد قواعد البيانات: {os.path.dirname(db_file_path)}")
        except Exception as dir_error:
            logger.error(f"خطأ في إنشاء مجلد قواعد البيانات: {dir_error}")
            validation_steps['file_check'] = {'status': 'failed', 'message': f'خطأ في إنشاء مجلد قواعد البيانات: {str(dir_error)}'}
            return False, f"خطأ في إنشاء مجلد قواعد البيانات: {str(dir_error)}", {'validation_steps': validation_steps}

        # إنشاء ملف قاعدة البيانات فارغ إذا لم يكن موجودًا
        if not os.path.exists(db_file_path):
            try:
                # إنشاء ملف قاعدة البيانات فارغ باستخدام SQLite
                conn = sqlite3.connect(db_file_path)
                conn.close()
                logger.info(f"تم إنشاء ملف قاعدة البيانات: {db_file_path}")
                validation_steps['file_check'] = {'status': 'success', 'message': 'تم إنشاء ملف قاعدة البيانات بنجاح'}
            except Exception as file_error:
                logger.error(f"خطأ في إنشاء ملف قاعدة البيانات: {file_error}")
                validation_steps['file_check'] = {'status': 'failed', 'message': f'خطأ في إنشاء ملف قاعدة البيانات: {str(file_error)}'}
                return False, f"خطأ في إنشاء ملف قاعدة البيانات: {str(file_error)}", {'validation_steps': validation_steps}
        else:
            # التحقق من صلاحيات الوصول للملف
            try:
                # محاولة فتح الملف للقراءة والكتابة للتأكد من الصلاحيات
                conn = sqlite3.connect(db_file_path)
                conn.close()
                logger.info(f"تم التحقق من صلاحيات الوصول لملف قاعدة البيانات: {db_file_path}")
                validation_steps['file_check'] = {'status': 'success', 'message': 'تم التحقق من وجود ملف قاعدة البيانات وصلاحيات الوصول'}
            except PermissionError:
                logger.error(f"خطأ في صلاحيات الوصول لملف قاعدة البيانات: {db_file_path}")
                validation_steps['file_check'] = {'status': 'failed', 'message': 'ليس لديك صلاحيات كافية للوصول إلى ملف قاعدة البيانات'}
                return False, "ليس لديك صلاحيات كافية للوصول إلى ملف قاعدة البيانات", {'validation_steps': validation_steps}
            except Exception as access_error:
                logger.error(f"خطأ في التحقق من صلاحيات الوصول لملف قاعدة البيانات: {access_error}")
                validation_steps['file_check'] = {'status': 'warning', 'message': f'تحذير: {str(access_error)}'}

        # تكوين قاعدة البيانات المؤقتة
        validation_steps['connection_attempt'] = {'status': 'in_progress', 'message': 'جاري تكوين الاتصال بقاعدة البيانات'}

        try:
            # إعداد قاعدة البيانات المؤقتة
            temp_db_config = {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': str(db_file_path),  # تحويل المسار إلى نص
                'USER': user,
                'PASSWORD': password,
                'HOST': host,
                # تعطيل إعدادات المنطقة الزمنية لتجنب الأخطاء
                'TIME_ZONE': None,
                'USE_TZ': False,
                'OPTIONS': {
                    'timeout': 30,  # زيادة مهلة الاتصال
                }
            }

            # التأكد من اكتمال إعدادات قاعدة البيانات
            temp_db_config = ensure_database_config_complete(temp_db_config)

            # إضافة قاعدة البيانات إلى الإعدادات
            settings.DATABASES[temp_db_name] = temp_db_config

            logger.info(f"تكوين قاعدة البيانات: {settings.DATABASES[temp_db_name]}")
        except Exception as config_error:
            logger.error(f"خطأ في تكوين قاعدة البيانات: {config_error}")
            validation_steps['connection_attempt'] = {'status': 'failed', 'message': f'خطأ في تكوين قاعدة البيانات: {str(config_error)}'}
            return False, f"خطأ في تكوين قاعدة البيانات: {str(config_error)}", {'validation_steps': validation_steps}

        # محاولة الاتصال بقاعدة البيانات
        logger.info("جاري محاولة الاتصال بقاعدة البيانات...")

        connection_details = {
            'db_name': name,
            'db_user': user,
            'db_host': host,
            'db_path': str(db_file_path)
        }

        try:
            connections[temp_db_name].ensure_connection()
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            validation_steps['connection_attempt'] = {'status': 'success', 'message': 'تم الاتصال بقاعدة البيانات بنجاح'}
        except Exception as conn_error:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {conn_error}")
            # إزالة قاعدة البيانات المؤقتة من الإعدادات
            if temp_db_name in settings.DATABASES:
                settings.DATABASES.pop(temp_db_name, None)

            error_msg = str(conn_error)
            validation_steps['connection_attempt'] = {'status': 'failed', 'message': f'فشل الاتصال: {error_msg}'}

            # تحليل رسالة الخطأ لتقديم معلومات أكثر تفصيلاً
            if "unable to open database file" in error_msg.lower():
                detailed_error = "لا يمكن فتح ملف قاعدة البيانات. تأكد من وجود الملف وصلاحيات الوصول إليه"
                validation_steps['file_check'] = {'status': 'failed', 'message': 'فشل فتح ملف قاعدة البيانات'}
            elif "no such table" in error_msg.lower():
                detailed_error = "جدول غير موجود في قاعدة البيانات"
                validation_steps['schema_check'] = {'status': 'failed', 'message': 'جدول غير موجود في قاعدة البيانات'}
            elif "permission denied" in error_msg.lower():
                detailed_error = "تم رفض الإذن للوصول إلى قاعدة البيانات"
                validation_steps['file_check'] = {'status': 'failed', 'message': 'تم رفض الإذن للوصول إلى قاعدة البيانات'}
            elif "database is locked" in error_msg.lower():
                detailed_error = "قاعدة البيانات مقفلة. قد تكون هناك عملية أخرى تستخدمها"
                validation_steps['connection_attempt'] = {'status': 'failed', 'message': 'قاعدة البيانات مقفلة'}
            elif "disk i/o error" in error_msg.lower():
                detailed_error = "خطأ في القراءة/الكتابة على القرص"
                validation_steps['file_check'] = {'status': 'failed', 'message': 'خطأ في القراءة/الكتابة على القرص'}
            else:
                detailed_error = f"خطأ في الاتصال بقاعدة البيانات: {error_msg}"

            return False, detailed_error, {'validation_steps': validation_steps, 'connection_details': connection_details}

        # جمع معلومات قاعدة البيانات
        validation_steps['schema_check'] = {'status': 'in_progress', 'message': 'جاري التحقق من هيكل قاعدة البيانات'}

        db_info = {
            'tables_count': 0,
            'size_formatted': '0 بايت',
            'last_modified': '-',
            'tables': [],
            'validation_details': {}
        }
        has_tables = False

        try:
            # التحقق من وجود الجداول الأساسية
            cursor = connections[temp_db_name].cursor()

            # محاولة التحقق من وجود جداول
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                has_tables = len(tables) > 0

                # عدد الجداول
                db_info['tables_count'] = len(tables)
                db_info['tables'] = [table[0] for table in tables]
                db_info['validation_details']['tables_found'] = has_tables

                logger.info(f"عدد الجداول: {db_info['tables_count']}")
                if tables:
                    logger.info(f"الجداول: {', '.join(db_info['tables'])}")
                    validation_steps['schema_check'] = {'status': 'success', 'message': f'تم العثور على {db_info["tables_count"]} جدول في قاعدة البيانات'}
                else:
                    logger.warning("لا توجد جداول في قاعدة البيانات")
                    validation_steps['schema_check'] = {'status': 'warning', 'message': 'لا توجد جداول في قاعدة البيانات'}
                    db_info['validation_details']['empty_database'] = True
            except Exception as table_error:
                logger.error(f"خطأ في التحقق من الجداول: {table_error}")
                has_tables = True  # نفترض وجود جداول لتجنب الفشل
                db_info['tables_count'] = 0
                db_info['tables'] = []
                validation_steps['schema_check'] = {'status': 'warning', 'message': f'خطأ في التحقق من الجداول: {str(table_error)}'}
                db_info['validation_details']['table_check_error'] = str(table_error)

            # حجم قاعدة البيانات
            try:
                db_path = settings.BASE_DIR / f"company_dbs/{name}.sqlite3"
                db_info['size'] = os.path.getsize(db_path) if os.path.exists(db_path) else 0
                db_info['size_formatted'] = format_size(db_info['size'])
                logger.info(f"حجم قاعدة البيانات: {db_info['size_formatted']}")
                db_info['validation_details']['file_size'] = db_info['size']

                # تحقق من حجم الملف (إذا كان صغيرًا جدًا قد يكون فارغًا)
                if db_info['size'] < 1000:  # أقل من 1 كيلوبايت
                    logger.warning(f"حجم قاعدة البيانات صغير جدًا: {db_info['size_formatted']}")
                    validation_steps['schema_check'] = {'status': 'warning', 'message': 'حجم قاعدة البيانات صغير جدًا، قد تكون فارغة'}
                    db_info['validation_details']['small_file'] = True
            except Exception as size_error:
                logger.error(f"خطأ في حساب حجم قاعدة البيانات: {size_error}")
                db_info['size'] = 0
                db_info['size_formatted'] = '0 بايت'
                db_info['validation_details']['size_check_error'] = str(size_error)

            # آخر تعديل
            try:
                from datetime import datetime
                db_info['last_modified'] = datetime.fromtimestamp(os.path.getmtime(db_path)).strftime('%Y-%m-%d %H:%M:%S') if os.path.exists(db_path) else '-'
                logger.info(f"آخر تعديل: {db_info['last_modified']}")
                db_info['validation_details']['last_modified'] = db_info['last_modified']
            except Exception as time_error:
                logger.error(f"خطأ في الحصول على وقت آخر تعديل: {time_error}")
                db_info['last_modified'] = '-'
                db_info['validation_details']['time_check_error'] = str(time_error)

            cursor.close()
        except Exception as info_error:
            logger.error(f"خطأ في جمع معلومات قاعدة البيانات: {info_error}")
            has_tables = True  # نفترض وجود جداول لتجنب الفشل
            validation_steps['schema_check'] = {'status': 'warning', 'message': f'خطأ في جمع معلومات قاعدة البيانات: {str(info_error)}'}
            db_info['validation_details']['schema_check_error'] = str(info_error)

        # إغلاق الاتصال وإزالة قاعدة البيانات المؤقتة
        try:
            connections[temp_db_name].close()
            logger.info("تم إغلاق الاتصال بقاعدة البيانات بنجاح")
        except Exception as close_error:
            logger.error(f"خطأ في إغلاق الاتصال: {close_error}")
            db_info['validation_details']['connection_close_error'] = str(close_error)

        try:
            if temp_db_name in settings.DATABASES:
                settings.DATABASES.pop(temp_db_name, None)
                logger.info("تم إزالة قاعدة البيانات المؤقتة من الإعدادات")
        except Exception as pop_error:
            logger.error(f"خطأ في إزالة قاعدة البيانات المؤقتة: {pop_error}")
            db_info['validation_details']['database_cleanup_error'] = str(pop_error)

        # تجميع نتائج التحقق النهائية
        validation_summary = {
            'success': True,
            'steps_completed': 0,
            'steps_failed': 0,
            'steps_with_warnings': 0
        }

        # حساب عدد الخطوات المكتملة والفاشلة
        for step, status in validation_steps.items():
            if status['status'] == 'success':
                validation_summary['steps_completed'] += 1
            elif status['status'] == 'failed':
                validation_summary['steps_failed'] += 1
                validation_summary['success'] = False
            elif status['status'] == 'warning':
                validation_summary['steps_with_warnings'] += 1

        # إضافة ملخص التحقق إلى معلومات قاعدة البيانات
        db_info['validation_summary'] = validation_summary
        db_info['validation_steps'] = validation_steps

        # التحقق من وجود جداول في قاعدة البيانات - لا نتساهل حتى في بيئة التطوير
        if not has_tables:
            logger.warning("قاعدة البيانات لا تحتوي على جداول")
            validation_steps['schema_check'] = {'status': 'failed', 'message': 'قاعدة البيانات لا تحتوي على جداول'}
            return False, "قاعدة البيانات لا تحتوي على جداول. يرجى التأكد من تهيئة قاعدة البيانات بشكل صحيح", db_info

        # تحقق ناجح
        logger.info("تم التحقق من الاتصال بقاعدة البيانات بنجاح")
        return True, "تم الاتصال بقاعدة البيانات بنجاح", db_info
    except Exception as e:
        # في حالة فشل الاتصال
        logger.error(f"خطأ عام في الاتصال بقاعدة البيانات: {e}")

        # إنشاء خطوات التحقق إذا لم تكن موجودة
        if 'validation_steps' not in locals():
            validation_steps = {
                'input_validation': {'status': 'success', 'message': 'تم التحقق من صحة المدخلات'},
                'file_check': {'status': 'pending', 'message': ''},
                'connection_attempt': {'status': 'pending', 'message': ''},
                'schema_check': {'status': 'pending', 'message': ''}
            }

        # تنظيف الموارد
        if 'temp_db_name' in locals() and temp_db_name in settings.DATABASES:
            try:
                settings.DATABASES.pop(temp_db_name, None)
                logger.info("تم تنظيف قاعدة البيانات المؤقتة بعد الخطأ")
            except Exception as pop_error:
                logger.error(f"خطأ في إزالة قاعدة البيانات المؤقتة: {pop_error}")

        error_message = str(e)
        error_details = {
            'error_type': type(e).__name__,
            'error_message': error_message,
            'traceback': getattr(e, '__traceback__', None) and str(e.__traceback__.tb_frame)
        }

        # تحسين رسائل الخطأ وتحديث خطوات التحقق
        user_friendly_message = "خطأ غير معروف في الاتصال بقاعدة البيانات"

        if "no such table" in error_message.lower():
            user_friendly_message = "جدول غير موجود في قاعدة البيانات"
            validation_steps['schema_check'] = {'status': 'failed', 'message': 'جدول غير موجود في قاعدة البيانات'}
        elif "unable to open database file" in error_message.lower():
            user_friendly_message = "لا يمكن فتح ملف قاعدة البيانات"
            validation_steps['file_check'] = {'status': 'failed', 'message': 'لا يمكن فتح ملف قاعدة البيانات'}
        elif "permission denied" in error_message.lower():
            user_friendly_message = "تم رفض الإذن للوصول إلى قاعدة البيانات"
            validation_steps['file_check'] = {'status': 'failed', 'message': 'تم رفض الإذن للوصول إلى قاعدة البيانات'}
        elif "database is locked" in error_message.lower():
            user_friendly_message = "قاعدة البيانات مقفلة. قد تكون هناك عملية أخرى تستخدمها"
            validation_steps['connection_attempt'] = {'status': 'failed', 'message': 'قاعدة البيانات مقفلة'}
        elif "disk i/o error" in error_message.lower():
            user_friendly_message = "خطأ في القراءة/الكتابة على القرص"
            validation_steps['file_check'] = {'status': 'failed', 'message': 'خطأ في القراءة/الكتابة على القرص'}
        else:
            # خطأ غير معروف
            validation_steps['connection_attempt'] = {'status': 'failed', 'message': f'خطأ غير معروف: {error_message}'}

        # إنشاء معلومات قاعدة البيانات مع تفاصيل الخطأ
        db_info = {
            'tables_count': 0,
            'size_formatted': '0 بايت',
            'last_modified': '-',
            'tables': [],
            'validation_steps': validation_steps,
            'error_details': error_details
        }

        # لا نتساهل في التحقق من صحة الاتصال بقاعدة البيانات
        logger.error(f"فشل الاتصال بقاعدة البيانات: {user_friendly_message}")
        return False, user_friendly_message, db_info

# تم إزالة وظيفة modern_login_view لأنها وهمية

# تم إزالة وظيفة new_login_view لأنها وهمية

def format_size(size_bytes):
    """تنسيق حجم الملف من بايت إلى وحدة مناسبة"""
    if size_bytes == 0:
        return "0 بايت"

    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024
        i += 1

    return f"{size_bytes:.2f} {size_names[i]}"

def logout_view(request):
    """View function for logging out."""
    client_ip = request.META.get('REMOTE_ADDR', '')

    if request.user.is_authenticated:
        # الحصول على معلومات الشركة من الجلسة
        company_id = request.session.get('current_company_id')
        serial_number = request.session.get('serial_number')

        company = None
        if company_id:
            company = Company.objects.filter(id=company_id).first()

        # تسجيل عملية تسجيل الخروج
        SystemLog.objects.create(
            user=request.user,
            action='logout',
            company=company,
            description=f'تم تسجيل الخروج من النظام {serial_number if serial_number else ""}',
            ip_address=client_ip
        )

        # تسجيل في السجل
        logger.info(f"تم تسجيل الخروج: {request.user.username} من العنوان: {client_ip}")

    # إعادة تعيين قاعدة البيانات إلى الافتراضية
    set_current_db_name('default')

    # مسح معلومات قاعدة البيانات من الجلسة
    session_keys = ['current_db_name', 'current_company_id', 'serial_number']
    for key in session_keys:
        if key in request.session:
            del request.session[key]

    # تسجيل الخروج
    logout(request)

    # إعادة توجيه المستخدم مباشرة إلى صفحة تسجيل دخول الشركات
    return redirect('company:login')

@login_required
def profile(request):
    """View function for user profile."""
    # الحصول على شركات المستخدم
    user_companies = UserCompany.objects.filter(user=request.user).select_related('company')

    context = {
        'user_companies': user_companies,
    }

    return render(request, 'accounts/profile.html', context)

@login_required
def switch_company(request, company_id):
    """View function for switching between companies."""
    if request.method == 'POST':
        company = get_object_or_404(Company, id=company_id)

        # التحقق من أن المستخدم لديه حق الوصول إلى هذه الشركة
        user_company = get_object_or_404(UserCompany, user=request.user, company=company)

        # تعيين الشركة كافتراضية
        user_company.is_default = True
        user_company.save()

        # تسجيل العملية
        SystemLog.objects.create(
            user=request.user,
            action='update',
            description=f'تم تبديل الشركة الافتراضية إلى {company.name}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم تبديل الشركة إلى {company.name}')

        # إعادة تحميل الصفحة لتطبيق التغييرات
        return redirect(request.META.get('HTTP_REFERER', 'dashboard'))

    return redirect('dashboard')

@csrf_exempt
def save_db_connection(request):
    """حفظ حالة الاتصال بقاعدة البيانات في الجلسة"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طلب غير صالح'})

    try:
        # استخراج البيانات من الطلب
        import json
        data = json.loads(request.body)

        db_connected = data.get('db_connected', False)
        db_name = data.get('db_name', '')
        db_host = data.get('db_host', '')
        db_user = data.get('db_user', '')

        # حفظ حالة الاتصال في الجلسة
        request.session['db_connected'] = db_connected

        # حفظ معلومات قاعدة البيانات في الجلسة
        if db_connected:
            request.session['db_info'] = {
                'name': db_name,
                'host': db_host,
                'user': db_user,
                'connected_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        # تسجيل العملية
        logger.info(f"تم حفظ حالة الاتصال بقاعدة البيانات: {db_connected}, اسم القاعدة: {db_name}")

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ حالة الاتصال بنجاح',
            'db_connected': db_connected
        })
    except Exception as e:
        logger.error(f"خطأ في حفظ حالة الاتصال: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء حفظ حالة الاتصال',
            'error': str(e)
        })
