"""
خدمة البريد الإلكتروني المحسنة لإرسال رموز المصادقة الثنائية
"""
import smtplib
import ssl
import random
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)

class EmailService:
    """خدمة إرسال البريد الإلكتروني"""
    
    def __init__(self):
        self.smtp_server = getattr(settings, 'EMAIL_HOST', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'EMAIL_PORT', 587)
        self.email_user = getattr(settings, 'EMAIL_HOST_USER', '')
        self.email_password = getattr(settings, 'EMAIL_HOST_PASSWORD', '')
        self.use_tls = getattr(settings, 'EMAIL_USE_TLS', True)
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', self.email_user)
    
    def generate_verification_code(self, length=6):
        """توليد رمز التحقق"""
        return ''.join(random.choices(string.digits, k=length))
    
    def send_verification_code(self, email, code, user_type='super_admin'):
        """إرسال رمز التحقق عبر البريد الإلكتروني"""
        try:
            # إعداد الموضوع والمحتوى
            if user_type == 'super_admin':
                subject = '🔐 رمز المصادقة الثنائية - المسؤول الأعلى'
                template_name = 'emails/super_admin_verification.html'
            else:
                subject = '🔐 رمز المصادقة الثنائية - منصة استقدامي'
                template_name = 'emails/verification_code.html'
            
            # إعداد السياق
            context = {
                'verification_code': code,
                'user_type': user_type,
                'platform_name': 'منصة استقدامي',
                'expiry_minutes': 1,
            }
            
            # إنشاء المحتوى
            html_content = render_to_string(template_name, context)
            text_content = strip_tags(html_content)
            
            # إرسال البريد
            success = send_mail(
                subject=subject,
                message=text_content,
                from_email=self.from_email,
                recipient_list=[email],
                html_message=html_content,
                fail_silently=False
            )
            
            if success:
                logger.info(f"تم إرسال رمز التحقق بنجاح إلى {email}")
                return True
            else:
                logger.error(f"فشل في إرسال رمز التحقق إلى {email}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
            return False
    
    def send_verification_code_smtp(self, email, code, user_type='super_admin'):
        """إرسال رمز التحقق باستخدام SMTP مباشرة"""
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = '🔐 رمز المصادقة الثنائية - منصة استقدامي'
            msg['From'] = self.from_email
            msg['To'] = email
            
            # المحتوى النصي
            text_content = f"""
            مرحباً،
            
            رمز المصادقة الثنائية الخاص بك هو: {code}
            
            هذا الرمز صالح لمدة دقيقة واحدة فقط.
            
            إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة.
            
            منصة استقدامي
            نظام أمان متقدم
            """
            
            # المحتوى HTML
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>رمز المصادقة الثنائية</title>
                <style>
                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        direction: rtl;
                        text-align: right;
                        background-color: #f8fafc;
                        margin: 0;
                        padding: 20px;
                    }}
                    .container {{
                        max-width: 600px;
                        margin: 0 auto;
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        overflow: hidden;
                    }}
                    .header {{
                        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }}
                    .content {{
                        padding: 30px;
                    }}
                    .code-box {{
                        background: #f1f5f9;
                        border: 2px solid #3b82f6;
                        border-radius: 8px;
                        padding: 20px;
                        text-align: center;
                        margin: 20px 0;
                    }}
                    .code {{
                        font-size: 32px;
                        font-weight: bold;
                        color: #1d4ed8;
                        letter-spacing: 8px;
                        font-family: monospace;
                    }}
                    .warning {{
                        background: #fef3c7;
                        border: 1px solid #f59e0b;
                        border-radius: 6px;
                        padding: 15px;
                        margin: 20px 0;
                        color: #92400e;
                    }}
                    .footer {{
                        background: #f8fafc;
                        padding: 20px;
                        text-align: center;
                        color: #64748b;
                        font-size: 14px;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔐 رمز المصادقة الثنائية</h1>
                        <p>منصة استقدامي - نظام أمان متقدم</p>
                    </div>
                    
                    <div class="content">
                        <h2>مرحباً،</h2>
                        <p>تم طلب رمز المصادقة الثنائية لحسابك في منصة استقدامي.</p>
                        
                        <div class="code-box">
                            <p style="margin: 0; font-size: 16px; color: #64748b;">رمز التحقق الخاص بك:</p>
                            <div class="code">{code}</div>
                        </div>
                        
                        <div class="warning">
                            <strong>⚠️ تحذير أمني:</strong>
                            <ul style="margin: 10px 0;">
                                <li>هذا الرمز صالح لمدة دقيقة واحدة فقط</li>
                                <li>لا تشارك هذا الرمز مع أي شخص</li>
                                <li>إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة</li>
                            </ul>
                        </div>
                        
                        <p>شكراً لاستخدامك منصة استقدامي.</p>
                    </div>
                    
                    <div class="footer">
                        <p>&copy; 2025 منصة استقدامي - جميع الحقوق محفوظة</p>
                        <p>نظام إدارة محمي | الإصدار 2.1.0</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # إضافة المحتوى للرسالة
            part1 = MIMEText(text_content, 'plain', 'utf-8')
            part2 = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(part1)
            msg.attach(part2)
            
            # إرسال الرسالة
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email_user, self.email_password)
                server.send_message(msg)
            
            logger.info(f"تم إرسال رمز التحقق بنجاح عبر SMTP إلى {email}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد عبر SMTP: {str(e)}")
            return False
    
    def test_email_connection(self):
        """اختبار الاتصال بخدمة البريد الإلكتروني"""
        try:
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email_user, self.email_password)
                
            logger.info("تم الاتصال بخدمة البريد الإلكتروني بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"فشل في الاتصال بخدمة البريد الإلكتروني: {str(e)}")
            return False

# إنشاء مثيل من الخدمة
email_service = EmailService()

def send_2fa_code(email, user_type='super_admin'):
    """دالة مساعدة لإرسال رمز المصادقة الثنائية"""
    code = email_service.generate_verification_code()
    
    # محاولة الإرسال باستخدام Django أولاً
    success = email_service.send_verification_code(email, code, user_type)
    
    # إذا فشل، جرب SMTP مباشرة
    if not success:
        success = email_service.send_verification_code_smtp(email, code, user_type)
    
    if success:
        return code
    else:
        return None

def test_email_setup():
    """اختبار إعداد البريد الإلكتروني"""
    return email_service.test_email_connection()
