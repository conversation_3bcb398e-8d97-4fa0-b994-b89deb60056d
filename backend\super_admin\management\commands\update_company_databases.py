"""
أمر إدارة Django لتحديث جميع قواعد بيانات الشركات
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from backend.super_admin.models import Company, SystemLog
from backend.super_admin.database_initializer import update_all_company_databases, update_database_schema

class Command(BaseCommand):
    help = 'تحديث هيكل جميع قواعد بيانات الشركات أو قاعدة بيانات شركة محددة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف الشركة المراد تحديث قاعدة بياناتها (اختياري)'
        )
        parser.add_argument(
            '--serial',
            type=str,
            help='الرقم التسلسلي للشركة المراد تحديث قاعدة بياناتها (اختياري)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='تحديث قواعد البيانات حتى للشركات غير النشطة'
        )

    def handle(self, *args, **options):
        start_time = timezone.now()
        self.stdout.write(self.style.SUCCESS(f'بدء تحديث قواعد بيانات الشركات في {start_time}'))

        company_id = options.get('company_id')
        serial = options.get('serial')
        force = options.get('force', False)

        # تحديث قاعدة بيانات شركة محددة
        if company_id or serial:
            try:
                if company_id:
                    company = Company.objects.get(id=company_id)
                    self.stdout.write(f'تحديث قاعدة بيانات الشركة: {company.name} (معرف: {company.id})')
                else:
                    company = Company.objects.get(serial_number=serial)
                    self.stdout.write(f'تحديث قاعدة بيانات الشركة: {company.name} (الرقم التسلسلي: {company.serial_number})')

                # التحقق من حالة الشركة
                if not force and company.status != 'active':
                    self.stdout.write(self.style.WARNING(f'الشركة غير نشطة. استخدم --force لتحديث قاعدة البيانات على أي حال.'))
                    return

                # تحديث قاعدة البيانات
                result = update_database_schema(company)

                if result['success']:
                    self.stdout.write(self.style.SUCCESS(f'تم تحديث قاعدة بيانات الشركة بنجاح: {company.name}'))
                else:
                    self.stdout.write(self.style.ERROR(f'فشل في تحديث قاعدة بيانات الشركة: {company.name}'))
                    self.stdout.write(self.style.ERROR(f'السبب: {result["message"]}'))

                # تسجيل العملية
                SystemLog.objects.create(
                    action='update',
                    company=company,
                    description=f'تم تحديث هيكل قاعدة البيانات عبر أمر الإدارة: {result["message"]}',
                    ip_address='127.0.0.1'  # عنوان IP محلي للأوامر
                )

            except Company.DoesNotExist:
                if company_id:
                    self.stdout.write(self.style.ERROR(f'الشركة غير موجودة: معرف {company_id}'))
                else:
                    self.stdout.write(self.style.ERROR(f'الشركة غير موجودة: الرقم التسلسلي {serial}'))
                return

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'حدث خطأ: {str(e)}'))
                return

        # تحديث جميع قواعد بيانات الشركات
        else:
            self.stdout.write('تحديث جميع قواعد بيانات الشركات...')

            # تحديث قواعد البيانات
            result = update_all_company_databases()

            if result['success']:
                self.stdout.write(self.style.SUCCESS(result['message']))

                # عرض تفاصيل النتائج
                results = result['results']
                self.stdout.write(f'إجمالي الشركات: {results["total"]}')
                self.stdout.write(self.style.SUCCESS(f'تم التحديث بنجاح: {results["success"]}'))

                if results['failed'] > 0:
                    self.stdout.write(self.style.ERROR(f'فشل التحديث: {results["failed"]}'))

                    # عرض تفاصيل الفشل
                    for detail in results['details']:
                        if not detail['success']:
                            self.stdout.write(self.style.ERROR(f'  - {detail["company_name"]}: {detail["message"]}'))
            else:
                self.stdout.write(self.style.ERROR(f'فشل في تحديث قواعد البيانات: {result["message"]}'))

        end_time = timezone.now()
        duration = end_time - start_time
        self.stdout.write(self.style.SUCCESS(f'اكتمل تحديث قواعد البيانات في {end_time}'))
        self.stdout.write(self.style.SUCCESS(f'المدة: {duration}'))
