<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\Models\Company;
use App\Models\CompanyLoginAttempt;
use App\Models\User;
use App\Models\UserCompany;
use App\Services\DatabaseConnectionService;

class CompanyController extends Controller
{
    /**
     * خدمة الاتصال بقواعد البيانات
     *
     * @var DatabaseConnectionService
     */
    protected $databaseService;

    /**
     * إنشاء مثيل جديد من المتحكم
     *
     * @param DatabaseConnectionService $databaseService
     * @return void
     */
    public function __construct(DatabaseConnectionService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * عرض صفحة تسجيل الدخول للشركات
     *
     * @return \Illuminate\View\View
     */
    public function login()
    {
        return view('company.login');
    }

    /**
     * التحقق من قاعدة بيانات الشركة
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyDatabase(Request $request)
    {
        // التحقق من صحة البيانات
        $request->validate([
            'serial_number' => 'required|string|min:16|max:19',
        ]);

        $serialNumber = $request->input('serial_number');
        
        // تنظيف الرقم التسلسلي (إزالة الشرطات والمسافات)
        $cleanSerial = str_replace(['-', ' '], '', $serialNumber);
        
        // التحقق من طول الرقم التسلسلي
        if (strlen($cleanSerial) !== 16) {
            return response()->json([
                'success' => false,
                'message' => 'الرقم التسلسلي يجب أن يتكون من 16 حرف'
            ], 400);
        }
        
        // تنسيق الرقم التسلسلي (إضافة الشرطات)
        $formattedSerial = implode('-', str_split($cleanSerial, 4));
        
        // البحث عن الشركة بالرقم التسلسلي
        $company = Company::where('serial_number', $cleanSerial)
                        ->orWhere('serial_number', $formattedSerial)
                        ->first();
        
        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة'
            ], 404);
        }
        
        // التحقق من حالة الشركة
        if ($company->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'الشركة غير نشطة'
            ], 403);
        }
        
        // اختبار الاتصال بقاعدة البيانات
        $connectionTest = $this->databaseService->testConnection($company);
        
        if (!$connectionTest['success']) {
            // محاولة إعادة تهيئة قاعدة البيانات
            try {
                Log::info("محاولة إعادة تهيئة قاعدة البيانات: {$company->database_name}");
                $initResult = $this->databaseService->initializeDatabase($company);
                
                // إعادة اختبار الاتصال
                $connectionTest = $this->databaseService->testConnection($company);
                
                if (!$connectionTest['success']) {
                    Log::error("فشل الاتصال بقاعدة البيانات بعد إعادة التهيئة: {$connectionTest['message']}");
                    return response()->json([
                        'success' => false,
                        'message' => 'تعذر الاتصال بقاعدة بيانات الشركة. يرجى التواصل مع الدعم الفني.'
                    ], 500);
                }
            } catch (\Exception $e) {
                Log::error("استثناء أثناء تهيئة قاعدة البيانات: {$e->getMessage()}");
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى التواصل مع الدعم الفني.'
                ], 500);
            }
        }
        
        // إعداد اتصال قاعدة البيانات في Laravel
        $setupSuccess = $this->databaseService->setupConnection($company);
        
        if (!$setupSuccess) {
            Log::error("فشل في إعداد اتصال قاعدة البيانات في Laravel: {$company->database_name}");
            return response()->json([
                'success' => false,
                'message' => 'فشل في إعداد اتصال قاعدة البيانات'
            ], 500);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'تم التحقق من قاعدة البيانات بنجاح',
            'company' => [
                'id' => $company->id,
                'name' => $company->name,
                'serial_number' => $company->serial_number,
                'database_name' => $company->database_name,
                'status' => $company->status
            ],
            'database' => [
                'type' => $connectionTest['db_type'] ?? 'unknown',
                'tables_count' => $connectionTest['tables_count'] ?? 0
            ]
        ]);
    }

    /**
     * تسجيل دخول المستخدم إلى الشركة
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function authenticate(Request $request)
    {
        // التحقق من صحة البيانات
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
            'serial_number' => 'required|string',
            'db_verified' => 'required|string',
        ]);
        
        $username = $request->input('username');
        $password = $request->input('password');
        $serialNumber = $request->input('serial_number');
        $dbVerified = $request->input('db_verified');
        $ipAddress = $request->ip();
        
        // التحقق من أن قاعدة البيانات تم التحقق منها
        if ($dbVerified !== 'true') {
            return back()->withErrors([
                'db_verified' => 'يجب التحقق من الرقم التسلسلي وقاعدة البيانات أولاً'
            ])->withInput();
        }
        
        // البحث عن الشركة بالرقم التسلسلي
        $cleanSerial = str_replace(['-', ' '], '', $serialNumber);
        $formattedSerial = implode('-', str_split($cleanSerial, 4));
        
        $company = Company::where('serial_number', $cleanSerial)
                        ->orWhere('serial_number', $formattedSerial)
                        ->first();
        
        if (!$company) {
            // تسجيل محاولة تسجيل دخول فاشلة
            $this->logLoginAttempt($username, $serialNumber, $ipAddress, false);
            
            return back()->withErrors([
                'serial_number' => 'الرقم التسلسلي غير صحيح أو الشركة غير موجودة'
            ])->withInput();
        }
        
        // التحقق من حالة الشركة
        if ($company->status !== 'active') {
            // تسجيل محاولة تسجيل دخول فاشلة
            $this->logLoginAttempt($username, $serialNumber, $ipAddress, false);
            
            return back()->withErrors([
                'serial_number' => 'الشركة غير نشطة'
            ])->withInput();
        }
        
        // إعداد اتصال قاعدة البيانات
        $setupSuccess = $this->databaseService->setupConnection($company);
        
        if (!$setupSuccess) {
            // تسجيل محاولة تسجيل دخول فاشلة
            $this->logLoginAttempt($username, $serialNumber, $ipAddress, false);
            
            return back()->withErrors([
                'db_connection' => 'فشل في الاتصال بقاعدة البيانات'
            ])->withInput();
        }
        
        // التحقق من وجود المستخدم في قاعدة بيانات الشركة
        $userFound = false;
        $userData = null;
        
        try {
            $userData = DB::connection('tenant')
                        ->table('users')
                        ->where('username', $username)
                        ->where('is_active', 1)
                        ->first();
            
            if ($userData && Hash::check($password, $userData->password)) {
                $userFound = true;
                Log::info("تم التحقق من صحة كلمة المرور للمستخدم {$username} في قاعدة بيانات الشركة");
            } elseif ($userData) {
                Log::warning("كلمة مرور غير صحيحة للمستخدم {$username} في قاعدة بيانات الشركة");
            } else {
                Log::warning("المستخدم {$username} غير موجود في قاعدة بيانات الشركة");
            }
        } catch (\Exception $e) {
            Log::error("خطأ في التحقق من المستخدم في قاعدة بيانات الشركة: {$e->getMessage()}");
        }
        
        // إذا لم يتم العثور على المستخدم في قاعدة بيانات الشركة، نحاول المصادقة العادية
        if (!$userFound) {
            if (!Auth::attempt(['username' => $username, 'password' => $password])) {
                // تسجيل محاولة تسجيل دخول فاشلة
                $this->logLoginAttempt($username, $serialNumber, $ipAddress, false);
                
                return back()->withErrors([
                    'username' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ])->withInput();
            }
            
            $user = Auth::user();
        } else {
            // استخراج المستخدم من قاعدة البيانات الرئيسية أو إنشائه إذا لم يكن موجودًا
            $user = User::where('username', $username)->first();
            
            if (!$user) {
                $user = User::create([
                    'username' => $username,
                    'password' => Hash::make($password),
                    'is_staff' => false,
                    'is_superuser' => false,
                    'is_active' => true
                ]);
                
                Log::info("تم إنشاء المستخدم {$username} في قاعدة البيانات الرئيسية");
            }
            
            // تسجيل دخول المستخدم يدويًا
            Auth::login($user);
        }
        
        // التحقق من أن المستخدم ينتمي إلى الشركة
        $userCompany = UserCompany::where('user_id', $user->id)
                                ->where('company_id', $company->id)
                                ->first();
        
        if (!$userCompany) {
            // إنشاء ارتباط بين المستخدم والشركة
            try {
                UserCompany::create([
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                    'is_default' => true
                ]);
                
                Log::info("تم إنشاء ارتباط بين المستخدم {$username} والشركة {$company->name}");
            } catch (\Exception $e) {
                // تسجيل محاولة تسجيل دخول فاشلة
                $this->logLoginAttempt($username, $serialNumber, $ipAddress, false);
                
                Log::error("خطأ في إنشاء ارتباط بين المستخدم والشركة: {$e->getMessage()}");
                
                return back()->withErrors([
                    'user_company' => 'حدث خطأ أثناء التحقق من ارتباط المستخدم بالشركة'
                ])->withInput();
            }
        }
        
        // تخزين معلومات الشركة في الجلسة
        Session::put('company_id', $company->id);
        Session::put('serial_number', $company->serial_number);
        Session::put('company_db', $company->database_name);
        Session::put('current_company_id', $company->id);
        
        // تسجيل محاولة تسجيل دخول ناجحة
        $this->logLoginAttempt($username, $serialNumber, $ipAddress, true);
        
        return redirect()->route('company.dashboard')->with('success', "تم تسجيل الدخول بنجاح إلى {$company->name}");
    }

    /**
     * تسجيل خروج المستخدم
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logout(Request $request)
    {
        // إزالة معلومات الشركة من الجلسة
        Session::forget([
            'company_id',
            'serial_number',
            'company_db',
            'current_company_id'
        ]);
        
        // تسجيل الخروج
        Auth::logout();
        
        // إعادة تعيين الجلسة
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('company.login')->with('success', 'تم تسجيل الخروج بنجاح');
    }

    /**
     * عرض لوحة التحكم
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        // الحصول على إحصائيات لوحة التحكم
        $stats = $this->getDashboardStats();
        
        return view('company.dashboard', [
            'stats' => $stats
        ]);
    }

    /**
     * عرض صفحة خطأ قاعدة البيانات
     *
     * @return \Illuminate\View\View
     */
    public function databaseError()
    {
        return view('company.errors.database_error');
    }

    /**
     * عرض صفحة الشركة غير موجودة
     *
     * @return \Illuminate\View\View
     */
    public function notFound()
    {
        return view('company.errors.not_found');
    }

    /**
     * الحصول على إحصائيات لوحة التحكم
     *
     * @return array
     */
    private function getDashboardStats()
    {
        // هذه الوظيفة تحتاج إلى تنفيذ حسب متطلبات المشروع
        return [
            'workers_count' => 0,
            'clients_count' => 0,
            'contracts_count' => 0,
            'services_count' => 0,
            'bookings_count' => 0,
            'active_bookings_count' => 0
        ];
    }

    /**
     * تسجيل محاولة تسجيل الدخول
     *
     * @param  string  $username
     * @param  string  $serialNumber
     * @param  string  $ipAddress
     * @param  bool  $isSuccessful
     * @return void
     */
    private function logLoginAttempt($username, $serialNumber, $ipAddress, $isSuccessful)
    {
        CompanyLoginAttempt::create([
            'username' => $username,
            'serial_number' => $serialNumber,
            'ip_address' => $ipAddress,
            'user_agent' => request()->userAgent(),
            'is_successful' => $isSuccessful
        ]);
    }
}
