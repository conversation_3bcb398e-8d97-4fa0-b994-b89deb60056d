"""
نظام إرسال البريد الإلكتروني غير المتزامن
لتحسين سرعة الاستجابة وتقليل التأخير
"""

import threading
import time
import logging
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

class AsyncEmailSender:
    """فئة إرسال البريد الإلكتروني غير المتزامن"""
    
    def __init__(self):
        self.email_queue = []
        self.is_sending = False
        
    def send_email_async(self, subject, message, recipient_list, html_message=None, from_email=None):
        """إرسال بريد إلكتروني في الخلفية"""
        
        if from_email is None:
            from_email = settings.DEFAULT_FROM_EMAIL
            
        # إضافة الرسالة إلى قائمة الانتظار
        email_data = {
            'subject': subject,
            'message': message,
            'from_email': from_email,
            'recipient_list': recipient_list,
            'html_message': html_message,
            'timestamp': time.time()
        }
        
        self.email_queue.append(email_data)
        
        # بدء معالجة قائمة الانتظار إذا لم تكن قيد التشغيل
        if not self.is_sending:
            self._start_email_worker()
            
        return True
        
    def _start_email_worker(self):
        """بدء معالج البريد الإلكتروني في خيط منفصل"""
        
        def email_worker():
            self.is_sending = True
            
            while self.email_queue:
                try:
                    email_data = self.email_queue.pop(0)
                    self._send_single_email(email_data)
                    
                    # انتظار قصير بين الرسائل لتجنب الحد الأقصى للمعدل
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
                    
            self.is_sending = False
            
        # تشغيل المعالج في خيط منفصل
        worker_thread = threading.Thread(target=email_worker, daemon=True)
        worker_thread.start()
        
    def _send_single_email(self, email_data):
        """إرسال رسالة واحدة"""
        
        try:
            logger.info(f"بدء إرسال بريد إلكتروني إلى: {email_data['recipient_list']}")
            
            result = send_mail(
                subject=email_data['subject'],
                message=email_data['message'],
                from_email=email_data['from_email'],
                recipient_list=email_data['recipient_list'],
                html_message=email_data['html_message'],
                fail_silently=False,
            )
            
            if result:
                logger.info(f"تم إرسال البريد الإلكتروني بنجاح إلى: {email_data['recipient_list']}")
                
                # تحديث حالة الإرسال في الكاش
                for recipient in email_data['recipient_list']:
                    cache_key = f"email_sent_{recipient}_{int(email_data['timestamp'])}"
                    cache.set(cache_key, {
                        'status': 'sent',
                        'timestamp': time.time(),
                        'subject': email_data['subject']
                    }, timeout=300)  # 5 دقائق
                    
            else:
                logger.warning(f"فشل في إرسال البريد الإلكتروني إلى: {email_data['recipient_list']}")
                
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
            
            # تحديث حالة الفشل في الكاش
            for recipient in email_data['recipient_list']:
                cache_key = f"email_sent_{recipient}_{int(email_data['timestamp'])}"
                cache.set(cache_key, {
                    'status': 'failed',
                    'timestamp': time.time(),
                    'error': str(e)
                }, timeout=300)

# إنشاء مثيل عام للاستخدام
async_email_sender = AsyncEmailSender()

def send_verification_code_async(user, email, verification_code):
    """إرسال رمز التحقق بشكل غير متزامن"""
    
    # إنشاء محتوى الرسالة
    subject = "رمز المصادقة الثنائية لمنصة استقدامي السحابية"
    
    message = f"""
مرحباً {user.username}،

رمز المصادقة الثنائية الخاص بك هو: {verification_code}

هذا الرمز صالح لمدة دقيقة واحدة.

إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة.

شكراً لك،
فريق منصة استقدامي السحابية
    """
    
    html_message = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>رمز المصادقة الثنائية</title>
    </head>
    <body style="font-family: 'Tajawal', Arial, sans-serif; direction: rtl; text-align: right; background-color: #f5f5f5; margin: 0; padding: 20px;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
            
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #174785 0%, #2563eb 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 24px; font-weight: bold;">منصة استقدامي السحابية</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">رمز المصادقة الثنائية</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 40px 30px;">
                <h2 style="color: #174785; margin-top: 0; font-size: 20px;">مرحباً {user.username}،</h2>
                
                <p style="color: #555; line-height: 1.6; margin: 20px 0;">
                    تم طلب رمز المصادقة الثنائية لحسابك. استخدم الرمز التالي لإكمال عملية تسجيل الدخول:
                </p>
                
                <!-- Verification Code -->
                <div style="background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%); border: 2px solid #174785; border-radius: 10px; padding: 25px; text-align: center; margin: 30px 0;">
                    <div style="color: #666; font-size: 14px; margin-bottom: 10px;">رمز التحقق</div>
                    <div style="font-size: 32px; font-weight: bold; color: #174785; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                        {verification_code}
                    </div>
                    <div style="color: #666; font-size: 12px; margin-top: 10px;">صالح لمدة دقيقة واحدة</div>
                </div>
                
                <!-- Security Notice -->
                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <div style="color: #856404; font-size: 14px;">
                        <strong>⚠️ تنبيه أمني:</strong><br>
                        إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة وتأمين حسابك.
                    </div>
                </div>
                
                <p style="color: #555; line-height: 1.6; margin: 20px 0;">
                    شكراً لاستخدامك منصة استقدامي السحابية.
                </p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px 30px; border-top: 1px solid #e9ecef;">
                <p style="margin: 0; color: #666; font-size: 12px; text-align: center;">
                    هذه رسالة تلقائية، يرجى عدم الرد عليها.<br>
                    © 2025 منصة استقدامي السحابية - جميع الحقوق محفوظة
                </p>
            </div>
            
        </div>
    </body>
    </html>
    """
    
    # إرسال الرسالة بشكل غير متزامن
    timestamp = time.time()
    
    # حفظ معلومات الإرسال في الكاش للمتابعة
    cache_key = f"email_sending_{email}_{int(timestamp)}"
    cache.set(cache_key, {
        'status': 'sending',
        'timestamp': timestamp,
        'user_id': user.id,
        'verification_code': verification_code
    }, timeout=300)
    
    # إرسال الرسالة
    success = async_email_sender.send_email_async(
        subject=subject,
        message=message,
        recipient_list=[email],
        html_message=html_message
    )
    
    if success:
        logger.info(f"تم إضافة رسالة التحقق إلى قائمة الإرسال للمستخدم: {user.username}")
        return True, verification_code, timestamp
    else:
        logger.error(f"فشل في إضافة رسالة التحقق إلى قائمة الإرسال للمستخدم: {user.username}")
        return False, None, None

def check_email_status(email, timestamp):
    """فحص حالة إرسال البريد الإلكتروني"""
    
    cache_key = f"email_sent_{email}_{int(timestamp)}"
    status = cache.get(cache_key)
    
    if status:
        return status
    else:
        # إذا لم توجد حالة، فالرسالة ما زالت قيد الإرسال
        return {'status': 'sending', 'timestamp': timestamp}
