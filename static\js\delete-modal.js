/**
 * منصة استقدامي السحابية - وظائف نوافذ تأكيد الحذف
 *
 * هذا الملف يحتوي على وظائف التعامل مع نوافذ تأكيد الحذف
 * يستخدم Alpine.js لإدارة حالة النافذة المنبثقة
 *
 * طريقة الاستخدام:
 * 1. استخدام الدالة deleteItem(url) لفتح نافذة تأكيد الحذف
 * 2. أو استخدام الدالة customizeDeleteModal لتخصيص نافذة التأكيد
 */

/**
 * دالة عامة لفتح نافذة تأكيد الحذف
 * @param {string} url - رابط الحذف
 */
window.deleteItem = function(url) {
    console.log('deleteItem called with URL:', url);
    const event = new CustomEvent('open-delete', {
        detail: url
    });
    window.dispatchEvent(event);
};

/**
 * فتح نافذة تأكيد الحذف (للتوافق مع الكود القديم)
 *
 * @param {string} deleteUrl - رابط الحذف
 * @param {string} itemName - اسم العنصر المراد حذفه
 * @returns {boolean} - إذا تم تأكيد الحذف أم لا
 */
function openDeleteModal(deleteUrl, itemName) {
    // استخدام نظام التأكيد الجديد
    window.deleteItem(deleteUrl);
    return true;
}

/**
 * تهيئة نظام تأكيد الحذف الموحد
 * يتم استدعاؤها تلقائيًا عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Delete modal script loaded');

    // استبدال window.confirm الأصلية بنظام التأكيد الموحد
    const originalConfirm = window.confirm;
    window.confirm = function(message) {
        console.log('Confirm intercepted with message:', message);

        // إذا كانت الرسالة تتعلق بالحذف، استخدم نظام التأكيد الموحد
        if (message.includes('حذف') || message.includes('delete') || message.includes('متأكد')) {
            // استخراج URL الحذف من الصفحة الحالية
            let deleteUrl = window.location.pathname;

            // إذا كان هناك نموذج حذف نشط، استخدم عنوان الإرسال الخاص به
            const activeForm = document.activeElement.closest('form');
            if (activeForm && activeForm.getAttribute('action')) {
                deleteUrl = activeForm.getAttribute('action');
            }

            // استخدام نظام التأكيد الموحد
            window.customizeDeleteModal(
                deleteUrl,
                'هل أنت متأكد من الحذف؟',
                message,
                'fa-trash-alt',
                'text-red-600 dark:text-red-400',
                'تأكيد الحذف',
                'bg-red-600 hover:bg-red-700'
            );

            // إرجاع true دائمًا لمنع ظهور مربع التأكيد الأصلي
            return true;
        }

        // استخدام confirm الأصلية للرسائل غير المتعلقة بالحذف
        return originalConfirm(message);
    };

    // إضافة مستمع لأحداث إرسال نموذج الحذف
    document.addEventListener('submit-delete-form', function(event) {
        const deleteUrl = event.detail;

        // الحصول على رمز CSRF من الصفحة
        let csrfToken = '';

        // محاولة الحصول على رمز CSRF من عنصر meta
        const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (metaToken) {
            csrfToken = metaToken;
        } else {
            // محاولة الحصول على رمز CSRF من عنصر input
            const inputToken = document.querySelector('input[name="csrfmiddlewaretoken"]')?.value;
            if (inputToken) {
                csrfToken = inputToken;
            } else {
                // محاولة الحصول على رمز CSRF من كوكيز
                const cookieValue = document.cookie
                    .split('; ')
                    .find(row => row.startsWith('csrftoken='))
                    ?.split('=')[1];
                if (cookieValue) {
                    csrfToken = cookieValue;
                }
            }
        }

        console.log('CSRF Token:', csrfToken);

        // إظهار مؤشر التحميل
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'fixed inset-0 bg-black/30 flex items-center justify-center z-[9999]';
        loadingIndicator.innerHTML = `
            <div class="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl flex items-center">
                <div class="animate-spin mr-3">
                    <i class="fas fa-circle-notch text-blue-600 text-xl"></i>
                </div>
                <span class="text-gray-800 dark:text-white">جاري الحذف...</span>
            </div>
        `;
        document.body.appendChild(loadingIndicator);

        // إنشاء نموذج وإرساله
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        form.style.display = 'none';

        // إضافة رمز CSRF
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);

        // إزالة مؤشر التحميل بعد تحميل الصفحة الجديدة
        window.addEventListener('beforeunload', function() {
            if (document.body.contains(loadingIndicator)) {
                document.body.removeChild(loadingIndicator);
            }
        });

        // إرسال النموذج
        form.submit();
    });

    // تخصيص نص نافذة التأكيد بناءً على نوع العملية
    window.customizeDeleteModal = function(url, title, message, icon, iconColor, confirmText, confirmColor) {
        // إرسال حدث لفتح نافذة التأكيد مع البيانات المخصصة
        const event = new CustomEvent('open-delete-custom', {
            detail: {
                url: url,
                title: title || 'هل أنت متأكد من الحذف؟',
                message: message || 'لن تتمكن من التراجع بعد تنفيذ الحذف.',
                icon: icon || 'fa-trash-alt',
                iconColor: iconColor || 'text-red-600 dark:text-red-400',
                confirmText: confirmText || 'تأكيد الحذف',
                confirmColor: confirmColor || 'bg-red-600 hover:bg-red-700'
            }
        });
        window.dispatchEvent(event);
    };

    // تحويل جميع أزرار الحذف التقليدية إلى أزرار تستخدم نظام التأكيد الموحد
    convertDeleteButtons();
});

/**
 * تحويل جميع أزرار الحذف التقليدية إلى أزرار تستخدم نظام التأكيد الموحد
 * يتم البحث عن الأزرار التي تحتوي على خصائص data-delete-url أو href يحتوي على /delete/
 */
function convertDeleteButtons() {
    console.log('Converting delete buttons...');

    // 1. تحويل الأزرار التي تحتوي على خاصية data-delete-url
    const dataDeleteButtons = document.querySelectorAll('[data-delete-url]');
    dataDeleteButtons.forEach(button => {
        const deleteUrl = button.getAttribute('data-delete-url');
        if (deleteUrl) {
            console.log('Found button with data-delete-url:', deleteUrl);

            // إزالة أي مستمعي أحداث سابقة
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // إضافة مستمع جديد
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.deleteItem(deleteUrl);
            });
        }
    });

    // 2. تحويل الروابط التي تحتوي على /delete/ في عنوانها
    const deleteLinks = document.querySelectorAll('a[href*="/delete/"]');
    deleteLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && !link.classList.contains('no-confirm')) {
            console.log('Found link with delete in href:', href);

            // إزالة أي مستمعي أحداث سابقة
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);

            // إضافة مستمع جديد
            newLink.addEventListener('click', function(e) {
                e.preventDefault();
                window.deleteItem(href);
            });
        }
    });

    // 3. تحويل الأزرار التي تحتوي على onclick يحتوي على confirm
    const confirmButtons = document.querySelectorAll('button[onclick*="confirm"], a[onclick*="confirm"]');
    confirmButtons.forEach(button => {
        const onclickAttr = button.getAttribute('onclick');
        if (onclickAttr) {
            // استخراج URL الحذف من onclick
            const match = onclickAttr.match(/window\.location\.href\s*=\s*['"]([^'"]*)['"]/);
            if (match && match[1]) {
                const deleteUrl = match[1];
                console.log('Found button with confirm in onclick, URL:', deleteUrl);

                // إزالة أي مستمعي أحداث سابقة
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // إزالة خاصية onclick
                newButton.removeAttribute('onclick');

                // إضافة مستمع جديد
                newButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.deleteItem(deleteUrl);
                });
            }
        }
    });

    // 4. تحويل النماذج التي تحتوي على onsubmit مع confirm
    const confirmForms = document.querySelectorAll('form[onsubmit*="confirm"]');
    confirmForms.forEach(form => {
        const onsubmitAttr = form.getAttribute('onsubmit');
        const actionUrl = form.getAttribute('action');

        if (onsubmitAttr && actionUrl) {
            console.log('Found form with confirm in onsubmit, URL:', actionUrl);

            // إزالة خاصية onsubmit
            form.removeAttribute('onsubmit');

            // إضافة مستمع جديد
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                window.deleteItem(actionUrl);
            });
        }
    });

    // 5. تحويل الأزرار التي تحتوي على كلمة حذف في النص
    const deleteTextButtons = document.querySelectorAll('button:not([onclick]):not([data-delete-url]), a:not([onclick]):not([href*="/delete/"]):not([data-delete-url])');
    deleteTextButtons.forEach(button => {
        const buttonText = button.textContent.toLowerCase();
        if (buttonText.includes('حذف') || buttonText.includes('delete')) {
            // البحث عن أقرب عنصر tr أو div يحتوي على معرف
            let parent = button.closest('tr, div[data-id]');
            if (parent) {
                const itemId = parent.getAttribute('data-id');
                const itemType = parent.getAttribute('data-type') || '';

                if (itemId) {
                    // محاولة بناء URL الحذف بناءً على نوع العنصر
                    let deleteUrl = '';

                    if (window.location.pathname.includes('/workers/')) {
                        deleteUrl = `/workers/${itemId}/delete/`;
                    } else if (window.location.pathname.includes('/clients/')) {
                        deleteUrl = `/clients/${itemId}/delete/`;
                    } else if (window.location.pathname.includes('/contracts/')) {
                        deleteUrl = `/contracts/${itemId}/delete/`;
                    } else if (window.location.pathname.includes('/documents/')) {
                        deleteUrl = `/documents/${itemId}/delete/`;
                    } else if (itemType) {
                        deleteUrl = `/${itemType}/${itemId}/delete/`;
                    }

                    if (deleteUrl) {
                        console.log('Found delete button with text, generated URL:', deleteUrl);

                        // إزالة أي مستمعي أحداث سابقة
                        const newButton = button.cloneNode(true);
                        button.parentNode.replaceChild(newButton, button);

                        // إضافة مستمع جديد
                        newButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.deleteItem(deleteUrl);
                        });
                    }
                }
            }
        }
    });
}
