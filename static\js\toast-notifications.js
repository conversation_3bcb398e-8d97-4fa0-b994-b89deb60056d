/**
 * نظام الإشعارات المحسن
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نظام الإشعارات
    initToastNotifications();
});

/**
 * تهيئة نظام الإشعارات
 */
function initToastNotifications() {
    // إنشاء حاوية الإشعارات إذا لم تكن موجودة
    if (!document.querySelector('.toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // إضافة مستمع لأزرار إغلاق الإشعارات
    document.addEventListener('click', function(e) {
        if (e.target.closest('.toast-close')) {
            const toast = e.target.closest('.toast');
            closeToast(toast);
        }
    });

    // إخفاء الإشعارات تلقائيًا بعد فترة
    document.querySelectorAll('.toast').forEach((toast, index) => {
        setTimeout(() => {
            if (toast && document.body.contains(toast)) {
                closeToast(toast);
            }
        }, 4000 + (index * 500)); // إضافة تأخير متزايد لكل رسالة
    });
}

/**
 * إغلاق الإشعار
 * @param {HTMLElement} toast - عنصر الإشعار
 */
function closeToast(toast) {
    toast.classList.remove('toast-in');
    toast.classList.add('toast-out');

    setTimeout(() => {
        if (toast && document.body.contains(toast)) {
            toast.remove();
        }
    }, 300);
}

/**
 * إظهار إشعار جديد
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع الإشعار (success, error, warning, info)
 * @param {number} duration - مدة ظهور الإشعار بالمللي ثانية (اختياري)
 */
function showToast(message, type = 'info', duration = 4000) {
    // التحقق من وجود حاوية الإشعارات
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // تحديد أيقونة الإشعار حسب النوع
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle"></i>';
            break;
        default:
            icon = '<i class="fas fa-bell"></i>';
    }

    // إنشاء عنصر الإشعار
    const toast = document.createElement('div');
    toast.className = `toast toast-${type} toast-in`;
    toast.innerHTML = `
        <div class="toast-icon">${icon}</div>
        <div class="toast-content">${message}</div>
        <button type="button" class="toast-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة الإشعار إلى الحاوية
    toastContainer.appendChild(toast);

    // إخفاء الإشعار تلقائيًا بعد فترة
    if (duration > 0) {
        setTimeout(() => {
            if (toast && document.body.contains(toast)) {
                closeToast(toast);
            }
        }, duration);
    }

    return toast;
}


