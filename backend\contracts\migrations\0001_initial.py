# Generated by Django 5.2 on 2025-06-02 14:50

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clients', '__first__'),
        ('workers', '0014_worker_daily_rate_worker_hourly_rate'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_number', models.CharField(max_length=50, unique=True, verbose_name='رقم العقد')),
                ('contract_type', models.CharField(choices=[('monthly', 'عقد شهري'), ('permanent', 'عقد دائم'), ('temporary', 'عقد مؤقت'), ('trial', 'عقد تجريبي')], max_length=20, verbose_name='نوع العقد')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('expired', 'منتهي الصلاحية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('trial_period_days', models.IntegerField(default=0, verbose_name='فترة التجربة (أيام)')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة العمولة (%)')),
                ('working_hours_per_day', models.IntegerField(default=8, verbose_name='ساعات العمل اليومية')),
                ('working_days_per_week', models.IntegerField(default=6, verbose_name='أيام العمل الأسبوعية')),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=1.5, max_digits=5, verbose_name='معدل الوقت الإضافي')),
                ('terms_and_conditions', models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='clients.client', verbose_name='العميل')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='workers.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'عقد',
                'verbose_name_plural': 'العقود',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContractDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('contract_copy', 'نسخة العقد'), ('amendment', 'تعديل'), ('termination', 'إنهاء'), ('renewal', 'تجديد'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان المستند')),
                ('file', models.FileField(upload_to='contracts/documents/%Y/%m/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='contracts.contract', verbose_name='العقد')),
            ],
            options={
                'verbose_name': 'مستند عقد',
                'verbose_name_plural': 'مستندات العقود',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='ContractPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_type', models.CharField(choices=[('salary', 'راتب'), ('commission', 'عمولة'), ('bonus', 'مكافأة'), ('overtime', 'وقت إضافي'), ('deduction', 'خصم')], max_length=20, verbose_name='نوع الدفعة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('payment_date', models.DateField(verbose_name='تاريخ الدفع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('paid', 'مدفوع'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='contracts.contract', verbose_name='العقد')),
            ],
            options={
                'verbose_name': 'دفعة عقد',
                'verbose_name_plural': 'دفعات العقود',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='ContractRenewal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('new_start_date', models.DateField(verbose_name='تاريخ البداية الجديد')),
                ('new_end_date', models.DateField(verbose_name='تاريخ النهاية الجديد')),
                ('new_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب الجديد')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='الحالة')),
                ('reason', models.TextField(verbose_name='سبب التجديد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('original_contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='renewals', to='contracts.contract', verbose_name='العقد الأصلي')),
            ],
            options={
                'verbose_name': 'تجديد عقد',
                'verbose_name_plural': 'تجديدات العقود',
                'ordering': ['-created_at'],
            },
        ),
    ]
