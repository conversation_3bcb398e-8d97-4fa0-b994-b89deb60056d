# Generated by Django 5.2 on 2025-05-18 23:23

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workers', '0012_worker_visa_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='worker',
            name='passport_issue_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ إصدار جواز السفر'),
        ),
        migrations.AlterField(
            model_name='worker',
            name='entry_date',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ إصدار التأشيرة / الستيكر'),
        ),
    ]
