from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from .serializers import (
    UserSerializer, UserDetailSerializer, CustomTokenObtainPairSerializer,
    TokenRefreshSerializer, ChangePasswordSerializer
)

User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """
    واجهة برمجة التطبيقات للمستخدمين.
    توفر عمليات CRUD للمستخدمين.
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'retrieve' or self.action == 'me':
            return UserDetailSerializer
        return UserSerializer
    
    def get_permissions(self):
        if self.action == 'create':
            return [permissions.IsAdminUser()]
        return super().get_permissions()
    
    @action(detail=False, methods=['get', 'put', 'patch'], url_path='me')
    def me(self, request):
        """
        الحصول على أو تحديث معلومات المستخدم الحالي.
        """
        user = request.user
        
        if request.method == 'GET':
            serializer = self.get_serializer(user)
            return Response(serializer.data)
        
        serializer = self.get_serializer(user, data=request.data, partial=request.method == 'PATCH')
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'], url_path='change-password')
    def change_password(self, request):
        """
        تغيير كلمة مرور المستخدم الحالي.
        """
        user = request.user
        serializer = ChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # التحقق من كلمة المرور القديمة
        if not user.check_password(serializer.validated_data['old_password']):
            return Response(
                {'old_password': _('كلمة المرور غير صحيحة')},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # تعيين كلمة المرور الجديدة
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return Response({'detail': _('تم تغيير كلمة المرور بنجاح')})


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    واجهة برمجة التطبيقات للحصول على رمز JWT.
    تتطلب اسم المستخدم وكلمة المرور والرقم التسلسلي.
    """
    serializer_class = CustomTokenObtainPairSerializer


class CustomTokenRefreshView(TokenRefreshView):
    """
    واجهة برمجة التطبيقات لتحديث رمز JWT.
    """
    serializer_class = TokenRefreshSerializer


class LogoutView(viewsets.ViewSet):
    """
    واجهة برمجة التطبيقات لتسجيل الخروج.
    تقوم بإبطال رمز JWT.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def logout(self, request):
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            return Response({'detail': _('تم تسجيل الخروج بنجاح')})
        except Exception as e:
            return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)
