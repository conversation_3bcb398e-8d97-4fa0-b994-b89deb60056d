from django.urls import path
from . import views

app_name = 'clients'

urlpatterns = [
    # قائمة العملاء
    path('', views.client_list, name='client_list'),
    
    # تفاصيل العميل
    path('<int:client_id>/', views.client_detail, name='client_detail'),
    
    # إضافة عميل جديد
    path('create/', views.client_create, name='client_create'),
    
    # تعديل العميل
    path('<int:client_id>/edit/', views.client_edit, name='client_edit'),
    
    # حذف العميل
    path('<int:client_id>/delete/', views.client_delete, name='client_delete'),
    
    # تغيير حالة العميل
    path('<int:client_id>/toggle-status/', views.client_toggle_status, name='client_toggle_status'),
    
    # رفع مستند للعميل
    path('<int:client_id>/upload-document/', views.client_document_upload, name='client_document_upload'),
    
    # إضافة جهة اتصال للعميل
    path('<int:client_id>/add-contact/', views.client_contact_add, name='client_contact_add'),
    
    # تصدير قائمة العملاء
    path('export/', views.client_export, name='client_export'),
    
    # API للبحث السريع
    path('api/search/', views.client_api_search, name='client_api_search'),
]
