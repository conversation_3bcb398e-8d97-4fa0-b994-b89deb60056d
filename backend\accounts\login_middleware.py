"""
Middleware for login protection.
"""
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin

class LoginRequiredMiddleware(MiddlewareMixin):
    """
    وسيط للتحقق من تسجيل الدخول قبل الوصول إلى أي صفحة أخرى
    """

    def process_request(self, request):
        # المسارات المستثناة من التحقق
        exempt_urls = [
            '/static/',
            '/media/',
            '/super_admin/login/',
            '/super_admin/resend-2fa-code/',
            '/company/login/',
            reverse('check_serial_number'),
        ]

        # التحقق من تسجيل الدخول
        if not request.user.is_authenticated:
            # التحقق من المسار الحالي
            current_path = request.path_info

            # السماح بالوصول إلى المسارات المستثناة
            for exempt_url in exempt_urls:
                if current_path.startswith(exempt_url):
                    return None

            # إعادة التوجيه إلى صفحة تسجيل دخول الشركات
            return redirect('company:login')

        return None
