import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const StatCard = ({ title, value, icon, color, change, changeType, link }) => {
  // Define color classes based on the color prop
  const colorClasses = {
    primary: {
      bg: 'bg-primary-light',
      text: 'text-primary',
      iconBg: 'bg-primary',
      border: 'border-primary-light',
    },
    secondary: {
      bg: 'bg-secondary-light',
      text: 'text-secondary-dark',
      iconBg: 'bg-secondary',
      border: 'border-secondary-light',
    },
    success: {
      bg: 'bg-success-light',
      text: 'text-success',
      iconBg: 'bg-success',
      border: 'border-success-light',
    },
    warning: {
      bg: 'bg-warning-light',
      text: 'text-warning-dark',
      iconBg: 'bg-warning',
      border: 'border-warning-light',
    },
    danger: {
      bg: 'bg-danger-light',
      text: 'text-danger',
      iconBg: 'bg-danger',
      border: 'border-danger-light',
    },
    info: {
      bg: 'bg-info-light',
      text: 'text-info',
      iconBg: 'bg-info',
      border: 'border-info-light',
    },
  };

  const classes = colorClasses[color] || colorClasses.primary;

  return (
    <motion.div
      whileHover={{ y: -5, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}
      transition={{ duration: 0.3 }}
      className={`stat-card ${classes.bg} ${classes.border}`}
    >
      <Link to={link || '#'} className="block p-6">
        <div className="flex items-center">
          <div className={`w-12 h-12 rounded-full ${classes.iconBg} text-white flex items-center justify-center mr-4`}>
            {icon}
          </div>

          <div>
            <h3 className="text-lg font-bold text-neutral-dark">{title}</h3>
            <div className="flex items-center mt-1">
              <span className={`text-2xl font-bold ${classes.text}`}>{value}</span>

              {change && (
                <span className={`mr-2 text-sm font-medium ${changeType === 'increase' ? 'text-success' : 'text-danger'}`}>
                  {change}
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default StatCard;
