from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenVerifyView

from .views import UserViewSet, CustomTokenObtainPairView, CustomTokenRefreshView, LogoutView

# إنشاء الموجه
router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')

# تعريف مسارات المصادقة
urlpatterns = [
    # مسارات JWT
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    
    # تسجيل الخروج
    path('logout/', LogoutView.as_view({'post': 'logout'}), name='logout'),
    
    # مسارات المستخدمين
    path('', include(router.urls)),
]
