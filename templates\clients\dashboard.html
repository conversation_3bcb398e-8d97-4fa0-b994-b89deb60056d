{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}إدارة العملاء | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة العملاء</h2>
            <p class="text-sm text-gray-500 dark:text-slate-400 mt-1">
                عرض وإدارة العملاء حسب نوع العقد
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'clients:client_list' %}"
               class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
                <i class="fas fa-list ml-2 text-sm"></i>
                <span>عرض جميع العملاء</span>
            </a>
        </div>
    </div>

    <!-- Client Categories Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- بطاقة 1: عملاء العقود الدائمة -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
            <div class="border-r-4 border-[#3b82f6] p-6">
                <div class="flex justify-between items-center mb-4">
                    <div class="text-[#3b82f6] bg-blue-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-user-tie text-xl"></i>
                    </div>
                    <div class="text-right">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عملاء العقود الدائمة</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العملاء المرتبطين بعقود دائمة</p>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <a href="{% url 'clients:client_create' %}?type=permanent"
                       class="bg-[#3b82f6] text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة عميل عقد دائم
                    </a>
                    <div class="text-3xl font-bold text-gray-800 dark:text-white">{{ permanent_clients_count|default:"0" }}</div>
                </div>
            </div>
        </div>

        <!-- بطاقة 2: عملاء العقود الشهرية -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
            <div class="border-r-4 border-[#c084fc] p-6">
                <div class="flex justify-between items-center mb-4">
                    <div class="text-[#c084fc] bg-purple-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-user-tie text-xl"></i>
                    </div>
                    <div class="text-right">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عملاء العقود الشهرية</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العملاء المرتبطين بعقود شهرية</p>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <a href="{% url 'clients:client_create' %}?type=monthly"
                       class="bg-[#c084fc] text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors duration-300 flex items-center">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة عميل عقد شهري
                    </a>
                    <div class="text-3xl font-bold text-gray-800 dark:text-white">{{ monthly_clients_count|default:"0" }}</div>
                </div>
            </div>
        </div>

        <!-- بطاقة 3: عملاء الخدمات المخصصة -->
        <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-slate-700">
            <div class="border-r-4 border-[#f97316] p-6">
                <div class="flex justify-between items-center mb-4">
                    <div class="text-[#f97316] bg-orange-100 dark:bg-slate-900 p-3 rounded-full w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-user-tie text-xl"></i>
                    </div>
                    <div class="text-right">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عملاء الخدمات المخصصة</h3>
                        <p class="text-sm text-gray-600 dark:text-slate-400 mt-1">العملاء المرتبطين بخدمات مخصصة</p>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <a href="{% url 'clients:client_create' %}?type=custom"
                       class="bg-[#f97316] text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors duration-300 flex items-center">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة عميل خدمة مخصصة
                    </a>
                    <div class="text-3xl font-bold text-gray-800 dark:text-white">{{ custom_clients_count|default:"0" }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Clients Section -->
    <div class="bg-white dark:bg-[#1E293B] rounded-lg shadow-md p-6 mb-8 border border-gray-100 dark:border-slate-700">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">آخر العملاء المضافين</h3>
        
        {% if recent_clients %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                <thead class="bg-gray-50 dark:bg-slate-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            اسم العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            نوع العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            رقم الهاتف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            تاريخ الإضافة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                    {% for client in recent_clients %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-slate-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ client.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if client.client_type == 'permanent' %}
                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                {% elif client.client_type == 'monthly' %}
                                    bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                {% else %}
                                    bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                {% endif %}">
                                {{ client.get_client_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-400">
                            {{ client.phone }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-400">
                            {{ client.created_at|date:"Y-m-d" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                            <a href="{% url 'clients:client_detail' client.id %}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-3">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'clients:client_edit' client.id %}" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 ml-3">
                                <i class="fas fa-edit"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8">
            <div class="text-gray-400 dark:text-slate-500 mb-2">
                <i class="fas fa-users text-5xl"></i>
            </div>
            <p class="text-gray-500 dark:text-slate-400">لا يوجد عملاء مضافين حتى الآن</p>
            <p class="text-sm text-gray-400 dark:text-slate-500 mt-1">قم بإضافة عميل جديد باستخدام الأزرار أعلاه</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
