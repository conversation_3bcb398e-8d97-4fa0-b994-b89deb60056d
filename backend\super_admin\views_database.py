"""
وظائف إدارة قواعد البيانات للمسؤول الأعلى
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
import os
import json
from .models import Company, SystemLog
from .database_utils import (
    extract_database_schema, reset_database, clone_database_structure,
    create_empty_database, get_database_tables_count, get_database_size, format_size
)

# التحقق من صلاحيات المسؤول الأعلى
def is_super_admin(user):
    """التحقق مما إذا كان المستخدم مسؤولاً أعلى (منشئ النظام)"""
    return user.is_superuser

# إدارة قواعد البيانات
@login_required
def database_management(request):
    """صفحة إدارة قواعد البيانات"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    # الحصول على قائمة الشركات
    companies = Company.objects.all().order_by('name')

    # إضافة معلومات إضافية عن قواعد البيانات
    for company in companies:
        company.db_tables_count = get_database_tables_count(company.database_name)
        company.db_size = get_database_size(company.database_name)
        company.db_size_formatted = format_size(company.db_size)

    # معالجة الطلبات
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        action = request.POST.get('action', '')
        company_id = request.POST.get('company_id', '')

        if not company_id:
            return JsonResponse({'success': False, 'message': 'معرف الشركة مفقود'})

        company = get_object_or_404(Company, id=company_id)

        if action == 'reset_database':
            # تصفير قاعدة البيانات
            result = reset_database(company.database_name)

            if result['success']:
                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='reset_database',
                    company=company,
                    description=f'تم تصفير قاعدة البيانات للشركة: {company.name}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            return JsonResponse(result)

        elif action == 'extract_schema':
            # استخراج هيكل قاعدة البيانات
            result = extract_database_schema(company.database_name)

            if result['success']:
                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='extract_schema',
                    company=company,
                    description=f'تم استخراج هيكل قاعدة البيانات للشركة: {company.name}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            return JsonResponse(result)

        elif action == 'clone_structure':
            # نسخ هيكل قاعدة البيانات
            target_company_id = request.POST.get('target_company_id', '')

            if not target_company_id:
                return JsonResponse({'success': False, 'message': 'معرف الشركة الهدف مفقود'})

            target_company = get_object_or_404(Company, id=target_company_id)

            result = clone_database_structure(company.database_name, target_company.database_name)

            if result['success']:
                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='clone_structure',
                    company=company,
                    description=f'تم نسخ هيكل قاعدة البيانات من الشركة: {company.name} إلى الشركة: {target_company.name}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            return JsonResponse(result)

        elif action == 'create_empty_database':
            # إنشاء قاعدة بيانات فارغة
            template_company_id = request.POST.get('template_company_id', '')
            template_db = None

            if template_company_id:
                template_company = get_object_or_404(Company, id=template_company_id)
                template_db = template_company.database_name

            result = create_empty_database(company.database_name, template_db)

            if result['success']:
                # تسجيل العملية
                SystemLog.objects.create(
                    user=request.user,
                    action='create_empty_database',
                    company=company,
                    description=f'تم إنشاء قاعدة بيانات فارغة للشركة: {company.name}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            return JsonResponse(result)

        return JsonResponse({'success': False, 'message': 'إجراء غير معروف'})

    return render(request, 'super_admin/database_management.html', {
        'companies': companies
    })

# عرض هيكل قاعدة البيانات
@login_required
def view_database_schema(request, company_id):
    """عرض هيكل قاعدة البيانات"""
    # التحقق من صلاحيات المسؤول الأعلى
    if not is_super_admin(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة')
        return redirect('super_admin:login')

    company = get_object_or_404(Company, id=company_id)

    # استخراج هيكل قاعدة البيانات
    result = extract_database_schema(company.database_name)

    if not result['success']:
        messages.error(request, result['message'])
        return redirect('super_admin:database_management')

    schema = result['schema']

    # تنظيم الجداول حسب الأقسام
    sections = {
        'auth': {
            'title': 'المصادقة والمستخدمين',
            'tables': []
        },
        'admin': {
            'title': 'الإدارة',
            'tables': []
        },
        'employees': {
            'title': 'العمال',
            'tables': []
        },
        'invoices': {
            'title': 'الفواتير',
            'tables': []
        },
        'services': {
            'title': 'الخدمات',
            'tables': []
        },
        'reports': {
            'title': 'التقارير',
            'tables': []
        },
        'documents': {
            'title': 'المستندات والنماذج',
            'tables': []
        },
        'other': {
            'title': 'أخرى',
            'tables': []
        }
    }

    # تصنيف الجداول
    for table_name, table_info in schema['tables'].items():
        if table_name.startswith('auth_') or table_name.startswith('django_auth_'):
            sections['auth']['tables'].append((table_name, table_info))
        elif table_name.startswith('admin_') or table_name.startswith('django_admin_'):
            sections['admin']['tables'].append((table_name, table_info))
        elif table_name.startswith('employee_') or 'worker' in table_name or 'labor' in table_name:
            sections['employees']['tables'].append((table_name, table_info))
        elif table_name.startswith('invoice_') or 'payment' in table_name or 'bill' in table_name:
            sections['invoices']['tables'].append((table_name, table_info))
        elif table_name.startswith('service_') or 'cleaning' in table_name:
            sections['services']['tables'].append((table_name, table_info))
        elif table_name.startswith('report_') or 'stat' in table_name:
            sections['reports']['tables'].append((table_name, table_info))
        elif table_name.startswith('document_') or 'form' in table_name or 'template' in table_name:
            sections['documents']['tables'].append((table_name, table_info))
        else:
            sections['other']['tables'].append((table_name, table_info))

    # إزالة الأقسام الفارغة
    sections = {k: v for k, v in sections.items() if v['tables']}

    return render(request, 'super_admin/view_database_schema.html', {
        'company': company,
        'schema': schema,
        'sections': sections
    })
