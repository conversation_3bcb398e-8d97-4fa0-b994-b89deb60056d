from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using a key.
    Usage: {{ my_dict|get_item:key_variable }}
    """
    if dictionary is None:
        return None
    return dictionary.get(key)

@register.filter
def split(value, delimiter):
    """
    Split a string into a list using the specified delimiter.
    Usage: {{ "1,2,3"|split:"," }}
    """
    if value is None:
        return []
    return value.split(delimiter)
