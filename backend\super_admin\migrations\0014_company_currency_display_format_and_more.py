# Generated by Django 5.2 on 2025-05-28 19:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0013_currency_systemsettings_auto_update_exchange_rates_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='currency_display_format',
            field=models.CharField(choices=[('symbol_after', 'الرمز بعد الرقم'), ('symbol_before', 'الرمز قبل الرقم'), ('code_after', 'الكود بعد الرقم'), ('code_before', 'الكود قبل الرقم')], default='symbol_after', max_length=20, verbose_name='تنسيق عرض العملة'),
        ),
        migrations.AddField(
            model_name='company',
            name='custom_exchange_rate',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True, verbose_name='سعر الصرف المخصص'),
        ),
        migrations.AddField(
            model_name='company',
            name='decimal_places_count',
            field=models.IntegerField(choices=[(0, 'بدون خانات عشرية'), (1, 'خانة واحدة'), (2, 'خانتان'), (3, 'ثلاث خانات')], default=2, verbose_name='عدد الخانات العشرية'),
        ),
        migrations.AddField(
            model_name='company',
            name='preferred_currency',
            field=models.CharField(choices=[('IQD', 'الدينار العراقي'), ('USD', 'الدولار الأمريكي')], default='IQD', max_length=3, verbose_name='العملة المفضلة'),
        ),
        migrations.AddField(
            model_name='company',
            name='show_decimal_places',
            field=models.BooleanField(default=True, verbose_name='إظهار الخانات العشرية'),
        ),
        migrations.AddField(
            model_name='company',
            name='use_custom_exchange_rate',
            field=models.BooleanField(default=False, verbose_name='استخدام سعر صرف مخصص'),
        ),
    ]
