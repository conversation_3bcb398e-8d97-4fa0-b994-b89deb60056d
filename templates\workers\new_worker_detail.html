{% extends 'layouts/dashboard.html' %}
{% load static %}

{% block title %}تفاصيل العامل - {{ worker.first_name }} {{ worker.last_name }} - منصة استقدامي السحابية{% endblock %}

{% block content %}
<div class="p-6">
    <!-- رأس الصفحة -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">تفاصيل العامل</h2>
            <p class="text-sm text-gray-600">عرض المعلومات الكاملة للعامل</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{% url 'workers:worker_update' worker.id %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                <i class="fas fa-edit ml-1"></i> تعديل
            </a>
            <a href="{% url 'workers:worker_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                <i class="fas fa-arrow-right ml-1"></i> العودة
            </a>
        </div>
    </div>

    <!-- بطاقة المعلومات الشخصية -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 transition-all duration-300 hover:shadow-lg">
        <div class="bg-gradient-to-l from-blue-700 to-blue-800 text-white px-6 py-4 flex items-center">
            <i class="fas fa-user-circle text-2xl ml-3 text-blue-200"></i>
            <h3 class="text-lg font-bold">المعلومات الشخصية</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row">
                <!-- الصورة الشخصية -->
                <div class="md:w-1/4 flex justify-center mb-4 md:mb-0">
                    {% if worker.photo %}
                        <div class="relative">
                            <img src="{{ worker.photo.url }}" alt="{{ worker.first_name }} {{ worker.last_name }}"
                                class="w-40 h-40 object-cover rounded-full border-4 border-blue-100 shadow-md">
                            <div class="absolute bottom-0 left-0 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center transform translate-x-2 translate-y-2">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                    {% else %}
                        <div class="relative">
                            <div class="w-40 h-40 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full flex items-center justify-center text-blue-300 border-4 border-blue-50 shadow-md">
                                <i class="fas fa-user text-5xl"></i>
                            </div>
                            <div class="absolute bottom-0 left-0 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center transform translate-x-2 translate-y-2">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- البيانات الشخصية -->
                <div class="md:w-3/4 md:pr-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-id-card text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">الاسم الكامل</h4>
                                <p class="text-lg font-semibold text-gray-800">{{ worker.first_name }} {{ worker.last_name }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-flag text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">الجنسية</h4>
                                <p class="text-lg text-gray-800">{{ worker.get_nationality_display }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-calendar-alt text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">تاريخ الميلاد</h4>
                                <p class="text-lg text-gray-800">{{ worker.date_of_birth|date:"Y-m-d" }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-venus-mars text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">الجنس</h4>
                                <p class="text-lg text-gray-800">{{ worker.get_gender_display }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-phone-alt text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">رقم الهاتف</h4>
                                <p class="text-lg text-gray-800">
                                    {% if worker.phone_number %}
                                        {{ worker.phone_number }}
                                    {% else %}
                                        <span class="text-gray-400 flex items-center">
                                            <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-briefcase text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">المهنة</h4>
                                <p class="text-lg text-gray-800">
                                    {% if worker.job_title %}
                                        {{ worker.job_title }}
                                    {% else %}
                                        <span class="text-gray-400 flex items-center">
                                            <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center ml-3 mt-1">
                                <i class="fas fa-check-circle text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">الحالة</h4>
                                <p class="text-lg">
                                    {% if worker.status == 'active' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-green-500 to-green-600 text-white shadow-sm">
                                            <i class="fas fa-check-circle ml-1"></i> نشط
                                        </span>
                                    {% elif worker.status == 'on_leave' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-yellow-500 to-yellow-600 text-white shadow-sm">
                                            <i class="fas fa-clock ml-1"></i> في إجازة
                                        </span>
                                    {% elif worker.status == 'inactive' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-red-500 to-red-600 text-white shadow-sm">
                                            <i class="fas fa-times-circle ml-1"></i> غير نشط
                                        </span>
                                    {% elif worker.status == 'terminated' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-sm">
                                            <i class="fas fa-ban ml-1"></i> منتهي الخدمة
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-sm">
                                            {{ worker.get_status_display }}
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة معلومات جواز السفر -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 transition-all duration-300 hover:shadow-lg">
        <div class="bg-gradient-to-l from-indigo-700 to-indigo-800 text-white px-6 py-4 flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-passport text-2xl ml-3 text-indigo-200"></i>
                <h3 class="text-lg font-bold">معلومات جواز السفر والتأشيرة</h3>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <span class="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
                    <i class="fas fa-passport ml-1"></i> الجواز
                </span>
                <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                    <i class="fas fa-id-card ml-1"></i> الستيكر
                </span>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-id-card text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">رقم الجواز</h4>
                    </div>
                    <p class="text-lg font-semibold text-gray-800 pr-10">{{ worker.passport_number }}</p>
                </div>

                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-calendar-plus text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">تاريخ الإصدار</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.passport_issue_date %}
                            {{ worker.passport_issue_date|date:"Y-m-d" }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-calendar-times text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">تاريخ انتهاء الجواز</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.passport_expiry %}
                            {{ worker.passport_expiry|date:"Y-m-d" }}
                            {% if worker.passport_expiry < today %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-2">
                                    <i class="fas fa-exclamation-circle ml-1"></i> منتهي
                                </span>
                            {% elif worker.passport_expiry < expiry_warning %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                    <i class="fas fa-exclamation-triangle ml-1"></i> ينتهي قريباً
                                </span>
                            {% endif %}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <!-- تاريخ انتهاء التأشيرة/الستيكر -->
                <div class="bg-green-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center ml-2">
                            <i class="fas fa-calendar-times text-green-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-green-700">تاريخ انتهاء الستيكر</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.visa_expiry %}
                            {{ worker.visa_expiry|date:"Y-m-d" }}
                            {% if worker.visa_expiry < today %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-2">
                                    <i class="fas fa-exclamation-circle ml-1"></i> منتهي
                                </span>
                            {% elif worker.visa_expiry < expiry_warning %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                    <i class="fas fa-exclamation-triangle ml-1"></i> ينتهي قريباً
                                </span>
                            {% endif %}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-map-marker-alt text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">مكان الإصدار</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.passport_issue_place %}
                            {{ worker.passport_issue_place }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-plane-arrival text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">تاريخ الدخول</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.entry_date %}
                            {{ worker.entry_date|date:"Y-m-d" }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-indigo-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center ml-2">
                            <i class="fas fa-plane-departure text-indigo-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-indigo-700">تاريخ المغادرة</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.departure_date %}
                            {{ worker.departure_date|date:"Y-m-d" }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="mt-6 flex justify-center space-x-6 space-x-reverse">
                <!-- صورة جواز السفر -->
                {% if worker.passport_image %}
                <div class="relative group">
                    <div class="absolute -top-3 right-3 bg-indigo-600 text-white text-xs px-2 py-1 rounded-md shadow-md z-10">
                        <i class="fas fa-passport ml-1"></i> جواز السفر
                    </div>
                    <img src="{{ worker.passport_image.url }}" alt="صورة جواز السفر" class="h-40 object-cover rounded-lg shadow-md border border-indigo-100 transition-all duration-300 group-hover:shadow-lg">
                    <div class="absolute inset-0 bg-indigo-900 bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300 rounded-lg">
                        <a href="{{ worker.passport_image.url }}" target="_blank" class="bg-white text-indigo-600 rounded-full p-2 transform scale-0 group-hover:scale-100 transition-all duration-300">
                            <i class="fas fa-search-plus"></i>
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- صورة الستيكر / التأشيرة -->
                {% if worker.visa_image %}
                <div class="relative group">
                    <div class="absolute -top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded-md shadow-md z-10">
                        <i class="fas fa-id-card ml-1"></i> الستيكر
                    </div>
                    <img src="{{ worker.visa_image.url }}" alt="صورة الستيكر" class="h-40 object-cover rounded-lg shadow-md border border-green-100 transition-all duration-300 group-hover:shadow-lg">
                    <div class="absolute inset-0 bg-green-900 bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300 rounded-lg">
                        <a href="{{ worker.visa_image.url }}" target="_blank" class="bg-white text-green-600 rounded-full p-2 transform scale-0 group-hover:scale-100 transition-all duration-300">
                            <i class="fas fa-search-plus"></i>
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- بطاقة معلومات الإقامة -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 transition-all duration-300 hover:shadow-lg">
        <div class="bg-gradient-to-l from-green-700 to-green-800 text-white px-6 py-4 flex items-center">
            <i class="fas fa-id-badge text-2xl ml-3 text-green-200"></i>
            <h3 class="text-lg font-bold">معلومات الإقامة</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-green-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center ml-2">
                            <i class="fas fa-id-card text-green-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-green-700">رقم الإقامة</h4>
                    </div>
                    <p class="text-lg font-semibold text-gray-800 pr-10">
                        {% if worker.residence_number %}
                            {{ worker.residence_number }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-green-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center ml-2">
                            <i class="fas fa-calendar-plus text-green-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-green-700">تاريخ الإصدار</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.residence_issue_date %}
                            {{ worker.residence_issue_date|date:"Y-m-d" }}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>

                <div class="bg-green-50 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow">
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center ml-2">
                            <i class="fas fa-calendar-times text-green-600"></i>
                        </div>
                        <h4 class="text-sm font-medium text-green-700">تاريخ الانتهاء</h4>
                    </div>
                    <p class="text-lg text-gray-800 pr-10">
                        {% if worker.residence_expiry_date %}
                            {{ worker.residence_expiry_date|date:"Y-m-d" }}
                            {% if worker.residence_days_remaining < 30 %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r {% if worker.residence_days_remaining < 7 %}from-red-500 to-red-600{% else %}from-yellow-500 to-yellow-600{% endif %} text-white shadow-sm ml-2">
                                    <i class="fas fa-exclamation-circle ml-1"></i> متبقي {{ worker.residence_days_remaining }} يوم
                                </span>
                            {% endif %}
                        {% else %}
                            <span class="text-gray-400 flex items-center">
                                <i class="fas fa-times-circle ml-1 text-sm"></i> غير متوفر
                            </span>
                        {% endif %}
                    </p>
                </div>
            </div>

            {% if worker.residence_image %}
            <div class="mt-6 flex justify-center">
                <div class="relative group">
                    <img src="{{ worker.residence_image.url }}" alt="صورة الإقامة" class="h-40 object-cover rounded-lg shadow-md border border-green-100 transition-all duration-300 group-hover:shadow-lg">
                    <div class="absolute inset-0 bg-green-900 bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300 rounded-lg">
                        <a href="{{ worker.residence_image.url }}" target="_blank" class="bg-white text-green-600 rounded-full p-2 transform scale-0 group-hover:scale-100 transition-all duration-300">
                            <i class="fas fa-search-plus"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- بطاقة العقود -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 transition-all duration-300 hover:shadow-lg">
        <div class="bg-gradient-to-l from-purple-700 to-purple-800 text-white px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-file-signature text-2xl ml-3 text-purple-200"></i>
                <h3 class="text-lg font-bold">العقود</h3>
            </div>
            <a href="{% url 'contract_create' %}?worker={{ worker.id }}" class="bg-white text-purple-800 px-4 py-1.5 rounded-lg text-sm font-medium hover:bg-purple-50 transition-colors duration-300 flex items-center shadow-sm">
                <i class="fas fa-plus ml-1.5"></i> إضافة عقد
            </a>
        </div>
        <div class="p-6">
            {% if worker.contracts.all %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                        <thead class="bg-purple-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">رقم العقد</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">تاريخ البدء</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">تاريخ الانتهاء</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for contract in worker.contracts.all %}
                                <tr class="hover:bg-purple-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ contract.contract_number }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ contract.client.name }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 flex items-center">
                                            <i class="fas fa-calendar-day text-purple-500 ml-1.5"></i>
                                            {{ contract.start_date|date:"Y-m-d" }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 flex items-center">
                                            <i class="fas fa-calendar-times text-purple-500 ml-1.5"></i>
                                            {{ contract.end_date|date:"Y-m-d" }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if contract.status == 'active' %}
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white shadow-sm">
                                                <i class="fas fa-check-circle ml-1"></i> نشط
                                            </span>
                                        {% elif contract.status == 'expired' %}
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white shadow-sm">
                                                <i class="fas fa-times-circle ml-1"></i> منتهي
                                            </span>
                                        {% elif contract.status == 'pending' %}
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white shadow-sm">
                                                <i class="fas fa-clock ml-1"></i> بانتظار
                                            </span>
                                        {% else %}
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-sm">
                                                {{ contract.get_status_display }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="{% url 'contract_detail' contract.id %}" class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-1.5 rounded-full transition-colors duration-200">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'contract_update' contract.id %}" class="text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 p-1.5 rounded-full transition-colors duration-200">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-12 bg-purple-50 dark:bg-slate-800/50 rounded-lg">
                    <div class="text-purple-300 dark:text-purple-400 mb-4">
                        <i class="fas fa-file-contract text-6xl"></i>
                    </div>
                    <p class="text-purple-800 dark:text-purple-300 mb-6 text-lg">لا توجد عقود مسجلة لهذا العامل</p>
                    <a href="{% url 'contract_create' %}?worker={{ worker.id }}" class="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-2.5 rounded-lg text-sm hover:from-purple-700 hover:to-purple-800 transition-colors duration-300 shadow-md dark:shadow-slate-900/50 flex items-center justify-center mx-auto w-48">
                        <i class="fas fa-plus ml-2"></i> إضافة عقد جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- بطاقة المستندات -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="bg-gradient-to-l from-yellow-600 to-yellow-700 text-white px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-file-alt text-2xl ml-3 text-yellow-200"></i>
                <h3 class="text-lg font-bold">المستندات</h3>
                <div class="mr-2 bg-yellow-800/30 dark:bg-yellow-700/30 rounded-full px-3 py-1 text-xs flex items-center">
                    <i class="fas fa-info-circle ml-1"></i>
                    <span>يمكنك رفع ستيكر التأشيرة أو دمجه مع الجواز</span>
                </div>
            </div>
            <a href="{% url 'documents:document_upload' %}?worker={{ worker.id }}" class="bg-white text-yellow-700 px-4 py-1.5 rounded-lg text-sm font-medium hover:bg-yellow-50 transition-colors duration-300 flex items-center shadow-sm">
                <i class="fas fa-upload ml-1.5"></i> رفع مستند
            </a>
        </div>
        <div class="p-6">
            {% if worker.documents.all or worker_documents and worker_documents.exists %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- عرض مستندات العامل من نموذج WorkerDocument -->
                    {% for document in worker.documents.all %}
                        <div class="bg-white border border-yellow-100 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 group">
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 px-4 py-3 border-b border-yellow-100 flex items-center justify-between">
                                <h4 class="font-medium text-yellow-800 flex items-center">
                                    {% if document.document_type == 'passport' %}
                                    <i class="fas fa-passport text-blue-600 ml-2"></i>
                                    {% elif document.document_type == 'visa' or document.document_type == 'visa_sticker' %}
                                    <i class="fas fa-id-card text-green-600 ml-2"></i>
                                    {% elif document.document_type == 'passport_with_visa' %}
                                    <div class="flex ml-2">
                                        <i class="fas fa-passport text-blue-600"></i>
                                        <i class="fas fa-plus text-gray-400 text-xs mx-1"></i>
                                        <i class="fas fa-id-card text-green-600"></i>
                                    </div>
                                    {% elif document.document_type == 'contract' %}
                                    <i class="fas fa-file-contract text-purple-600 ml-2"></i>
                                    {% elif document.document_type == 'medical' %}
                                    <i class="fas fa-notes-medical text-red-600 ml-2"></i>
                                    {% else %}
                                    <i class="fas fa-file text-yellow-600 ml-2"></i>
                                    {% endif %}
                                    {{ document.get_document_type_display }}
                                </h4>
                                <span class="text-xs text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">
                                    {{ document.upload_date|date:"Y-m-d" }}
                                </span>
                            </div>
                            <div class="p-4">
                                <div class="flex flex-col space-y-2">
                                    <div class="text-sm text-gray-700 mb-2">
                                        <span class="font-medium">العنوان:</span> {{ document.title }}
                                    </div>
                                    {% if document.expiry_date %}
                                    <div class="text-sm text-gray-700 mb-2">
                                        <span class="font-medium">تاريخ الانتهاء:</span>
                                        {% if document.expiry_date < today %}
                                        <span class="text-red-600">{{ document.expiry_date|date:"Y-m-d" }} (منتهي)</span>
                                        {% elif document.expiry_date < expiry_warning %}
                                        <span class="text-yellow-600">{{ document.expiry_date|date:"Y-m-d" }} (ينتهي قريباً)</span>
                                        {% else %}
                                        <span>{{ document.expiry_date|date:"Y-m-d" }}</span>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    <div class="flex justify-between items-center mt-2">
                                        <a href="{{ document.file.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-lg transition-colors duration-200 flex items-center">
                                            <i class="fas fa-eye ml-1.5"></i> عرض
                                        </a>
                                        <a href="{{ document.file.url }}" download class="text-green-600 hover:text-green-800 text-sm bg-green-50 hover:bg-green-100 px-3 py-1.5 rounded-lg transition-colors duration-200 flex items-center">
                                            <i class="fas fa-download ml-1.5"></i> تحميل
                                        </a>
                                        {% include 'components/delete-button.html' with url=document.get_delete_url btn_size="sm" data_id=document.id data_type="document" data_name=document.get_document_type_display %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}

                    <!-- عرض مستندات العامل من نموذج Document -->
                    {% for document in worker_documents %}
                        {% for attachment in document.attachments.all %}
                        <div class="bg-white border border-yellow-100 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 group">
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 px-4 py-3 border-b border-yellow-100 flex items-center justify-between">
                                <h4 class="font-medium text-yellow-800 flex items-center">
                                    {% if attachment.attachment_type == 'passport' %}
                                    <i class="fas fa-passport text-blue-600 ml-2"></i>
                                    {% elif attachment.attachment_type == 'visa_sticker' %}
                                    <i class="fas fa-id-badge text-green-600 ml-2"></i>
                                    {% elif attachment.attachment_type == 'passport_with_visa' %}
                                    <div class="flex ml-2">
                                        <i class="fas fa-passport text-blue-600"></i>
                                        <i class="fas fa-plus text-gray-400 text-xs mx-1"></i>
                                        <i class="fas fa-id-badge text-green-600"></i>
                                    </div>
                                    {% elif attachment.attachment_type == 'iqama' %}
                                    <i class="fas fa-id-card text-green-600 ml-2"></i>
                                    {% elif attachment.attachment_type == 'contract' %}
                                    <i class="fas fa-file-contract text-purple-600 ml-2"></i>
                                    {% elif attachment.attachment_type == 'medical' %}
                                    <i class="fas fa-notes-medical text-red-600 ml-2"></i>
                                    {% elif attachment.attachment_type == 'certificate' %}
                                    <i class="fas fa-certificate text-yellow-600 ml-2"></i>
                                    {% else %}
                                    <i class="fas fa-file text-yellow-600 ml-2"></i>
                                    {% endif %}
                                    {{ attachment.get_attachment_type_display }}
                                </h4>
                                <span class="text-xs text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">
                                    {{ attachment.uploaded_at|date:"Y-m-d" }}
                                </span>
                            </div>
                            <div class="p-4">
                                <div class="flex flex-col space-y-2">
                                    {% if attachment.description %}
                                    <div class="text-sm text-gray-700 mb-2">
                                        <span class="font-medium">الوصف:</span> {{ attachment.description }}
                                    </div>
                                    {% endif %}
                                    {% if attachment.expiry_date %}
                                    <div class="text-sm text-gray-700 mb-2">
                                        <span class="font-medium">تاريخ الانتهاء:</span>
                                        {% if attachment.expiry_date < today %}
                                        <span class="text-red-600">{{ attachment.expiry_date|date:"Y-m-d" }} (منتهي)</span>
                                        {% elif attachment.expiry_date < expiry_warning %}
                                        <span class="text-yellow-600">{{ attachment.expiry_date|date:"Y-m-d" }} (ينتهي قريباً)</span>
                                        {% else %}
                                        <span>{{ attachment.expiry_date|date:"Y-m-d" }}</span>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    <div class="flex justify-between items-center mt-2">
                                        <a href="{{ attachment.file.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-lg transition-colors duration-200 flex items-center">
                                            <i class="fas fa-eye ml-1.5"></i> عرض
                                        </a>
                                        <a href="{{ attachment.file.url }}" download class="text-green-600 hover:text-green-800 text-sm bg-green-50 hover:bg-green-100 px-3 py-1.5 rounded-lg transition-colors duration-200 flex items-center">
                                            <i class="fas fa-download ml-1.5"></i> تحميل
                                        </a>
                                        {% include 'components/delete-button.html' with url=attachment.get_delete_url btn_size="sm" data_id=attachment.id data_type="attachment" data_name=attachment.get_attachment_type_display %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12 bg-yellow-50 dark:bg-slate-800/50 rounded-lg">
                    <div class="text-yellow-300 dark:text-yellow-400 mb-4">
                        <i class="fas fa-file-alt text-6xl"></i>
                    </div>
                    <p class="text-yellow-800 dark:text-yellow-300 mb-6 text-lg">لا توجد مستندات مرفقة لهذا العامل</p>
                    <a href="{% url 'documents:document_upload' %}?worker={{ worker.id }}" class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-2.5 rounded-lg text-sm hover:from-yellow-600 hover:to-yellow-700 transition-colors duration-300 shadow-md dark:shadow-slate-900/50 flex items-center justify-center mx-auto w-48">
                        <i class="fas fa-upload ml-2"></i> رفع مستند جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}