from django.core.management.base import BaseCommand
from backend.services.models import ServiceType

class Command(BaseCommand):
    help = 'Creates default service types if they do not exist'

    def handle(self, *args, **options):
        # Define default service types
        default_types = [
            {
                'name': 'خدمة اعتيادية',
                'service_type': 'regular',
                'duration_hours': 7,
                'price': 150,
                'hourly_rate': 25,
                'color_code': '#3b82f6',
                'description': 'خدمة تنظيف منزلية لمدة 7 ساعات، مناسبة للمنازل والشقق.'
            },
            {
                'name': 'خدمة يوم كامل',
                'service_type': 'full_day',
                'duration_hours': 24,
                'price': 350,
                'hourly_rate': 20,
                'color_code': '#8b5cf6',
                'description': 'خدمة على مدار 24 ساعة، مناسبة للمناسبات والفعاليات الخاصة.'
            },
            {
                'name': 'خدمة مخصصة',
                'service_type': 'custom',
                'duration_hours': 4,
                'price': 100,
                'hourly_rate': 30,
                'color_code': '#f97316',
                'description': 'خدمة بساعات مرنة حسب احتياجاتك، مع إمكانية اختيار العمال.'
            }
        ]

        # Create or update service types
        for service_type_data in default_types:
            service_type, created = ServiceType.objects.update_or_create(
                service_type=service_type_data['service_type'],
                defaults=service_type_data
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created service type: {service_type.name}'))
            else:
                self.stdout.write(self.style.SUCCESS(f'Updated service type: {service_type.name}'))
