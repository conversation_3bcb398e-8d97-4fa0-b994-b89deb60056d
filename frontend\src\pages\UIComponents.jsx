import React from 'react';
import { FaCheck, FaInfo, FaExclamationTriangle, FaTimes, FaUser, FaDatabase, FaEdit, FaTrash } from 'react-icons/fa';

// المكونات المشتركة
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import Badge from '../components/common/Badge';

/**
 * صفحة عرض مكونات واجهة المستخدم
 */
const UIComponents = () => {
  return (
    <div className="space-y-10">
      {/* عنوان الصفحة */}
      <div>
        <h1 className="text-2xl font-bold text-neutral-dark mb-2">مكونات واجهة المستخدم</h1>
        <p className="text-neutral">عرض لمكونات واجهة المستخدم المختلفة مع الألوان والأنماط المتاحة</p>
      </div>
      
      {/* قسم الألوان */}
      <section>
        <h2 className="text-xl font-bold text-neutral-dark mb-4">الألوان</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {/* الألوان الأساسية */}
          <div className="space-y-2">
            <div className="h-20 bg-primary rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">الأساسي (Primary)</p>
            <p className="text-xs text-neutral">#0781ae</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-secondary rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">الثانوي (Secondary)</p>
            <p className="text-xs text-neutral">#ce913d</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-success rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">النجاح (Success)</p>
            <p className="text-xs text-neutral">#535b2c</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-danger rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">الخطر (Danger)</p>
            <p className="text-xs text-neutral">#74250c</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-warning rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">التحذير (Warning)</p>
            <p className="text-xs text-neutral">#ce913d</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-info rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">المعلومات (Info)</p>
            <p className="text-xs text-neutral">#065ca5</p>
          </div>
          
          {/* الألوان الإضافية */}
          <div className="space-y-2">
            <div className="h-20 bg-blue-345785 rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">أزرق 345785</p>
            <p className="text-xs text-neutral">#345785</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-blue-4b93da rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">أزرق 4b93da</p>
            <p className="text-xs text-neutral">#4b93da</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-gray-294252 rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">رمادي 294252</p>
            <p className="text-xs text-neutral">#294252</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-accent-42759b rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">لون مميز 42759b</p>
            <p className="text-xs text-neutral">#42759b</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-accent-d34169 rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">لون مميز d34169</p>
            <p className="text-xs text-neutral">#d34169</p>
          </div>
          
          <div className="space-y-2">
            <div className="h-20 bg-accent-c38d61 rounded-lg shadow-sm"></div>
            <p className="text-sm font-medium">لون مميز c38d61</p>
            <p className="text-xs text-neutral">#c38d61</p>
          </div>
        </div>
      </section>
      
      {/* قسم الأزرار */}
      <section>
        <h2 className="text-xl font-bold text-neutral-dark mb-4">الأزرار</h2>
        
        <div className="space-y-6">
          {/* الأزرار الأساسية */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">الأزرار الأساسية</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary">أساسي</Button>
              <Button variant="secondary">ثانوي</Button>
              <Button variant="success">نجاح</Button>
              <Button variant="danger">خطر</Button>
              <Button variant="warning">تحذير</Button>
              <Button variant="info">معلومات</Button>
              <Button variant="neutral">محايد</Button>
            </div>
          </div>
          
          {/* أزرار الحدود */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">أزرار الحدود</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="outline-primary">أساسي</Button>
              <Button variant="outline-secondary">ثانوي</Button>
              <Button variant="outline-success">نجاح</Button>
              <Button variant="outline-danger">خطر</Button>
              <Button variant="outline-warning">تحذير</Button>
              <Button variant="outline-info">معلومات</Button>
              <Button variant="outline-neutral">محايد</Button>
            </div>
          </div>
          
          {/* أزرار الألوان الإضافية */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">أزرار الألوان الإضافية</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="blue-345785">أزرق 345785</Button>
              <Button variant="blue-4b93da">أزرق 4b93da</Button>
              <Button variant="gray-294252">رمادي 294252</Button>
              <Button variant="gray-3773b5">رمادي 3773b5</Button>
              <Button variant="accent-42759b">مميز 42759b</Button>
              <Button variant="accent-d34169">مميز d34169</Button>
            </div>
          </div>
          
          {/* أحجام الأزرار */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">أحجام الأزرار</h3>
            <div className="flex flex-wrap items-center gap-3">
              <Button variant="primary" size="sm">صغير</Button>
              <Button variant="primary" size="md">متوسط</Button>
              <Button variant="primary" size="lg">كبير</Button>
              <Button variant="primary" size="icon" icon={<FaUser />}></Button>
            </div>
          </div>
          
          {/* أزرار مع أيقونات */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">أزرار مع أيقونات</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary" icon={<FaUser />}>مستخدم</Button>
              <Button variant="success" icon={<FaCheck />}>تأكيد</Button>
              <Button variant="danger" icon={<FaTimes />}>إلغاء</Button>
              <Button variant="warning" icon={<FaExclamationTriangle />}>تحذير</Button>
              <Button variant="info" icon={<FaInfo />}>معلومات</Button>
              <Button variant="outline-primary" icon={<FaDatabase />}>قاعدة البيانات</Button>
            </div>
          </div>
          
          {/* حالات الأزرار */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">حالات الأزرار</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary">عادي</Button>
              <Button variant="primary" disabled>معطل</Button>
              <Button variant="primary" loading>تحميل</Button>
            </div>
          </div>
        </div>
      </section>
      
      {/* قسم البطاقات */}
      <section>
        <h2 className="text-xl font-bold text-neutral-dark mb-4">البطاقات</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* بطاقة أساسية */}
          <Card
            title="بطاقة أساسية"
            subtitle="وصف البطاقة"
            icon={<FaInfo />}
            color="primary"
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
          
          {/* بطاقة ثانوية */}
          <Card
            title="بطاقة ثانوية"
            subtitle="وصف البطاقة"
            icon={<FaDatabase />}
            color="secondary"
            footer={
              <div className="flex justify-end">
                <Button variant="secondary" size="sm">إجراء</Button>
              </div>
            }
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
          
          {/* بطاقة نجاح */}
          <Card
            title="بطاقة نجاح"
            subtitle="وصف البطاقة"
            icon={<FaCheck />}
            color="success"
            actions={
              <div className="flex space-x-reverse space-x-2">
                <Button variant="success" size="icon" icon={<FaEdit />}></Button>
                <Button variant="danger" size="icon" icon={<FaTrash />}></Button>
              </div>
            }
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
          
          {/* بطاقة لون إضافي */}
          <Card
            title="بطاقة لون إضافي"
            subtitle="وصف البطاقة"
            icon={<FaInfo />}
            color="blue-345785"
            animate={true}
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
          
          {/* بطاقة لون إضافي آخر */}
          <Card
            title="بطاقة لون إضافي آخر"
            subtitle="وصف البطاقة"
            icon={<FaInfo />}
            color="accent-42759b"
            animate={true}
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
          
          {/* بطاقة مسطحة */}
          <Card
            title="بطاقة مسطحة"
            subtitle="وصف البطاقة"
            variant="flat"
            color="accent-d34169"
          >
            <p>محتوى البطاقة يظهر هنا. يمكن إضافة أي محتوى داخل البطاقة.</p>
          </Card>
        </div>
      </section>
      
      {/* قسم الشارات */}
      <section>
        <h2 className="text-xl font-bold text-neutral-dark mb-4">الشارات</h2>
        
        <div className="space-y-6">
          {/* الشارات الافتراضية */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">الشارات الافتراضية</h3>
            <div className="flex flex-wrap gap-3">
              <Badge color="primary">أساسي</Badge>
              <Badge color="secondary">ثانوي</Badge>
              <Badge color="success">نجاح</Badge>
              <Badge color="danger">خطر</Badge>
              <Badge color="warning">تحذير</Badge>
              <Badge color="info">معلومات</Badge>
              <Badge color="neutral">محايد</Badge>
            </div>
          </div>
          
          {/* الشارات الصلبة */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">الشارات الصلبة</h3>
            <div className="flex flex-wrap gap-3">
              <Badge color="primary" variant="solid">أساسي</Badge>
              <Badge color="secondary" variant="solid">ثانوي</Badge>
              <Badge color="success" variant="solid">نجاح</Badge>
              <Badge color="danger" variant="solid">خطر</Badge>
              <Badge color="warning" variant="solid">تحذير</Badge>
              <Badge color="info" variant="solid">معلومات</Badge>
              <Badge color="neutral" variant="solid">محايد</Badge>
            </div>
          </div>
          
          {/* شارات الحدود */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">شارات الحدود</h3>
            <div className="flex flex-wrap gap-3">
              <Badge color="primary" variant="outline">أساسي</Badge>
              <Badge color="secondary" variant="outline">ثانوي</Badge>
              <Badge color="success" variant="outline">نجاح</Badge>
              <Badge color="danger" variant="outline">خطر</Badge>
              <Badge color="warning" variant="outline">تحذير</Badge>
              <Badge color="info" variant="outline">معلومات</Badge>
              <Badge color="neutral" variant="outline">محايد</Badge>
            </div>
          </div>
          
          {/* شارات الألوان الإضافية */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">شارات الألوان الإضافية</h3>
            <div className="flex flex-wrap gap-3">
              <Badge color="blue-345785">أزرق 345785</Badge>
              <Badge color="blue-4b93da">أزرق 4b93da</Badge>
              <Badge color="blue-90c1dc">أزرق 90c1dc</Badge>
              <Badge color="gray-294252">رمادي 294252</Badge>
              <Badge color="gray-3773b5">رمادي 3773b5</Badge>
              <Badge color="accent-42759b">مميز 42759b</Badge>
              <Badge color="accent-d34169">مميز d34169</Badge>
              <Badge color="accent-c38d61">مميز c38d61</Badge>
              <Badge color="accent-8faee1">مميز 8faee1</Badge>
            </div>
          </div>
          
          {/* أحجام الشارات */}
          <div>
            <h3 className="text-lg font-medium text-neutral-dark mb-3">أحجام الشارات</h3>
            <div className="flex flex-wrap items-center gap-3">
              <Badge color="primary" size="sm">صغير</Badge>
              <Badge color="primary" size="md">متوسط</Badge>
              <Badge color="primary" size="lg">كبير</Badge>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default UIComponents;
