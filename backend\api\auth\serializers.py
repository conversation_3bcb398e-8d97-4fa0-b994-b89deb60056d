from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """مسلسل بيانات المستخدم"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'date_joined', 'last_login']
        read_only_fields = ['id', 'is_active', 'is_staff', 'date_joined', 'last_login']


class UserDetailSerializer(UserSerializer):
    """مسلسل بيانات المستخدم التفصيلية"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + ['full_name']
    
    def get_full_name(self, obj):
        return obj.get_full_name()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """مسلسل مخصص للحصول على رمز JWT"""
    
    serial_number = serializers.CharField(required=True, write_only=True)
    
    def validate(self, attrs):
        # التحقق من الرقم التسلسلي
        serial_number = attrs.pop('serial_number', None)
        if not serial_number:
            raise serializers.ValidationError({'serial_number': _('الرقم التسلسلي مطلوب')})
        
        # التحقق من تنسيق الرقم التسلسلي
        if not self.is_valid_serial_number(serial_number):
            raise serializers.ValidationError({'serial_number': _('تنسيق الرقم التسلسلي غير صحيح')})
        
        # التحقق من وجود الرقم التسلسلي في قاعدة البيانات
        # هنا يمكن إضافة منطق التحقق من الرقم التسلسلي
        
        # الحصول على الرمز باستخدام الطريقة الأصلية
        data = super().validate(attrs)
        
        # إضافة معلومات المستخدم إلى الاستجابة
        user = self.user
        data['user'] = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name(),
            'is_staff': user.is_staff,
        }
        
        return data
    
    @staticmethod
    def is_valid_serial_number(serial_number):
        """التحقق من تنسيق الرقم التسلسلي"""
        parts = serial_number.split('-')
        if len(parts) != 4:
            return False
        
        for part in parts:
            if len(part) != 4:
                return False
            
            if not part.isalnum():
                return False
        
        return True


class TokenRefreshSerializer(serializers.Serializer):
    """مسلسل تحديث رمز JWT"""
    
    refresh = serializers.CharField()
    
    def validate(self, attrs):
        refresh = RefreshToken(attrs['refresh'])
        
        data = {
            'access': str(refresh.access_token),
        }
        
        return data


class ChangePasswordSerializer(serializers.Serializer):
    """مسلسل تغيير كلمة المرور"""
    
    old_password = serializers.CharField(required=True, write_only=True)
    new_password = serializers.CharField(required=True, write_only=True)
    confirm_password = serializers.CharField(required=True, write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({'confirm_password': _('كلمات المرور غير متطابقة')})
        
        return attrs
