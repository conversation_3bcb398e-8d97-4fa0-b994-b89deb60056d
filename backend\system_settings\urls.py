from django.urls import path
from . import views

urlpatterns = [
    # Main settings page - redirect to admin settings
    path('', views.admin_settings, name='settings_home'),

    # Admin settings page
    path('admin/', views.admin_settings, name='admin_settings'),

    # System settings
    path('system/', views.system_settings, name='system_settings'),
    path('system/edit/<int:setting_id>/', views.edit_system_setting, name='edit_system_setting'),

    # User management
    path('users/', views.user_management, name='user_management'),
    path('users/create/', views.create_user, name='create_user'),
    path('users/<int:user_id>/', views.user_detail, name='user_detail'),
    path('users/<int:user_id>/edit/', views.edit_user, name='edit_user'),
    path('users/<int:user_id>/permissions/', views.user_permissions, name='user_permissions'),

    # Worker settings
    path('workers/', views.worker_settings, name='worker_settings'),
    path('workers/create/', views.create_worker_setting, name='create_worker_setting'),
    path('workers/edit/<int:setting_id>/', views.edit_worker_setting, name='edit_worker_setting'),
    path('workers/update/', views.update_worker_setting, name='update_worker_setting'),
    path('workers/delete/<int:setting_id>/', views.delete_worker_setting, name='delete_worker_setting'),

    # Notification settings
    path('notifications/', views.notification_settings, name='notification_settings'),

    # Audit logs
    path('audit-logs/', views.audit_logs, name='audit_logs'),

    # System actions
    path('actions/', views.system_actions, name='system_actions'),
    path('actions/undo/<int:action_id>/', views.undo_action, name='undo_action'),

    # Notifications
    path('notifications/all/', views.all_notifications, name='all_notifications'),

    # User profile
    path('profile/', views.user_profile, name='user_profile'),

    # Authentication
    path('logout/', views.logout_view, name='logout'),

    # Currencies
    path('currencies/', views.currencies, name='currencies'),
    path('currencies/create/', views.create_currency, name='create_currency'),
    path('currencies/edit/<int:currency_id>/', views.edit_currency, name='edit_currency'),
    path('currencies/delete/<int:currency_id>/', views.delete_currency, name='delete_currency'),
    path('currencies/set-default/<int:currency_id>/', views.set_default_currency, name='set_default_currency'),

    # Recruitment Companies
    path('recruitment-companies/', views.recruitment_companies, name='recruitment_companies'),
    path('recruitment-companies/create/', views.create_recruitment_company, name='create_recruitment_company'),
    path('recruitment-companies/edit/<int:company_id>/', views.edit_recruitment_company, name='edit_recruitment_company'),
    path('recruitment-companies/delete/<int:company_id>/', views.delete_recruitment_company, name='delete_recruitment_company'),
]
