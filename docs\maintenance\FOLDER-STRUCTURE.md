# هيكل المجلدات المقترح لنظام إدارة العمالة

هذا الملف يقترح هيكل مجلدات منظم لتسهيل الوصول إلى الملفات والصيانة.

## الهيكل الحالي

```
labor_management/
├── accounts/
├── api/
├── backups/
├── company_dbs/
├── config/
├── contracts/
├── dashboard/
├── documents/
├── frontend/
├── labor_management/
├── media/
├── procedures/
├── react-template-editor/
├── reports/
├── scanner/
├── services/
├── static/
├── super_admin/
├── system_settings/
├── templates/
├── venv/
├── workers/
├── create_logo.html
├── db.sqlite3
├── fix_backgrounds.js
├── generate_test_data.py
├── manage.py
├── README-UNIFIED-DESIGN.md
├── start_server.bat
├── tailwind.config.js
├── UNIFIED-DESIGN-IMPLEMENTATION.md
└── worker_detail_source.html
```

## الهيكل المقترح

```
labor_management/
├── backend/                  # كل تطبيقات Django
│   ├── accounts/             # إدارة الحسابات والمصادقة
│   ├── api/                  # واجهة برمجة التطبيقات (API)
│   │   ├── contracts/        # API للعقود
│   │   ├── documents/        # API للمستندات
│   │   ├── procedures/       # API للإجراءات
│   │   ├── services/         # API للخدمات
│   │   ├── settings/         # API للإعدادات
│   │   └── workers/          # API للعمال
│   ├── contracts/            # تطبيق العقود
│   ├── dashboard/            # تطبيق لوحة التحكم
│   ├── documents/            # تطبيق المستندات
│   ├── labor_management/     # التطبيق الرئيسي
│   ├── procedures/           # تطبيق الإجراءات
│   ├── reports/              # تطبيق التقارير
│   ├── scanner/              # تطبيق الماسح الضوئي
│   ├── services/             # تطبيق الخدمات
│   ├── super_admin/          # تطبيق المسؤول الأعلى
│   ├── system_settings/      # تطبيق إعدادات النظام
│   └── workers/              # تطبيق العمال
├── config/                   # ملفات التكوين
│   ├── database_custom.json  # تكوين قواعد البيانات
│   ├── worker_settings.json  # إعدادات العمال
│   └── worker_types.json     # أنواع العمال
├── data/                     # البيانات
│   ├── backups/              # النسخ الاحتياطية
│   ├── company_dbs/          # قواعد بيانات الشركات
│   └── media/                # ملفات الوسائط المرفوعة
├── docs/                     # الوثائق
│   ├── api/                  # وثائق API
│   ├── frontend/             # وثائق الواجهة الأمامية
│   ├── maintenance/          # دليل الصيانة
│   └── template-editor/      # وثائق محرر القوالب
├── frontend/                 # واجهة المستخدم الرئيسية (React)
│   ├── public/               # الملفات العامة
│   └── src/                  # كود المصدر
├── react-template-editor/    # محرر القوالب (React)
│   ├── public/               # الملفات العامة
│   └── src/                  # كود المصدر
├── scripts/                  # النصوص البرمجية
│   ├── backup/               # نصوص النسخ الاحتياطي
│   ├── deployment/           # نصوص النشر
│   └── maintenance/          # نصوص الصيانة
├── static/                   # الملفات الثابتة
│   ├── css/                  # ملفات CSS
│   ├── js/                   # ملفات JavaScript
│   ├── images/               # الصور
│   └── dwt-resources/        # موارد Dynamic Web TWAIN
├── templates/                # قوالب HTML
│   ├── accounts/             # قوالب الحسابات
│   ├── dashboard/            # قوالب لوحة التحكم
│   ├── workers/              # قوالب العمال
│   └── ...                   # قوالب أخرى
├── tools/                    # الأدوات المساعدة
│   ├── fix_backgrounds.js    # إصلاح الخلفيات
│   └── specific-date-fix.js  # إصلاح التواريخ
├── venv/                     # البيئة الافتراضية
├── .gitignore                # ملف تجاهل Git
├── FILE-INDEX.md             # فهرس الملفات
├── FOLDER-STRUCTURE.md       # هيكل المجلدات
├── MAINTENANCE-GUIDE.md      # دليل الصيانة
├── manage.py                 # أداة إدارة Django
├── PROJECT-CLEANUP.md        # خطة تنظيف المشروع
├── README.md                 # الملف التعريفي الرئيسي
├── start_server.bat          # تشغيل الخادم
├── tailwind.config.base.js   # تكوين Tailwind CSS الأساسي
└── tailwind.config.js        # تكوين Tailwind CSS
```

## خطوات إعادة الهيكلة

### 1. إنشاء المجلدات الجديدة

```bash
# إنشاء المجلدات الرئيسية
mkdir -p backend
mkdir -p data/backups data/company_dbs data/media
mkdir -p docs/api docs/frontend docs/maintenance docs/template-editor
mkdir -p scripts/backup scripts/deployment scripts/maintenance
mkdir -p tools
```

### 2. نقل الملفات إلى المجلدات الجديدة

#### نقل تطبيقات Django إلى مجلد backend

```bash
# نقل تطبيقات Django
mv accounts backend/
mv api backend/
mv contracts backend/
mv dashboard backend/
mv documents backend/
mv labor_management backend/
mv procedures backend/
mv reports backend/
mv scanner backend/
mv services backend/
mv super_admin backend/
mv system_settings backend/
mv workers backend/
```

#### نقل البيانات إلى مجلد data

```bash
# نقل البيانات
mv backups/* data/backups/
mv company_dbs/* data/company_dbs/
mv media/* data/media/
```

#### نقل الوثائق إلى مجلد docs

```bash
# نقل الوثائق
mv README-UNIFIED-DESIGN.md docs/
mv UNIFIED-DESIGN-IMPLEMENTATION.md docs/
```

#### نقل الأدوات المساعدة إلى مجلد tools

```bash
# نقل الأدوات المساعدة
mv fix_backgrounds.js tools/
mv specific-date-fix.js tools/
```

### 3. تحديث المسارات في ملفات التكوين

#### تحديث مسارات Django في ملف settings.py

```python
# تحديث مسار التطبيقات
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'django_filters',
    'drf_yasg',
    'corsheaders',

    # Custom apps
    'backend.accounts',
    'backend.dashboard',
    'backend.workers',
    'backend.contracts',
    'backend.services',
    'backend.reports',
    'backend.system_settings',
    'backend.scanner',
    'backend.documents',
    'backend.procedures',
    'backend.super_admin',
    'backend.api',
]

# تحديث مسار الوسائط
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'data', 'media')

# تحديث مسار قواعد البيانات
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'data' / 'db.sqlite3',
        'ATOMIC_REQUESTS': True,
    }
    # Company databases will be added dynamically
}

# تحديث مسار القوالب
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'backend.accounts.context_processors.company_context',  # تحديث المسار
            ],
        },
    },
]

# تحديث مسار الملفات الثابتة
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
```

#### تحديث مسارات في ملفات أخرى

يجب تحديث المسارات في جميع الملفات التي تشير إلى المجلدات القديمة، مثل:
- ملفات urls.py
- ملفات views.py
- ملفات models.py
- ملفات JavaScript
- ملفات HTML

### 4. تحديث ملفات package.json

#### تحديث مسارات في ملف frontend/package.json

```json
{
  "name": "labor-management-frontend",
  "version": "1.0.0",
  "description": "واجهة المستخدم الجديدة لنظام إدارة العمالة",
  "private": true,
  "dependencies": {
    // ...
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build && cp -r build/* ../static/frontend/",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  // ...
}
```

#### تحديث مسارات في ملف react-template-editor/package.json

```json
{
  "name": "react-template-editor",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    // ...
  },
  "devDependencies": {
    // ...
  },
  "scripts": {
    "dev": "vite",
    "build": "vite build && cp -r dist/* ../static/template-editor/",
    "preview": "vite preview"
  }
}
```

## مزايا الهيكل الجديد

1. **تنظيم أفضل**: فصل واضح بين الواجهة الخلفية والأمامية والأدوات المساعدة والوثائق.
2. **سهولة الصيانة**: تجميع الملفات المتشابهة في مجلدات منطقية.
3. **سهولة التوسع**: إضافة تطبيقات أو مكونات جديدة بسهولة.
4. **سهولة النشر**: فصل البيانات عن الكود لتسهيل النشر والنسخ الاحتياطي.
5. **سهولة التعاون**: هيكل واضح يسهل على المطورين الجدد فهم المشروع.

## ملاحظات هامة

- يجب عمل نسخة احتياطية قبل البدء في إعادة الهيكلة.
- يجب اختبار النظام بعد كل خطوة من خطوات إعادة الهيكلة.
- يجب تحديث الوثائق لتعكس الهيكل الجديد.
- يجب تحديث أي نصوص برمجية تعتمد على المسارات القديمة.
