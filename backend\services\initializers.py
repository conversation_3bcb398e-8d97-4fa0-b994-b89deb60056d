"""
وحدة تهيئة بيانات الخدمات
تستخدم لإنشاء بيانات أولية للخدمات والإعدادات
"""

import logging
from django.utils import timezone
from django.contrib.auth.models import User
from .models_new_services import SystemSettings, Service, ServiceAssignment, FixedReservationNew, CustomClient

logger = logging.getLogger(__name__)

def initialize_data(db_name, company=None):
    """
    تهيئة البيانات الأولية للخدمات

    Args:
        db_name: اسم قاعدة البيانات
        company: كائن الشركة (اختياري)

    Returns:
        bool: نجاح أو فشل العملية
    """
    try:
        # إنشاء إعدادات النظام الافتراضية إذا لم تكن موجودة
        if not SystemSettings.objects.exists():
            logger.info(f"إنشاء إعدادات النظام الافتراضية لقاعدة البيانات {db_name}")

            # الحصول على المستخدم الأول (المسؤول)
            admin_user = User.objects.first()

            # استيراد الإعدادات من ملف التكوين
            import json
            import os
            from django.conf import settings

            # محاولة قراءة الإعدادات من ملف التكوين
            config_file = os.path.join(settings.BASE_DIR, 'config', 'service_settings.json')
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    service_settings = json.load(f)

                # إنشاء إعدادات النظام من ملف التكوين
                SystemSettings.objects.create(
                    service_price_regular=service_settings.get('service_price_regular', 0.00),
                    service_price_full_day=service_settings.get('service_price_full_day', 0.00),
                    service_price_custom=service_settings.get('service_price_custom', 0.00),
                    worker_salary_regular=service_settings.get('worker_salary_regular', 0.00),
                    worker_salary_full_day=service_settings.get('worker_salary_full_day', 0.00),
                    worker_salary_custom=service_settings.get('worker_salary_custom', 0.00),
                    overtime_hour_price=service_settings.get('overtime_hour_price', 0.00),
                    created_by=admin_user
                )
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.error(f"خطأ في قراءة ملف إعدادات الخدمات: {str(e)}")
                # إنشاء إعدادات افتراضية فارغة
                SystemSettings.objects.create(
                    service_price_regular=0.00,
                    service_price_full_day=0.00,
                    service_price_custom=0.00,
                    worker_salary_regular=0.00,
                    worker_salary_full_day=0.00,
                    worker_salary_custom=0.00,
                    overtime_hour_price=0.00,
                    created_by=admin_user
                )

            logger.info("تم إنشاء إعدادات النظام بنجاح")

        return True
    except Exception as e:
        logger.error(f"خطأ في تهيئة بيانات الخدمات: {str(e)}")
        return False
