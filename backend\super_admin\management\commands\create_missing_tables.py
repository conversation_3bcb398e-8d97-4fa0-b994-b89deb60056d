"""
أمر Django لإنشاء الجداول المفقودة في قواعد بيانات الشركات
"""

from django.core.management.base import BaseCommand
from django.db import connections
from django.conf import settings
from backend.super_admin.models import Company
from backend.super_admin.database_connector import setup_company_db_connection, get_connection_name
import logging
import os

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'إنشاء الجداول المفقودة في قواعد بيانات الشركات'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف شركة محددة لإصلاحها فقط'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 بدء إنشاء الجداول المفقودة...'))

        # الحصول على الشركات
        if options['company_id']:
            companies = Company.objects.filter(id=options['company_id'])
            if not companies.exists():
                self.stdout.write(self.style.ERROR(f'❌ لم يتم العثور على شركة بالمعرف: {options["company_id"]}'))
                return
        else:
            companies = Company.objects.filter(status='active')

        self.stdout.write(f'📊 تم العثور على {companies.count()} شركة للإصلاح')

        success_count = 0
        error_count = 0

        for company in companies:
            try:
                self.stdout.write(f'🔧 إصلاح قاعدة بيانات الشركة: {company.name}')

                # إنشاء اسم قاعدة البيانات
                db_name = f'company_{company.id}'

                # إنشاء الجداول المفقودة
                self.create_missing_tables(db_name, company)

                self.stdout.write(self.style.SUCCESS(f'✅ تم إصلاح {company.name} بنجاح'))
                success_count += 1

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ خطأ في إصلاح {company.name}: {str(e)}'))
                error_count += 1

        # عرض النتائج
        self.stdout.write(self.style.SUCCESS(f'🎉 تم الانتهاء من الإصلاح'))
        self.stdout.write(f'✅ نجح: {success_count} شركة')
        if error_count > 0:
            self.stdout.write(self.style.WARNING(f'❌ فشل: {error_count} شركة'))

    def create_missing_tables(self, db_name, company):
        """إنشاء الجداول المفقودة"""
        try:
            # إعداد الاتصال بقاعدة البيانات
            setup_success = setup_company_db_connection(company)
            if not setup_success:
                raise Exception("فشل في إعداد الاتصال بقاعدة البيانات")

            # الحصول على اسم الاتصال
            connection_name = get_connection_name(company)
            connection = connections[connection_name]
            cursor = connection.cursor()

            # إنشاء جدول العملاء
            self.stdout.write(f'📋 إنشاء جدول العملاء...')
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients_client (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(255) NOT NULL,
                    client_type VARCHAR(20) NOT NULL,
                    phone VARCHAR(20),
                    email VARCHAR(254),
                    address TEXT,
                    national_id VARCHAR(20),
                    passport_number VARCHAR(20),
                    visa_number VARCHAR(50),
                    sponsor_name VARCHAR(255),
                    sponsor_phone VARCHAR(20),
                    notes TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                );
            ''')

            # إنشاء جدول الخدمات
            self.stdout.write(f'📋 إنشاء جدول الخدمات...')
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS services_service (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    service_type VARCHAR(20) NOT NULL,
                    booking_type VARCHAR(20) NOT NULL,
                    client_id INTEGER,
                    worker_id INTEGER,
                    start_datetime DATETIME,
                    end_datetime DATETIME,
                    hours_per_day INTEGER,
                    total_days INTEGER,
                    hourly_rate DECIMAL(10, 2),
                    total_amount DECIMAL(10, 2),
                    status VARCHAR(20) NOT NULL,
                    notes TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (client_id) REFERENCES clients_client (id),
                    FOREIGN KEY (worker_id) REFERENCES workers_worker (id)
                );
            ''')

            # إنشاء جدول تأجيل الخدمات
            self.stdout.write(f'📋 إنشاء جدول تأجيل الخدمات...')
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS services_servicepostponement (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    service_id INTEGER NOT NULL,
                    postponement_date DATE NOT NULL,
                    reason TEXT,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (service_id) REFERENCES services_service (id)
                );
            ''')

            connection.commit()
            self.stdout.write(self.style.SUCCESS(f'✅ تم إنشاء الجداول بنجاح لشركة {company.name}'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ خطأ في إنشاء الجداول: {str(e)}'))
            raise
