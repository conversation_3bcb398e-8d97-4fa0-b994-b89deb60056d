{# 
    مكون البطاقة الإحصائية
    
    الاستخدام:
    {% include 'components/stat-card.html' with label='عدد العمال' value='45' icon='fas fa-users' borderColor='border-green-500' iconColor='text-green-500' trend='+12%' trendUp=True link='/workers' %}
#}

{% load static %}

<div class="group {% if link %}cursor-pointer{% endif %}" 
     {% if link %}onclick="window.location.href='{{ link }}'"{% endif %}>
    
    <div class="flex items-center justify-between bg-white dark:bg-gray-800 shadow-md hover:shadow-lg rounded-xl p-6 border-t-4 {{ borderColor|default:'border-blue-500' }} transition-all duration-300 hover:transform hover:scale-105">
        
        <!-- المحتوى الرئيسي -->
        <div class="flex-1">
            <!-- التسمية -->
            <div class="text-sm text-gray-500 dark:text-gray-400 mb-2 font-medium">
                {{ label|default:'إحصائية' }}
            </div>
            
            <!-- القيمة -->
            <div class="text-3xl font-bold text-gray-800 dark:text-white mb-2" data-stat="{{ stat_type|default:'' }}">
                {{ value|default:'0' }}
            </div>
            
            <!-- الاتجاه (اختياري) -->
            {% if trend %}
                <div class="flex items-center">
                    <span class="text-sm font-medium {% if trendUp %}text-green-600{% else %}text-red-600{% endif %}">
                        <i class="fas {% if trendUp %}fa-arrow-up{% else %}fa-arrow-down{% endif %} mr-1"></i>
                        {{ trend }}
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">من الشهر الماضي</span>
                </div>
            {% endif %}
        </div>
        
        <!-- الأيقونة -->
        <div class="mr-4">
            <div class="w-16 h-16 bg-gray-50 dark:bg-gray-700 rounded-full flex items-center justify-center group-hover:bg-gray-100 dark:group-hover:bg-gray-600 transition-colors duration-300">
                <i class="{{ icon|default:'fas fa-chart-bar' }} text-3xl {{ iconColor|default:'text-blue-500' }} group-hover:scale-110 transition-transform duration-300"></i>
            </div>
        </div>
    </div>
    
    <!-- مؤشر الرابط -->
    {% if link %}
        <div class="mt-2 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <span class="text-xs {{ iconColor|default:'text-blue-500' }} font-medium">
                انقر للعرض التفصيلي
                <i class="fas fa-arrow-left mr-1"></i>
            </span>
        </div>
    {% endif %}
</div>
