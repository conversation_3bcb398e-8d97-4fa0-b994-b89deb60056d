from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    ProcedureViewSet, BloodTestViewSet, SponsorshipTransferViewSet,
    OperationViewSet, WorkPermitViewSet, ResidenceIDViewSet, BranchTransferViewSet
)

# إنشاء الموجه
router = DefaultRouter()
router.register(r'procedures', ProcedureViewSet, basename='procedure')
router.register(r'blood-tests', BloodTestViewSet, basename='blood-test')
router.register(r'sponsorship-transfers', SponsorshipTransferViewSet, basename='sponsorship-transfer')
router.register(r'operations', OperationViewSet, basename='operation')
router.register(r'work-permits', WorkPermitViewSet, basename='work-permit')
router.register(r'residence-ids', ResidenceIDViewSet, basename='residence-id')
router.register(r'branch-transfers', BranchTransferViewSet, basename='branch-transfer')

# تعريف مسارات الإجراءات
urlpatterns = [
    # مسارات الإجراءات
    path('', include(router.urls)),
]
