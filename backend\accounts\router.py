"""
Database router for dynamic database switching based on user login.
"""
from threading import local
from django.conf import settings

_thread_local = local()

def get_current_db_name():
    """Get the current database name from thread local storage."""
    return getattr(_thread_local, 'DB_NAME', 'default')

def set_current_db_name(db_name):
    """Set the current database name in thread local storage."""
    _thread_local.DB_NAME = db_name


def get_company_database():
    """Get the current company database name, ensuring it's not default."""
    current_db = get_current_db_name()
    if current_db and current_db != 'default':
        return current_db
    return None


def ensure_company_database(model_class):
    """
    Ensure operations are performed on the company database.
    Returns the model manager with the correct database.
    """
    company_db = get_company_database()
    if company_db:
        return model_class.objects.using(company_db)
    return model_class.objects

class CompanyRouter:
    """
    A router to control database operations based on the current user's company.
    Enhanced for strict multi-tenant isolation.
    """

    def db_for_read(self, model, **hints):
        """
        Point all read operations to the current database.
        """
        current_db = get_current_db_name()

        # For company-specific apps, always use the current company database
        if model._meta.app_label in settings.COMPANY_APPS:
            if current_db and current_db != 'default':
                return current_db
            return 'default'

        # For super admin models, always use default
        if model._meta.app_label in ['super_admin', 'accounts']:
            return 'default'

        return 'default'

    def db_for_write(self, model, **hints):
        """
        Point all write operations to the current database.
        """
        current_db = get_current_db_name()

        # For company-specific apps, always use the current company database
        if model._meta.app_label in settings.COMPANY_APPS:
            if current_db and current_db != 'default':
                return current_db
            return 'default'

        # For super admin models, always use default
        if model._meta.app_label in ['super_admin', 'accounts']:
            return 'default'

        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if both objects are in the same database or in the default database.
        """
        db1 = 'default'
        db2 = 'default'

        if obj1._meta.app_label in settings.COMPANY_APPS:
            db1 = get_current_db_name()

        if obj2._meta.app_label in settings.COMPANY_APPS:
            db2 = get_current_db_name()

        return db1 == db2 or db1 == 'default' or db2 == 'default'

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure company-specific apps only appear in the 'default' database.
        """
        if db == 'default':
            return True

        if app_label in settings.COMPANY_APPS:
            return False

        return None  # Let other routers decide


def create_in_company_db(model_class, company_db=None, **kwargs):
    """
    Create object in company database.
    """
    if not company_db:
        company_db = get_company_database()

    if company_db:
        return model_class.objects.using(company_db).create(**kwargs)
    else:
        return model_class.objects.create(**kwargs)


def get_company_manager(model_class, company_db=None):
    """
    Get model manager for company database operations.
    """
    if not company_db:
        company_db = get_company_database()

    if company_db:
        return model_class.objects.using(company_db)
    else:
        return model_class.objects


def filter_company_db(model_class, company_db=None, **kwargs):
    """
    Filter objects from company database.
    """
    manager = get_company_manager(model_class, company_db)
    return manager.filter(**kwargs)


def get_company_object(model_class, company_db=None, **kwargs):
    """
    Get single object from company database.
    """
    manager = get_company_manager(model_class, company_db)
    return manager.get(**kwargs)


def all_company_objects(model_class, company_db=None):
    """
    Get all objects from company database.
    """
    manager = get_company_manager(model_class, company_db)
    return manager.all()


def count_company_objects(model_class, company_db=None, **kwargs):
    """
    Count objects in company database.
    """
    manager = get_company_manager(model_class, company_db)
    if kwargs:
        return manager.filter(**kwargs).count()
    return manager.count()


def save_to_company_db(instance, company_db=None):
    """
    Save instance to company database.
    """
    if not company_db:
        company_db = get_company_database()

    if company_db:
        instance.save(using=company_db)
    else:
        instance.save()
    return instance


def ensure_database_connection(db_name):
    """
    Ensure database connection exists and is available.
    """
    from django.db import connections
    from django.conf import settings

    if db_name not in connections.databases:
        # If connection doesn't exist, try to get it from settings
        if db_name in settings.DATABASES:
            connections.databases[db_name] = settings.DATABASES[db_name]
        else:
            return False

    try:
        connection = connections[db_name]
        connection.ensure_connection()
        return True
    except Exception as e:
        print(f"❌ Database connection failed for {db_name}: {e}")
        return False


def setup_company_database(company):
    """
    Setup database configuration for a company.
    """
    from django.conf import settings
    from django.db import connections

    db_name = f"company_{company.id}"

    # Create database configuration
    db_config = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': settings.BASE_DIR / f"data/company_dbs/{company.database_name}.sqlite3",
        'USER': company.database_user or '',
        'PASSWORD': company.database_password or '',
        'HOST': company.database_host or '',
        'PORT': '',
        'ATOMIC_REQUESTS': True,
        'AUTOCOMMIT': True,
        'CONN_HEALTH_CHECKS': False,
        'CONN_MAX_AGE': 0,
        'TIME_ZONE': 'UTC',
        'OPTIONS': {
            'timeout': 30,
            'isolation_level': None,
            'check_same_thread': False
        }
    }

    # Add to settings
    settings.DATABASES[db_name] = db_config

    # Add to connections
    if db_name not in connections.databases:
        connections.databases[db_name] = db_config

    # Test connection
    if ensure_database_connection(db_name):
        set_current_db_name(db_name)
        return db_name
    else:
        return None
