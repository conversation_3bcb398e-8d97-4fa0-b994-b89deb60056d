# Generated by Django 5.2 on 2025-05-13 14:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EntryVisa',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visa_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السمة')),
                ('issue_date', models.DateField(verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(verbose_name='تاريخ الانتهاء')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('document', models.FileField(blank=True, null=True, upload_to='entry_visas/', verbose_name='المستند')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'سمة دخول',
                'verbose_name_plural': 'سمات الدخول',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='SecurityClearance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clearance_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الموافقة')),
                ('issue_date', models.DateField(verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(verbose_name='تاريخ الانتهاء')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('document', models.FileField(blank=True, null=True, upload_to='security_clearances/', verbose_name='المستند')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'موافقة أمنية',
                'verbose_name_plural': 'الموافقات الأمنية',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='WorkPermit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permit_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإذن')),
                ('issue_date', models.DateField(verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(verbose_name='تاريخ الانتهاء')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('document', models.FileField(blank=True, null=True, upload_to='work_permits/', verbose_name='المستند')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إذن عمل',
                'verbose_name_plural': 'أذونات العمل',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='BloodTestRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('request_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('hospital', models.CharField(max_length=100, verbose_name='المستشفى')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_blood_tests', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'طلب فحص دم',
                'verbose_name_plural': 'طلبات فحص الدم',
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفرع')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('is_main', models.BooleanField(default=False, verbose_name='فرع رئيسي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_branches', to=settings.AUTH_USER_MODEL, verbose_name='مدير الفرع')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['-is_main', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('client_type', models.CharField(choices=[('permanent', 'دائم'), ('monthly', 'شهري'), ('daily', 'يومي')], max_length=20, verbose_name='نوع العميل')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('cr_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم السجل التجاري')),
                ('vat_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرقم الضريبي')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clients', to='labor_management.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Worker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('nationality', models.CharField(max_length=50, verbose_name='الجنسية')),
                ('passport_number', models.CharField(max_length=20, unique=True, verbose_name='رقم جواز السفر')),
                ('iqama_number', models.CharField(blank=True, max_length=20, null=True, unique=True, verbose_name='رقم الإقامة')),
                ('worker_type', models.CharField(choices=[('contract', 'عقد'), ('monthly_service', 'خدمة شهرية'), ('daily_service', 'خدمة يومية')], max_length=20, verbose_name='نوع العامل')),
                ('date_of_birth', models.DateField(verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='workers/', verbose_name='الصورة الشخصية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workers', to='labor_management.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'عامل',
                'verbose_name_plural': 'العمال',
                'ordering': ['full_name'],
            },
        ),
        migrations.CreateModel(
            name='SponsorshipTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة التحويل')),
                ('document', models.FileField(blank=True, null=True, upload_to='sponsorship_transfers/', verbose_name='مستند التحويل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sponsorship_transfers', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sponsorship_transfers', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'تحويل كفالة',
                'verbose_name_plural': 'تحويلات الكفالة',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='DailyService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الخدمة')),
                ('service_date', models.DateField(verbose_name='تاريخ الخدمة')),
                ('hours', models.PositiveIntegerField(default=8, verbose_name='عدد الساعات')),
                ('fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='رسوم الخدمة')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الخدمة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_services', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_daily_services', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_services', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'خدمة يومية',
                'verbose_name_plural': 'الخدمات اليومية',
                'ordering': ['-service_date'],
            },
        ),
        migrations.CreateModel(
            name='Daily24HService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الخدمة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية الخدمة')),
                ('end_date', models.DateField(verbose_name='تاريخ نهاية الخدمة')),
                ('fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='رسوم الخدمة')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الخدمة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_24h_services', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_24h_services', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_24h_services', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'خدمة 24 ساعة',
                'verbose_name_plural': 'خدمات 24 ساعة',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_number', models.CharField(max_length=50, unique=True, verbose_name='رقم العقد')),
                ('contract_type', models.CharField(choices=[('permanent', 'دائم'), ('monthly', 'شهري'), ('custom', 'مخصص')], max_length=20, verbose_name='نوع العقد')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية العقد')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية العقد')),
                ('monthly_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الرسوم الشهرية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='حالة العقد')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contracts', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_contracts', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contracts', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'عقد',
                'verbose_name_plural': 'العقود',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BloodTestRequestWorker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_date', models.DateField(verbose_name='تاريخ الفحص')),
                ('result', models.CharField(choices=[('positive', 'إيجابي'), ('negative', 'سلبي'), ('pending', 'معلق')], default='pending', max_length=20, verbose_name='نتيجة الفحص')),
                ('result_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النتيجة')),
                ('document', models.FileField(blank=True, null=True, upload_to='blood_tests/', verbose_name='مستند النتيجة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('blood_test_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workers', to='labor_management.bloodtestrequest', verbose_name='طلب فحص الدم')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blood_tests', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'فحص دم للعامل',
                'verbose_name_plural': 'فحوصات الدم للعمال',
                'ordering': ['-test_date'],
            },
        ),
        migrations.CreateModel(
            name='WorkerTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('reason', models.TextField(verbose_name='سبب التحويل')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة التحويل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_branch_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('from_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='labor_management.branch', verbose_name='من فرع')),
                ('to_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='labor_management.branch', verbose_name='إلى فرع')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branch_transfers', to='labor_management.worker', verbose_name='العامل')),
            ],
            options={
                'verbose_name': 'تحويل عامل بين الفروع',
                'verbose_name_plural': 'تحويلات العمال بين الفروع',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='WorkerOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_number', models.CharField(max_length=50, unique=True, verbose_name='رقم العملية')),
                ('operation_date', models.DateField(verbose_name='تاريخ العملية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة العملية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='worker_operations', to='labor_management.client', verbose_name='العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_operations', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('entry_visa', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='worker_operation', to='labor_management.entryvisa', verbose_name='سمة الدخول')),
                ('security_clearance', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='worker_operation', to='labor_management.securityclearance', verbose_name='الموافقة الأمنية')),
                ('worker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='labor_management.worker', verbose_name='العامل')),
                ('work_permit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='worker_operations', to='labor_management.workpermit', verbose_name='إذن العمل')),
            ],
            options={
                'verbose_name': 'عملية تشغيل',
                'verbose_name_plural': 'عمليات التشغيل',
                'ordering': ['-operation_date'],
            },
        ),
    ]
