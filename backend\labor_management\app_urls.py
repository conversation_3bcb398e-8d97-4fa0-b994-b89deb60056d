from django.urls import path
from . import views

app_name = 'labor_management'

urlpatterns = [
    # URLs para trabajadores
    path('workers/', views.worker_list, name='worker_list'),
    path('workers/add/', views.worker_add, name='worker_add'),
    path('workers/<int:worker_id>/', views.worker_detail, name='worker_detail'),
    path('workers/<int:worker_id>/edit/', views.worker_edit, name='worker_edit'),

    # URLs para clientes
    path('clients/', views.client_list, name='client_list'),
    path('clients/add/', views.client_add, name='client_add'),
    path('clients/<int:client_id>/', views.client_detail, name='client_detail'),
    path('clients/<int:client_id>/edit/', views.client_edit, name='client_edit'),

    # URLs para contratos
    path('contracts/', views.contract_list, name='contract_list'),
    path('contracts/add/', views.contract_add, name='contract_add'),
    path('contracts/<int:contract_id>/', views.contract_detail, name='contract_detail'),
    path('contracts/<int:contract_id>/edit/', views.contract_edit, name='contract_edit'),

    # URLs para servicios diarios
    path('daily-services/', views.daily_service_list, name='daily_service_list'),
    path('daily-services/add/', views.daily_service_add, name='daily_service_add'),
    path('daily-services/<int:service_id>/', views.daily_service_detail, name='daily_service_detail'),
    path('daily-services/<int:service_id>/edit/', views.daily_service_edit, name='daily_service_edit'),
    path('daily-services/<int:service_id>/delete/', views.daily_service_delete, name='daily_service_delete'),
    path('daily-services/calendar/', views.daily_service_calendar, name='daily_service_calendar'),
    path('daily-services/summary/', views.daily_service_summary, name='daily_service_summary'),

    # URLs para servicios de 24 horas
    path('24h-services/', views.daily_24h_service_list, name='daily_24h_service_list'),
    path('24h-services/add/', views.daily_24h_service_add, name='daily_24h_service_add'),
    path('24h-services/<int:service_id>/', views.daily_24h_service_detail, name='daily_24h_service_detail'),
    path('24h-services/<int:service_id>/edit/', views.daily_24h_service_edit, name='daily_24h_service_edit'),
    path('24h-services/<int:service_id>/delete/', views.daily_24h_service_delete, name='daily_24h_service_delete'),
    path('24h-services/calendar/', views.daily_24h_service_calendar, name='daily_24h_service_calendar'),
    path('24h-services/summary/', views.daily_24h_service_summary, name='daily_24h_service_summary'),

    # URLs para servicios personalizados
    path('custom-services/', views.custom_service_list, name='custom_service_list'),
    path('custom-services/add/', views.custom_service_create, name='custom_service_add'),
    path('custom-services/<int:service_id>/', views.custom_service_detail, name='custom_service_detail'),
    path('custom-services/<int:service_id>/edit/', views.custom_service_edit, name='custom_service_edit'),
    path('custom-services/<int:service_id>/delete/', views.custom_service_delete, name='custom_service_delete'),
    path('custom-services/calendar/', views.custom_service_calendar, name='custom_service_calendar'),

    # URLs para operaciones de trabajadores
    path('operations/', views.worker_operation_list, name='worker_operation_list'),
    path('operations/add/', views.worker_operation_add, name='worker_operation_add'),
    path('operations/<int:operation_id>/', views.worker_operation_detail, name='worker_operation_detail'),
    path('operations/<int:operation_id>/edit/', views.worker_operation_edit, name='worker_operation_edit'),

    # URLs para pruebas de sangre
    path('blood-tests/', views.blood_test_list, name='blood_test_list'),
    path('blood-tests/add/', views.blood_test_add, name='blood_test_add'),
    path('blood-tests/<int:test_id>/', views.blood_test_detail, name='blood_test_detail'),
    path('blood-tests/<int:test_id>/edit/', views.blood_test_edit, name='blood_test_edit'),
    path('blood-tests/<int:test_id>/add-worker/', views.blood_test_add_worker, name='blood_test_add_worker'),

    # URLs para transferencias de patrocinio
    path('sponsorship-transfers/', views.sponsorship_transfer_list, name='sponsorship_transfer_list'),
    path('sponsorship-transfers/add/', views.sponsorship_transfer_add, name='sponsorship_transfer_add'),
    path('sponsorship-transfers/<int:transfer_id>/', views.sponsorship_transfer_detail, name='sponsorship_transfer_detail'),
    path('sponsorship-transfers/<int:transfer_id>/edit/', views.sponsorship_transfer_edit, name='sponsorship_transfer_edit'),

    # URLs para transferencias de trabajadores entre sucursales
    path('worker-transfers/', views.worker_transfer_list, name='worker_transfer_list'),
    path('worker-transfers/add/', views.worker_transfer_add, name='worker_transfer_add'),
    path('worker-transfers/<int:transfer_id>/', views.worker_transfer_detail, name='worker_transfer_detail'),
    path('worker-transfers/<int:transfer_id>/edit/', views.worker_transfer_edit, name='worker_transfer_edit'),

    # URLs para sucursales
    path('branches/', views.branch_list, name='branch_list'),
    path('branches/add/', views.branch_add, name='branch_add'),
    path('branches/<int:branch_id>/', views.branch_detail, name='branch_detail'),
    path('branches/<int:branch_id>/edit/', views.branch_edit, name='branch_edit'),

    # URLs para documentos oficiales
    path('security-clearances/', views.security_clearance_list, name='security_clearance_list'),
    path('entry-visas/', views.entry_visa_list, name='entry_visa_list'),
    path('work-permits/', views.work_permit_list, name='work_permit_list'),

    # URLs para el panel de control
    path('dashboard/', views.dashboard, name='dashboard'),

    # APIs de utilidad para servicios
    path('api/available-workers/', views.available_workers, name='api_available_workers'),
    path('api/service-statistics/', views.service_statistics, name='api_service_statistics'),
]
