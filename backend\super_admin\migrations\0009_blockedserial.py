# Generated by Django 5.2 on 2025-05-12 21:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('super_admin', '0008_schemachange'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BlockedSerial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=20, unique=True, verbose_name='الرقم التسلسلي')),
                ('reason', models.TextField(verbose_name='سبب الحظر')),
                ('is_permanent', models.BooleanField(default=True, verbose_name='حظر دائم')),
                ('blocked_until', models.DateField(blank=True, null=True, verbose_name='محظور حتى تاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='blocked_serials', to=settings.AUTH_USER_MODEL, verbose_name='تم الحظر بواسطة')),
            ],
            options={
                'verbose_name': 'رقم تسلسلي محظور',
                'verbose_name_plural': 'الأرقام التسلسلية المحظورة',
                'ordering': ['-created_at'],
            },
        ),
    ]
