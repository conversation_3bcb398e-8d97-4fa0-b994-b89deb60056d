from django.db import models
from django.contrib.auth.models import User
from backend.super_admin.models import Company


class CompanyCurrencySettings(models.Model):
    """إعدادات العملة الخاصة بالشركة"""
    
    CURRENCY_CHOICES = (
        ('IQD', 'الدينار العراقي'),
        ('USD', 'الدولار الأمريكي'),
        ('EUR', 'اليورو'),
        ('GBP', 'الجنيه الإسترليني'),
        ('SAR', 'الريال السعودي'),
        ('AED', 'الدرهم الإماراتي'),
        ('KWD', 'الدينار الكويتي'),
        ('QAR', 'الريال القطري'),
        ('BHD', 'الدينار البحريني'),
        ('OMR', 'الريال العماني'),
        ('JOD', 'الدينار الأردني'),
        ('LBP', 'الليرة اللبنانية'),
        ('EGP', 'الجنيه المصري'),
        ('TRY', 'الليرة التركية'),
        ('IRR', 'الريال الإيراني'),
    )
    
    DISPLAY_FORMAT_CHOICES = (
        ('symbol_after', 'الرمز بعد الرقم (1,000 د.ع)'),
        ('symbol_before', 'الرمز قبل الرقم (د.ع 1,000)'),
        ('code_after', 'الكود بعد الرقم (1,000 IQD)'),
        ('code_before', 'الكود قبل الرقم (IQD 1,000)'),
    )
    
    company = models.OneToOneField(
        Company, 
        on_delete=models.CASCADE, 
        related_name='currency_settings',
        verbose_name='الشركة'
    )
    
    # العملة الأساسية
    primary_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='IQD',
        verbose_name='العملة الأساسية'
    )
    
    # العملات الثانوية المدعومة
    secondary_currencies = models.JSONField(
        default=list,
        blank=True,
        verbose_name='العملات الثانوية'
    )
    
    # تنسيق العرض
    display_format = models.CharField(
        max_length=20,
        choices=DISPLAY_FORMAT_CHOICES,
        default='symbol_after',
        verbose_name='تنسيق العرض'
    )
    
    # الخانات العشرية
    show_decimal_places = models.BooleanField(
        default=True,
        verbose_name='إظهار الخانات العشرية'
    )
    
    decimal_places_count = models.IntegerField(
        default=2,
        choices=((0, 'بدون خانات'), (1, 'خانة واحدة'), (2, 'خانتان'), (3, 'ثلاث خانات')),
        verbose_name='عدد الخانات العشرية'
    )
    
    # فاصل الآلاف
    use_thousands_separator = models.BooleanField(
        default=True,
        verbose_name='استخدام فاصل الآلاف'
    )
    
    thousands_separator = models.CharField(
        max_length=1,
        default=',',
        choices=((',', 'فاصلة (,)'), ('.', 'نقطة (.)'), (' ', 'مسافة ( )')),
        verbose_name='رمز فاصل الآلاف'
    )
    
    # فاصل العشرية
    decimal_separator = models.CharField(
        max_length=1,
        default='.',
        choices=(('.', 'نقطة (.)'), (',', 'فاصلة (,)')),
        verbose_name='رمز فاصل العشرية'
    )
    
    # أسعار الصرف المخصصة
    use_custom_exchange_rates = models.BooleanField(
        default=False,
        verbose_name='استخدام أسعار صرف مخصصة'
    )
    
    custom_exchange_rates = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='أسعار الصرف المخصصة'
    )
    
    # إعدادات التحديث التلقائي
    auto_update_rates = models.BooleanField(
        default=False,
        verbose_name='تحديث أسعار الصرف تلقائياً'
    )
    
    last_rates_update = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='آخر تحديث لأسعار الصرف'
    )
    
    # إعدادات التقارير
    default_report_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='IQD',
        verbose_name='عملة التقارير الافتراضية'
    )
    
    show_currency_conversion_in_reports = models.BooleanField(
        default=True,
        verbose_name='إظهار تحويل العملات في التقارير'
    )
    
    # إعدادات الفواتير
    invoice_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='IQD',
        verbose_name='عملة الفواتير'
    )
    
    show_currency_note_in_invoices = models.BooleanField(
        default=True,
        verbose_name='إظهار ملاحظة العملة في الفواتير'
    )
    
    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='تم التحديث بواسطة'
    )
    
    class Meta:
        verbose_name = 'إعدادات العملة'
        verbose_name_plural = 'إعدادات العملات'
        db_table = 'company_currency_settings'
    
    def __str__(self):
        return f"إعدادات العملة - {self.company.name}"
    
    def get_currency_symbol(self, currency_code=None):
        """الحصول على رمز العملة"""
        if not currency_code:
            currency_code = self.primary_currency
        
        symbols = {
            'IQD': 'د.ع',
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'SAR': 'ر.س',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع',
            'JOD': 'د.أ',
            'LBP': 'ل.ل',
            'EGP': 'ج.م',
            'TRY': '₺',
            'IRR': 'ر.إ',
        }
        
        return symbols.get(currency_code, currency_code)
    
    def format_amount(self, amount, currency_code=None):
        """تنسيق المبلغ حسب إعدادات الشركة"""
        try:
            amount = float(amount)
            
            if not currency_code:
                currency_code = self.primary_currency
            
            # تحديد عدد الخانات العشرية
            if not self.show_decimal_places:
                decimal_places = 0
            else:
                decimal_places = self.decimal_places_count
            
            # تنسيق الرقم
            if decimal_places > 0:
                if self.use_thousands_separator:
                    # استخدام تنسيق مخصص للفواصل
                    formatted_amount = f"{amount:,.{decimal_places}f}"
                    # استبدال الفواصل حسب الإعدادات
                    if self.thousands_separator != ',':
                        formatted_amount = formatted_amount.replace(',', '|TEMP|')
                        formatted_amount = formatted_amount.replace('.', self.decimal_separator)
                        formatted_amount = formatted_amount.replace('|TEMP|', self.thousands_separator)
                    elif self.decimal_separator != '.':
                        formatted_amount = formatted_amount.replace('.', self.decimal_separator)
                else:
                    formatted_amount = f"{amount:.{decimal_places}f}"
                    if self.decimal_separator != '.':
                        formatted_amount = formatted_amount.replace('.', self.decimal_separator)
            else:
                if self.use_thousands_separator:
                    formatted_amount = f"{int(amount):,}"
                    if self.thousands_separator != ',':
                        formatted_amount = formatted_amount.replace(',', self.thousands_separator)
                else:
                    formatted_amount = str(int(amount))
            
            # الحصول على رمز العملة
            symbol = self.get_currency_symbol(currency_code)
            
            # تطبيق تنسيق العرض
            if self.display_format == 'symbol_before':
                return f"{symbol} {formatted_amount}"
            elif self.display_format == 'code_before':
                return f"{currency_code} {formatted_amount}"
            elif self.display_format == 'code_after':
                return f"{formatted_amount} {currency_code}"
            else:  # symbol_after (default)
                return f"{formatted_amount} {symbol}"
                
        except (ValueError, TypeError):
            return f"0 {self.get_currency_symbol(currency_code)}"
    
    def get_exchange_rate(self, from_currency, to_currency):
        """الحصول على سعر الصرف بين عملتين"""
        if from_currency == to_currency:
            return 1.0
        
        if self.use_custom_exchange_rates:
            # البحث في أسعار الصرف المخصصة
            rate_key = f"{from_currency}_to_{to_currency}"
            if rate_key in self.custom_exchange_rates:
                return float(self.custom_exchange_rates[rate_key])
            
            # البحث في الاتجاه المعاكس
            reverse_key = f"{to_currency}_to_{from_currency}"
            if reverse_key in self.custom_exchange_rates:
                return 1.0 / float(self.custom_exchange_rates[reverse_key])
        
        # استخدام أسعار النظام الافتراضية
        from backend.super_admin.models import Currency
        try:
            from_curr = Currency.objects.get(code=from_currency, is_active=True)
            to_curr = Currency.objects.get(code=to_currency, is_active=True)
            
            # تحويل عبر الدولار
            if from_currency == 'USD':
                return float(to_curr.exchange_rate_to_usd)
            elif to_currency == 'USD':
                return 1.0 / float(from_curr.exchange_rate_to_usd)
            else:
                usd_rate = 1.0 / float(from_curr.exchange_rate_to_usd)
                return usd_rate * float(to_curr.exchange_rate_to_usd)
                
        except Currency.DoesNotExist:
            return 1.0
    
    def convert_amount(self, amount, from_currency, to_currency):
        """تحويل مبلغ من عملة إلى أخرى"""
        try:
            amount = float(amount)
            rate = self.get_exchange_rate(from_currency, to_currency)
            return amount * rate
        except (ValueError, TypeError):
            return 0.0
    
    def get_supported_currencies(self):
        """الحصول على قائمة العملات المدعومة"""
        currencies = [self.primary_currency]
        if self.secondary_currencies:
            currencies.extend(self.secondary_currencies)
        return list(set(currencies))  # إزالة التكرار
    
    def update_exchange_rate(self, from_currency, to_currency, rate):
        """تحديث سعر صرف مخصص"""
        if not self.custom_exchange_rates:
            self.custom_exchange_rates = {}
        
        rate_key = f"{from_currency}_to_{to_currency}"
        self.custom_exchange_rates[rate_key] = float(rate)
        self.save()
    
    def get_currency_settings_summary(self):
        """الحصول على ملخص إعدادات العملة"""
        return {
            'primary_currency': self.primary_currency,
            'symbol': self.get_currency_symbol(),
            'display_format': self.get_display_format_display(),
            'decimal_places': self.decimal_places_count if self.show_decimal_places else 0,
            'thousands_separator': self.thousands_separator if self.use_thousands_separator else None,
            'decimal_separator': self.decimal_separator,
            'supported_currencies': self.get_supported_currencies(),
            'custom_rates_enabled': self.use_custom_exchange_rates,
            'example': self.format_amount(1000)
        }
