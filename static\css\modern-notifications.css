/* نظام الإشعارات العصري */
.modern-notification {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    min-width: 320px;
    max-width: 400px;
}

.modern-notification.show {
    transform: translateX(0) scale(1);
    opacity: 1;
}

.modern-notification.removing {
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
    transition: all 0.3s ease-in;
}

.modern-notification::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.modern-notification.success::before {
    background: linear-gradient(135deg, #10b981, #059669);
}

.modern-notification.error::before {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.modern-notification.warning::before {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.modern-notification.info::before {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 20px 24px;
    position: relative;
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
    margin-top: 2px;
}

.modern-notification.success .notification-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.modern-notification.error .notification-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.modern-notification.warning .notification-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.modern-notification.info .notification-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.notification-message {
    flex: 1;
    color: #1f2937;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

.notification-close {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.notification-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transform: translateX(-100%);
    animation: progressBar 5s linear forwards;
}

.modern-notification.success .notification-progress::after {
    background: linear-gradient(135deg, #10b981, #059669);
}

.modern-notification.error .notification-progress::after {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation-duration: 7s;
}

.modern-notification.warning .notification-progress::after {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    animation-duration: 6s;
}

@keyframes progressBar {
    to {
        transform: translateX(0);
    }
}

/* نظام التأكيد العصري */
.modern-confirm-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.modern-confirm-overlay.show {
    opacity: 1;
}

.modern-confirm-overlay.removing {
    opacity: 0;
    transition: opacity 0.3s ease-in;
}

.modern-confirm-modal {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 100%;
    font-family: 'Tajawal', sans-serif;
    overflow: hidden;
    transform: scale(0.9) translateY(-20px);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.modern-confirm-overlay.show .modern-confirm-modal {
    transform: scale(1) translateY(0);
}

.modern-confirm-header {
    padding: 24px 24px 0;
    text-align: center;
}

.modern-confirm-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.modern-confirm-modal.warning .modern-confirm-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.modern-confirm-modal.success .modern-confirm-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.modern-confirm-modal.error .modern-confirm-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.modern-confirm-modal.info .modern-confirm-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.modern-confirm-title {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.modern-confirm-body {
    padding: 16px 24px 24px;
    text-align: center;
}

.modern-confirm-message {
    color: #6b7280;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
}

.modern-confirm-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding: 0 24px 24px;
}

.modern-btn {
    padding: 12px 24px;
    border-radius: 12px;
    border: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: 'Tajawal', sans-serif;
    min-width: 100px;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.modern-btn-secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.modern-btn-secondary:hover {
    background: rgba(107, 114, 128, 0.15);
    color: #374151;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 640px) {
    #modern-notifications-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
    
    .modern-confirm-overlay {
        padding: 10px;
    }
    
    .modern-confirm-actions {
        flex-direction: column;
    }
    
    .modern-btn {
        width: 100%;
    }
}

/* إخفاء رسائل Django القديمة */
.messages {
    display: none !important;
}

.alert {
    display: none !important;
}
