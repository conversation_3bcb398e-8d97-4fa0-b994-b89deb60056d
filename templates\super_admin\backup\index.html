{% extends 'super_admin/base.html' %}
{% load static %}

{% block title %}النسخ الاحتياطي | لوحة المسؤول الأعلى{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق البطاقات */
    .backup-card {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .backup-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    /* تنسيق الأيقونات */
    .backup-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1.25rem;
    }

    /* تنسيق جدول النسخ الاحتياطية */
    .backup-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .backup-table th, .backup-table td {
        padding: 0.75rem 1rem;
        text-align: right;
    }

    .backup-table th {
        background-color: #f9fafb;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .backup-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e5e7eb;
    }

    .backup-table tbody tr:hover {
        background-color: #f3f4f6;
    }

    /* تنسيق الأزرار */
    button, .btn {
        transition: all 0.3s ease;
    }

    button:hover, .btn:hover {
        transform: translateY(-1px);
    }

    /* تنسيق الرسائل */
    .alert {
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-10 px-6 bg-white shadow-xl rounded-2xl space-y-8">
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-blue-800 flex items-center">
            <i class="fas fa-database text-blue-600 ml-3 text-2xl"></i>
            النسخ الاحتياطي
        </h2>
        <a href="{% url 'super_admin:dashboard' %}" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-colors shadow-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة للوحة التحكم
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md alert" role="alert">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle text-blue-500 text-xl ml-3"></i>
                {% endif %}
                <span class="block sm:inline">{{ message }}</span>
                <button type="button" class="absolute top-0 left-0 mt-3 ml-3 text-{{ message.tags }}-500 hover:text-{{ message.tags }}-700 close-message">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- قسم إنشاء نسخة احتياطية جديدة -->
    <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md mb-6">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-green-400 to-green-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-plus-circle text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">إنشاء نسخة احتياطية جديدة</h3>
                <p class="text-gray-500 text-sm mt-1">إنشاء نسخة احتياطية لقواعد البيانات</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- نسخة احتياطية للنظام بالكامل -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-500 text-white p-3 rounded-full ml-4 shadow-sm">
                        <i class="fas fa-server text-lg"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-bold text-blue-800">النظام بالكامل</h4>
                        <p class="text-blue-600 text-sm mt-1">إنشاء نسخة احتياطية لجميع قواعد البيانات</p>
                    </div>
                </div>
                <form method="post" action="{% url 'super_admin:create_backup' %}" class="mt-4">
                    {% csrf_token %}
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-blue-100 mb-4">
                        <label for="system_backup_description" class="block text-sm font-medium text-gray-700 mb-2">وصف النسخة الاحتياطية (اختياري)</label>
                        <textarea name="description" id="system_backup_description" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="أدخل وصفًا للنسخة الاحتياطية"></textarea>
                    </div>
                    <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-database ml-2"></i> إنشاء نسخة احتياطية للنظام
                    </button>
                </form>
            </div>

            <!-- نسخة احتياطية لشركة محددة -->
            <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-lg border border-purple-200 shadow-sm hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-500 text-white p-3 rounded-full ml-4 shadow-sm">
                        <i class="fas fa-building text-lg"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-bold text-purple-800">شركة محددة</h4>
                        <p class="text-purple-600 text-sm mt-1">إنشاء نسخة احتياطية لشركة محددة</p>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-purple-100 mb-4">
                    <label for="company_id" class="block text-sm font-medium text-gray-700 mb-2">اختر الشركة</label>
                    <select id="company_id" name="company_id" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                        {% for company in companies %}
                            <option value="{{ company.id }}">{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-purple-100 mb-4">
                    <label for="company_backup_description" class="block text-sm font-medium text-gray-700 mb-2">وصف النسخة الاحتياطية (اختياري)</label>
                    <textarea id="company_backup_description" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm" placeholder="أدخل وصفًا للنسخة الاحتياطية"></textarea>
                </div>
                <button type="button" id="create_company_backup" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                    <i class="fas fa-database ml-2"></i> إنشاء نسخة احتياطية للشركة
                </button>
            </div>
        </div>
    </div>

    <!-- قسم النسخ الاحتياطية السابقة -->
    <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-md">
        <div class="flex items-center mb-6 border-b border-gray-100 pb-4">
            <div class="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-3 rounded-full ml-4 shadow-md">
                <i class="fas fa-history text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">النسخ الاحتياطية السابقة</h3>
                <p class="text-gray-500 text-sm mt-1">قائمة النسخ الاحتياطية المحفوظة في النظام</p>
            </div>
            <div class="mr-auto">
                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full shadow-sm">
                    {% if backups %}{{ backups|length }} نسخة{% else %}لا توجد نسخ{% endif %}
                </span>
            </div>
        </div>

        <div class="overflow-x-auto rounded-lg shadow">
            <table class="backup-table min-w-full divide-y divide-gray-200">
                <thead class="bg-gradient-to-r from-blue-50 to-blue-100">
                    <tr>
                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tr-lg py-3 px-4">الاسم</th>
                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">التاريخ</th>
                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider py-3 px-4">الحجم</th>
                        <th scope="col" class="text-right text-xs font-medium text-blue-700 uppercase tracking-wider rounded-tl-lg py-3 px-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if backups %}
                        {% for backup in backups %}
                        <tr class="hover:bg-blue-50 transition-colors">
                            <td class="text-sm font-medium text-gray-900 py-4 px-4">
                                <div class="flex items-center">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-file-archive"></i>
                                    </div>
                                    {{ backup.filename }}
                                </div>
                            </td>
                            <td class="text-sm text-gray-600 py-4 px-4">
                                <div class="flex items-center">
                                    <div class="bg-gray-100 text-gray-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    {{ backup.created_at }}
                                </div>
                            </td>
                            <td class="text-sm text-gray-600 py-4 px-4">
                                <div class="flex items-center">
                                    <div class="bg-green-100 text-green-600 p-2 rounded-full ml-3">
                                        <i class="fas fa-hdd"></i>
                                    </div>
                                    {{ backup.size_formatted }}
                                </div>
                            </td>
                            <td class="text-sm font-medium py-4 px-4">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{% url 'super_admin:download_system_backup' filename=backup.filename %}" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-md hover:bg-blue-200 transition-colors">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button type="button" class="restore-backup-btn bg-green-100 text-green-700 px-3 py-1 rounded-md hover:bg-green-200 transition-colors" data-filename="{{ backup.filename }}">
                                        <i class="fas fa-undo-alt"></i>
                                    </button>
                                    <button type="button" class="delete-backup-btn bg-red-100 text-red-700 px-3 py-1 rounded-md hover:bg-red-200 transition-colors" data-filename="{{ backup.filename }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="4" class="text-center py-8">
                                <div class="flex flex-col items-center">
                                    <div class="bg-blue-100 text-blue-500 p-4 rounded-full mb-4 shadow-sm">
                                        <i class="fas fa-database text-3xl"></i>
                                    </div>
                                    <p class="text-lg font-medium text-gray-700">لا توجد نسخ احتياطية</p>
                                    <p class="text-sm text-gray-500 mt-2 max-w-md">لم يتم العثور على أي نسخ احتياطية. قم بإنشاء نسخة احتياطية جديدة.</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نافذة تأكيد استعادة النسخة الاحتياطية -->
<div id="restore-backup-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-4">
            <h3 class="text-lg font-bold">تأكيد استعادة النسخة الاحتياطية</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center mb-4 bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full ml-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="text-yellow-700">هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
            </div>
            <p class="text-gray-700 mb-4">اسم الملف: <span id="restore-filename" class="font-bold"></span></p>
            <form id="restore-form" method="post" action="{% url 'super_admin:restore_backup' %}">
                {% csrf_token %}
                <input type="hidden" name="filename" id="restore-filename-input">
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" id="cancel-restore" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">إلغاء</button>
                    <button type="submit" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors">تأكيد الاستعادة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تأكيد حذف النسخة الاحتياطية -->
<div id="delete-backup-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4">
            <h3 class="text-lg font-bold">تأكيد حذف النسخة الاحتياطية</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center mb-4 bg-red-50 p-4 rounded-lg border border-red-200">
                <div class="bg-red-100 text-red-600 p-2 rounded-full ml-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="text-red-700">هل أنت متأكد من حذف النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <p class="text-gray-700 mb-4">اسم الملف: <span id="delete-filename" class="font-bold"></span></p>
            <form id="delete-form" method="post" action="{% url 'super_admin:delete_system_backup' filename='placeholder' %}">
                {% csrf_token %}
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" id="cancel-delete" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">إلغاء</button>
                    <button type="submit" class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-red-700 transition-colors">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إغلاق الرسائل
        document.querySelectorAll('.close-message').forEach(function(button) {
            button.addEventListener('click', function() {
                this.closest('.alert').remove();
            });
        });

        // إنشاء نسخة احتياطية لشركة محددة
        document.getElementById('create_company_backup').addEventListener('click', function() {
            const companyId = document.getElementById('company_id').value;
            const description = document.getElementById('company_backup_description').value;
            
            // إظهار رسالة تحميل
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg relative flex items-center shadow-md alert';
            loadingMessage.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-500 text-xl ml-3"></i><span class="block sm:inline">جاري إنشاء النسخة الاحتياطية...</span>';
            
            document.querySelector('.max-w-7xl').insertBefore(loadingMessage, document.querySelector('.bg-white.p-6.rounded-xl.border.border-gray-200.shadow-md.mb-6'));
            
            // إرسال طلب AJAX
            fetch('{% url "super_admin:create_backup" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: new URLSearchParams({
                    'company_id': companyId,
                    'description': description
                })
            })
            .then(response => response.json())
            .then(data => {
                // إزالة رسالة التحميل
                loadingMessage.remove();
                
                // إظهار رسالة النجاح أو الفشل
                const messageClass = data.success ? 'success' : 'error';
                const messageIcon = data.success ? 'check-circle' : 'exclamation-circle';
                
                const resultMessage = document.createElement('div');
                resultMessage.className = `bg-${messageClass}-100 border border-${messageClass}-400 text-${messageClass}-700 px-4 py-3 rounded-lg relative flex items-center shadow-md alert`;
                resultMessage.innerHTML = `<i class="fas fa-${messageIcon} text-${messageClass}-500 text-xl ml-3"></i><span class="block sm:inline">${data.message}</span>`;
                
                document.querySelector('.max-w-7xl').insertBefore(resultMessage, document.querySelector('.bg-white.p-6.rounded-xl.border.border-gray-200.shadow-md.mb-6'));
                
                // إعادة تحميل الصفحة بعد 2 ثانية في حالة النجاح
                if (data.success) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            })
            .catch(error => {
                // إزالة رسالة التحميل
                loadingMessage.remove();
                
                // إظهار رسالة الخطأ
                const errorMessage = document.createElement('div');
                errorMessage.className = 'bg-error-100 border border-error-400 text-error-700 px-4 py-3 rounded-lg relative flex items-center shadow-md alert';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-circle text-error-500 text-xl ml-3"></i><span class="block sm:inline">حدث خطأ أثناء إنشاء النسخة الاحتياطية</span>';
                
                document.querySelector('.max-w-7xl').insertBefore(errorMessage, document.querySelector('.bg-white.p-6.rounded-xl.border.border-gray-200.shadow-md.mb-6'));
            });
        });

        // استعادة النسخة الاحتياطية
        document.querySelectorAll('.restore-backup-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                document.getElementById('restore-filename').textContent = filename;
                document.getElementById('restore-filename-input').value = filename;
                document.getElementById('restore-backup-modal').classList.remove('hidden');
            });
        });

        // إلغاء استعادة النسخة الاحتياطية
        document.getElementById('cancel-restore').addEventListener('click', function() {
            document.getElementById('restore-backup-modal').classList.add('hidden');
        });

        // حذف النسخة الاحتياطية
        document.querySelectorAll('.delete-backup-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                document.getElementById('delete-filename').textContent = filename;
                document.getElementById('delete-form').action = "{% url 'super_admin:delete_system_backup' filename='placeholder' %}".replace('placeholder', filename);
                document.getElementById('delete-backup-modal').classList.remove('hidden');
            });
        });

        // إلغاء حذف النسخة الاحتياطية
        document.getElementById('cancel-delete').addEventListener('click', function() {
            document.getElementById('delete-backup-modal').classList.add('hidden');
        });
    });
</script>
{% endblock %}
