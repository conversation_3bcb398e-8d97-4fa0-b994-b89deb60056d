{% extends 'final_layout.html' %}
{% load static %}

{% block inner_title %}إضافة عامل عقد دائم | منصة استقدامي السحابية{% endblock %}

{% block inner_content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">إضافة عامل عقد دائم</h2>
            <p class="text-sm text-gray-500 mt-1">
                إضافة عامل جديد بعقد دائم إلى النظام
            </p>
        </div>
        <a href="{% url 'workers:contract_workers' %}"
           class="bg-primary-light text-white dark:bg-dark-button dark:text-gray-100 px-5 py-2 rounded-lg hover:bg-primary hover:shadow-md dark:hover:bg-dark-accent transition-all duration-300 flex items-center">
            <i class="fas fa-arrow-right ml-2 text-sm"></i>
            <span>العودة</span>
        </a>
    </div>

    <!-- Form -->
    <form method="POST" enctype="multipart/form-data" class="space-y-6 bg-white p-6 rounded-lg shadow-md">
        {% csrf_token %}
        <input type="hidden" name="worker_type" value="contract">

        <!-- Personal Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">البيانات الشخصية</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="first_name" class="block mb-1 text-gray-700">الاسم الأول</label>
                    <input type="text" id="first_name" name="first_name" value="{{ worker.first_name|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="last_name" class="block mb-1 text-gray-700">الاسم الأخير</label>
                    <input type="text" id="last_name" name="last_name" value="{{ worker.last_name|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="gender" class="block mb-1 text-gray-700">الجنس</label>
                    <select id="gender" name="gender" class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                        <option value="M" {% if worker.gender == 'M' %}selected{% endif %}>ذكر</option>
                        <option value="F" {% if worker.gender == 'F' %}selected{% endif %}>أنثى</option>
                    </select>
                </div>

                <div>
                    <label for="nationality" class="block mb-1 text-gray-700">الجنسية</label>
                    <select id="nationality" name="nationality" class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                        <option value="">اختر الجنسية</option>
                        {% for code, name in nationality_choices %}
                            <option value="{{ code }}" {% if worker.nationality == code %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="date_of_birth" class="block mb-1 text-gray-700">تاريخ الميلاد</label>
                    <input type="date" id="date_of_birth" name="date_of_birth" value="{{ worker.date_of_birth|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="phone_number" class="block mb-1 text-gray-700">رقم الهاتف</label>
                    <input type="tel" id="phone_number" name="phone_number" value="{{ worker.phone_number|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500"
                           placeholder="مثال: +966501234567">
                </div>

                <div>
                    <label for="job_title" class="block mb-1 text-gray-700">المهنة</label>
                    <input type="text" id="job_title" name="job_title" value="{{ worker.job_title|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500"
                           placeholder="مثال: سائق، عامل، ممرضة">
                </div>
            </div>
        </div>

        <!-- Passport Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">بيانات جواز السفر</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="passport_number" class="block mb-1 text-gray-700">رقم جواز السفر</label>
                    <input type="text" id="passport_number" name="passport_number" value="{{ worker.passport_number|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="passport_expiry" class="block mb-1 text-gray-700">تاريخ انتهاء جواز السفر</label>
                    <input type="date" id="passport_expiry" name="passport_expiry" value="{{ worker.passport_expiry|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="visa_number" class="block mb-1 text-gray-700">رقم التأشيرة</label>
                    <input type="text" id="visa_number" name="visa_number" value="{{ worker.visa_number|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="entry_date" class="block mb-1 text-gray-700">تاريخ إصدار التأشيرة / الستيكر</label>
                    <input type="date" id="entry_date" name="entry_date" value="{{ worker.entry_date|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="visa_expiry" class="block mb-1 text-gray-700">تاريخ انتهاء التأشيرة / الستيكر</label>
                    <input type="date" id="visa_expiry" name="visa_expiry" value="{{ worker.visa_expiry|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Contract Information Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">بيانات العقد الدائم</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="date_joined" class="block mb-1 text-gray-700">تاريخ بداية العقد</label>
                    <input type="date" id="date_joined" name="date_joined" value="{{ worker.date_joined|date:'Y-m-d'|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>

                <div>
                    <label for="company_beneficiary" class="block mb-1 text-gray-700">الشركة المستفيدة</label>
                    <input type="text" id="company_beneficiary" name="company_beneficiary" value="{{ worker.company_beneficiary|default:'' }}"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="contract_type" class="block mb-1 text-gray-700">نوع العقد</label>
                    <select id="contract_type" name="contract_type" class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                        <option value="full_time">دوام كامل</option>
                        <option value="part_time">دوام جزئي</option>
                    </select>
                </div>

                <div>
                    <label for="salary" class="block mb-1 text-gray-700">الراتب</label>
                    <input type="number" id="salary" name="salary" value="{{ worker.salary|default:'' }}" step="0.01"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500" required>
                </div>
            </div>
        </div>

        <!-- Documents Section -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-r-4 border-blue-600 pr-3">الصور والمستندات</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="photo" class="block mb-1 text-gray-700">الصورة الشخصية</label>
                    <input type="file" id="photo" name="photo" accept="image/*"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="passport_image" class="block mb-1 text-gray-700">صورة جواز السفر</label>
                    <input type="file" id="passport_image" name="passport_image" accept="image/*"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>

                <div>
                    <label for="visa_image" class="block mb-1 text-gray-700">صورة التأشيرة/الستيكر</label>
                    <input type="file" id="visa_image" name="visa_image" accept="image/*"
                           class="w-full border border-gray-300 rounded-md px-4 py-2 focus:ring focus:ring-blue-200 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
            <button type="submit" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md shadow-md transition-colors duration-300">
                <i class="fas fa-plus ml-2"></i> إضافة العامل
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block inner_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة أي سلوك JavaScript هنا
    });
</script>
{% endblock %}
