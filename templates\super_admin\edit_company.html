{% extends 'layouts/super_admin.html' %}

{% block title %}تعديل الشركة - {{ company.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-4 space-x-reverse">
                <a href="{% url 'super_admin:company_detail' company.id %}" 
                   class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    العودة لتفاصيل الشركة
                </a>
                <h1 class="text-3xl font-bold text-white">
                    <i class="fas fa-edit text-blue-400 mr-3"></i>
                    تعديل الشركة
                </h1>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Company Name -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                            <i class="fas fa-building mr-2 text-blue-400"></i>
                            اسم الشركة
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="mt-1 text-red-400 text-sm">
                                {% for error in form.name.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                            <i class="fas fa-toggle-on mr-2 text-green-400"></i>
                            حالة الشركة
                        </label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="mt-1 text-red-400 text-sm">
                                {% for error in form.status.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Serial Number (Read Only) -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-barcode mr-2 text-purple-400"></i>
                        الرقم التسلسلي
                    </label>
                    <input type="text" value="{{ company.serial_number }}" readonly
                           class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white font-mono cursor-not-allowed">
                    <p class="mt-1 text-gray-400 text-sm">الرقم التسلسلي لا يمكن تعديله</p>
                </div>

                <!-- Expiry Date -->
                <div>
                    <label for="{{ form.expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-calendar-alt mr-2 text-yellow-400"></i>
                        تاريخ انتهاء الصلاحية
                    </label>
                    {{ form.expiry_date }}
                    {% if form.expiry_date.errors %}
                        <div class="mt-1 text-red-400 text-sm">
                            {% for error in form.expiry_date.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Database Settings -->
                <div class="border-t border-gray-700 pt-6">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i class="fas fa-database mr-2 text-green-400"></i>
                        إعدادات قاعدة البيانات
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Database Name -->
                        <div>
                            <label for="{{ form.database_name.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                                اسم قاعدة البيانات
                            </label>
                            {{ form.database_name }}
                            {% if form.database_name.errors %}
                                <div class="mt-1 text-red-400 text-sm">
                                    {% for error in form.database_name.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Database User -->
                        <div>
                            <label for="{{ form.database_user.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                                مستخدم قاعدة البيانات
                            </label>
                            {{ form.database_user }}
                            {% if form.database_user.errors %}
                                <div class="mt-1 text-red-400 text-sm">
                                    {% for error in form.database_user.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Database Password -->
                        <div>
                            <label for="{{ form.database_password.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                                كلمة مرور قاعدة البيانات
                            </label>
                            {{ form.database_password }}
                            {% if form.database_password.errors %}
                                <div class="mt-1 text-red-400 text-sm">
                                    {% for error in form.database_password.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Database Host -->
                        <div>
                            <label for="{{ form.database_host.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                                خادم قاعدة البيانات
                            </label>
                            {{ form.database_host }}
                            {% if form.database_host.errors %}
                                <div class="mt-1 text-red-400 text-sm">
                                    {% for error in form.database_host.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Test Connection Button -->
                    <div class="mt-4">
                        <button type="button" onclick="testConnection()" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                            <i class="fas fa-plug mr-2"></i>
                            اختبار الاتصال
                        </button>
                        <div id="connection-result" class="mt-2"></div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700">
                    <button type="submit" 
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        حفظ التغييرات
                    </button>
                    
                    <a href="{% url 'super_admin:company_detail' company.id %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fas fa-times mr-2"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function testConnection() {
    const button = event.target;
    const resultDiv = document.getElementById('connection-result');
    
    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الاختبار...';
    
    // Get form data
    const formData = new FormData();
    formData.append('test_connection', '1');
    formData.append('database_name', document.getElementById('{{ form.database_name.id_for_label }}').value);
    formData.append('database_user', document.getElementById('{{ form.database_user.id_for_label }}').value);
    formData.append('database_password', document.getElementById('{{ form.database_password.id_for_label }}').value);
    formData.append('database_host', document.getElementById('{{ form.database_host.id_for_label }}').value);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="bg-green-600/20 border border-green-600 text-green-400 px-4 py-2 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i>
                    ${data.message}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="bg-red-600/20 border border-red-600 text-red-400 px-4 py-2 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="bg-red-600/20 border border-red-600 text-red-400 px-4 py-2 rounded-lg">
                <i class="fas fa-exclamation-circle mr-2"></i>
                حدث خطأ أثناء اختبار الاتصال
            </div>
        `;
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-plug mr-2"></i>اختبار الاتصال';
    });
}
</script>
{% endblock %}
