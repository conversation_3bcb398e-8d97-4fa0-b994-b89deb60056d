/**
 * تهيئة Alpine.js
 * هذا الملف يقوم بتهيئة Alpine.js وإعداد المتغيرات والوظائف العامة
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Alpine.js initialization script loaded');
    
    // التحقق من وجود Alpine.js
    if (typeof Alpine === 'undefined') {
        console.error('Alpine.js is not loaded! Loading it now...');
        
        // تحميل Alpine.js ديناميكيًا إذا لم يكن موجودًا
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);
        
        // الانتظار حتى يتم تحميل Alpine.js
        script.onload = function() {
            console.log('Alpine.js loaded dynamically');
            initAlpine();
        };
    } else {
        console.log('Alpine.js already loaded');
        initAlpine();
    }
    
    // تهيئة Alpine.js
    function initAlpine() {
        if (typeof Alpine !== 'undefined' && typeof Alpine.store === 'function') {
            // تهيئة مخزن حالة نافذة تأكيد الحذف
            Alpine.store('deleteModal', {
                show: false,
                url: '',
                itemName: '',
                itemType: '',
                title: 'تأكيد الحذف',
                message: 'هل أنت متأكد من رغبتك في الحذف؟ هذا الإجراء لا يمكن التراجع عنه.',
                icon: 'fa-trash-alt',
                iconColor: 'text-red-600 dark:text-red-400',
                confirmText: 'تأكيد الحذف',
                confirmColor: 'bg-red-600 hover:bg-red-700',
                
                open(url, itemName = '', itemType = 'هذا العنصر') {
                    this.url = url;
                    this.itemName = itemName;
                    this.itemType = itemType;
                    this.show = true;
                    console.log('Delete modal opened with URL:', url);
                },
                
                openCustom(options) {
                    this.url = options.url;
                    this.title = options.title || 'تأكيد الحذف';
                    this.message = options.message || 'هل أنت متأكد من رغبتك في الحذف؟ هذا الإجراء لا يمكن التراجع عنه.';
                    this.icon = options.icon || 'fa-trash-alt';
                    this.iconColor = options.iconColor || 'text-red-600 dark:text-red-400';
                    this.confirmText = options.confirmText || 'تأكيد الحذف';
                    this.confirmColor = options.confirmColor || 'bg-red-600 hover:bg-red-700';
                    this.itemName = options.itemName || '';
                    this.itemType = options.itemType || 'هذا العنصر';
                    this.show = true;
                    console.log('Custom delete modal opened with URL:', options.url);
                },
                
                close() {
                    this.show = false;
                    console.log('Delete modal closed');
                },
                
                confirm() {
                    console.log('Delete confirmed for URL:', this.url);
                    this.show = false;
                    
                    // إرسال حدث لإرسال نموذج الحذف
                    const event = new CustomEvent('submit-delete-form', {
                        detail: this.url
                    });
                    window.dispatchEvent(event);
                }
            });
            
            // الاستماع لأحداث فتح نافذة تأكيد الحذف
            window.addEventListener('open-delete', function(event) {
                const url = event.detail;
                Alpine.store('deleteModal').open(url);
            });
            
            // الاستماع لأحداث فتح نافذة تأكيد الحذف المخصصة
            window.addEventListener('open-delete-custom', function(event) {
                const options = event.detail;
                Alpine.store('deleteModal').openCustom(options);
            });
            
            console.log('Alpine.js stores initialized');
        } else {
            console.error('Alpine.js or Alpine.store is not available');
        }
    }
});
