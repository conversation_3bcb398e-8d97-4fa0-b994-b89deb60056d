from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db import transaction
from django.utils import timezone
import secrets
import string

from .models import CompanyUser
from backend.super_admin.models import Company


def get_user_company(user):
    """الحصول على شركة المستخدم"""
    try:
        company_user = CompanyUser.objects.get(user=user, is_active=True)
        return company_user.company, company_user
    except CompanyUser.DoesNotExist:
        return None, None


def require_permission(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            company, company_user = get_user_company(request.user)
            if not company or not company_user.has_permission(permission):
                messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
                return redirect('company:dashboard')
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


@login_required
@require_permission('manage_users')
def users_list(request):
    """قائمة مستخدمي الشركة"""
    company, current_user = get_user_company(request.user)
    if not company:
        messages.error(request, 'لم يتم العثور على شركتك')
        return redirect('company:dashboard')

    # الحصول على جميع مستخدمي الشركة
    users = CompanyUser.objects.filter(company=company).select_related('user').order_by('-created_at')

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        users = users.filter(
            user__username__icontains=search_query
        ) | users.filter(
            user__first_name__icontains=search_query
        ) | users.filter(
            user__last_name__icontains=search_query
        )

    # التصفح
    paginator = Paginator(users, 10)
    page_number = request.GET.get('page')
    users_page = paginator.get_page(page_number)

    context = {
        'users': users_page,
        'search_query': search_query,
        'company': company,
        'current_user': current_user,
        'total_users': users.count(),
    }

    return render(request, 'company/users/list.html', context)


@login_required
@require_permission('manage_users')
def create_user(request):
    """إنشاء مستخدم جديد"""
    company, current_user = get_user_company(request.user)
    if not company:
        messages.error(request, 'لم يتم العثور على شركتك')
        return redirect('company:dashboard')

    if request.method == 'POST':
        try:
            with transaction.atomic():
                # الحصول على البيانات
                username = request.POST.get('username', '').strip()
                first_name = request.POST.get('first_name', '').strip()
                last_name = request.POST.get('last_name', '').strip()
                email = request.POST.get('email', '').strip()
                role = request.POST.get('role', 'employee')

                # التحقق من البيانات
                if not username or not first_name or not email:
                    messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                    return render(request, 'company/users/create.html', {
                        'company': company,
                        'roles': CompanyUser.ROLE_CHOICES
                    })

                # التحقق من عدم وجود اسم المستخدم
                if User.objects.filter(username=username).exists():
                    messages.error(request, 'اسم المستخدم موجود بالفعل')
                    return render(request, 'company/users/create.html', {
                        'company': company,
                        'roles': CompanyUser.ROLE_CHOICES
                    })

                # توليد كلمة مرور قوية
                password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
                password = ''.join(secrets.choice(password_chars) for _ in range(10))

                # إنشاء المستخدم
                user = User.objects.create_user(
                    username=username,
                    password=password,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    is_active=True,
                    is_staff=False,
                    is_superuser=False
                )

                # ربط المستخدم بالشركة مع إعدادات إخفاء الأقسام
                company_user = CompanyUser.objects.create(
                    user=user,
                    company=company,
                    role=role,
                    is_active=True,
                    created_by=request.user,
                    # إعدادات إخفاء الأقسام
                    hide_workers_section='hide_workers_section' in request.POST,
                    hide_clients_section='hide_clients_section' in request.POST,
                    hide_contracts_section='hide_contracts_section' in request.POST,
                    hide_procedures_section='hide_procedures_section' in request.POST,
                    hide_services_section='hide_services_section' in request.POST,
                    hide_finance_section='hide_finance_section' in request.POST,
                    hide_reports_section='hide_reports_section' in request.POST,
                    hide_tools_section='hide_tools_section' in request.POST,
                )

                messages.success(request, f'''
                تم إنشاء المستخدم بنجاح!<br>
                <strong>بيانات تسجيل الدخول:</strong><br>
                اسم المستخدم: <code>{username}</code><br>
                كلمة المرور: <code>{password}</code><br>
                الرقم التسلسلي: <code>{company.serial_number}</code>
                ''')

                return redirect('company:users_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}')

    context = {
        'company': company,
        'roles': CompanyUser.ROLE_CHOICES
    }

    return render(request, 'company/users/create.html', context)


@login_required
@require_permission('manage_users')
def edit_user(request, user_id):
    """تعديل مستخدم"""
    company, current_user = get_user_company(request.user)
    if not company:
        messages.error(request, 'لم يتم العثور على شركتك')
        return redirect('company:dashboard')

    # الحصول على المستخدم
    company_user = get_object_or_404(CompanyUser, id=user_id, company=company)

    # منع تعديل مدير الشركة الأساسي
    if company_user.role == 'admin' and company_user.created_by is None:
        messages.error(request, 'لا يمكن تعديل مدير الشركة الأساسي')
        return redirect('company:users_list')

    if request.method == 'POST':
        try:
            # تحديث بيانات المستخدم
            company_user.user.first_name = request.POST.get('first_name', '').strip()
            company_user.user.last_name = request.POST.get('last_name', '').strip()
            company_user.user.email = request.POST.get('email', '').strip()
            company_user.role = request.POST.get('role', company_user.role)
            company_user.is_active = 'is_active' in request.POST

            company_user.user.save()
            company_user.save()

            messages.success(request, 'تم تحديث بيانات المستخدم بنجاح')
            return redirect('company:users_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث المستخدم: {str(e)}')

    context = {
        'company': company,
        'company_user': company_user,
        'roles': CompanyUser.ROLE_CHOICES
    }

    return render(request, 'company/users/edit.html', context)


@login_required
@require_permission('manage_users')
def delete_user(request, user_id):
    """حذف مستخدم"""
    company, current_user = get_user_company(request.user)
    if not company:
        messages.error(request, 'لم يتم العثور على شركتك')
        return redirect('company:dashboard')

    # الحصول على المستخدم
    company_user = get_object_or_404(CompanyUser, id=user_id, company=company)

    # منع حذف مدير الشركة الأساسي
    if company_user.role == 'admin' and company_user.created_by is None:
        messages.error(request, 'لا يمكن حذف مدير الشركة الأساسي')
        return redirect('company:users_list')

    # منع حذف النفس
    if company_user.user == request.user:
        messages.error(request, 'لا يمكنك حذف حسابك الخاص')
        return redirect('company:users_list')

    if request.method == 'POST':
        try:
            username = company_user.user.username
            company_user.user.delete()  # سيحذف CompanyUser تلقائياً

            messages.success(request, f'تم حذف المستخدم {username} بنجاح')
            return redirect('company:users_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف المستخدم: {str(e)}')

    context = {
        'company': company,
        'company_user': company_user
    }

    return render(request, 'company/users/delete.html', context)


@login_required
@csrf_exempt
def reset_password(request, user_id):
    """إعادة تعيين كلمة مرور المستخدم"""
    if request.method == 'POST':
        company, current_user = get_user_company(request.user)
        if not company or not current_user.has_permission('manage_users'):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

        try:
            company_user = get_object_or_404(CompanyUser, id=user_id, company=company)

            # توليد كلمة مرور جديدة
            password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
            new_password = ''.join(secrets.choice(password_chars) for _ in range(10))

            # تحديث كلمة المرور
            company_user.user.set_password(new_password)
            company_user.user.save()

            return JsonResponse({
                'success': True,
                'message': 'تم إعادة تعيين كلمة المرور بنجاح',
                'new_password': new_password
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})
