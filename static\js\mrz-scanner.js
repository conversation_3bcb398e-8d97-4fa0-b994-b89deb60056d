/**
 * مكتبة قارئ منطقة القراءة الآلية (MRZ) لجوازات السفر
 *
 * هذه المكتبة تستخدم Tesseract.js للتعرف الضوئي على الحروف (OCR)
 * وتحليل منطقة القراءة الآلية (MRZ) في جوازات السفر
 */

class MRZScanner {
    /**
     * إنشاء كائن قارئ منطقة القراءة الآلية
     */
    constructor() {
        // تهيئة المتغيرات
        this.initialized = false;
        this.worker = null;
        this.lang = 'eng';
        this.tesseractLoaded = false;
        this.progressCallback = null;

        // تحميل Tesseract.js من CDN
        this.loadTesseract();
    }

    /**
     * تحميل مكتبة Tesseract.js
     * @private
     */
    loadTesseract() {
        // التحقق مما إذا كانت المكتبة محملة بالفعل
        if (typeof Tesseract !== 'undefined') {
            console.log('Tesseract.js محمل بالفعل');
            this.tesseractLoaded = true;
            return Promise.resolve();
        }

        console.log('جاري تحميل Tesseract.js...');

        // نستخدم المكتبة المحملة مسبقاً في الصفحة
        this.tesseractLoaded = true;
        return Promise.resolve();
    }

    /**
     * تهيئة قارئ منطقة القراءة الآلية
     * @returns {Promise} وعد يحل عند اكتمال التهيئة
     */
    async initialize() {
        if (this.initialized) {
            return Promise.resolve();
        }

        try {
            console.log('جاري تهيئة قارئ منطقة القراءة الآلية...');

            // التأكد من تحميل Tesseract.js
            if (!this.tesseractLoaded) {
                await this.loadTesseract();
            }

            // التحقق من وجود Tesseract
            if (typeof Tesseract === 'undefined') {
                throw new Error('Tesseract.js غير متوفر');
            }

            console.log('إنشاء عامل Tesseract...');

            // إنشاء عامل Tesseract
            this.worker = await Tesseract.createWorker({
                logger: m => console.log(m)
            });

            console.log('تحميل لغة التعرف...');

            // تحميل لغة التعرف
            await this.worker.loadLanguage('eng');
            await this.worker.initialize('eng');

            console.log('تكوين إعدادات التعرف...');

            // تكوين إعدادات التعرف لمنطقة القراءة الآلية
            await this.worker.setParameters({
                tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789<',
                tessedit_pageseg_mode: '6', // تقسيم الصفحة إلى كتل نصية
                tessjs_create_hocr: '0',
                tessjs_create_tsv: '0',
                tessjs_create_box: '0',
                tessjs_create_unlv: '0',
                tessjs_create_osd: '0'
            });

            console.log('تم تهيئة قارئ منطقة القراءة الآلية بنجاح');

            this.initialized = true;
            return Promise.resolve();
        } catch (error) {
            console.error('خطأ في تهيئة قارئ منطقة القراءة الآلية:', error);
            return Promise.reject(error);
        }
    }

    /**
     * التعرف على منطقة القراءة الآلية من صورة
     * @param {File|Blob|String} image ملف الصورة أو URL الصورة
     * @param {Function} progressCallback دالة الاستدعاء لتتبع التقدم
     * @returns {Promise<Object>} وعد يحل إلى كائن يحتوي على بيانات منطقة القراءة الآلية
     */
    async recognize(image, progressCallback = null) {
        try {
            console.log('جاري التعرف على منطقة القراءة الآلية...');
            
            // تخزين دالة التقدم
            this.progressCallback = progressCallback;

            // التأكد من تهيئة القارئ
            if (!this.initialized) {
                await this.initialize();
            }

            // التحقق من وجود العامل
            if (!this.worker) {
                throw new Error('لم يتم تهيئة عامل Tesseract');
            }
            
            // معالجة الصورة قبل التعرف عليها
            const processedImage = await this.preprocessImage(image);

            console.log('جاري معالجة الصورة...');

            // التعرف على النص في الصورة
            const result = await this.worker.recognize(processedImage || image);

            console.log('تم التعرف على النص:', result.data.text);

            // استخراج منطقة القراءة الآلية
            const mrzData = this.extractMRZ(result.data.text);
            
            // إذا لم يتم العثور على منطقة القراءة الآلية، حاول مرة أخرى مع إعدادات مختلفة
            if (!mrzData.success && !processedImage) {
                console.log('محاولة أخرى مع إعدادات مختلفة...');
                
                // تغيير إعدادات التعرف
                await this.worker.setParameters({
                    tessedit_pageseg_mode: '4' // نمط تقسيم مختلف
                });
                
                // محاولة التعرف مرة أخرى
                const result2 = await this.worker.recognize(image);
                console.log('تم التعرف على النص (المحاولة الثانية):', result2.data.text);
                
                // استخراج منطقة القراءة الآلية مرة أخرى
                const mrzData2 = this.extractMRZ(result2.data.text);
                
                // إعادة الإعدادات الأصلية
                await this.worker.setParameters({
                    tessedit_pageseg_mode: '6'
                });
                
                // إذا نجحت المحاولة الثانية، استخدمها
                if (mrzData2.success) {
                    return mrzData2;
                }
            }

            return mrzData;
        } catch (error) {
            console.error('خطأ في التعرف على منطقة القراءة الآلية:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * معالجة الصورة قبل التعرف عليها
     * @private
     * @param {File|Blob|String} image ملف الصورة أو URL الصورة
     * @returns {Promise<String|null>} وعد يحل إلى الصورة المعالجة أو null إذا فشلت المعالجة
     */
    async preprocessImage(image) {
        // ملاحظة: هذه الدالة يمكن تنفيذها بشكل أفضل باستخدام مكتبة معالجة صور مثل OpenCV.js
        // لكن للتبسيط، نعود null للإشارة إلى أنه لم يتم إجراء أي معالجة
        try {
            // في التنفيذ الفعلي، يمكن إضافة خطوات مثل:
            // 1. تحويل الصورة إلى تدرج رمادي
            // 2. ضبط التباين
            // 3. إزالة الضوضاء
            // 4. الكشف عن الحواف واقتصاص منطقة القراءة الآلية
            return null;
        } catch (error) {
            console.error('خطأ في معالجة الصورة:', error);
            return null;
        }
    }

    /**
     * استخراج بيانات منطقة القراءة الآلية من النص
     * @private
     * @param {String} text النص المستخرج من الصورة
     * @returns {Object} كائن يحتوي على بيانات منطقة القراءة الآلية
     */
    extractMRZ(text) {
        // تقسيم النص إلى أسطر
        const lines = text.split('\n').map(line => line.trim());

        // تنظيف الأسطر وإزالة الأسطر الفارغة
        const cleanedLines = lines.filter(line => line.length > 0);
        
        // البحث عن أسطر منطقة القراءة الآلية (عادة ما تكون 2-3 أسطر)
        const mrzLines = cleanedLines.filter(line => {
            // البحث عن الأسطر التي تحتوي على أحرف MRZ (30+ حرف للسطر)
            return line.length >= 30 && /^[A-Z0-9<]+$/.test(line);
        });

        console.log('أسطر MRZ المكتشفة:', mrzLines);

        // إذا لم يتم العثور على أسطر MRZ
        if (mrzLines.length < 2) {
            // محاولة بديلة للعثور على أسطر تشبه MRZ
            const potentialMRZLines = cleanedLines.filter(line => {
                // البحث عن الأسطر التي تحتوي على نسبة عالية من الأحرف والأرقام والرمز <
                return line.length >= 25 && line.match(/[A-Z0-9<]/g) && line.match(/[A-Z0-9<]/g).length > line.length * 0.8;
            });
            
            console.log('أسطر MRZ المحتملة:', potentialMRZLines);
            
            if (potentialMRZLines.length >= 2) {
                // استخدام الأسطر المحتملة
                const line1 = potentialMRZLines[potentialMRZLines.length - 2].replace(/[^A-Z0-9<]/g, '');
                const line2 = potentialMRZLines[potentialMRZLines.length - 1].replace(/[^A-Z0-9<]/g, '');
                return this.extractMRZData(line1, line2);
            }
            
            return {
                success: false,
                error: 'لم يتم العثور على منطقة القراءة الآلية في الصورة'
            };
        }

        // استخدام آخر سطرين (في حالة وجود أكثر من سطرين)
        const line1 = mrzLines[mrzLines.length - 2];
        const line2 = mrzLines[mrzLines.length - 1];
        
        return this.extractMRZData(line1, line2);
    }
    
    /**
     * استخراج البيانات من سطري MRZ
     * @private
     * @param {String} line1 السطر الأول من MRZ
     * @param {String} line2 السطر الثاني من MRZ
     * @returns {Object} كائن يحتوي على بيانات منطقة القراءة الآلية
     */
    extractMRZData(line1, line2) {
        try {
            console.log('استخراج البيانات من سطري MRZ:', line1, line2);
            
            // نوع المستند (عادة P للجواز)
            const documentType = line1.charAt(0);

            // رمز الدولة المصدرة
            const issuingCountry = line1.substring(2, 5);

            // اسم العائلة
            let surname = '';
            let givenNames = '';

            // البحث عن الاسم في السطر الأول
            const nameParts = line1.substring(5).split('<<');
            if (nameParts.length >= 2) {
                surname = nameParts[0].replace(/</g, ' ').trim();
                givenNames = nameParts[1].replace(/</g, ' ').trim();
            }

            // رقم جواز السفر
            const passportNumber = line2.substring(0, 9).replace(/</g, '');

            // الجنسية
            const nationality = line2.substring(10, 13);

            // تاريخ الميلاد (YYMMDD)
            const dobString = line2.substring(13, 19);
            // تحويل إلى تنسيق تاريخ مناسب
            const dobYear = dobString.substring(0, 2);
            const dobMonth = dobString.substring(2, 4);
            const dobDay = dobString.substring(4, 6);
            // تحويل السنة إلى 4 أرقام (افتراض أن السنوات < 50 هي من القرن 21)
            const fullYear = parseInt(dobYear) < 50 ? '20' + dobYear : '19' + dobYear;
            const dob = `${fullYear}-${dobMonth}-${dobDay}`;

            // الجنس
            const sex = line2.charAt(20);
            const gender = sex === 'M' ? 'ذكر' : sex === 'F' ? 'أنثى' : 'غير محدد';

            // تاريخ انتهاء الصلاحية (YYMMDD)
            const expiryString = line2.substring(21, 27);
            const expiryYear = expiryString.substring(0, 2);
            const expiryMonth = expiryString.substring(2, 4);
            const expiryDay = expiryString.substring(4, 6);
            // تحويل السنة إلى 4 أرقام (افتراض أن جميع تواريخ انتهاء الصلاحية هي من القرن 21)
            const fullExpiryYear = '20' + expiryYear;
            const expiryDate = `${fullExpiryYear}-${expiryMonth}-${expiryDay}`;

            // الرقم الشخصي (إذا كان موجودًا)
            let personalNumber = '';
            if (line2.length > 28 && line2.charAt(28) !== '<') {
                personalNumber = line2.substring(28, 42).replace(/</g, '');
            }

            // تحويل رموز الدول إلى أسماء الدول
            const countryNames = {
                'GHA': 'غانا',
                'EGY': 'مصر',
                'SAU': 'المملكة العربية السعودية',
                'UAE': 'الإمارات العربية المتحدة',
                'KWT': 'الكويت',
                'QAT': 'قطر',
                'BHR': 'البحرين',
                'OMN': 'عمان',
                'JOR': 'الأردن',
                'LBN': 'لبنان',
                'IRQ': 'العراق',
                'SYR': 'سوريا',
                'YEM': 'اليمن',
                'PAK': 'باكستان',
                'IND': 'الهند',
                'BGD': 'بنغلاديش',
                'NPL': 'نيبال',
                'LKA': 'سريلانكا',
                'PHL': 'الفلبين',
                'IDN': 'إندونيسيا',
                'MYS': 'ماليزيا',
                'ETH': 'إثيوبيا',
                'KEN': 'كينيا',
                'NGA': 'نيجيريا',
                'SEN': 'السنغال',
                'MAR': 'المغرب',
                'TUN': 'تونس',
                'DZA': 'الجزائر',
                'SDN': 'السودان',
                'SOM': 'الصومال'
            };
            
            // الحصول على اسم الدولة من الرمز
            const countryName = countryNames[issuingCountry] || countryNames[nationality] || issuingCountry;
            const nationalityName = countryNames[nationality] || nationality;
            
            // تنسيق الاسم الكامل
            const fullName = givenNames ? (givenNames + ' ' + surname).trim() : surname;
            
            // التحقق من صحة التواريخ
            const validDOB = this.isValidDate(dob);
            const validExpiry = this.isValidDate(expiryDate);
            
            // إعداد البيانات للنموذج
            return {
                success: true,
                documentType,
                issuingCountry,
                countryName,
                surname,
                givenNames,
                fullName,
                firstName: givenNames,  // للتوافق مع النموذج
                lastName: surname,      // للتوافق مع النموذج
                passportNumber,
                nationality,
                nationalityName,
                dateOfBirth: validDOB ? dob : '',
                gender,
                sex,
                passportExpiry: validExpiry ? expiryDate : '',  // للتوافق مع النموذج
                expiryDate: validExpiry ? expiryDate : '',
                personalNumber,
                mrzText: line1 + '\n' + line2
            };
        } catch (error) {
            console.error('خطأ في تحليل منطقة القراءة الآلية:', error);
            return {
                success: false,
                error: 'خطأ في تحليل منطقة القراءة الآلية',
                mrzText: line1 + '\n' + line2
            };
        }
    }
    
    /**
     * التحقق من صحة التاريخ
     * @private
     * @param {String} dateStr سلسلة التاريخ بتنسيق YYYY-MM-DD
     * @returns {Boolean} صحيح إذا كان التاريخ صالحاً
     */
    isValidDate(dateStr) {
        if (!dateStr) return false;
        
        // التحقق من تنسيق التاريخ YYYY-MM-DD
        if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
        
        // تحويل التاريخ إلى كائن Date
        const parts = dateStr.split('-');
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // الشهور في JavaScript تبدأ من 0
        const day = parseInt(parts[2], 10);
        
        const date = new Date(year, month, day);
        
        // التحقق من أن التاريخ صالح
        return date.getFullYear() === year && 
               date.getMonth() === month && 
               date.getDate() === day;
    }
}
