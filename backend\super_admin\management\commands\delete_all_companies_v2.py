"""
أمر إدارة Django لحذف جميع الشركات من النظام أو شركة محددة
"""

from django.core.management.base import BaseCommand
from django.db import transaction
import logging
import os
import sqlite3
from backend.super_admin.models import Company, SystemLog
from backend.super_admin.database_connector import test_company_db_connection

# محاولة استيراد وحدة MySQL
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'حذف جميع الشركات من النظام أو شركة محددة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='تأكيد حذف الشركات'
        )
        parser.add_argument(
            '--company-id',
            type=int,
            help='معرف الشركة المراد حذفها (اختياري)'
        )
        parser.add_argument(
            '--serial',
            type=str,
            help='الرقم التسلسلي للشركة المراد حذفها (اختياري)'
        )
        parser.add_argument(
            '--delete-database',
            action='store_true',
            help='حذف قاعدة البيانات الخاصة بالشركة'
        )

    def handle(self, *args, **options):
        confirm = options.get('confirm', False)
        company_id = options.get('company_id')
        serial = options.get('serial')
        delete_database = options.get('delete_database', False)

        # التحقق من وجود تأكيد
        if not confirm:
            if company_id:
                self.stdout.write(self.style.WARNING(f'تحذير: هذا الأمر سيحذف الشركة ذات المعرف {company_id} من النظام.'))
            elif serial:
                self.stdout.write(self.style.WARNING(f'تحذير: هذا الأمر سيحذف الشركة ذات الرقم التسلسلي {serial} من النظام.'))
            else:
                self.stdout.write(self.style.WARNING('تحذير: هذا الأمر سيحذف جميع الشركات من النظام.'))

            self.stdout.write(self.style.WARNING('استخدم --confirm لتأكيد الحذف.'))
            return

        try:
            # تحديد الشركات المراد حذفها
            if company_id:
                # حذف شركة محددة بواسطة المعرف
                companies = Company.objects.filter(id=company_id)
                if not companies.exists():
                    self.stdout.write(self.style.ERROR(f"لا توجد شركة بالمعرف {company_id}"))
                    return
            elif serial:
                # حذف شركة محددة بواسطة الرقم التسلسلي
                companies = Company.objects.filter(serial_number=serial)
                if not companies.exists():
                    self.stdout.write(self.style.ERROR(f"لا توجد شركة بالرقم التسلسلي {serial}"))
                    return
            else:
                # حذف جميع الشركات
                companies = Company.objects.all()

            # عدد الشركات قبل الحذف
            companies_count = companies.count()
            self.stdout.write(self.style.SUCCESS(f"عدد الشركات المراد حذفها: {companies_count}"))

            # عرض معلومات الشركات قبل الحذف
            if companies_count > 0:
                self.stdout.write(self.style.SUCCESS("معلومات الشركات المراد حذفها:"))
                for company in companies:
                    self.stdout.write(self.style.SUCCESS(f"- {company.name} (الرقم التسلسلي: {company.serial_number})"))

                    # حذف قاعدة البيانات إذا تم تحديد ذلك
                    if delete_database:
                        self.delete_company_database(company)

            # حذف الشركات
            with transaction.atomic():
                for company in companies:
                    # تسجيل العملية في سجل النظام
                    SystemLog.objects.create(
                        action='delete_company',
                        details=f"تم حذف الشركة: {company.name} (الرقم التسلسلي: {company.serial_number})",
                        ip_address='127.0.0.1',
                        user_agent='Django Management Command',
                    )

                # حذف الشركات
                deleted_count = companies.delete()

            self.stdout.write(self.style.SUCCESS(f"تم حذف الشركات بنجاح. عدد العناصر المحذوفة: {deleted_count}"))

            # التأكد من عدم وجود شركات بعد الحذف
            if company_id:
                remaining_count = Company.objects.filter(id=company_id).count()
                self.stdout.write(self.style.SUCCESS(f"عدد الشركات المتبقية بالمعرف {company_id}: {remaining_count}"))
            elif serial:
                remaining_count = Company.objects.filter(serial_number=serial).count()
                self.stdout.write(self.style.SUCCESS(f"عدد الشركات المتبقية بالرقم التسلسلي {serial}: {remaining_count}"))
            else:
                remaining_count = Company.objects.count()
                self.stdout.write(self.style.SUCCESS(f"عدد الشركات المتبقية: {remaining_count}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء حذف الشركات: {str(e)}"))
            logger.error(f"حدث خطأ أثناء حذف الشركات: {str(e)}", exc_info=True)

        self.stdout.write(self.style.SUCCESS('تم الانتهاء من عملية حذف الشركات.'))

    def delete_company_database(self, company):
        """
        حذف قاعدة البيانات الخاصة بالشركة

        Args:
            company: كائن الشركة
        """
        try:
            # الحصول على معلومات قاعدة البيانات
            db_name = company.database_name
            db_user = company.database_user
            db_password = company.database_password
            db_host = company.database_host

            # تحديد نوع قاعدة البيانات
            is_mysql = db_host not in ['localhost', '127.0.0.1']

            if is_mysql:
                # حذف قاعدة بيانات MySQL
                if not MYSQL_AVAILABLE:
                    self.stdout.write(self.style.ERROR(f"وحدة mysql.connector غير متوفرة. قم بتثبيتها باستخدام pip install mysql-connector-python"))
                    return

                # الاتصال بخادم MySQL
                connection = mysql.connector.connect(
                    host=db_host,
                    user=db_user,
                    password=db_password
                )

                if connection.is_connected():
                    cursor = connection.cursor()

                    # التحقق من وجود قاعدة البيانات
                    cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
                    result = cursor.fetchone()

                    if result:
                        # حذف قاعدة البيانات
                        cursor.execute(f"DROP DATABASE `{db_name}`")
                        self.stdout.write(self.style.SUCCESS(f"تم حذف قاعدة البيانات {db_name}"))
                    else:
                        self.stdout.write(self.style.WARNING(f"قاعدة البيانات {db_name} غير موجودة"))

                    # إغلاق الاتصال
                    cursor.close()
                    connection.close()
                else:
                    self.stdout.write(self.style.ERROR(f"فشل في الاتصال بخادم قاعدة البيانات"))
            else:
                # حذف قاعدة بيانات SQLite
                db_dir = os.path.join('data', 'databases')
                db_path = os.path.join(db_dir, f"{db_name}.db")

                # التحقق من وجود قاعدة البيانات
                if os.path.exists(db_path):
                    # حذف قاعدة البيانات
                    os.remove(db_path)
                    self.stdout.write(self.style.SUCCESS(f"تم حذف قاعدة البيانات {db_name}"))
                else:
                    self.stdout.write(self.style.WARNING(f"قاعدة البيانات {db_name} غير موجودة"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"حدث خطأ أثناء حذف قاعدة البيانات: {str(e)}"))
            logger.error(f"حدث خطأ أثناء حذف قاعدة البيانات: {str(e)}", exc_info=True)
