from django import template
from django.utils.safestring import mark_safe
from ..models import Currency, SystemSettings

register = template.Library()

@register.filter
def format_currency(amount, currency_code=None):
    """
    تنسيق المبلغ حسب العملة المحددة أو العملة الافتراضية
    
    الاستخدام:
    {{ amount|format_currency }}
    {{ amount|format_currency:"USD" }}
    """
    try:
        # إذا لم يتم تحديد رمز العملة، استخدم العملة الافتراضية
        if not currency_code:
            try:
                settings = SystemSettings.get_settings()
                currency_code = settings.default_currency
            except:
                currency_code = 'IQD'  # افتراضي
        
        # الحصول على العملة
        try:
            currency = Currency.objects.get(code=currency_code, is_active=True)
            return currency.format_amount(amount)
        except Currency.DoesNotExist:
            # إذا لم توجد العملة، استخدم تنسيق افتراضي
            if currency_code == 'IQD':
                return f"{int(float(amount)):,} د.ع"
            else:
                return f"{float(amount):.2f} DUR"
                
    except (ValueError, TypeError):
        return "0"

@register.filter
def currency_symbol(currency_code=None):
    """
    الحصول على رمز العملة
    
    الاستخدام:
    {{ "IQD"|currency_symbol }}
    {{ currency_code|currency_symbol }}
    """
    try:
        if not currency_code:
            try:
                settings = SystemSettings.get_settings()
                currency_code = settings.default_currency
            except:
                currency_code = 'IQD'
        
        try:
            currency = Currency.objects.get(code=currency_code, is_active=True)
            return currency.symbol
        except Currency.DoesNotExist:
            if currency_code == 'IQD':
                return 'د.ع'
            else:
                return 'DUR'
                
    except:
        return ''

@register.simple_tag
def get_default_currency():
    """
    الحصول على العملة الافتراضية
    
    الاستخدام:
    {% get_default_currency as default_currency %}
    """
    try:
        settings = SystemSettings.get_settings()
        return Currency.objects.get(code=settings.default_currency, is_active=True)
    except:
        # إرجاع الدينار العراقي كافتراضي
        try:
            return Currency.objects.get(code='IQD', is_active=True)
        except Currency.DoesNotExist:
            return None

@register.simple_tag
def get_active_currencies():
    """
    الحصول على جميع العملات النشطة
    
    الاستخدام:
    {% get_active_currencies as currencies %}
    """
    return Currency.objects.filter(is_active=True).order_by('name')

@register.simple_tag
def convert_currency(amount, from_currency, to_currency):
    """
    تحويل مبلغ من عملة إلى أخرى
    
    الاستخدام:
    {% convert_currency 1000 "IQD" "USD" as converted_amount %}
    """
    try:
        from_curr = Currency.objects.get(code=from_currency, is_active=True)
        to_curr = Currency.objects.get(code=to_currency, is_active=True)
        
        # تحويل إلى الدولار أولاً
        usd_amount = from_curr.convert_to_usd(amount)
        
        # ثم تحويل إلى العملة المطلوبة
        converted_amount = to_curr.convert_from_usd(usd_amount)
        
        return to_curr.format_amount(converted_amount)
        
    except (Currency.DoesNotExist, ValueError, TypeError):
        return "0"

@register.inclusion_tag('components/currency_converter.html')
def currency_converter(amount=1000):
    """
    عرض محول العملات
    
    الاستخدام:
    {% currency_converter %}
    {% currency_converter 5000 %}
    """
    try:
        settings = SystemSettings.get_settings()
        if not settings.show_currency_converter:
            return {'show_converter': False}
        
        currencies = Currency.objects.filter(is_active=True).order_by('name')
        default_currency = Currency.objects.get(code=settings.default_currency, is_active=True)
        
        return {
            'show_converter': True,
            'currencies': currencies,
            'default_currency': default_currency,
            'amount': amount
        }
    except:
        return {'show_converter': False}

@register.filter
def multiply(value, arg):
    """
    ضرب قيمة في رقم آخر
    
    الاستخدام:
    {{ price|multiply:quantity|format_currency }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def add_currency(value, arg):
    """
    جمع مبلغين
    
    الاستخدام:
    {{ price1|add_currency:price2|format_currency }}
    """
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def subtract_currency(value, arg):
    """
    طرح مبلغين
    
    الاستخدام:
    {{ total|subtract_currency:discount|format_currency }}
    """
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.simple_tag
def currency_examples(currency_code):
    """
    عرض أمثلة على تنسيق العملة
    
    الاستخدام:
    {% currency_examples "IQD" as examples %}
    """
    try:
        currency = Currency.objects.get(code=currency_code, is_active=True)
        examples = []
        
        amounts = [1, 10, 100, 1000, 1500, 10000, 100000]
        
        for amount in amounts:
            examples.append({
                'amount': amount,
                'formatted': currency.format_amount(amount)
            })
        
        return examples
        
    except Currency.DoesNotExist:
        return []

@register.filter
def is_default_currency(currency_code):
    """
    التحقق من كون العملة هي الافتراضية
    
    الاستخدام:
    {% if currency.code|is_default_currency %}
    """
    try:
        settings = SystemSettings.get_settings()
        return currency_code == settings.default_currency
    except:
        return False

@register.simple_tag
def get_exchange_rate(from_currency, to_currency):
    """
    الحصول على سعر الصرف بين عملتين
    
    الاستخدام:
    {% get_exchange_rate "IQD" "USD" as rate %}
    """
    try:
        from_curr = Currency.objects.get(code=from_currency, is_active=True)
        to_curr = Currency.objects.get(code=to_currency, is_active=True)
        
        if from_currency == to_currency:
            return 1.0
        
        # تحويل عبر الدولار
        if from_currency == 'USD':
            return float(to_curr.exchange_rate_to_usd)
        elif to_currency == 'USD':
            return 1.0 / float(from_curr.exchange_rate_to_usd)
        else:
            # تحويل من العملة الأولى إلى الدولار ثم إلى العملة الثانية
            usd_rate = 1.0 / float(from_curr.exchange_rate_to_usd)
            return usd_rate * float(to_curr.exchange_rate_to_usd)
            
    except (Currency.DoesNotExist, ValueError, ZeroDivisionError):
        return 1.0
