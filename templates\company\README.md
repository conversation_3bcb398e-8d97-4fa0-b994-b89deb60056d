# قوالب الشركة - منصة استقدامي

## نظرة عامة

تم تصميم قوالب الشركة لتوفير واجهة مستخدم حديثة وسهلة الاستخدام للشركات المسجلة في منصة استقدامي. تتميز هذه القوالب بالتصميم العصري والألوان المشتقة من الشعار والدعم الكامل للوضع الليلي.

## الميزات الرئيسية

### 🎨 التصميم
- **ألوان الشعار**: أزرق غامق (#1e3a8a)، بنفسجي (#7c3aed)، سماوي (#06b6d4)
- **خط Tajawal**: دعم كامل للغة العربية
- **تدرجات لونية**: تدرجات جميلة مشتقة من ألوان الشعار
- **الوضع الليلي**: دعم كامل مع تخزين التفضيل

### 🧩 المكونات المتاحة
- **البطاقات (Cards)**: بطاقات مرنة للإحصائيات والبيانات
- **الجداول (Tables)**: جداول متجاوبة مع البحث والترتيب
- **الأزرار (Buttons)**: أزرار متعددة الألوان والأحجام
- **القوائم المنسدلة (Dropdowns)**: قوائم تفاعلية مع تأثيرات سلسة
- **شريط التنقل (Navbar)**: شريط علوي مع الإشعارات والملف الشخصي

### 📱 التجاوب
- **متجاوب بالكامل**: يعمل على جميع أحجام الشاشات
- **قائمة محمولة**: قائمة تنقل مخصصة للأجهزة المحمولة
- **تحسينات اللمس**: تفاعل محسن للأجهزة اللمسية

## هيكل الملفات

```
templates/
├── layouts/
│   └── company.blade.php          # القالب الأساسي
├── components/
│   ├── card.blade.php             # مكون البطاقة
│   ├── table.blade.php            # مكون الجدول
│   ├── button.blade.php           # مكون الأزرار
│   ├── dropdown.blade.php         # مكون القائمة المنسدلة
│   └── navbar.blade.php           # مكون شريط التنقل
├── company/
│   ├── dashboard.blade.php        # لوحة التحكم الرئيسية
│   └── README.md                  # هذا الملف
static/
├── css/
│   └── company.css                # ملف CSS مخصص
└── js/
    └── company.js                 # ملف JavaScript مخصص
```

## كيفية الاستخدام

### 1. إنشاء صفحة جديدة

```blade
@extends('layouts.company')

@section('title', 'عنوان الصفحة')

@section('content')
    <!-- محتوى الصفحة هنا -->
@endsection
```

### 2. استخدام البطاقات

```blade
@include('components.card', [
    'title' => 'إجمالي العمال',
    'icon' => 'fas fa-hard-hat',
    'value' => '150',
    'description' => 'عامل نشط',
    'color' => 'blue',
    'trend' => '+12%',
    'trendUp' => true
])
```

### 3. استخدام الجداول

```blade
@include('components.table', [
    'title' => 'قائمة العمال',
    'headers' => ['الاسم', 'الجنسية', 'المهنة', 'الحالة'],
    'data' => $workers,
    'actions' => true,
    'searchable' => true,
    'sortable' => true,
    'exportable' => true
])
```

### 4. استخدام الأزرار

```blade
@include('components.button', [
    'text' => 'حفظ',
    'type' => 'primary',
    'icon' => 'fas fa-save',
    'onclick' => 'saveData()'
])
```

### 5. استخدام القوائم المنسدلة

```blade
@include('components.dropdown', [
    'trigger' => 'الإجراءات',
    'triggerIcon' => 'fas fa-cog',
    'items' => [
        [
            'text' => 'عرض',
            'icon' => 'fas fa-eye',
            'href' => '/view/1'
        ],
        [
            'text' => 'تعديل',
            'icon' => 'fas fa-edit',
            'onclick' => 'editItem(1)'
        ]
    ]
])
```

## الألوان المتاحة

### ألوان البطاقات والأزرار
- `blue` - أزرق (#1e3a8a)
- `purple` - بنفسجي (#7c3aed)
- `cyan` - سماوي (#06b6d4)
- `green` - أخضر (#10b981)
- `red` - أحمر (#ef4444)
- `yellow` - أصفر (#f59e0b)

### أنواع الأزرار
- `primary` - أساسي (تدرج بنفسجي-أزرق)
- `secondary` - ثانوي (رمادي)
- `success` - نجاح (أخضر)
- `danger` - خطر (أحمر)
- `warning` - تحذير (أصفر)
- `info` - معلومات (سماوي)

## الوظائف JavaScript المتاحة

### إدارة الرسائل
```javascript
showSuccess('تم الحفظ بنجاح');
showError('حدث خطأ');
showWarning('تحذير');
showInfo('معلومة');
```

### التأكيدات
```javascript
confirmAction('تأكيد الحذف', 'هل أنت متأكد؟')
.then((result) => {
    if (result.isConfirmed) {
        // تنفيذ الإجراء
    }
});
```

### التحميل
```javascript
showLoading('جاري الحفظ...');
// تنفيذ العملية
closeLoading();
```

### تصدير البيانات
```javascript
exportToCSV('table-id', 'filename.csv');
```

### تنسيق البيانات
```javascript
formatNumber(1234567);     // 1,234,567
formatDate('2024-01-15');   // 15 يناير 2024
formatCurrency(1000);       // 1,000.00 ر.س
```

## التخصيص

### إضافة ألوان جديدة
يمكن إضافة ألوان جديدة في ملف `static/css/company.css`:

```css
:root {
    --custom-color: #your-color;
}
```

### تخصيص المكونات
يمكن تخصيص أي مكون عبر تمرير معاملات إضافية أو تعديل ملف المكون مباشرة.

### إضافة وظائف JavaScript
يمكن إضافة وظائف جديدة في ملف `static/js/company.js` أو في قسم `@section('scripts')`.

## أمثلة متقدمة

### بطاقة مع شريط تقدم
```blade
@include('components.card', [
    'title' => 'تقدم المشروع',
    'icon' => 'fas fa-tasks',
    'color' => 'purple',
    'progress' => 75,
    'progressLabel' => 'المهام المكتملة'
])
```

### جدول مع إجراءات مخصصة
```blade
@include('components.table', [
    'headers' => ['الاسم', 'الحالة'],
    'data' => $data,
    'actions' => true,
    'customActions' => '<button onclick="customAction(:id)" class="text-purple-600"><i class="fas fa-star"></i></button>'
])
```

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات المكونات للمزيد من الخيارات
3. اطلع على صفحة `dashboard.blade.php` للأمثلة العملية

## الإصدار
الإصدار الحالي: 2.0
تاريخ آخر تحديث: {{ date('Y/m/d') }}
